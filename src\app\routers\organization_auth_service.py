import logging
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi_pagination import Params, Page
from fastapi_pagination.ext.sqlalchemy import paginate

from app import settings, schema, crud, exceptions
from app.database import create_session, SessionLocal
from app.permissions import permission, decode_auth_token_from_headers

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/organization_authorization_service",
    tags=['organization_authorization_service', ],
    dependencies=[Depends(permission)]
)


async def oas_filter_parameters(name: str = None):
    return {'name': name}


@router.post("/", response_model=schema.OrganizationAuthenticationServiceResponse,
             status_code=status.HTTP_201_CREATED)
async def add_oas(request: Request, oas_data: schema.OrganizationAuthenticationService,
                  dbsession: SessionLocal = Depends(create_session)):
    """
    Add an API Key Pair

    :param OAS oas_data: OAS Data
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        is_superuser = crud.get_user_by_id(dbsession, auth_token_data['user_id']).is_superuser
        if not is_superuser:
            raise HTTPException(400,
                                'You do not have rights to create API Key, '
                                'please contact Administrator.')
        db_oas = crud.create_oas(dbsession, oas_data)
        return db_oas
    except exceptions.ApolloOASError as e:
        raise HTTPException(400, e.__str__())


@router.get("/", response_model=Page[schema.OrganizationAuthenticationServiceResponse],
            status_code=status.HTTP_200_OK)
async def get_all_oas(filter_oas: dict = Depends(oas_filter_parameters),
                      params: Params = Depends(),
                      dbsession: SessionLocal = Depends(create_session)):
    query = crud.get_oas_with_filters(db=dbsession, oas_filters=filter_oas)
    return paginate(query, params)


@router.get("/{oas_id}",
            status_code=status.HTTP_200_OK, response_model=schema.OrganizationAuthenticationServiceResponse)
async def get_oas(oas_id: UUID,
                  dbsession: SessionLocal = Depends(create_session)):
    """
    Get an OAS

    :param str oas_id: Target OAS ID
    """
    try:
        db_oas = crud.get_oas(dbsession, oas_id)
        return db_oas
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/{oas_id}/regenerate", response_model=schema.OrganizationAuthenticationServiceResponse,
             status_code=status.HTTP_200_OK)
async def regenerate_oas(request: Request, oas_id: UUID,
                         dbsession: SessionLocal = Depends(create_session)):
    """
    Add an API Key Pair

    :param OAS oas_data: OAS Data
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        is_superuser = crud.get_user_by_id(dbsession, auth_token_data['user_id']).is_superuser
        if not is_superuser:
            raise HTTPException(400,
                                'You do not have rights to regenerate API Key, '
                                'please contact Administrator.')
        db_oas = crud.get_oas(dbsession, oas_id)
        if db_oas.is_editable is False:
            raise HTTPException(400,
                                'The Selected API Key is not editable.')
        new_oas = crud.regenerate_oas(dbsession, oas_id)
        return new_oas
    except exceptions.ApolloOASError as e:
        raise HTTPException(400, e.__str__())


@router.patch("/{oas_id}",
              status_code=status.HTTP_200_OK, response_model=schema.OrganizationAuthenticationServiceResponse)
async def update_oas(request: Request, oas_data: schema.OASUpdate, oas_id: UUID,
                     dbsession: SessionLocal = Depends(create_session)):
    """
    Update an OAS

    :param str oas_id: Target OAS ID
    :param OASUpdate oas_data: OAS Code Data
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        is_superuser = crud.get_user_by_id(dbsession, auth_token_data['user_id']).is_superuser
        if not is_superuser:
            raise HTTPException(400,
                                'You do not have rights to update API Key, '
                                'please contact Administrator.')
        db_oas = crud.get_oas(dbsession, oas_id)
        if db_oas.is_editable is False:
            raise HTTPException(400,
                                'The Selected API Key is not editable.')
        db_oas = crud.update_oas(dbsession, oas_data, oas_id)

        return db_oas
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{oas_id}", status_code=status.HTTP_200_OK)
async def delete_oas(request: Request, oas_id: UUID,
                     dbsession: SessionLocal = Depends(create_session)):
    """
    Delete an OAS

    :param str oas_id: Target OAS ID
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        is_superuser = crud.get_user_by_id(dbsession, auth_token_data['user_id']).is_superuser
        if not is_superuser:
            raise HTTPException(400,
                                'You do not have rights to update API Key, '
                                'please contact Administrator.')
        db_oas = crud.get_oas(dbsession, oas_id)
        if db_oas.is_editable is False:
            raise HTTPException(400,
                                'The Selected API Key is not editable.')
        crud.delete_oas(dbsession, oas_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
