import sqlalchemy as db
from .base import BaseModel


class ProviderLog(BaseModel):
    '''Class to log call for Plug and Charge'''
    __tablename__ = 'provider_log'

    name = db.Column(db.String)
    url = db.Column(db.String)
    http_method = db.Column(db.String)
    request_at = db.Column(db.DateTime(timezone=True))
    request_header = db.Column(db.String)
    request_body = db.Column(db.String)
    response_status_code = db.Column(db.String)
    response_body = db.Column(db.String)
    response_at = db.Column(db.DateTime(timezone=True))


db.Index('provider_log_created_at_idx', ProviderLog.created_at, unique=False)
