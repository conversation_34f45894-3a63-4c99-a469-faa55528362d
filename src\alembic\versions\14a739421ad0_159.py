"""159

Revision ID: 14a739421ad0
Revises: f6349c46a80b
Create Date: 2025-04-15 05:43:43.731369

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '14a739421ad0'
down_revision = 'f6349c46a80b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_prepaid_wallet_plan', sa.Column('timezone', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_prepaid_wallet_plan', 'timezone')
    # ### end Alembic commands ###
