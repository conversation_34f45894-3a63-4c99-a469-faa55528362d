import json
import logging
from datetime import datetime
from dateutil import parser

from fastapi import APIRouter, Depends, Request, HTTPException, status
from fastapi_pagination import Params, Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.exc import IntegrityError

from app import settings, models, schema, crud, exceptions
from app import utils
from app.database import create_session, SessionLocal
from app.middlewares import deactivate_audit
from app.ruby_proxy_utils import start_charging_ruby_ocpi_proxy, stop_charging_ruby_ocpi_proxy
from app.schema import OCPIHistories
from app.utils import (
    send_request, generate_charger_header, push_update,
    calculate_charging_usage, calculate_charging_cost
)
from app.utils import decode_auth_token_from_headers

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH
CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/ocpi",
    tags=['ocpi', ],
)


def ocpi_log_filters(start_date: datetime = None, end_date: datetime = None, ocpi_module: str = None,
                     request_method: str = None, response_code: int = None, request_url: str = None,
                     request_type: str = "Outgoing"):

    # defautled to Outgoing because ticket is previously developed on Outgoing only
    return {"start_date": start_date, "end_date": end_date, "ocpi_module": ocpi_module,
            "request_method": request_method, "response_code": response_code, "request_url": request_url,
            "request_type": request_type}


@router.get('/push-ocpi-locations', status_code=200)
async def push_ocpi_locations(request: Request, page: int = 1, size: int = 50,
                              is_private: bool = False,
                              dbsession: SessionLocal = Depends(create_session)):
    """
    Push OCPI Location to connected parties with pagination (Limit and Page)
    """
    # TODO: Temporary disable
    # return True
    auth_token_data = decode_auth_token_from_headers(request.headers)
    if not auth_token_data:
        raise HTTPException(403, "You are not allowed to perform this action")
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    query_params = request.query_params
    logger.debug('header for request to charger service: %s', headers)
    db_user = crud.get_user_by_id(dbsession, user_id)
    if db_user.is_superuser:  # pylint: disable=too-many-nested-blocks
        try:
            url = f'{CHARGER_URL_PREFIX}/location/'
            response = await send_request('GET', url, headers=headers, query_params=query_params)
            if response.status_code == 200 and len(response.json()['items']) > 0:
                for location in response.json()['items']:
                    await push_update(dbsession, 'location', location)
                    charge_points = location.get('charge_points', [])
                    # print('charge_points', charge_points)
                    for charge_point in charge_points:
                        if not charge_point.get('is_deleted', False):
                            connectors = charge_point.get('connectors', [])
                            for connector in connectors:
                                connector_id = connector['id']
                                connector_url = f'{CHARGER_URL_PREFIX}/connector/{connector_id}'
                                connector_response = await send_request('GET', connector_url, headers=headers)
                                await push_update(dbsession, 'connector', connector_response.json())

                return response.json()
            raise HTTPException(404, "Location not found.")
        except (exceptions.ApolloUserDataError, exceptions.ApolloDuplicateMembershipError,
                exceptions.ApolloExternalTokenDataError) as e:
            raise HTTPException(400, e.__str__())
    raise HTTPException(403, "You are not allowed to perform this action")


@router.get('/cdr', status_code=200)
async def get_cdr(request: Request, params: Params = Depends(),  # pylint: disable=too-many-locals  # noqa
                  dbsession: SessionLocal = Depends(create_session)):
    party_id = request.headers.get('party_id', 'None')
    country_code = request.headers.get('country_code', 'None')

    headers = {
        'party_id': party_id,
        'country_code': country_code,
    }
    query_params = request.query_params
    dict_params = dict(query_params)
    dict_params = {**dict_params, **dict(params)}

    url = f'{CHARGER_URL_PREFIX}/ocpi/cdr'
    response = await send_request('GET', url, headers=headers, query_params=dict_params)
    response_json = response.json()
    # print(response_json)
    for i in range(len(response_json['items'])):
        cpo_cs = response_json['items'][i]
        subscription_discount = {'subscription_discount': float(0)}
        organization_name = 'MGT'
        if str(settings.OCPI_PARTY_ID).upper() == 'CDG':
            organization_name = 'CDG'
        elif str(settings.OCPI_PARTY_ID).upper() == 'HGM':
            organization_name = 'DCH'

        charging_session = response_json['items'][i]['charging_session']
        # cdr_data = charging_session['meta']['cdr_data']
        # cdr_uid = cdr_data['uid']
        # db_cpo_token = dbsession.query(models.OCPICPOToken).filter(
        #     models.OCPICPOToken.partner_ocpi_cpo_token_id == cdr_uid  # nosec
        # ).first()

        if charging_session['meta']['billing_info']['billing_type'] == schema.BillingType.kwh:

            usage = calculate_charging_usage(
                charging_session['meter_start'],
                charging_session['meter_stop'],
                charging_session['meta']['billing_info']['billing_type'],
                charging_session['meta']['billing_info']['billing_cycle']
            )
        else:
            input_format = "%Y-%m-%dT%H:%M:%S%z"
            output_format = "%Y-%m-%dT%H:%M:%SZ"

            parsed_date = datetime.strptime(charging_session['session_start'], input_format)
            charging_session['session_start'] = parsed_date.strftime(output_format)

            parsed_date = datetime.strptime(charging_session['session_end'], input_format)
            charging_session['session_end'] = parsed_date.strftime(output_format)

            usage = calculate_charging_usage(
                charging_session['session_start'],
                charging_session['session_end'],
                charging_session['meta']['billing_info']['billing_type'],
                charging_session['meta']['billing_info']['billing_cycle']
            )
        charging_usage = float(charging_session['meter_stop']) - float(charging_session['meter_start'])

        # duration = datetime.strptime(charging_session['session_end'], "%Y-%m-%dT%H:%M:%SZ") - datetime.strptime(
        #     charging_session['session_start'], "%Y-%m-%dT%H:%M:%SZ")

        # changed to isoparse as it can automatically parse iso8601 timestamps
        duration = parser.isoparse(charging_session['session_end']) - parser.isoparse(
            charging_session['session_start'])
        amount = calculate_charging_cost(
            unit_price=charging_session['meta']['billing_info']['billing_unit_fee'],
            usage=usage,
            constant=charging_session['meta']['billing_info']['connection_fee']
        )

        # not completed
        # tax_info = calculate_tax_amount(dbsession,
        #                                 charging_session['meta']['location'][''],
        #                                 amount,
        #                                 charging_session['meta']['billing_info']['billing_currency'])
        #

        # No discount for EXT
        meta_data = {
            'transaction_id': str(charging_session['transaction_id']),
            'organization_name': organization_name
        }

        invoice_number = str(organization_name) + '-' + str(charging_session['transaction_id'])

        charging_session_bill = schema.ChargingSessionBill(
            charging_session_id=charging_session['id'],
            usage_amount=float(amount),
            usage_type=charging_session['meta']['billing_info']['billing_type'],
            discount=subscription_discount,
            meta=meta_data,
            invoice_number=invoice_number,
            charging_session_type=charging_session['charging_session_type']
        )

        # free charging within 60 seconds
        if duration.total_seconds() <= 60:
            amount = 0
            charging_session_bill.usage_amount = 0
            charging_session_bill.discount = {"subscription_discount": 0.0}
            subscription_discount = {"subscription_discount": 0.0}

        try:
            db_charging_session_bill = dbsession.query(models.ChargingSessionBill).filter(
                models.ChargingSessionBill.charging_session_id == charging_session['id'],
            ).first()
            if not db_charging_session_bill:
                db_charging_session_bill = models.ChargingSessionBill(**charging_session_bill.dict())
                dbsession.add(db_charging_session_bill)
                dbsession.commit()
        except IntegrityError as e:
            logger.error(e)
            return None

        db_charging_session_bill = schema.ChargingSessionBillResponse.from_orm(db_charging_session_bill).dict()
        total_charge_amount = float(amount) - float(subscription_discount['subscription_discount'])
        charging_session['total_charge_amount'] = total_charge_amount
        charging_session['charging_usage'] = charging_usage
        charging_session['duration'] = float(duration.total_seconds())

        cs = {
            'cpo_cs': cpo_cs,
            'charging_session': charging_session,
            'charging_session_bill': db_charging_session_bill,
        }
        response_json['items'][i] = cs

    return response_json


@router.post('/start-charging-ruby-ocpi')
async def start_charging_ruby_ocpi(id_tag: str, serial_number: str, connector_number: str,
                                   connector_id: str, charge_point_id: str,
                                   cpo_charging_session: dict,
                                   dbsession: SessionLocal = Depends(create_session)):
    url = f'{CHARGER_URL_PREFIX}/ocpi/rails/cpo_session'

    headers = {
        'id_tag': id_tag
    }
    response = await send_request('POST', url, data=json.dumps(cpo_charging_session['cpo_charging_session']),
                                  headers=headers)
    response_json = response.json()
    id_tag_with_cpo = id_tag + '-' + str(response_json['cpo_transaction_id'])
    result = await start_charging_ruby_ocpi_proxy(dbsession, id_tag_with_cpo, serial_number, connector_number,
                                                  str(connector_id), charge_point_id)
    url = f'{CHARGER_URL_PREFIX}/ocpi/rails/session'
    charging_session = result
    headers = {
        'id_tag': id_tag_with_cpo
    }

    response = await send_request('POST', url, data=json.dumps(charging_session), headers=headers)

    return response.json()


@router.post('/stop-charging-ruby-ocpi')
async def stop_charging_ruby_ocpi(id_tag: str, serial_number: str, connector_number: str,
                                  connector_id: str, charge_point_id: str,
                                  dbsession: SessionLocal = Depends(create_session)):
    await stop_charging_ruby_ocpi_proxy(dbsession, id_tag, serial_number, connector_number,
                                        str(connector_id), charge_point_id)

    url = f'{CHARGER_URL_PREFIX}/ocpi/rails/stop'
    headers = {
        'id_tag': id_tag
    }
    data = {
        'charge_point_id': charge_point_id,
        'connector_id': connector_id,
    }

    response = await send_request('POST', url, query_params=data, headers=headers)
    return response.json()


@router.post('/incoming-log')
async def log_ocpi_incoming(ocpi_histories: OCPIHistories, dbsession: SessionLocal = Depends(create_session)):
    deactivate_audit()
    return crud.create_ocpi_histories(dbsession, ocpi_histories)


@router.get('/ocpi-logs', status_code=status.HTTP_200_OK,
            response_model=Page[schema.EMSPLogs]
            )
async def get_ocpi_logs(request: Request, params: Params = Depends(),
                        filters: dict = Depends(ocpi_log_filters),
                        dbsession: SessionLocal = Depends(create_session),
                        ):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    query = utils.get_ocpi_histories(dbsession, filters)
    return paginate(query, params)
