from typing import List
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy.exc import NoResultFound

from app import exceptions

from .auth import MembershipCRUD


def add_favorite_cp(db: Session, member_id: str, charge_point_id: str) -> List[UUID]:
    try:
        db_member = MembershipCRUD.get(db, member_id)
        favorite_cp_list = list(db_member.favorite_charge_points)
        if charge_point_id not in favorite_cp_list:
            favorite_cp_list.append(charge_point_id)
        db_member = MembershipCRUD.update(db, member_id,
                                          {'favorite_charge_points': favorite_cp_list}, check_permission=False)
        return db_member.favorite_charge_points
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_member_favorite_cp_list(db: Session, member_id: str) -> List[UUID]:
    try:
        db_member = MembershipCRUD.get(db, member_id)
        return db_member.favorite_charge_points
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def remove_favorite_cp(db: Session, member_id: str, charge_point_id: str) -> List[UUID]:
    try:
        db_member = MembershipCRUD.get(db, member_id)
        favorite_cp_list = list(db_member.favorite_charge_points)
        if charge_point_id in favorite_cp_list:
            favorite_cp_list.remove(charge_point_id)
        MembershipCRUD.update(db, member_id, {'favorite_charge_points': favorite_cp_list}, check_permission=False)
        return favorite_cp_list
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')
