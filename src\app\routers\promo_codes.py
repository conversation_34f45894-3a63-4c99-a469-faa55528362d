import logging
from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app import settings, schema, crud, exceptions
from app.database import create_session, SessionLocal
from app.permissions import permission, decode_auth_token_from_headers

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH


router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/promo_code",
    tags=['promo-code', ],
    dependencies=[Depends(permission)],
)


@router.post("/",
             status_code=status.HTTP_201_CREATED, response_model=schema.PromoCodeResponse)
async def add_promo_code(promo_code_data: schema.PromoCode,
                         dbsession: SessionLocal = Depends(create_session)):
    """
    Add a Promo Code

    :param PromoCode promo_code_data: Promo Code Data
    """
    try:
        db_promo_code = crud.create_promo_code(dbsession, promo_code_data)
        return db_promo_code
    except exceptions.ApolloPromoCodeError as e:
        raise HTTPException(400, e.__str__())


@router.get("/",
            status_code=status.HTTP_200_OK, response_model=List[schema.PromoCodeResponse])
async def get_promo_list(dbsession: SessionLocal = Depends(create_session)):
    """
    Get List of Promo Codes
    """
    db_promo_code_list = crud.get_promo_code_list(dbsession)
    return db_promo_code_list


@router.get("/code/{code}",
            status_code=status.HTTP_200_OK, response_model=schema.PromoCodeResponse)
async def get_promo_code_by_code(code: str,
                                 dbsession: SessionLocal = Depends(create_session)):
    """
    Get a Promo Code by code

    :param str code: Promo Code Coupon Code
    """
    try:
        db_promo_code = crud.get_promo_code_by_code(dbsession, code)
        return db_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/{promo_code_id}",
            status_code=status.HTTP_200_OK, response_model=schema.PromoCodeResponse)
async def get_promo_code(promo_code_id: UUID,
                         dbsession: SessionLocal = Depends(create_session)):
    """
    Get a Promo Code

    :param str promo_code_id: Target Promo Code ID
    """
    try:
        db_promo_code = crud.get_promo_code(dbsession, promo_code_id)
        return db_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/{promo_code_id}/remaining", status_code=status.HTTP_200_OK)
async def get_promo_code_remaining(request: Request, promo_code_id: str,
                                   dbsession: SessionLocal = Depends(create_session)) -> float:
    """
    Get remaining amount of an Accumulative Promo Code

    :param str promo_code_id: Target Promo Code ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_promo_code = crud.get_promo_code(dbsession, promo_code_id)
        if db_promo_code.type == schema.PromoType.accumulative:
            return crud.get_remaining_accumulative_promo(dbsession, promo_code_id, membership_id)
        raise HTTPException(400, 'Promo code is not of type accumulative')
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.patch("/{promo_code_id}",
              status_code=status.HTTP_200_OK, response_model=schema.PromoCodeResponse)
async def update_promo_code(promo_code_id: str,
                            promo_code_data: schema.PromoCodeUpdate,
                            dbsession: SessionLocal = Depends(create_session)):
    """
    Update a Promo Code

    :param str promo_code_id: Target Promo Code ID
    :param PromoCode promo_code_data: Promo Code Data
    """
    try:
        db_promo_code = crud.update_promo_code(dbsession, promo_code_id, promo_code_data)
        return db_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{promo_code_id}", status_code=status.HTTP_200_OK)
async def delete_promo_code(promo_code_id: str,
                            dbsession: SessionLocal = Depends(create_session)):
    """
    Delete a Promo Code

    :param str promo_code_id: Target Promo Code ID
    """
    try:
        crud.delete_promo_code(dbsession, promo_code_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
    except exceptions.ApolloPromoCodeError:
        raise HTTPException(400, 'Promo Code cannot be deleted')


@router.post("/{promo_code_id}/members",
             status_code=status.HTTP_200_OK, response_model=schema.PromoCodeResponse)
async def assign_promo_code_to_members(promo_code_id: str,
                                       member_id_list: List[str],
                                       is_excluded: bool = False,
                                       dbsession: SessionLocal = Depends(create_session)):
    """
    Assign promo code to members

    :param str promo_code_id: Target Promo Code ID
    :param list member_id_list: List of Membership IDs
    :param bool is_excluded: Wether the Membership is Included or Excluded
    """
    try:
        db_promo_code = crud.get_promo_code(dbsession, promo_code_id)
        for member_id in member_id_list:
            crud.assign_promo_code_to_member(dbsession, promo_code_id, member_id, is_excluded)
        return db_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{promo_code_id}/members",
               status_code=status.HTTP_200_OK, response_model=schema.PromoCodeResponse)
async def delete_promo_code_association_with_members(promo_code_id: str,
                                                     member_id_list: List[str],
                                                     dbsession: SessionLocal = Depends(create_session)):
    """
    Delete promo code association with members

    :param str promo_code_id: Target Promo Code ID
    :param list member_id_list: List of Membership IDs
    """
    try:
        db_promo_code = crud.get_promo_code(dbsession, promo_code_id)
        for member_id in member_id_list:
            crud.delete_promo_code_association_with_member(dbsession, promo_code_id, member_id)
        return db_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/{promo_code_id}/charge_points",
             status_code=status.HTTP_200_OK, response_model=schema.PromoCodeResponse)
async def assign_promo_code_to_charge_points(promo_code_id: str,
                                             charge_point_id_list: List[str],
                                             is_excluded: bool = False,
                                             dbsession: SessionLocal = Depends(create_session)):
    """
    Assign promo code to charge points

    :param str promo_code_id: Target Promo Code ID
    :param list charge_point_id_list: List of Charge Point IDs
    :param bool is_excluded: Wether the Charge Point is Included or Excluded
    """
    try:
        db_promo_code = crud.get_promo_code(dbsession, promo_code_id)
        for charge_point_id in charge_point_id_list:
            crud.assign_promo_code_to_charge_point(dbsession, promo_code_id, charge_point_id, is_excluded)
        return db_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{promo_code_id}/charge_points",
               status_code=status.HTTP_200_OK, response_model=schema.PromoCodeResponse)
async def delete_promo_code_association_with_charge_points(promo_code_id: str,
                                                           charge_point_id_list: List[str],
                                                           dbsession: SessionLocal = Depends(create_session)):
    """
    Delete promo code association with charge points

    :param str promo_code_id: Target Promo Code ID
    :param list charge_point_id_list: List of Charge Point IDs
    """
    try:
        db_promo_code = crud.get_promo_code(dbsession, promo_code_id)
        for charge_point_id in charge_point_id_list:
            crud.delete_promo_code_association_with_charge_point(dbsession, promo_code_id, charge_point_id)
        return db_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/{promo_code_id}/operators",
             status_code=status.HTTP_200_OK, response_model=schema.PromoCodeResponse)
async def assign_promo_code_to_operators(promo_code_id: str,
                                         operator_id_list: List[str],
                                         dbsession: SessionLocal = Depends(create_session)):
    """
    Assign promo code to operators

    :param str promo_code_id: Target Promo Code ID
    :param list operator_id_list: List of Operator IDs
    """
    try:
        db_promo_code = crud.get_promo_code(dbsession, promo_code_id)
        for operator_id in operator_id_list:
            crud.assign_promo_code_to_operator(dbsession, promo_code_id, operator_id)
        return db_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{promo_code_id}/operators",
               status_code=status.HTTP_200_OK, response_model=schema.PromoCodeResponse)
async def delete_promo_code_association_with_operators(promo_code_id: str,
                                                       operator_id_list: List[str],
                                                       dbsession: SessionLocal = Depends(create_session)):
    """
    Delete promo code association with operators

    :param str promo_code_id: Target Promo Code ID
    :param list operator_id_list: List of Operator IDs
    """
    try:
        db_promo_code = crud.get_promo_code(dbsession, promo_code_id)
        for operator_id in operator_id_list:
            crud.delete_promo_code_association_with_operator(dbsession, promo_code_id, operator_id)
        return db_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/{promo_code_id}/organizations",
             status_code=status.HTTP_200_OK, response_model=schema.PromoCodeResponse)
async def assign_promo_code_to_organizations(promo_code_id: str,
                                             organization_id_list: List[str],
                                             dbsession: SessionLocal = Depends(create_session)):
    """
    Assign promo code to organizations

    :param str promo_code_id: Target Promo Code ID
    :param list organization_id_list: List of Organization IDs
    """
    try:
        db_promo_code = crud.get_promo_code(dbsession, promo_code_id, check_permission=False)
        for organization_id in organization_id_list:
            crud.assign_promo_code_to_organization(dbsession, promo_code_id, organization_id)
        return db_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{promo_code_id}/organizations",
               status_code=status.HTTP_200_OK, response_model=schema.PromoCodeResponse)
async def delete_promo_code_association_with_organizations(promo_code_id: str,
                                                           organization_id_list: List[str],
                                                           dbsession: SessionLocal = Depends(create_session)):
    """
    Delete promo code association with organizations

    :param str promo_code_id: Target Promo Code ID
    :param list organization_id_list: List of Organization IDs
    """
    try:
        db_promo_code = crud.get_promo_code(dbsession, promo_code_id, check_permission=False)
        for organization_id in organization_id_list:
            crud.delete_promo_code_association_with_organization(dbsession, promo_code_id, organization_id)
        return db_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
