"""180

Revision ID: e7663f12b887
Revises: 979346a8f7b9
Create Date: 2025-06-25 13:57:33.540564

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e7663f12b887'
down_revision = '979346a8f7b9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_auth_external_token', sa.Column('markup_percentage', sa.Numeric(scale=3), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_auth_external_token', 'markup_percentage')
    # ### end Alembic commands ###
