import secrets
from contextlib import contextmanager
from datetime import datetime, timedelta
from unittest.mock import patch

import jwt
import pytest
from fastapi.testclient import TestClient

from app import settings, schema, crud
from app.database import SessionLocal, create_session, Base, engine
from app.main import app, ROOT_PATH
from app.models import Wallet, Membership
from app.schema import MembershipType
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory, CreditCardFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory,
                                 OrganizationAuthenticationServiceFactory, WalletFactory,
                                 PaymentRequestFactory, MembershipExtendedFactory)

client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        WalletFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PaymentRequestFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipExtendedFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session

WALLET_BASE_URL = f'{ROOT_PATH}/api/v1/wallet'


@patch('app.crud.base.BaseCRUD.membership')
def test_get_pre_auth_info(membership_mock, test_db):
    url = f'{WALLET_BASE_URL}/pre-auth-info?currency=MYR'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=MembershipType.regular_user,
        )
        db.commit()
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        PaymentRequestFactory(
            amount='14.20',
            type=schema.PaymentRequestType.wallet,
            billing_description='Topup',
            reason=schema.PaymentRequestReason.deposit,
            member_id=mem.id,
            status=schema.PaymentRequestStatus.done,
            currency=schema.Currency.myr
        )
        wallet = WalletFactory(
            member_id=str(mem.id),
            balance='10.0',
            currency='MYR'
        )
        db.commit()

        CreditCardFactory(
            member_id=str(mem.id),
            primary=True,
            last_four_digit='1111',
            currency='MYR'
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{WALLET_BASE_URL}/pre-auth-info',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            'exp': datetime.now() + timedelta(days=1),
            'user_id': str(user.id),
            'membership_id': str(mem.id)
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        crud.update_wallet_balance(db, wallet.id)

        response = client.get(url, headers=headers)
        assert response.status_code == 200
        assert response.json()['success']
        assert response.json()['data']
        assert response.json()['data']['pre_auth_info']['pre_auth_currency'] == 'MYR'
        assert response.json()['data']['pre_auth_info']['pre_auth_amount'] == settings.MY_PRE_AUTH_DEFAULT_AMOUNT
        assert response.json()['data']['credit_card']['last_four_digit'] == '1111'
        wallet = db.query(Wallet).get(str(wallet.id))
        assert float(wallet.balance) == 14.20


def test_update_pre_auth_preferred(test_db):
    url = f'{WALLET_BASE_URL}/pre-auth-preferred'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        membership = db.query(Membership).get(str(mem.id))
        assert membership.preferred_payment_method == 'Credit-Card'

        MembershipExtendedFactory(
            membership_id=str(mem.id)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{WALLET_BASE_URL}/pre-auth-preferred',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            'exp': datetime.now() + timedelta(days=1),
            'user_id': str(user.id),
            'membership_id': str(mem.id)
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        data = {
            'preferred_payment_method': 'Wallet'
        }
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }

        response = client.patch(url, headers=headers, json=data)
        assert response.status_code == 200
        assert response.json()['success']
        assert response.json()['data']['preferred_payment_method'] == 'Wallet'


def test_update_normal_preferred_payment_flow(test_db):
    url = f'{WALLET_BASE_URL}/preferred-payment-flow'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        membership = db.query(Membership).get(str(mem.id))
        assert membership.preferred_payment_flow == 'Credit-Card'

        MembershipExtendedFactory(
            membership_id=str(mem.id)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{WALLET_BASE_URL}/preferred-payment-flow',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            'exp': datetime.now() + timedelta(days=1),
            'user_id': str(user.id),
            'membership_id': str(mem.id)
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        data = {
            'preferred_payment_flow': 'Wallet'
        }
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }

        response = client.patch(url, headers=headers, json=data)
        assert response.status_code == 200
        assert response.json()['success']
        assert response.json()['data']['preferred_payment_flow'] == 'Wallet'
