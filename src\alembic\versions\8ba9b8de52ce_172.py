"""172

Revision ID: 8ba9b8de52ce
Revises: 4653db4b3c98
Create Date: 2025-05-30 11:44:04.705728

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8ba9b8de52ce'
down_revision = '4653db4b3c98'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_e_credit_note', sa.Column('lhdn_uid', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('lhdn_qr', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('lhdn_validated_date_time', sa.DateTime(timezone=True), nullable=True))
    op.add_column('main_e_invoice', sa.Column('lhdn_uid', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('lhdn_qr', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('lhdn_validated_date_time', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_e_invoice', 'lhdn_validated_date_time')
    op.drop_column('main_e_invoice', 'lhdn_qr')
    op.drop_column('main_e_invoice', 'lhdn_uid')
    op.drop_column('main_e_credit_note', 'lhdn_validated_date_time')
    op.drop_column('main_e_credit_note', 'lhdn_qr')
    op.drop_column('main_e_credit_note', 'lhdn_uid')
    # ### end Alembic commands ###
