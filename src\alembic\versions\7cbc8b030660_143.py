"""143

Revision ID: 7cbc8b030660
Revises: ab0e16f7ab1b
Create Date: 2025-01-15 15:10:04.343835

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7cbc8b030660'
down_revision = 'ab0e16f7ab1b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_campaign',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('public_description', sa.String(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('discount_category', sa.String(), nullable=False),
    sa.Column('discount_amount', sa.Numeric(scale=2), nullable=False),
    sa.Column('discount_amount_cap', sa.Numeric(scale=2), nullable=True),
    sa.Column('currency', sa.String(), nullable=False),
    sa.Column('min_amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('is_reusable', sa.Boolean(), nullable=True),
    sa.Column('promo_code_type', sa.String(), nullable=False),
    sa.Column('start_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('expire_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('only_exclusive_locations', sa.Boolean(), nullable=True, default=False),
    sa.Column('exclusive_location_ids', postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True, default=[]),
    sa.Column('ocpi_usable', sa.Boolean(), nullable=True, default=False),
    sa.ForeignKeyConstraint(['organization_id'], ['main_organization.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('main_campaign_promo_code',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('limit', sa.Integer(), nullable=True),
    sa.Column('campaign_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['campaign_id'], ['main_campaign.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('main_campaign_promo_code_usage',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('booked', sa.Boolean(), nullable=True),
    sa.Column('membership_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('used', sa.Boolean(), nullable=True),
    sa.Column('used_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('charging_session_id', sa.String(), nullable=True),
    sa.Column('campaign_promo_code_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['campaign_promo_code_id'], ['main_campaign_promo_code.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['membership_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_campaign_promo_code_usage')
    op.drop_table('main_campaign_promo_code')
    op.drop_table('main_campaign')
    # ### end Alembic commands ###
