import datetime
import logging
import time
import asyncio

import pytz
from fastapi import Request, HTTPException, Depends, Security
from fastapi.security.api_key import APIKeyHeader
from starlette.authentication import (
    AuthenticationBackend, SimpleUser, AuthCredentials, UnauthenticatedUser
)

from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError

from app import crud, exceptions, redis, schema, settings
from app.database import create_session, SessionLocal
from app.utils import decode_auth_token_from_headers

logger = logging.getLogger(__name__)

API_KEY_NAME = "x-api-key"
SID_KEY_NAME = "x-api-sid"

api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)
api_sid_header = APIKeyHeader(name=SID_KEY_NAME, auto_error=False)

argon_ph = PasswordHasher()


class AuthorizedUser(SimpleUser):
    def __init__(self, roles, *args, **kwargs):
        self.roles = roles
        super().__init__(*args, **kwargs)


class UnauthorizedUser(UnauthenticatedUser):
    def __init__(self, *args, **kwargs):
        self.roles = []
        super().__init__(*args, **kwargs)


class BasicAuthBackend(AuthenticationBackend):
    async def authenticate(self, request):
        roles = []
        auth = request.headers.get("authorization")
        if not auth:
            # equal to creating UnauthenticatedUser user
            return None, UnauthorizedUser()

        return AuthCredentials(["authenticated"]), AuthorizedUser(roles, "Authorized User")


class Permission:
    """
    Checks if user has specified Resource in their Role
    """

    async def __call__(self, request: Request, dbsession: SessionLocal = Depends(create_session)):  # noqa mccabe: MC0001
        try:  # pylint: disable=too-many-nested-blocks
            # Get organization from authorization header
            decoded_auth_headers = decode_auth_token_from_headers(request.headers)
            path = request.url.path
            method = request.method.lower()
            auth_check = crud.get_user_resource_authorization(
                dbsession,
                path,
                method,
                decoded_auth_headers.get('user_id'),
                decoded_auth_headers.get('membership_id'),
            )
            if settings.DISABLE_SIMULTANEOUS_LOGIN and decoded_auth_headers.get('membership_id') is not None:
                member_id = decoded_auth_headers.get('membership_id')
                db_member = crud.get_membership_by_id(dbsession, member_id)
                if db_member.membership_type != schema.MembershipType.regular_user:
                    try:
                        timeout = settings.REDIS_VALIDATE_USER_TOKEN_TIMEOUT
                        validate_token = await asyncio.wait_for(
                            redis.validate_user_token_from_redis(request.headers.get('authorization'),
                                                                 member_id),
                            timeout=timeout
                        )
                        if not validate_token:
                            raise HTTPException(status_code=430, detail='Invalid token')
                    except asyncio.TimeoutError:
                        logger.warning("Redis membership validation timed out after %s seconds", timeout)
                    except Exception as e:  # pylint: disable=broad-except
                        if isinstance(e, HTTPException) and e.status_code == 430:
                            raise e   # re-raise so it's not caught here
                        logger.error("Redis membership failed with error: %s", e)

            if not auth_check.allow_access:  # pylint:disable=no-else-raise
                logging.info('Error: %s', auth_check.message)
                raise HTTPException(status_code=403, detail='User does not have permission to access this resource.')
            else:
                if settings.CHECK_CSMS_TOKEN_EXPIRY:
                    if decoded_auth_headers.get('user_id'):
                        user_data = crud.get_user_by_id(dbsession, decoded_auth_headers.get('user_id'))
                        if user_data is not None:
                            db_member = crud.get_membership_by_user_id(dbsession, user_data.id)
                            if db_member.membership_type != schema.MembershipType.regular_user:
                                if time.time() > decoded_auth_headers.get('exp'):
                                    raise HTTPException(status_code=403, detail='User has expired permission.')

        except AttributeError as e:
            logging.info(e.__str__())
            raise HTTPException(status_code=403, detail='Invalid user.')
        except exceptions.ApolloObjectDoesNotExist as e:
            logging.info(e.__str__())
            raise HTTPException(status_code=403, detail='User does not have permission to access this resource.')


permission = Permission()


class XAPIKey:
    """
    Checks if user is passing correct API Key in the headers.
    """

    def __call__(self, request: Request,  # noqa: MC0001,# pylint: disable=too-many-nested-blocks
                 dbsession: SessionLocal = Depends(create_session),
                 api_key_header: str = Security(api_key_header), api_sid_header: str = Security(api_sid_header)):

        app_session_id = request.headers.get('appSessionId')
        logger.info('appSessionID %s accessed the api', str(app_session_id))
        try:  # pylint: disable=too-many-nested-blocks
            # Check if request contains auth header
            decoded_auth_headers = decode_auth_token_from_headers(request.headers)
            if decoded_auth_headers:
                is_superuser = crud.get_user_by_id(dbsession, decoded_auth_headers['user_id']).is_superuser
                if not is_superuser:
                    db_org_auth_svc = crud.get_oas_by_organization_id(dbsession, api_sid_header)
                    if not db_org_auth_svc:
                        raise HTTPException(status_code=403, detail='API Key authentication failed.')
                    if db_org_auth_svc:
                        if db_org_auth_svc.expiry_date is not None:
                            if db_org_auth_svc.expiry_date < datetime.datetime.now(pytz.UTC):
                                logger.error('Organization %s API key have expired but accessed the API.',
                                             db_org_auth_svc.organization.name)
                                raise HTTPException(status_code=403, detail='API Key Expired.')
                    # todo: add back in verification, commented due to lost of key
                    argon_ph.verify(db_org_auth_svc.secret_key, api_key_header)
                    logger.info('Organization %s accessed the API.', db_org_auth_svc.organization.name)
            else:
                # todo: add back in verification, commented due to lost of key
                db_org_auth_svc = crud.get_oas_by_organization_id(dbsession, api_sid_header)
                if not db_org_auth_svc:
                    raise HTTPException(status_code=403, detail='API Key authentication failed.')
                if db_org_auth_svc:
                    if db_org_auth_svc.expiry_date is not None:
                        if db_org_auth_svc.expiry_date < datetime.datetime.now(pytz.UTC):
                            logger.error('Organization %s API key have expired but accessed the API.',
                                         db_org_auth_svc.organization.name)
                            raise HTTPException(status_code=403, detail='API Key Expired.')
                argon_ph.verify(db_org_auth_svc.secret_key, api_key_header)
                logger.info('Organization %s accessed the API.', db_org_auth_svc.organization.name)

        except VerifyMismatchError:
            raise HTTPException(
                status_code=403, detail="API Key authentication failed."
            )
        except exceptions.ApolloObjectDoesNotExist as e:
            logging.info(e.__str__())
            raise HTTPException(status_code=403, detail='API Key authentication failed.')


x_api_key = XAPIKey()
