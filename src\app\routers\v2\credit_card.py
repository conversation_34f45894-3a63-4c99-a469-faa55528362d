import logging
import urllib
from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, Request, HTTPException, status, BackgroundTasks

from app import settings, schema, crud, exceptions
from app import cybersource_payment_utils as cyber_utils
from app.constants import PAYMENT_DIRECT_API_URL, SG_PAYMENT_DIRECT_API_URL
from app.constants import BN_PAYMENT_DIRECT_API_URL, KH_PAYMENT_DIRECT_API_URL
from app.crud import get_billing_info_given_member_info
from app.database import create_session, SessionLocal
from app.permissions import permission, x_api_key
from app.utils import decode_auth_token_from_headers, calculate_md5_hash, RouteErrorHandler, generate_charger_header, \
    check_active_charging_session, MAIN_URL_PREFIX

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/cc",
    tags=['v2 credit-card', ],
    dependencies=[Depends(permission), Depends(x_api_key)],
    route_class=RouteErrorHandler,
)


@router.post("/", status_code=status.HTTP_200_OK)
async def add_cc(request: Request, currency: schema.Currency = schema.Currency.myr,  # noqa: MC0001
                 dbsession: SessionLocal = Depends(create_session)):
    """
    Add a Credit Card
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_mem = crud.get_membership_by_id(dbsession, membership_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Membership not found.')

    try:
        pr = schema.PaymentRequestCreditCard(currency=currency)
        if settings.PAYMENT_GATEWAY_TYPE != "Fiuu":
            pr.amount = '0.00'
        if currency == schema.Currency.khr:
            pr.amount = '1001'

        db_pr = crud.create_payment_request(dbsession, pr, membership_id)

        # chargEV: requires CC infront for order_id
        pr_update = schema.PaymentRequestUpdateInvoice(
            invoice_number='CC-' + str(db_pr.pr_running_number)
        )
        db_pr = crud.update_payment_request(dbsession, pr_update, db_pr.id)
        if settings.PAYMENT_GATEWAY_TYPE == "Fiuu":
            if currency == schema.Currency.myr:
                payment_url = PAYMENT_DIRECT_API_URL
                merchant_id = settings.MERCHANT_ID
                vkey = calculate_md5_hash(
                    f'{db_pr.amount}{settings.MERCHANT_ID}{db_pr.invoice_number}{settings.VERIFY_KEY}')
            elif currency == schema.Currency.sgd:
                payment_url = SG_PAYMENT_DIRECT_API_URL
                merchant_id = settings.SG_MERCHANT_ID
                vkey = calculate_md5_hash(
                    f'{db_pr.amount}{settings.SG_MERCHANT_ID}{db_pr.invoice_number}{settings.SG_VERIFY_KEY}')
            elif currency == schema.Currency.bnd:
                payment_url = BN_PAYMENT_DIRECT_API_URL
                merchant_id = settings.BN_MERCHANT_ID
                vkey = calculate_md5_hash(
                    f'{db_pr.amount}{settings.BN_MERCHANT_ID}{db_pr.invoice_number}{settings.BN_VERIFY_KEY}')
            elif currency == schema.Currency.khr:
                payment_url = KH_PAYMENT_DIRECT_API_URL
                merchant_id = settings.KH_MERCHANT_ID
                vkey = calculate_md5_hash(
                    f'{db_pr.amount}{settings.KH_MERCHANT_ID}{db_pr.invoice_number}{settings.KH_VERIFY_KEY}')

            billing_info = get_billing_info_given_member_info(db_mem)
            if billing_info['bill_email'] == '':
                billing_info['bill_email'] = '<EMAIL>'
            if billing_info['bill_mobile'] == '':
                billing_info['bill_mobile'] = '+60322893888'

            params = {
                'currency': db_pr.currency,
                'amount': db_pr.amount,
                'orderid': db_pr.invoice_number,
                'bill_name': billing_info['bill_name'],
                'bill_email': billing_info['bill_email'],
                'bill_mobile': billing_info['bill_mobile'],
                'bill_desc': db_pr.billing_description,
                'vcode': vkey,
                'channel': 'creditAN',
                'username': merchant_id,
                'app_name': 'Apollo',
                'mpstokenstatus': 1,
                'token_status': 2,
            }
            if currency in [schema.Currency.sgd]:
                params['channel'] = 'creditAI'  # SGD and BND use credtiAI
                params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
                params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
            elif currency in [schema.Currency.bnd]:
                params['channel'] = 'creditAI'  # SGD and BND use credtiAI
                params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
                params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
            elif currency in [schema.Currency.khr]:
                params['channel'] = 'creditAI'  # SGD and BND use credtiAI
                params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
                params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
            else:
                params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-return-url'
                params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-callback'

            url = f'{payment_url}?' + urllib.parse.urlencode(params)

            return {'detail': url}
        if settings.PAYMENT_GATEWAY_TYPE == "CyberSource":
            # Get the link that serve HTML for CC
            base_url = str(request.base_url)
            if not base_url.endswith('/'):
                base_url += '/'
            domain = settings.APOLLO_MAIN_DOMAIN.rstrip('/')

            url_path = request.url_for('serve_cybersource_cc_html')
            url_path = url_path.replace(base_url, f'{domain}/')

            query_params = urllib.parse.urlencode({
                "membership_id": membership_id,
                "payment_request_id": db_pr.id
            })

            url_path = f'{url_path}?{query_params}'
            return {'detail': url_path}

        return {'detail': ''}

    except exceptions.ApolloCreditCardError as e:
        raise HTTPException(400, e.__str__())


@router.get("/",
            status_code=status.HTTP_200_OK, response_model=List[schema.CreditCardResponse])
async def get_cc_list(request: Request,
                      dbsession: SessionLocal = Depends(create_session)):
    """
    Get Member Credit Card List
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    db_cc_list = crud.get_member_cc_list(dbsession, membership_id)
    return db_cc_list


@router.delete("/{cc_id}", response_model=schema.BasicMessage, status_code=status.HTTP_200_OK)
async def delete_cc(request: Request, cc_id: UUID, background_tasks: BackgroundTasks,
                    dbsession: SessionLocal = Depends(create_session)):
    """
    Delete a Credit Card
    :param str cc_id: Target Credit Card ID
    """
    success_msg = 'Credit Card Deleted'

    try:

        db_cc = crud.get_cc(dbsession, cc_id)
        # db_mem = db_cc.member
        # token = db_cc.token
        # currency = db_cc.currency
        payment_gateway = db_cc.payment_gateway
        user_token = db_cc.user_token
        # billing_name = f'{db_mem.first_name} {db_mem.last_name}'
        #
        # if db_cc.currency == schema.Currency.myr:
        #     token_url = TOKEN_API_URL
        #     merchant_id = settings.MERCHANT_ID
        #     signature = calculate_sha256_hash(
        #         f'DELETE_TOKEN{db_mem.user.email}{db_mem.user.phone_number}{billing_name}'
        #         f'{settings.MERCHANT_ID}{db_cc.token}',
        #         settings.SECRET_KEY
        #     )
        # if db_cc.currency == schema.Currency.sgd:
        #     token_url = SG_TOKEN_API_URL
        #     merchant_id = settings.SG_MERCHANT_ID
        #     signature = calculate_sha256_hash(
        #         f'DELETE_TOKEN{db_mem.user.email}{db_mem.user.phone_number}{billing_name}'
        #         f'{settings.SG_MERCHANT_ID}{db_cc.token}',
        #         settings.SG_SECRET_KEY
        #     )

        # params = {
        #     'action': 'DELETE_TOKEN',
        #     'token': f'{db_cc.token}',
        #     'billing_name': billing_name,
        #     'billing_mobile': f'{db_mem.user.phone_number}',
        #     'billing_email': f'{db_mem.user.email}',
        #     'merchantID': merchant_id,
        #     'signature': signature
        # }
        #
        # async with httpx.AsyncClient() as client:
        #     response = await client.post(token_url, data=params)
        # if response.status_code >= 400:
        #     raise HTTPException(400, f'Failed to delete CC on merchant page. Error {response.json()["error_desc"]}')

        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
        headers = generate_charger_header(dbsession, membership_id)

        db_active_session = await check_active_charging_session(headers)
        if db_active_session == 402:
            raise HTTPException(status_code=402, detail='On-going session, cannot delete card')
        if db_active_session == 401:
            raise HTTPException(status_code=401, detail='Please try again later.')

        crud.delete_cc(dbsession, cc_id)

        # if payment_gateway == schema.CreditCardPaymentGateway.fiuu:
        #     background_tasks.add_task(fiuu_delete_token, db_mem, token, currency)

        if payment_gateway == schema.CreditCardPaymentGateway.cybersource:
            background_tasks.add_task(cyber_utils.delete_customer, user_token)

        return schema.BasicMessage(detail=success_msg)

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
