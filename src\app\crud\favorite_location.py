from typing import List
from uuid import UUID
from enum import Enum
import logging

# from fastapi import HTTPException

from sqlalchemy.orm import Session
from sqlalchemy.exc import NoResultFound

from app import exceptions, settings

from .auth import MembershipCRUD

logger = logging.getLogger(__name__)

CHARGER_URL_V2_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'


class FavoriteType(str, Enum):
    Favorite = "Favorite"
    Unfavorite = "Unfavorite"


def set_favorite_location(db: Session, member_id: str, action_type: FavoriteType, location_id: str) -> List[UUID]:
    try:

        db_member = MembershipCRUD.get(db, member_id)
        favorite_location_list = list(db_member.favorite_locations)

        if action_type.value == "Favorite":
            if location_id not in favorite_location_list:
                favorite_location_list.append(location_id)
                MembershipCRUD.update(db, member_id,
                                      {'favorite_locations': favorite_location_list},
                                      check_permission=False)
            # else:
            #     raise HTTPException(400, "Location already set as favorite.")

        elif action_type.value == "Unfavorite":
            if location_id in favorite_location_list:
                favorite_location_list.remove(location_id)
                MembershipCRUD.update(db, member_id, {'favorite_locations': favorite_location_list},
                                      check_permission=False)
            # else:
            #     raise HTTPException(404, "No location found")

        return favorite_location_list
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_member_favorite_location_list(db: Session, member_id: str) -> List[UUID]:
    try:
        db_member = MembershipCRUD.get(db, member_id)
        return db_member.favorite_locations
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')
