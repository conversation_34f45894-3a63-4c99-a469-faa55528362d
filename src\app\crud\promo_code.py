from datetime import datetime
from typing import List

from sqlalchemy import func
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, NoResultFound

from app import models, schema, exceptions
from app.settings import TIMEZONE

from .base import BaseCRUD, RelationBaseCRUD
from .favorite_cp import MembershipCRUD


class PromoCodeCRUD(BaseCRUD):
    model = models.PromoCode

    @classmethod
    def can_create(cls, dbsession: Session, data, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser or membership.membership_type in (schema.MembershipType.staff,
                                                                          schema.MembershipType.sub_staff,
                                                                          schema.MembershipType.custom):
            return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser or membership.membership_type in (schema.MembershipType.staff,
                                                                          schema.MembershipType.sub_staff,
                                                                          schema.MembershipType.custom):
            return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        membership = cls.membership()
        if membership.user.is_superuser or not check_permission:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            query_1 = super().query(dbsession, *columns).join(models.OrganizationPromoCode).filter(
                models.OrganizationPromoCode.organization_id.in_(allowed_orgs)
            )
            query_2 = super().query(dbsession, *columns).join(models.MembershipPromoCode) \
                .join(models.Membership).filter(
                models.Membership.organization_id.in_(allowed_orgs),
            )
            return query_1.union(query_2)
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     query_1 = super().query(dbsession, *columns).join(models.OrganizationPromoCode).filter(
        #         models.OrganizationPromoCode.organization_id == membership.organization_id
        #     )
        #     query_2 = super().query(dbsession, *columns).join(models.MembershipPromoCode) \
        #         .join(models.Membership).filter(
        #         models.Membership.organization_id == membership.organization_id
        #     )
        #     return query_1.union(query_2)
        if membership.membership_type == schema.MembershipType.regular_user:
            query_1 = super().query(dbsession, *columns).join(models.OrganizationPromoCode).filter(
                models.OrganizationPromoCode.organization_id == membership.organization_id
            )
            query_2 = super().query(dbsession, *columns).join(models.MembershipPromoCode) \
                .join(models.Membership).filter(
                models.Membership.id == membership.id,
            )
            return query_1.union(query_2)
        raise exceptions.ApolloPermissionError()


class MembershipPromoCodeCRUD(RelationBaseCRUD):
    model = models.MembershipPromoCode

    @classmethod
    def can_create(cls, dbsession: Session, data, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        db_member = dbsession.query(models.Membership).get(data['member_id'])
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_member.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_member.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        db_member_promo = dbsession.query(models.MembershipPromoCode).get(object_id)
        if not db_member_promo:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_member_promo.member.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_member_promo.member.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()


class ChargePointPromoCodeCRUD(RelationBaseCRUD):
    model = models.ChargePointPromoCode


class OrganizationPromoCodeCRUD(RelationBaseCRUD):
    model = models.OrganizationPromoCode

    @classmethod
    def can_create(cls, dbsession: Session, data, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        cls.check_root_organization(dbsession, data['organization_id'])
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if data['organization_id'] in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if data['organization_id'] == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        db_org_promo = dbsession.query(models.OrganizationPromoCode).get(object_id)
        if not db_org_promo:
            return True
        cls.check_root_organization(dbsession, db_org_promo.organization_id)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_org_promo.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_org_promo.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()


class OperatorPromoCodeCRUD(RelationBaseCRUD):
    model = models.OperatorPromoCode

    @classmethod
    def can_create(cls, dbsession: Session, data, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        db_operator = dbsession.query(models.Operator).get(data['operator_id'])
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_operator.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_operator.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        db_operator_promo = dbsession.query(cls.model).get(object_id)
        if not db_operator_promo:
            return True
        if membership.user.is_superuser:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_operator_promo.operator.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_operator_promo.operator.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()


class PromoCodeUsageCRUD(BaseCRUD):
    model = models.PromoCodeUsage


class OperatorChargepointCRUD(RelationBaseCRUD):
    model = models.OperatorChargepoint

    @classmethod
    def query(cls, dbsession: Session, *columns):
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession, *columns).join(models.Operator).filter(
                models.Operator.organization_id.in_(allowed_orgs)
            )
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     return super().query(dbsession, *columns).join(models.Operator).filter(
        #         models.Operator.organization_id == membership.organization_id
        #     )
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        db_operator_cp = cls.query(dbsession).get(object_id)
        if not db_operator_cp:
            return True
        if membership.user.is_superuser:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_operator_cp.operator.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_operator_cp.operator.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()


def get_promo_code(db: Session, promo_code_id: str, check_permission=True) -> models.PromoCode:
    try:
        db_promo_code = PromoCodeCRUD.get(db, str(promo_code_id), check_permission=check_permission)
        return db_promo_code
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PromoCode')


def get_promo_code_by_code(db: Session, code: str) -> models.PromoCode:
    query = PromoCodeCRUD.query(db).filter(models.PromoCode.code == code)
    try:
        return query.one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PromoCode')


def get_promo_code_list(db: Session) -> List[models.PromoCode]:
    return PromoCodeCRUD.query(db).all()


def update_promo_code(db: Session, promo_code_id: str, promo_code_data: schema.PromoCode) -> models.PromoCode:
    try:
        db_promo_code = PromoCodeCRUD.update(db,
                                             promo_code_id,
                                             promo_code_data.dict(exclude_unset=True, exclude_defaults=True))
        return db_promo_code
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PromoCode')


def create_promo_code(db: Session, promo_code_data: schema.PromoCode) -> models.PromoCode:
    try:
        db_promo_code = PromoCodeCRUD.add(db, promo_code_data.dict(exclude_unset=True))
        return db_promo_code
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloPromoCodeError()


def delete_promo_code(db: Session, promo_code_id: str) -> models.PromoCode:
    try:
        PromoCodeCRUD.delete(db, promo_code_id)
        return promo_code_id
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PromoCode')
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloPromoCodeError()


def assign_promo_code_to_member(db: Session, promo_code_id: str, member_id: str, is_excluded: bool):
    try:
        db_member_promo_code = dict(
            promo_code_id=promo_code_id,
            member_id=member_id,
            is_excluded=is_excluded,
        )
        MembershipPromoCodeCRUD.add(db, db_member_promo_code)
        return db_member_promo_code
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloMemberPromoCodeError()


def delete_promo_code_association_with_member(db: Session, promo_code_id: str, member_id: str):
    query = MembershipPromoCodeCRUD.query(db).filter(
        models.MembershipPromoCode.promo_code_id == promo_code_id,
        models.MembershipPromoCode.member_id == member_id,
    )
    try:
        db_membership_promo_code = query.one()
        MembershipPromoCodeCRUD.delete(db, str(db_membership_promo_code.id))
        return
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='MembershipPromoCode')


def assign_promo_code_to_charge_point(db: Session, promo_code_id: str, charge_point_id: str, is_excluded: bool):
    try:
        db_charge_point_promo_code = dict(
            promo_code_id=promo_code_id,
            charge_point_id=charge_point_id,
            is_excluded=is_excluded,
        )
        ChargePointPromoCodeCRUD.add(db, db_charge_point_promo_code)
        return db_charge_point_promo_code
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloChargepointPromoCodeError()


def delete_promo_code_association_with_charge_point(db: Session, promo_code_id: str, charge_point_id: str):
    query = ChargePointPromoCodeCRUD.query(db).filter(
        models.ChargePointPromoCode.promo_code_id == promo_code_id,
        models.ChargePointPromoCode.charge_point_id == charge_point_id,
    )
    try:
        db_charge_point_promo_code = query.one()
        ChargePointPromoCodeCRUD.delete(db, str(db_charge_point_promo_code.id))
        return
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargePointPromoCode')


def assign_promo_code_to_organization(db: Session, promo_code_id: str, organization_id: str):
    try:
        db_organization_promo_code = dict(
            promo_code_id=promo_code_id,
            organization_id=organization_id
        )
        OrganizationPromoCodeCRUD.add(db, db_organization_promo_code, check_permission=False)
        return db_organization_promo_code
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloOrganizationPromoCodeError()


def delete_promo_code_association_with_organization(db: Session, promo_code_id: str, organization_id: str):
    query = OrganizationPromoCodeCRUD.query(db).filter(
        models.OrganizationPromoCode.promo_code_id == promo_code_id,
        models.OrganizationPromoCode.organization_id == organization_id,
    )
    try:
        db_organization_promo_code = query.one()
        OrganizationPromoCodeCRUD.delete(db, str(db_organization_promo_code.id))
        return
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OrganizationPromoCode')


def assign_promo_code_to_operator(db: Session, promo_code_id: str, operator_id: str):
    try:
        db_operator_promo_code = models.OperatorPromoCode(
            promo_code_id=promo_code_id,
            operator_id=operator_id
        )
        OperatorPromoCodeCRUD.add(db, db_operator_promo_code)
        return db_operator_promo_code
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloOperatorPromoCodeError()


def delete_promo_code_association_with_operator(db: Session, promo_code_id: str, operator_id: str):
    query = OperatorPromoCodeCRUD.query(db).filter(
        models.OperatorPromoCode.promo_code_id == promo_code_id,
        models.OperatorPromoCode.operator_id == operator_id,
    )
    try:
        db_operator_promo_code = query.one()
        OperatorPromoCodeCRUD.delete(db, str(db_operator_promo_code.id))
        return
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OperatorPromoCode')


def get_remaining_accumulative_promo(db: Session, promo_code_id: str, member_id: str) -> float:
    total_usage = PromoCodeUsageCRUD.query(db, func.sum(models.PromoCodeUsage.amount)).filter(
        models.PromoCodeUsage.promo_code_id == promo_code_id,
        models.PromoCodeUsage.member_id == member_id
    ).one()[0] or 0
    db_promo_code = get_promo_code(db, promo_code_id)
    total_available = db_promo_code.amount
    return total_available - total_usage


def calculate_promo_applicable_amount(db: Session, amount: str, promo_type: schema.PromoType,
                                      pormo_value: float, promo_code_id: str, member_id: str):
    amount = float(amount)
    if promo_type == schema.PromoType.percentage:
        return round((amount * pormo_value) / 100, 2)
    if promo_type == schema.PromoType.amount:
        return min(amount, pormo_value)
    if promo_type == schema.PromoType.accumulative:
        total_used_amount = get_remaining_accumulative_promo(db, promo_code_id, member_id)
        return min(amount, max(pormo_value - total_used_amount, 0))
    return 0


def check_member_promo_code_association(db: Session, promo_code_id: str, member_id: str):
    # check membership association/exclusion
    query = MembershipPromoCodeCRUD.query(db).filter(
        models.MembershipPromoCode.member_id == member_id,
        models.MembershipPromoCode.promo_code_id == promo_code_id
    )
    db_member_promo_code = query.first()
    if db_member_promo_code:
        if db_member_promo_code.is_excluded:
            return False, None
        return True, schema.PromoUsageAssociationType.direct

    # check organization association
    try:
        db_member = MembershipCRUD.get(db, member_id)
        query = OrganizationPromoCodeCRUD.query(db).filter(
            models.OrganizationPromoCode.organization_id == db_member.organization_id,
            models.OrganizationPromoCode.promo_code_id == promo_code_id
        )
        if query.first():
            return True, schema.PromoUsageAssociationType.organization

        # check label association
        db_promo_code = get_promo_code(db, promo_code_id)
        if set(db_member.labels).intersection(set(db_promo_code.labels)):
            return True, schema.PromoUsageAssociationType.label

        return False, None

    except NoResultFound:
        return False, None


def check_charge_point_promo_code_association(db: Session, promo_code_id: str, charge_point_id: str):
    # check charge point association
    query = ChargePointPromoCodeCRUD.query(db).filter(
        models.ChargePointPromoCode.charge_point_id == charge_point_id,
        models.ChargePointPromoCode.promo_code_id == promo_code_id
    )
    db_charge_point_promo_code = query.first()
    if db_charge_point_promo_code:
        if db_charge_point_promo_code.is_excluded:
            return False
        return True

    # check organization association
    db_operator_charge_point = OperatorChargepointCRUD.query(db).filter(
        models.OperatorChargepoint.charge_point_id == charge_point_id
    ).first()
    if db_operator_charge_point:
        query = OrganizationPromoCodeCRUD.query(db).filter(
            models.OrganizationPromoCode.organization_id == db_operator_charge_point.operator.organization_id,
            models.OrganizationPromoCode.promo_code_id == promo_code_id
        )
        if query.first():
            return True

        # check operator association
        query = OperatorPromoCodeCRUD.query(db).filter(
            models.OperatorPromoCode.operator_id == db_operator_charge_point.operator_id,
            models.OperatorPromoCode.promo_code_id == promo_code_id
        )
        if query.first():
            return True

    return False


def verify_promo_code(db: Session, promo_code_id: str, member_id: str,
                      charge_point_id: str, amount: str) -> models.PromoCodeUsage:
    message = 'Ok'
    ok = True
    db_promo_code = get_promo_code(db, promo_code_id)

    if db_promo_code.expire_at < datetime.now(TIMEZONE):
        message = 'Promotion is expired'
        ok = False

    if db_promo_code.start_at > datetime.now(TIMEZONE):
        message = 'Promotion has not started yet'
        ok = False

    if not ok:
        return False, None, message

    charge_point_promo_ok = check_charge_point_promo_code_association(db, promo_code_id, charge_point_id)
    member_promo_ok, promo_usage_association_type = check_member_promo_code_association(db, promo_code_id, member_id)

    if all([not member_promo_ok, not charge_point_promo_ok]):
        message = 'Promotion cant be used'
        return False, None, message

    query = PromoCodeUsageCRUD.query(db).filter(
        models.PromoCodeUsage.promo_code_id == promo_code_id,
        models.PromoCodeUsage.member_id == member_id,
    )

    if (db_promo_code.type in [schema.PromoType.percentage, schema.PromoType.amount] and
            query.count() > db_promo_code.limit):
        ok = False
        message = 'Promotion already used'

    if db_promo_code.type == schema.PromoType.accumulative:
        total_used_amount = get_remaining_accumulative_promo(db, promo_code_id, member_id)
        if total_used_amount >= db_promo_code.amount:
            ok = False
            message = 'Promotion already used'

    return ok, promo_usage_association_type, message


def create_promo_usage(db: Session, code: str, member_id: str, charge_point_id: str,
                       amount: str) -> models.PromoCodeUsage:
    db_promo_code = get_promo_code_by_code(db, code)
    promo_ok, promo_usage_association_type, message = verify_promo_code(db, db_promo_code.id, member_id,
                                                                        charge_point_id, amount)

    if promo_ok:
        promo_amount = calculate_promo_applicable_amount(db, amount, db_promo_code.type, db_promo_code.amount,
                                                         db_promo_code.id, member_id)
        try:
            db_promo_usage = PromoCodeUsageCRUD.add(db, dict(
                promo_code_id=db_promo_code.id,
                member_id=member_id,
                amount=promo_amount,
                association_type=promo_usage_association_type
            ))
            return db_promo_usage
        except IntegrityError:
            db.rollback()
            raise exceptions.ApolloPromoCodeUsageError()
    raise exceptions.ApolloPromoCodeUsageError(message)


def update_promo_usage_amount(db, promo_usage_id, amount):
    try:
        db_promo_usage = PromoCodeUsageCRUD.get(db, promo_usage_id)
        return PromoCodeUsageCRUD.update(db, str(db_promo_usage.id), {'amount': amount})
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PromoCodeUsage')
