name: apollo-main.develop

on:
  push:
    branches:
      - 'develop'

concurrency: develop

  
jobs:
  sonar-scanner:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          persist-credentials: false

      - name: preparing files
        run: |
          cp ./docker/.ci.env .env
          cp ./docker/docker-compose.sonarscanner.ci.yml  docker-compose.sonarscanner.ci.yml 

      - name: Set up .env file
        run: |
          echo "SONAR_HOST_URL=https://sonarqube.yinson-apollo.com" >> .env
          echo "SONAR_TOKEN_V1=${{ secrets.SONAR_TOKEN_V1 }}" >> .env
          echo "SONAR_TOKEN_V2=${{ secrets.SONAR_TOKEN_V2 }}" >> .env

      - name: Run sonar-scanner
        run: |
          docker compose -f docker-compose.sonarscanner.ci.yml up -d postgres rabbitmq sonarscanner-v1 --build
          sleep 10
          docker compose exec -T sonarscanner-v1 coverage run --source=app/routers -m pytest || true
          docker compose exec -T sonarscanner-v1 coverage report -m
          docker compose exec -T sonarscanner-v1 coverage xml
          docker compose exec -T sonarscanner-v1 sonar-scanner
          sleep 10
          docker compose -f docker-compose.sonarscanner.ci.yml up -d sonarscanner-v2 --build
          sleep 10
          docker compose exec -T sonarscanner-v2 coverage run --source=app/routers/v2 -m pytest || true
          docker compose exec -T sonarscanner-v2 coverage report -m
          docker compose exec -T sonarscanner-v2 coverage xml
          docker compose exec -T sonarscanner-v2 sonar-scanner

      - name: SonarQube Dashboard URL
        run: |
          echo "::notice::Visit the [MAIN-V1](https://sonarqube.yinson-apollo.com/dashboard?id=apollo-main-v1), [MAIN-V2](https://sonarqube.yinson-apollo.com/dashboard?id=apollo-main-v2) dashboard for the report details."

  push_to_agmo_ecr:
    runs-on: ubuntu-latest
    environment: develop
    permissions:
      contents: read # Required for actions/checkout 
    # needs: test
    steps:
      - uses: actions/checkout@v2
        with:
          persist-credentials: false
      - name: Get version number from version.txt
        id: get_version
        run: |
          echo "version_number=$(head -1 version.txt)-${GITHUB_SHA::7}" >> $GITHUB_ENV
          echo "version_number=$(head -1 version.txt)-${GITHUB_SHA::7}" >> $GITHUB_OUTPUT
      - uses: whoan/docker-build-with-cache-action@v5
        with:
          username: '${{ secrets.AWS_ACCESS_KEY_ID }}'
          password: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
          registry: ${{ vars.AWS_ACCOUNT_ID }}.dkr.ecr.ap-southeast-1.amazonaws.com
          dockerfile: ./docker/Dockerfile.main
          image_name: ${{ vars.MAIN_IMAGE_NAME }}
          image_tag: latest,22.1.${{ github.run_number }},${{ steps.get_version.outputs.version_number }}
          # image_tag: mobile

      - uses: tibdex/github-app-token@v1
        id: get_installation_token
        with:
          app_id: 465705
          installation_id: ********
          private_key: ${{ secrets.CONFIG_REPO_PRIVATE_KEY }}

      - name: Update Variable for Develop environment in Config Repo
        run: |
          # Set the required variables
          repo_owner="yinsondigital" 
          repo_name="${{ vars.CONFIG_REPO_NAME }}"  

          curl \
          -X PATCH \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer ${{ steps.get_installation_token.outputs.token }}" \
          https://api.github.com/repos/$repo_owner/$repo_name/environments/develop/variables/MAIN_VERSION_NUMBER \
          -d '{"name":"MAIN_VERSION_NUMBER","value":"'"$version_number"'"}'

      - name: Trigger Deployment Workflow in Config Repo
        run: |
          # Set the required variables
          repo_owner="yinsondigital" 
          repo_name="${{ vars.CONFIG_REPO_NAME }}"  
          workflow_id="develop.yml"
          branch='develop'
          event_type="trigger-deployment-develop" 

          curl \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer ${{ steps.get_installation_token.outputs.token }}" \
          https://api.github.com/repos/$repo_owner/$repo_name/actions/workflows/$workflow_id/dispatches \
          -d "{\"ref\": \"$branch\", \"inputs\": {\"branch\": \"$branch\"}}"