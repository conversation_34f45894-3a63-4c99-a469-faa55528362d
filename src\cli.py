from contextlib import contextmanager
import typer

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, NoResultFound

from app import models, schema
from app.defaults import default_roles, default_users,\
    default_memberships, default_organizations, default_resource_servers
from app.defaults.default_resources import STAFF_ALLOWED_RESOURCES, REGULAR_USER_ALLOWED_RESOURCES,\
    SUB_STAFF_ALLOWED_RESOURCES
from app.settings import MAIN_ROOT_PATH
from app.database import create_session


app = typer.Typer()


def add(model, dbsession: Session, data: dict, *args, **kwargs):
    """create an object
    """
    db_object = model(**data)
    dbsession.add(db_object)
    dbsession.commit()
    dbsession.refresh(db_object)
    return db_object


def update(model, dbsession: Session, object_id, data: dict, *args, **kwargs):
    """update an object
    """
    db_object = dbsession.query(model).get(object_id)
    for key, value in data.items():
        setattr(db_object, key, value)
    dbsession.commit()
    dbsession.refresh(db_object)
    return db_object


@app.callback()
def callback():
    """
    CLI application is used in order to initialise database or update records within.
    """


# pylint:disable=logging-not-lazy
@app.command()
def init(): # noqa
    """
    Init command initializes database.

    If database is empty, it will create default objects.
    """
    with contextmanager(create_session)() as dbsession:
        db_organization = dbsession.query(models.Organization).filter(models.Organization.name == 'Apollo').first()
        if db_organization:
            typer.echo('Database is not empty, there is no option to use init command.')
        else:
            root_org_id = create_default_organizations()
            create_default_roles()
            create_default_users(org_id=root_org_id)
            create_default_memberships()
            create_default_resource_servers()
            create_default_resources()
            update_staff_resources()
            update_sub_staff_resources()
            update_user_resources()


@app.command()
def update_staff_resources():
    with contextmanager(create_session)() as dbsession:
        staff_role = dbsession.query(models.Role).filter(
            models.Role.name == default_roles.staff.name
        ).first()
        main_res_server = dbsession.query(models.ResourceServer).filter(
            models.ResourceServer.name == default_resource_servers.resource_server.name
        ).first()

        # Staff Resources
        existing_staff_resources = [res.id for res in staff_role.resources]
        for res in STAFF_ALLOWED_RESOURCES:
            resource = dbsession.query(models.Resource).filter(
                models.Resource.resourceserver_id == main_res_server.id,
                models.Resource.path == res['path'],
                models.Resource.scope == res['scope'],
            ).first()
            if resource:
                if resource.id not in existing_staff_resources:
                    staff_role.resources.append(resource)
        try:
            dbsession.commit()
        except IntegrityError:
            dbsession.rollback()


@app.command()
def update_sub_staff_resources():
    with contextmanager(create_session)() as dbsession:
        # Query all created resources
        sub_staff_role = dbsession.query(models.Role).filter(
            models.Role.name == default_roles.sub_staff.name
        ).first()
        main_res_server = dbsession.query(models.ResourceServer).filter(
            models.ResourceServer.name == default_resource_servers.resource_server.name
        ).first()

        # Sub-staff Resources
        existing_sub_staff_resources = [res.id for res in sub_staff_role.resources]
        for res in SUB_STAFF_ALLOWED_RESOURCES:
            resource = dbsession.query(models.Resource).filter(
                models.Resource.resourceserver_id == main_res_server.id,
                models.Resource.path == res['path'],
                models.Resource.scope == res['scope'],
            ).first()
            if resource:
                if resource.id not in existing_sub_staff_resources:
                    sub_staff_role.resources.append(resource)
        try:
            dbsession.commit()
        except IntegrityError:
            dbsession.rollback()


@app.command()
def update_user_resources():
    with contextmanager(create_session)() as dbsession:
        regular_role = dbsession.query(models.Role).filter(
            models.Role.name == default_roles.regular_user.name
        ).first()
        main_res_server = dbsession.query(models.ResourceServer).filter(
            models.ResourceServer.name == default_resource_servers.resource_server.name
        ).first()

        # Regular User Resources
        existing_regular_user_resources = [res.id for res in regular_role.resources]
        for res in REGULAR_USER_ALLOWED_RESOURCES:
            resource = dbsession.query(models.Resource).filter(
                models.Resource.resourceserver_id == main_res_server.id,
                models.Resource.path == res['path'],
                models.Resource.scope == res['scope'],
            ).first()
            if resource:
                if resource.id not in existing_regular_user_resources:
                    regular_role.resources.append(resource)
        try:
            dbsession.commit()
        except IntegrityError:
            dbsession.rollback()


@app.command()
def create_default_memberships():
    """
    Function creates default membership objects.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            apollo_org = dbsession.query(models.Organization).filter(
                models.Organization.name == default_organizations.organization.name
            ).first()
            sys_admin_user = dbsession.query(models.User).filter(
                models.User.email == default_users.sys_admin_user['email']
            ).one()
            sys_admin_role = dbsession.query(models.Role).filter(
                models.Role.name == default_roles.sys_admin.name).first()
        except NoResultFound:
            typer.echo('Default objects does not exist')
            raise typer.Exit()
        try:
            sys_admin_membership = default_memberships.sys_admin_mem
            sys_admin_membership['organization_id'] = apollo_org.id
            sys_admin_membership['user_id'] = sys_admin_user.id
            sys_admin_membership = add(models.Membership, dbsession, sys_admin_membership)
            sys_admin_membership.roles.append(sys_admin_role)
            dbsession.commit()
            typer.echo('New default membership created!')
        except IntegrityError:
            dbsession.rollback()
            typer.echo('Default membership already exist!')


@app.command()
def update_default_memberships():
    """
    Function updates default membership object.

    If the object is not found by its ID, command will crate new one.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            apollo_org = dbsession.query(models.Organization).filter(
                models.Organization.name == default_organizations.organization.name
            ).first()
            sys_admin_membership = dbsession.query(models.Membership).filter(
                models.Membership.organization_id == apollo_org.id
            ).one()

            data = default_memberships.sys_admin_mem
            update(models.Membership, dbsession, sys_admin_membership.id, data)
            typer.echo('Database updated.')
        except NoResultFound:
            create = typer.confirm('There is no such membership in database, do you want to add it?')
            if create:
                create_default_memberships()
            else:
                raise typer.Exit()


@app.command()
def create_default_roles():
    """
    Function creates default roles objects.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            for role in default_roles.roles_list:
                role_data = role.dict()
                role_data.update({'is_global': True})
                add(models.Role, dbsession, role_data)

            system_admin_role_schema = default_roles.sys_admin
            apollo_org = dbsession.query(models.Organization).filter(models.Organization.name == 'Root').first()
            system_admin_role_schema.organization_id = apollo_org.id
            add(models.Role, dbsession, system_admin_role_schema.dict())
            typer.echo('New default roles created.')
        except IntegrityError:
            dbsession.rollback()


@app.command()
def update_default_roles():
    """
    Function updates default roles object.

    If the object is not found by its ID, command will crate new one.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            staff_role = dbsession.query(models.Role).filter(
                models.Role.name == default_roles.staff.name).first()
            regular_role = dbsession.query(models.Role).filter(
                models.Role.name == default_roles.regular_user.name).first()
            sub_staff_role = dbsession.query(models.Role).filter(
                models.Role.name == default_roles.sub_staff.name).first()
            admin_role = dbsession.query(models.Role).filter(
                models.Role.name == default_roles.sys_admin.name).first()

            update(models.Role, dbsession, staff_role.id, default_roles.staff.dict())
            update(models.Role, dbsession, regular_role.id, default_roles.regular_user.dict())
            update(models.Role, dbsession, sub_staff_role.id, default_roles.sub_staff.dict())

            system_admin_role_schema = default_roles.sys_admin
            apollo_org = dbsession.query(models.Organization).filter(models.Organization.name == 'Apollo').first()
            system_admin_role_schema.organization_id = apollo_org.id
            update(models.Role, dbsession, admin_role.id, system_admin_role_schema.dict())
            typer.echo('Database updated.')
        except NoResultFound:
            create = typer.confirm('There is no such role in database, do you want to add it?')
            if create:
                create_default_roles()
            else:
                raise typer.Exit()


@app.command()
def create_default_users(org_id):
    """
    Function creates default user objects.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            data = default_users.sys_admin_user
            data['organization_id'] = org_id
            add(models.User, dbsession, dict(**data))
            typer.echo('New default user created.')
        except IntegrityError:
            dbsession.rollback()
            typer.echo('Default System admin user already exist')


@app.command()
def update_default_users():
    """
    Function updates default user object.

    If the object is not found by its ID, command will crate new one.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            sys_admin_user = dbsession.query(models.User).filter(
                models.User.email == default_users.sys_admin_user['email']
            ).one()

            data = default_users.sys_admin_user
            update(models.User, dbsession, sys_admin_user.id, data)
            typer.echo('Database updated.')
        except NoResultFound:
            create = typer.confirm('There is no such user in database, do you want to add it?')
            if create:
                root_org = dbsession.query(models.Organization).filter(models.Organization.name == 'Root').first()
                root_org_id = root_org.id
                create_default_users(org_id=root_org_id)
            else:
                raise typer.Exit()


@app.command()
def create_default_resource_servers():
    """
    Function creates default resource server objects.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            data = default_resource_servers.resource_server
            add(models.ResourceServer, dbsession, dict(data, root_path=MAIN_ROOT_PATH))
            typer.echo('New default Resource Server created.')
        except IntegrityError:
            dbsession.rollback()
            typer.echo('Default resource server already exists')


@app.command()
def update_default_resource_servers():
    """
    Function updates default resource server object.

    If the object is not found by its ID, command will crate new one.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            jc_main_res_server = dbsession.query(models.ResourceServer).filter(
                models.ResourceServer.name == default_resource_servers.resource_server.name
            ).one()

            data = default_resource_servers.resource_server
            update(models.ResourceServer, dbsession, jc_main_res_server.id, dict(data, root_path=MAIN_ROOT_PATH))
            typer.echo('Database updated.')
        except NoResultFound:
            create = typer.confirm('There is no such resource server in database, do you want to add it?')
            if create:
                create_default_resource_servers()
            else:
                raise typer.Exit()


@app.command()
def create_default_resources():
    """
    Function creates default resource objects.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            res_ser = dbsession.query(models.ResourceServer).filter(
                models.ResourceServer.name == default_resource_servers.resource_server.name
            ).first()
        except NoResultFound:
            typer.echo('Default Resource server does not exist')
        for resource in STAFF_ALLOWED_RESOURCES + SUB_STAFF_ALLOWED_RESOURCES + REGULAR_USER_ALLOWED_RESOURCES:
            res_data = {
                'resourceserver_id': str(res_ser.id),
                'name': '',
                **resource,
            }
            try:
                add(models.Resource, dbsession, res_data)
            except IntegrityError:
                dbsession.rollback()
                typer.echo('Default Resources already exist')


@app.command()
def update_default_resources():
    """
    Function updates default resource object.

    If the object is not found by its ID, command will crate new one.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            res_ser = dbsession.query(models.ResourceServer).filter(
                models.ResourceServer.name == default_resource_servers.resource_server.name
            ).first()
            for resource in STAFF_ALLOWED_RESOURCES + SUB_STAFF_ALLOWED_RESOURCES + REGULAR_USER_ALLOWED_RESOURCES:
                res_data = {
                    'resourceserver_id': str(res_ser.id),
                    'name': '',
                    **resource,
                }
                db_res = dbsession.query(models.Resource).filter(
                    models.Resource.path == resource['path'],
                    models.Resource.scope == resource['scope'],
                ).one()
                update(models.Resource, dbsession, db_res.id, res_data)
                dbsession.commit()
            typer.echo('Database updated.')
        except NoResultFound:
            create = typer.confirm('There is no such resource in database, do you want to add it?')
            if create:
                create_default_resources()
                typer.echo('Object successfully created.')
            else:
                raise typer.Exit()


@app.command()
def create_default_organizations():
    """
    Function creates default organization object.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            data = default_organizations.organization
            add(models.Organization, dbsession, data.dict())
            typer.echo('New default organization created.')
        except IntegrityError:
            dbsession.rollback()
            typer.echo('Default organization already exists')

        try:
            root_org = dbsession.query(models.Organization).filter(models.Organization.name == 'Root').first()
            data = schema.Organization(name='CDG', parent_id=root_org.id)
            add(models.Organization, dbsession, data.dict())
            typer.echo('Organization CDG created.')
        except IntegrityError:
            dbsession.rollback()
            typer.echo('Organization CDG already exists')

        try:
            cdg_org = dbsession.query(models.Organization).filter(models.Organization.name == 'CDG').first()
            data = schema.Operator(name='CDG', organization_id=cdg_org.id)
            add(models.Operator, dbsession, data.dict())
            typer.echo('Operator CDG created.')
        except IntegrityError:
            dbsession.rollback()
            typer.echo('Operator CDG already exists')
        return root_org.id


@app.command()
def update_default_organizations():
    """
    Function updates default organization object.

    If the object is not found by its ID, command will crate new one.
    """
    with contextmanager(create_session)() as dbsession:
        try:
            apollo_org = dbsession.query(models.Organization).filter(
                models.Organization.name == default_organizations.organization.name
            ).one()

            update(models.Organization, dbsession, apollo_org.id, default_organizations.organization.dict())
            typer.echo('Database updated.')
        except NoResultFound:
            create = typer.confirm('There is no such organization in database, do you want to add it?')
            if create:
                create_default_organizations()
            else:
                raise typer.Exit()


if __name__ == '__main__':
    app()
