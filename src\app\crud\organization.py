# pylint:disable=too-many-lines
from collections import defaultdict
import typing
import secrets
from typing import List
from datetime import datetime, timedelta
from uuid import UUID
import bcrypt
from faker import Faker
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy import func, or_, and_, desc
from sqlalchemy.orm import Session, Query, joinedload
from sqlalchemy.exc import IntegrityError, NoResultFound

from app import models, schema, exceptions, settings
from app.schema import v2 as mobile_schema
from app.middlewares import deactivate_audit
from app.crud.auth import (
    create_user_membership)
from .base import BaseCRUD

from .auth import MembershipCRUD, UserCRUD, MembershipExtendedCRUD, ManualLinkingTokenCRUD, \
    get_user_by_email_or_phone_number_v2, get_user_by_phone_number_v2, MembershipDunningCRUD
from .role import RoleCRUD
from .operator import OperatorCRUD, UserAccessOperatorCRUD
from .wallet import WalletCRUD
from .credit_card import CreditCardCRUD

fake = Faker()


class OrganizationCRUD(BaseCRUD):
    model = models.Organization

    @classmethod
    def can_create(cls, dbsession: Session, data, *args, **kwargs):
        membership = cls.membership()
        allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
        if membership.user.is_superuser:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.custom):
            if data['parent_id'] in allowed_orgs:
                return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, check_permission=True, **kwargs):
        if not check_permission:
            cls.can_write(dbsession, object_id, *args, **kwargs)
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        cls.check_root_organization(dbsession, object_id)
        if membership.membership_type is not schema.MembershipType.regular_user:
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if object_id in allowed_orgs:
                return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_delete(cls, dbsession: Session, object_id, *args, **kwargs):
        cls.check_root_organization(dbsession, object_id)
        # membership = cls.membership()
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     raise exceptions.ApolloPermissionError()
        return cls.can_write(dbsession, object_id, *args, **kwargs)

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        if not check_permission:
            return super().query(dbsession, *columns)
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession, *columns).filter(
                cls.model.id.in_(allowed_orgs)
            )
        if membership.membership_type == schema.MembershipType.regular_user:
            return super().query(dbsession, *columns).filter(cls.model.id == membership.organization_id)
        raise exceptions.ApolloPermissionError()


class IDTagAuthorizationHistoriesCRUD(BaseCRUD):
    model = models.IDTagAuthorizationHistories


class IDTagCRUD(BaseCRUD):
    model = models.IDTag

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        id_tag = dbsession.query(cls.model).get(object_id)
        if not id_tag:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if id_tag.organization_id in allowed_orgs and id_tag.member is None:
                return True
            if id_tag.member and id_tag.member.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if id_tag.member.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission: bool = True):
        membership = cls.membership()
        if membership.user.is_superuser or not check_permission:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession, *columns).outerjoin(models.Membership).filter(
                or_(models.Membership.organization_id.in_(allowed_orgs),
                    and_(models.IDTag.organization_id.in_(allowed_orgs),
                         models.IDTag.member_id.is_(None)))
            )
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     return super().query(dbsession, *columns).join(models.Membership).filter(
        #         models.Membership.organization_id == membership.organization_id)
        if membership.membership_type == schema.MembershipType.regular_user:
            return super().query(dbsession, *columns).filter(cls.model.member_id == membership.id)
        raise exceptions.ApolloPermissionError()


class OCPITokenCRUD(BaseCRUD):
    model = models.OCPIToken


class OCPICPOTokenCRUD(BaseCRUD):
    model = models.OCPICPOToken


class OrganizationAuthenticationServiceCRUD(BaseCRUD):
    model = models.OrganizationAuthenticationService


class ExternalOrganizationCRUD(BaseCRUD):
    model = models.ExternalOrganization


class TaxRateCRUD(BaseCRUD):
    model = models.TaxRate


class UserAuditLogCRUD(BaseCRUD):
    model = models.UserAuditLog

    @classmethod
    def query(cls, dbsession: Session, *columns):
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession, *columns).join(models.Membership).filter(
                models.Membership.organization_id.in_(allowed_orgs))
        raise exceptions.ApolloPermissionError()


# Organization


# pylint:disable=unsubscriptable-object
def get_organization_by_id(db: Session, organization_id: str) -> models.Organization:
    try:
        db_organization = OrganizationCRUD.get(db, organization_id)
        return db_organization
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Organization')


def get_organization_list(db: Session) -> typing.List[models.Organization]:
    db_organizations = OrganizationCRUD.query(db).all()
    return db_organizations


def get_all_child_organizations(db: Session, organization_id: str) -> set[str]:
    """
    Get all child organization ids, including nested child organizations.

    Copied from from app/utils.py to avoid circular import
    """
    final_child_orgs = set()
    queue = [organization_id]  # Initialize the queue with the starting organization

    while queue:
        current_org_id = queue.pop(0)  # Get the first organization ID from the queue

        # Query to get the child organizations of the current organization
        mem_org_children = db.query(models.Organization) \
            .filter(models.Organization.parent_id == current_org_id) \
            .all()

        for child in mem_org_children:
            final_child_orgs.add(child.id)
            queue.append(child.id)  # Add the current child to the queue to explore its children

    return final_child_orgs


def get_organization_staff_membership_by_user_id(db: Session, organization_id: str,
                                                 user_id: str) -> typing.List[models.Membership]:
    db_mem_list = MembershipCRUD.query(db, check_permission=False).filter(
        models.Membership.organization_id == organization_id,
        models.Membership.user_id == user_id,
        models.Membership.membership_type.in_([schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                               schema.MembershipType.custom]),
    ).all()
    if db_mem_list:
        return db_mem_list
    raise exceptions.ApolloNonStaffMembershipError()


def check_organization_duplication(db: Session, data: schema.Organization):
    duplicate = OrganizationCRUD.query(db).filter(func.lower(models.Organization.name) == data.name.lower(),
                                                  models.Organization.is_deleted == False).all()  # pylint: disable=singleton-comparison, # noqa: E712
    if duplicate:
        raise exceptions.ApolloOrganizationDuplication()


def create_organization(db: Session, data: schema.Organization) -> models.Organization:
    check_organization_duplication(db, data)
    data = dict(data)
    if data['parent_id'] is not None:
        db_organization = OrganizationCRUD.add(db, data)
        return db_organization
    raise exceptions.ApolloRootOrganizationError()


def update_organization(db: Session, data: schema.OrganizationUpdate, organization_id: str) -> models.Organization:
    check_organization_duplication(db, data)
    try:
        db_organization = OrganizationCRUD.update(db, organization_id, data.dict(exclude_unset=True))
        return db_organization
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Organization')


def delete_organization(db: Session, organization_id: str) -> str:
    try:
        OrganizationCRUD.delete(db, organization_id)
        return organization_id
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Organization')


def update_organization_logo(db: Session, logo, organization_id: str, logo_format) -> models.Organization:
    try:
        db_organization = OrganizationCRUD.update(db, organization_id, {'logo': logo, 'logo_format_type': logo_format})
        return db_organization
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Organization')


def get_organization_logo(db: Session, organization_id: str) -> models.Organization:
    try:
        db_organization = OrganizationCRUD.get(db, organization_id)
        if db_organization.logo is None:
            raise exceptions.ApolloObjectDoesNotExist(model_name='OrganizationLogo')
        return db_organization
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Organization')


def get_membership_by_id(db: Session, membership_id: str) -> models.Membership:
    try:
        db_membership = MembershipCRUD.get(db, membership_id)
        return db_membership
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_membership_by_filters(db: Session, membership_filter: dict) -> models.Membership:
    try:
        query = MembershipCRUD.query(db)
        if membership_filter['user_id']:
            query = query.filter(models.Membership.user_id == membership_filter['user_id'])
        if membership_filter['membership_id']:
            query = query.filter(models.Membership.id == membership_filter['membership_id'])
        return query.one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_membership_by_email_list(db: Session, emails: list[str]) -> models.Membership:
    try:
        query = MembershipCRUD.query(db).join(models.User)
        query = query.filter(models.User.email.in_(emails))
        return query.all()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def filter_membership(query: Query, filters: dict) -> Query:
    username = filters.pop("name")
    if username:
        query = query.filter(
            func.concat(models.Membership.first_name, ' ', models.Membership.last_name).ilike(f'%{username}%')
        )
    organization = filters.pop("organization")
    if organization:
        query = query.join(models.Organization).filter(models.Organization.name.ilike(f'%{organization}%'))
    query = models.filters_using_like(filters, query, models.Membership)
    return query


def filter_user(query: Query, filters: dict) -> Query:
    query = query.join(models.User)
    for key, value in filters.items():
        if key == 'migrated_only':
            if value:
                query = query.filter(models.User.is_migrated.is_(True))
            else:
                query = query.filter(
                    or_(
                        models.User.is_migrated.is_(False),
                        and_(
                            models.User.is_migrated.is_(True),
                            models.User.migration_status.is_(True)
                        )
                    )
                )
        elif isinstance(value, bool) and key != 'migrated_only':
            query = query.filter(getattr(models.User, key) == value)
        else:
            query = models.filters_using_like({key: value}, query, models.User)
    return query


def filter_manual_link_user(query: Query, filters: dict, migrated_user: bool = False) -> Query:
    query = query.join(models.User)
    if not migrated_user:
        query = query.filter(models.User.is_migrated.is_(False))
    if migrated_user:
        query = query.filter(models.User.is_migrated.is_(True),
                             models.User.migration_status.is_(False))
    for key, value in filters.items():
        if isinstance(value, bool):
            query = query.filter(getattr(models.User, key) == value)
        else:
            query = models.filters_using_like({key: value}, query, models.User)
    return query


def filter_dunning(query: Query, filters: dict) -> Query:
    query = query.join(models.MembershipDunning)
    for key, value in filters.items():
        if key == 'block_status':
            if value == 'available':
                query = query.filter(
                    models.MembershipDunning.block_status == schema.DunningBlockStatusEnum.available.value)
            elif value == 'blocked':
                query = query.filter(
                    models.MembershipDunning.block_status == schema.DunningBlockStatusEnum.blocked.value)
        if key == 'admin_block_status':
            if value == 'no_status':
                query = query.filter(
                    models.MembershipDunning.admin_block_status == schema.DunningAdminBlockStatusEnum.no_status.value)
            elif value == 'blocked':
                query = query.filter(
                    models.MembershipDunning.admin_block_status == schema.DunningAdminBlockStatusEnum.blocked.value)
            elif value == 'unblocked':
                query = query.filter(
                    models.MembershipDunning.admin_block_status == schema.DunningAdminBlockStatusEnum.unblocked.value)
        elif isinstance(value, bool):
            query = query.filter(getattr(models.MembershipDunning, key) == value)
        else:
            query = models.filters_using_like({key: value}, query, models.MembershipDunning)
    return query


def get_organization_membership_list(db: Session, organization_id: str,
                                     membership_type: str = None,
                                     include_child: bool = False,
                                     user_filter: dict = None,
                                     mem_filter: dict = None,
                                     dunning_filter: dict = None,
                                     check_permission: bool = True,
                                     migrated_user: bool = False,
                                     return_email: bool = False):
    query = MembershipCRUD.query(db, check_permission=check_permission) \
        .options(joinedload(models.Membership.dunning_details))
    if organization_id:
        org_ids = [organization_id]
        if include_child:
            child_org_ids = get_all_child_organizations(db, organization_id)
            org_ids.extend(child_org_ids)
        query = query.filter(models.Membership.organization_id.in_(org_ids))

    if membership_type is not None:
        query = query.filter(models.Membership.membership_type == membership_type)

    if mem_filter:
        query = filter_membership(query, mem_filter)
    if user_filter and not return_email:
        query = filter_user(query, user_filter)
    if user_filter and return_email:
        query = filter_manual_link_user(query, user_filter, migrated_user)
    if dunning_filter and any(value is not None for value in dunning_filter.values()):
        query = filter_dunning(query, dunning_filter)
    query = query.order_by(models.Membership.created_at.desc(),
                           models.Membership.updated_at.desc().nullslast())
    return query


def create_dunning_rc_for_all_member(db: Session):
    try:
        message = 'Initiated all dunning for all member'
        query = db.query(models.Membership).filter(
            models.Membership.is_deleted.is_(False),
        )
        db_members = query.all()
        for member in db_members:
            try:
                get_membership_dunning_by_membership_id(db, str(member.id))
            except exceptions.ApolloObjectDoesNotExist:
                membership_dunning_data = schema.InitiateMembershipDunning(
                    membership_id=str(member.id),
                )
                create_membership_dunning(db, membership_dunning_data)
        return message
    except Exception as e:  # pylint: disable=broad-except
        print('error :', e)
        message = 'Failed to initiate dunning'
        return message


def get_membership_dunning_by_membership_id(
        db: Session,
        membership_id: str
):
    try:
        db_membership_dunning = MembershipDunningCRUD.query(db).filter(
            models.MembershipDunning.membership_id == membership_id,
        ).first()
        if db_membership_dunning is None:
            raise NoResultFound
        return db_membership_dunning
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='MembershipDunning')


def create_membership_dunning(db: Session, membership_dunning: schema.InitiateMembershipDunning
                              ) -> models.MembershipDunning:
    """
    Creates necessary data to keep track of user's dunning status
    """
    try:
        # Create extended membership
        db_membership_dunning = MembershipDunningCRUD.add(db, membership_dunning.dict())

        return db_membership_dunning
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloDuplicateMembershipError()


def update_membership_dunning(db: Session, mem_dunning_id, update_data) -> models.MembershipDunning:
    deactivate_audit()
    try:
        mem_dunning = MembershipDunningCRUD.update(db, mem_dunning_id, update_data, check_permission=False)
        return mem_dunning

    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='MembershipDunning')

    except IntegrityError:
        db.rollback()
        raise exceptions.UpdateDunningError()


def admin_update_membership_dunning(db: Session,
                                    membership_id: str,
                                    type: str,
                                    data: schema.DunningAdminBlockBody = None) -> models.MembershipDunning:
    if data:
        data = data.dict(exclude_unset=True)
    try:
        # TODO: need to calculate if user have outstanding amount first, reject if none.
        db_mem_dunning = get_membership_dunning_by_membership_id(db, str(membership_id))
        dunning_data = {}
        if type == 'block':
            dunning_data['admin_block_status'] = schema.DunningAdminBlockStatusEnum.blocked.value
            dunning_data['admin_block_date'] = data['admin_block_date']
            dunning_data['admin_block_remark'] = data['admin_block_remark']

        if type == 'unblock':
            admin_unblock_days = data['admin_unblock_days']
            unblock_to_date = datetime.utcnow() + timedelta(days=admin_unblock_days)
            dunning_data['admin_block_status'] = schema.DunningAdminBlockStatusEnum.unblocked.value
            dunning_data['admin_block_date'] = unblock_to_date
            dunning_data['admin_block_remark'] = data['admin_unblock_remark']

        if type == 'clear':
            dunning_data['admin_block_status'] = schema.DunningAdminBlockStatusEnum.no_status.value
            dunning_data['admin_block_date'] = None
            dunning_data['admin_block_remark'] = ""

        db_mem_dunning = MembershipDunningCRUD.update(db, db_mem_dunning.id, dunning_data, check_permission=False)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='MembershipDunning')

    except IntegrityError:
        db.rollback()
        raise exceptions.UpdateDunningError()

    except exceptions.ApolloPermissionError:
        pass
    return db_mem_dunning


def get_organization_membership_list_with_user(db: Session, organization_id: str,  # noqa
                                               membership_type: str = None,
                                               include_child: bool = False,
                                               user_filter: dict = None,
                                               mem_filter: dict = None):
    query = db.query(models.Membership, models.User).join(models.User)

    if organization_id is not None:
        organizations = [organization_id]
        if include_child:
            orgs_list = get_organization_list(db)
            organizations = [str(org.id) for org in orgs_list]
        query = query.filter(models.Membership.organization_id.in_(organizations))

    if membership_type is not None:
        query = query.filter(models.Membership.membership_type == membership_type)

    if mem_filter:
        query = filter_membership(query, mem_filter)
    if user_filter:
        query = models.filters_using_like(user_filter, query, models.User)
    return query


def get_user_organization_list(db: Session, user_id: str):  # noqa
    members = MembershipCRUD.query(db).filter(models.Membership.user_id == str(user_id),
                                              models.Membership.is_deleted == False).all()  # pylint; # noqa
    return [member.organization_id for member in members] if members else []


def check_membership_existence(db: Session, organization_id: str, user_id: str, user_type: str) -> bool:
    query = MembershipCRUD.query(db, check_permission=False).join(models.User) \
        .filter(models.Membership.organization_id == organization_id,
                models.Membership.user_id == user_id,  # pylint; # noqa
                models.Membership.is_deleted.is_(False),
                models.User.user_type == user_type).all()  # pylint; # noqa
    if query:
        return True
    return False


def create_organization_membership(db: Session, data: schema.StaffMembership,  # noqa: MC0001
                                   membership_type: schema.MembershipType) -> models.Membership:
    data = data.dict()
    if 'role_id' in data:
        data.pop('role_id')
    data.pop('vehicle_brand', None)
    membership_data = {
        'first_name': data.pop('first_name'),
        'last_name': data.pop('last_name'),
        'user_id_tag': data.pop('user_id_tag'),
        'password': data.pop('password'),
        'organization_id': data.pop('organization_id'),
        'vehicle_model': data.pop('vehicle_model', None),
        'allow_marketing': data.pop('allow_marketing', False),
    }

    if membership_type == schema.MembershipType.regular_user:
        try:
            data['user_type'] = schema.UserType.regular
            user = get_user_by_phone_number_v2(db, data.get('phone_number'), schema.UserType.regular)
            if user:
                raise exceptions.ApolloDuplicateUser()
        except exceptions.ApolloObjectDoesNotExist:
            pass
    else:
        data['user_type'] = schema.UserType.staff
    organization = OrganizationCRUD.get(db, membership_data['organization_id'], check_permission=False)
    if organization.parent is None:
        raise exceptions.ApolloRootOrganizationMembershipCreation

    # Remove data that are not used for User model
    keys_to_remove = ['password_confirmation', 'invite_token']
    for key in keys_to_remove:
        if data[key] or key in data:
            del data[key]
    if membership_type != membership_type.regular_user:
        data['verification_method'] = schema.auth.VerificationMethodEnum.email
    try:
        db_user = UserCRUD.add(db, dict(data, organization_id=membership_data['organization_id'], is_verified=True))
    except IntegrityError:
        db.rollback()

    # User already exists (User might have a membership for another Organization)
    try:
        db_user = get_user_by_email_or_phone_number_v2(db, data['email'], data['phone_number'],
                                                       membership_data['organization_id'], data['user_type'])
    except exceptions.ApolloObjectDoesNotExist:
        raise exceptions.ApolloUserDataError

    membership = schema.Membership(
        user_id=str(db_user.id),
        membership_type=membership_type,
        **membership_data
    )
    user_org = get_user_organization_list(db, db_user.id)
    child_orgs = OrganizationCRUD.child_organizations(db, membership)
    common_orgs = list(set(user_org).intersection(child_orgs))
    if common_orgs:
        common_org = OrganizationCRUD.get(db, common_orgs[0])
        raise exceptions.ApolloDuplicateMembershipParentError(None, common_org.name)

    check_same_org = check_membership_existence(db, organization.id, db_user.id, data['user_type'])
    check_parent_org = check_membership_existence(db, organization.parent.id, db_user.id, data['user_type'])
    if check_parent_org:
        raise exceptions.ApolloDuplicateMembershipParentError(organization.name, organization.parent.name)
    if check_same_org:
        raise exceptions.ApolloDuplicateMembershipParentError(organization.name)

    # Create User's membership
    db_membership = create_user_membership(db, membership)

    return db_membership


def update_organization_membership(db: Session, data: schema.MembershipUpdate,  # noqa: MC0001
                                   organization_id: str, membership_id: str) -> models.Membership:
    data = data.dict(exclude_unset=True)
    try:
        membership_data = {}
        if 'password' in data:
            membership_data['password'] = bcrypt.hashpw(data.pop('password').encode('utf-8'),
                                                        bcrypt.gensalt(11, b'2a')).decode('utf-8')
        for key in ['membership_type', 'first_name', 'last_name', 'user_id_tag']:
            if key in data:
                membership_data[key] = data.pop(key)
        if organization_id:
            membership_data['organization_id'] = organization_id
            data['organization_id'] = organization_id
        db_membership = MembershipCRUD.update(db, membership_id, membership_data, check_permission=False)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')

    try:
        UserCRUD.update(db, str(db_membership.user_id), data, check_permission=False)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloEmailPhoneNumberAlreadyAssigned
    # Don't throw permission error when accessing user doesn't have permission, but prevent from updating User model
    except exceptions.ApolloPermissionError:
        pass
    return db_membership


def get_soft_deleted_number(db: Session):
    try:
        db_user = db.query(models.User).filter(
            models.User.is_deleted.is_(True)
        ).order_by(models.User.deleted_at.desc()).first()
        if db_user:
            email = db_user.email.split('_')
            return int(email[2]) + 1
        return 1
    except (IndexError, AttributeError):
        db_user = db.query(models.User).filter(
            models.User.is_deleted.is_(True)
        ).order_by(models.User.deleted_at.desc()).count()
        return int(db_user) + 1
    return 1


def delete_organization_membership(db: Session, membership_id: str) -> str:  # noqa: MC0001
    try:
        db_mem = get_membership_by_id(db, membership_id)
        MembershipCRUD.delete(db, membership_id, check_permission=False)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')
    try:
        db_user = UserCRUD.get(db, db_mem.user_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')
    if db_user.is_superuser:
        return membership_id
    # Soft delete unique fields of User model
    soft_deleted_number = get_soft_deleted_number(db)

    # check if its guest, if guest, dont have phone and email
    if not db_user.is_guest:
        user_phone_truncated = db_user.phone_number[-5:]
        user_email_domain = db_user.email.split('@')[1]
    else:
        user_phone_truncated = 'guest_' + str(db_mem.id)
        if db_user.email is None:
            user_email_domain = 'guest.com'
        else:
            try:
                user_email_domain = db_user.email.split('@')[1]
            except Exception:  # pylint: disable=broad-except
                user_email_domain = 'guest.com'

    soft_delete_data = {'is_deleted': True, 'deleted_at': datetime.now()}
    WalletCRUD.query(db, check_permission=False).filter(
        models.Wallet.member_id == membership_id).update(soft_delete_data)
    CreditCardCRUD.query(db, check_permission=False).filter(
        models.CreditCard.member_id == membership_id).update(soft_delete_data)

    user_orgs = get_user_organization_list(db, db_user.id)
    if len(user_orgs) > 1:
        return membership_id
    for i in range(soft_deleted_number, soft_deleted_number + 3):
        try:
            unique_user_data_deletion = {
                'email': f'deleted_user_{i}_{db_mem.membership_type}_@{user_email_domain}',
                'phone_number': f'+deleted_{i}_{db_mem.membership_type}_{user_phone_truncated}'
            }
            if db_user.is_guest:
                unique_user_data_deletion[
                    'guest_app_id'] = f'deleted_user_{i}_{db_mem.membership_type}_@{db_user.guest_app_id}'

            UserCRUD.update(db, db_mem.user_id, unique_user_data_deletion, check_permission=False)
            UserCRUD.delete(db, db_user.id, check_permission=False)
            return membership_id
        except IntegrityError:
            continue
    raise exceptions.ApolloUserDataError


def update_manual_linking_verification(db: Session, data: schema.ManualLinkingTokenUpdate, migrated_member,
                                       target_member):
    """
    Updates verification token for membership extended
    """
    manual_linking_token_id = data['id']
    del data['id']
    try:
        target_user = UserCRUD.query(db, check_permission=False).filter(
            models.User.id == str(target_member.user_id)
        ).one()
        email_to_update = target_user.email
        phone_to_update = target_user.phone_number
        print('email_to_update', email_to_update)
        print('phone_to_update', phone_to_update)
        migrated_user = UserCRUD.query(db, check_permission=False).filter(
            models.User.id == str(migrated_member.user_id)
        ).one()
        delete_organization_membership(db, target_member.id)
        migrated_member_update_data = {
            'password': target_member.password,
            'first_name': target_member.first_name,
            'last_name': target_member.last_name,
        }
        migrated_user_update_data = {
            # 'email': email_to_update,
            'phone_number': phone_to_update,
            'migration_status': True,
            'verification_method': schema.VerificationMethodEnum.phone,
            'is_verified': True,
            'link_date': datetime.now(),
        }
        membership_extended_data = {
            'verification_method': schema.VerificationMethodEnum.phone,
            'email_verified': True,
            'phone_verified': True,
            'membership_id': str(migrated_member.id),
        }
        MembershipCRUD.update(db, migrated_member.id, migrated_member_update_data, check_permission=False)
        UserCRUD.update(db, migrated_user.id, migrated_user_update_data, check_permission=False)
        MembershipExtendedCRUD.add(db, membership_extended_data)
        ManualLinkingTokenCRUD.update(db, manual_linking_token_id, data)
    except IntegrityError:
        db.rollback()
        raise exceptions.ManualLikingError()


def delete_all_memberships_from_organization(db: Session, organization_id: str) -> str:
    try:
        data = {'is_deleted': True, 'deleted_at': datetime.now()}
        members = MembershipCRUD.query(db, check_permission=False).filter(
            models.Membership.organization_id == organization_id)
        members_query = members.all()
        members.update(data)

        members_id_str = [mem_id.id for mem_id in members_query]
        WalletCRUD.query(db, check_permission=False).filter(
            models.Wallet.member_id.in_(members_id_str)).update(data)
        CreditCardCRUD.query(db, check_permission=False).filter(
            models.CreditCard.member_id.in_(members_id_str)).update(data)

        user_deletion = []
        for member in members_query:
            memberships_count = MembershipCRUD.query(db, check_permission=False).filter(
                models.Membership.user_id == member.user_id).count()
            if memberships_count == 0:
                user_deletion.append(member.user_id)
        UserCRUD.query(db, check_permission=False).filter(
            models.User.id.in_(user_deletion)).update(data)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def attach_roles_to_membership(db: Session, data: schema.MembershipRoles, membership_id: str) -> models.Membership:
    try:
        db_membership = MembershipCRUD.get(db, membership_id)
        for role_id in data.roles:
            try:
                db_role = RoleCRUD.query(db).filter(models.Role.id == role_id).one()
                if db_role.organization_id == db_membership.organization_id:
                    MembershipCRUD.can_write(db, membership_id)
                    db_membership.roles.append(db_role)
                elif db_role.organization_id is None:
                    db_membership.roles.append(db_role)
                else:
                    raise exceptions.ApolloRoleAssociationError(role_id)
            except NoResultFound:
                raise exceptions.ApolloObjectDoesNotExist(f'Role {role_id}')
        db.commit()

        return db_membership
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def attach_operator_to_membership(db: Session, data: schema.MembershipOperator,
                                  membership_id: str) -> models.Membership:
    try:
        db_membership = MembershipCRUD.get(db, membership_id)
        db_operator = OperatorCRUD.query(db).filter(models.Operator.id == data.operator_id).first()
        if db_operator:
            MembershipCRUD.can_write(db, membership_id)
            db_membership.operators.append(db_operator)
        else:
            pass
        db.commit()

        return db_membership
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def remove_operator_from_membership(db: Session, data: schema.MembershipOperator,
                                    membership_id: str) -> models.Membership:
    try:
        db_membership = MembershipCRUD.get(db, membership_id)
        db_operator = OperatorCRUD.query(db).filter(models.Operator.id == data.operator_id).first()
        if db_operator:
            MembershipCRUD.can_write(db, membership_id)
            db_membership.operators.remove(db_operator)
        else:
            pass
        db.commit()

        return db_membership
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def remove_roles_from_membership(db: Session, data: schema.MembershipRoles, membership_id: str) -> models.Membership:
    try:
        db_membership = MembershipCRUD.get(db, membership_id)
        for role_id in data.roles:
            db_role = RoleCRUD.query(db).filter(models.Role.id == role_id).first()
            if db_role:
                MembershipCRUD.can_write(db, membership_id)
                db_membership.roles.remove(db_role)
            else:
                pass
        db.commit()

        return db_membership
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_id_tag(db: Session, id_tag_id: str) -> models.IDTag:
    try:
        db_id_tag = IDTagCRUD.get(db, id_tag_id)
        return db_id_tag
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='IDTag')


def get_id_tag_by_id_tag(db: Session, id_tag: str) -> models.IDTag:
    try:
        db_id_tag = IDTagCRUD.query(db).filter(models.IDTag.id_tag == id_tag).one()
        return db_id_tag
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='IDTag')


def create_user_id_tag(db: Session) -> str:
    while True:
        new_id_tag = fake.pystr(min_chars=10, max_chars=10)
        if not user_id_tag_exists(db, new_id_tag):
            return new_id_tag


def user_id_tag_exists(db: Session, user_id_tag: str) -> bool:
    return MembershipCRUD.query(db, check_permission=False).filter(
        models.Membership.user_id_tag == user_id_tag).first() is not None


def get_member_id_tag_list(db: Session, member_id: str) -> models.IDTag:
    query = IDTagCRUD.query(db).filter(
        models.IDTag.member_id == member_id,
    ).order_by(
        models.IDTag.updated_at.desc().nullslast(),
        models.IDTag.created_at.desc()
    )
    return query.all()


def get_member_id_tag_list_from_list(db: Session, member_id: list[str]) -> models.IDTag:
    query = IDTagCRUD.query(db, check_permission=False).filter(
        models.IDTag.member_id.in_(member_id),
    ).order_by(
        models.IDTag.updated_at.desc().nullslast(),
        models.IDTag.created_at.desc()
    )
    return query.all()


def get_member_by_id_tags(db: Session, id_tag: list) -> models.Membership:
    query = MembershipCRUD.query(db).filter(models.Membership.user_id_tag.in_(id_tag))
    try:
        db_members = query.all()
        return db_members
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_operator_by_operator_list(db: Session, operator_ids: list) -> models.Operator:
    query = OperatorCRUD.query(db).filter(models.Operator.id.in_(operator_ids))
    try:
        db_operators = query.all()
        return db_operators
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Operator')


def get_external_organization_by_operator_list(db: Session, operator_ids: list) -> models.ExternalOrganization:
    query = ExternalOrganizationCRUD.query(db).filter(models.ExternalOrganization.id.in_(operator_ids))
    try:
        db_operators = query.all()
        return db_operators
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ExternalOrganization')


def get_external_organization_by_operator_name(db: Session, operators_names: list) -> models.ExternalOrganization:
    query = ExternalOrganizationCRUD.query(db).filter(models.ExternalOrganization.name.in_(operators_names))
    db_operators = query.all()
    return db_operators


def get_external_organization_by_operator_id(dbsession: Session, operator_id: str) -> models.ExternalOrganization:
    query = ExternalOrganizationCRUD.query(dbsession).filter(models.ExternalOrganization.id == operator_id)
    try:
        return query.one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ExternalOrganization')


def get_ocpi_external_organization_list(dbsession: Session) -> models.ExternalOrganization:
    query = ExternalOrganizationCRUD.query(dbsession).filter(
        models.ExternalOrganization.organization_type == 'OCPI')
    return query.all()


def update_external_organization(db: Session, external_organization_data: schema.UpdateExternalOrganization,
                                 external_organization_id: str) -> models.ExternalOrganization:
    try:
        db_id_tag = ExternalOrganizationCRUD.update(db, external_organization_id,
                                                    external_organization_data.dict(exclude_unset=True,
                                                                                    exclude_defaults=True))
        return db_id_tag
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ExternalOrganization')


def delete_external_organization(db: Session, external_organization_id: str) -> str:
    try:
        ExternalOrganizationCRUD.delete(db, external_organization_id)
        return external_organization_id
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Organization')


def get_member_id_tag(db: Session, member_id: str, id_tag_type: schema.IDTagType) -> models.IDTag:
    query = IDTagCRUD.query(db).filter(
        models.IDTag.member_id == member_id,
        models.IDTag.type == id_tag_type,
        models.IDTag.is_active.is_(True),
        models.IDTag.expiration > datetime.now(),
    ).order_by(
        models.IDTag.updated_at.desc().nullslast(),
        models.IDTag.created_at.desc()
    )
    try:
        id_tag = query.first()
        if id_tag is None:
            raise exceptions.ApolloObjectDoesNotExist(model_name='IDTag')
        return id_tag
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='IDTag')


def check_member_authorization_by_id_tag(db: Session, id_tag: str):
    query = IDTagCRUD.query(db).filter(
        func.lower(models.IDTag.id_tag) == id_tag.lower(),
    )
    try:
        return query.one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='IDTag')


def get_membership_by_id_tag(db: Session, id_tag: str):
    query = MembershipCRUD.query(db, check_permission=False).filter(
        func.lower(models.Membership.user_id_tag) == id_tag.lower(),
    )
    try:
        return query.one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_member_by_id_tag(db: Session, id_tag: str) -> models.Membership:
    query = IDTagCRUD.query(db).filter(
        func.lower(models.IDTag.id_tag) == id_tag.lower(),
    )
    try:
        db_id_tag = query.one()
        return db_id_tag.member
    except NoResultFound:
        member = MembershipCRUD.query(db, check_permission=False).filter(
            func.lower(models.Membership.user_id_tag) == id_tag.lower()).first()
        if member:
            return member
        raise exceptions.ApolloObjectDoesNotExist(model_name='IDTag')


def get_member_by_id_tags_lta(db: Session, id_tags: list[str]):
    try:
        query = IDTagCRUD.query(db, models.IDTag.member).filter(
            models.IDTag.id_tag.in_(id_tags)
        )
        db_id_tag = query.all()
        member = MembershipCRUD.query(db, check_permission=False).filter(
            models.Membership.user_id_tag.in_(id_tags)).all()
        return member, db_id_tag
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='IDTag')


def get_member_by_id_tag_ilike(db: Session, id_tag: str) -> models.Membership:
    query = IDTagCRUD.query(db, check_permission=False).filter(
        func.lower(models.IDTag.id_tag).ilike(f'%{id_tag}%'),
    )
    db_id_tag = query.all()
    if db_id_tag:
        return [idtag.member for idtag in db_id_tag]
    member = MembershipCRUD.query(db, check_permission=False).filter(
        func.lower(models.Membership.user_id_tag).ilike(f'%{id_tag}%')).all()
    return member


def create_id_tag(db: Session, id_tag_data: schema.IDTag) -> models.IDTag:
    try:
        data = id_tag_data.dict()
        if 'member_id' in data and data['member_id']:
            data['organization_id'] = None
        elif not data['organization_id']:
            mem = IDTagCRUD.membership()
            data['organization_id'] = mem.organization_id
        db_id_tag = IDTagCRUD.add(db, data)
        return db_id_tag
    except IntegrityError:
        db.rollback()
        id_tag_dict = id_tag_data.dict()
        try:
            db_id_tag = IDTagCRUD.query(db).filter(models.IDTag.id_tag == id_tag_dict['id_tag'],
                                                   models.IDTag.member_id.is_(None)).one()
            db_id_tag = IDTagCRUD.update(db, db_id_tag.id, id_tag_data.dict())
            return db_id_tag
        except NoResultFound:
            raise exceptions.ApolloIDTagError()


def get_membership_by_ocpi_token_id(db: Session, ocpi_token_id: str) -> models.Membership:
    try:
        db_ocpi_token = OCPITokenCRUD.get(db, ocpi_token_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPIToken')
    try:
        db_membership = MembershipCRUD.get(db, db_ocpi_token.member_id)
        return db_membership
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_or_create_ocpi_app_token(db: Session, member_id: str) -> models.OCPIToken:
    try:
        db_membership = MembershipCRUD.get(db, member_id, check_permission=False)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')
    try:
        db_ocpi_token = OCPITokenCRUD.query(db).filter(
            models.OCPIToken.member_id == member_id,
            models.OCPIToken.type == 'APP_USER'
        ).one()
        return db_ocpi_token
    except NoResultFound:
        if str(settings.OCPI_PARTY_ID).upper() == 'CEV':
            issuer = 'CHARGEV'
        elif str(settings.OCPI_PARTY_ID).upper() == 'HGM':
            issuer = 'HGM'
        else:
            issuer = 'CDGENGIE'
        ocpi_token = schema.OCPIToken(member_id=member_id, contract_id=db_membership.user_id_tag, issuer=issuer)
        db_ocpi_token = OCPITokenCRUD.add(db, ocpi_token.dict())
        return db_ocpi_token


def get_or_create_ocpi_vehicle_token(db: Session, member_id: str, emaid: str) -> models.OCPIToken:
    try:
        db_ocpi_token = OCPITokenCRUD.query(db).filter(
            models.OCPIToken.member_id == member_id,
            models.OCPIToken.contract_id == emaid,
            models.OCPIToken.type == 'OTHER'
        ).one()
        return db_ocpi_token
    except NoResultFound:
        if str(settings.OCPI_PARTY_ID).upper() == 'CEV':
            issuer = 'CHARGEV'
        elif str(settings.OCPI_PARTY_ID).upper() == 'HGM':
            issuer = 'HGM'
        else:
            issuer = 'CDGENGIE'
        ocpi_token = schema.OCPIToken(member_id=member_id, contract_id=emaid, issuer=issuer,
                                      type='OTHER', whitelist='ALLOWED')
        db_ocpi_token = OCPITokenCRUD.add(db, ocpi_token.dict())
        return db_ocpi_token


def update_id_tag(db: Session, id_tag_data: schema.IDTag, id_tag_id: str) -> models.IDTag:
    try:
        db_id_tag = IDTagCRUD.update(db, id_tag_id, id_tag_data.dict(exclude_unset=True, exclude_defaults=True))
        return db_id_tag
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='IDTag')


def delete_id_tag(db: Session, id_tag_id: str):
    try:
        db_id_tag = IDTagCRUD.query(db).filter(
            models.IDTag.id == id_tag_id
        ).first()
        id_tag = db_id_tag.id_tag

        soft_deleted_number = get_id_tag_soft_deleted_number(db)

        for i in range(soft_deleted_number, soft_deleted_number + 3):
            try:
                unique_id_tag_data_deletion = {
                    'id_tag': f'deleted_tag_{i}_{id_tag}',
                }
                IDTagCRUD.update(db, db_id_tag.id, unique_id_tag_data_deletion, check_permission=False)
                IDTagCRUD.delete(db, db_id_tag.id, check_permission=False)
                return db_id_tag
            except IntegrityError:
                continue
        raise exceptions.ApolloIDTagError()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='IDTag')


def get_id_tag_soft_deleted_number(db: Session):
    try:
        db_id_tag = db.query(models.IDTag).filter(
            models.IDTag.is_deleted.is_(True)
        ).order_by(models.IDTag.deleted_at.desc()).first()
        if db_id_tag:
            id_tag = db_id_tag.id_tag.split('_')
            return int(id_tag[2]) + 1
        return 1
    except IndexError:
        db_id_tag = db.query(models.IDTag).filter(
            models.IDTag.is_deleted.is_(True)
        ).order_by(models.IDTag.deleted_at.desc()).count()
        return int(db_id_tag) + 1
    return 1


def get_all_id_tags_with_filters(db: Session, tag_filters: dict,  # noqa: MC0001
                                 id_fitlers: dict = None, check_permission: bool = True):
    query = IDTagCRUD.query(db, check_permission=check_permission) \
        .order_by(models.IDTag.updated_at.desc().nullslast(), models.IDTag.created_at.desc())
    membership = IDTagCRUD.membership()
    is_superuser = membership.user.is_superuser
    if not check_permission or is_superuser:
        query = query.outerjoin(models.Membership)
    if tag_filters['name']:
        query = query.filter(models.IDTag.name.ilike(f'%{tag_filters["name"]}%'))
    if tag_filters['type']:
        query = query.filter(models.IDTag.type == tag_filters['type'])
    if tag_filters['is_active'] is not None:
        query = query.filter(models.IDTag.is_active == tag_filters['is_active'])
    if tag_filters['from_date']:
        query = query.filter(models.IDTag.expiration >= tag_filters['from_date'])
        # query = query.filter(models.IDTag.created_at >= tag_filters['from_date'])
    if tag_filters['to_date']:
        query = query.filter(models.IDTag.expiration <= tag_filters['to_date'])
    if tag_filters['username']:
        query = query.filter(
            func.concat(models.Membership.first_name, ' ',
                        models.Membership.last_name).ilike(f'%{tag_filters["username"]}%')
        )
    if id_fitlers:
        phone_number = id_fitlers.pop('phone_number') if 'phone_number' in id_fitlers else None
        email = id_fitlers.pop('email') if 'email' in id_fitlers else None
        if phone_number and email:
            query = query.join(models.User) \
                .filter(models.User.phone_number.ilike(f'%{phone_number}%'),
                        models.User.email.ilike(f'%{email}%'))
        elif phone_number:
            query = query.join(models.User) \
                .filter(models.User.phone_number.ilike(f'%{phone_number}%'))
        elif email:
            query = query.join(models.User) \
                .filter(models.User.email.ilike(f'%{email}%'))
    if id_fitlers:
        query = models.filters_using_like(id_fitlers, query, models.IDTag)
    return query


def get_organization_from_user_id(db: Session, user_id: UUID) -> List[models.Organization]:
    try:
        db_organization_list = MembershipCRUD.query(db, check_permission=False).filter(
            models.Membership.user_id == user_id,
            models.Membership.membership_type.in_([schema.MembershipType.staff,
                                                   schema.MembershipType.sub_staff,
                                                   schema.MembershipType.custom]),
        ).all()

        clean_organization_list = list(map(
            lambda x: {'id': x.organization_id, 'name': x.organization.name}, db_organization_list))

        return clean_organization_list
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Organization')


def get_membership_record_by_user_id(db: Session,
                                     user_id: str, organization_id: str) -> typing.List[models.Membership]:
    membership_record = MembershipCRUD.query(db, check_permission=False).filter(
        models.Membership.user_id == user_id,
        models.Membership.organization_id == organization_id,
    ).one()
    if membership_record:
        return membership_record

    raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def update_membership_profile(db: Session, data: mobile_schema.MobileMembershipUpdateResponse,  # noqa: MC0001
                              member_id: str) -> models.Membership:
    if data.password:
        data.password = bcrypt.hashpw(data.password.encode('utf-8'), bcrypt.gensalt(11, b'2a')).decode('utf-8')
    data = data.dict(exclude_unset=True)
    data.pop('vehicle_brand', None)
    data.pop('password_confirmation', None)
    try:
        membership_data = {}
        for key in ['password', 'first_name', 'last_name', 'vehicle_model', 'preferred_payment_method',
                    'pre_auth_amount', 'preferred_payment_flow']:
            if key in data:
                membership_data[key] = data[key]
                del data[key]

        db_membership = MembershipCRUD.update(db, member_id, membership_data, check_permission=False)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')

    if data:
        try:
            db_user = UserCRUD.get(db, db_membership.user_id)
            # Do not update email for migrated user.
            if db_user.is_migrated:
                if "email" in data:
                    del data['email']
                if "organization_id" in data:
                    del data['organization_id']
            UserCRUD.update(db, str(db_membership.user_id), data, check_permission=False)
        except NoResultFound:
            raise exceptions.ApolloObjectDoesNotExist(model_name='User')
        except exceptions.ApolloPermissionError:
            pass
    return db_membership


def create_organization_authentication_service(db: Session, organization_id: str) \
        -> models.OrganizationAuthenticationService:
    api_key = secrets.token_hex(32)
    schema_data = {
        'id': organization_id,
        'organization_id': organization_id,
        'secret_key': schema.argon_ph.hash(api_key),
    }
    db_organization_auth_service = OrganizationAuthenticationServiceCRUD.add(db, schema_data)
    return {"id": db_organization_auth_service.id,
            "secret_key": api_key, "organization_id": db_organization_auth_service.organization_id}


def get_oas_by_organization_id(db: Session, organization_id: str) -> models.OrganizationAuthenticationService:
    try:
        db_oas = OrganizationAuthenticationServiceCRUD.query(db).filter(
            models.OrganizationAuthenticationService.id == organization_id,
        ).first()

        return db_oas
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Organization')


def regenerate_api_key(db: Session, organization_id: str) -> models.OrganizationAuthenticationService:
    try:
        api_key = secrets.token_hex(32)

        db_oas = OrganizationAuthenticationServiceCRUD.query(db).filter(
            models.OrganizationAuthenticationService.id == organization_id,
        ).first()
        if db_oas:
            db_oas = OrganizationAuthenticationServiceCRUD.update(db, db_oas.id,
                                                                  {'secret_key': schema.argon_ph.hash(api_key)})
        else:
            db_oas = create_organization_authentication_service(db, organization_id)
        return {"id": db_oas.id, "secret_key": api_key, "organization_id": db_oas.organization_id}
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Organization')


def get_operator_by_organization_id(db: Session, organization_id: str):
    db_operator = OperatorCRUD.query(db).filter(
        models.Operator.organization_id == organization_id,
    ).first()

    return db_operator


def get_operator_by_organization_id_include_child(db: Session, membership: schema.MembershipResponse):
    child_orgs = OperatorCRUD.child_organizations(db, membership)
    allowed_orgs = child_orgs + [membership.organization_id]
    db_operator = OperatorCRUD.query(db).filter(
        models.Operator.organization_id.in_(allowed_orgs),
    ).with_entities(models.Operator.id).all()

    return [op[0] for op in db_operator]


def get_operator_by_operator_name(db: Session, operator_name: str):
    db_operator = OperatorCRUD.query(db).filter(
        models.Operator.name.ilike(f'%{operator_name}%'),
    ).all()

    return db_operator


def get_chargev_lhn_np(db: Session):
    db_operator = OperatorCRUD.query(db).filter(
        or_(
            models.Operator.name == 'LHN',
            models.Operator.name == 'NP',
            models.Operator.name == 'chargEV', )
    ).all()
    return db_operator

    # adapted_data = {
    #     'party_id': data['party_id'],
    #     'country_code': data['country_code'],
    #     'images': data['business_details']['logo'],
    #     'website': data['business_details']['website'],
    #     'description': str(data['business_details']['name'] + ' ' + data['role'])

    # }


def create_external_organization(db: Session, data: dict) -> models.ExternalOrganization:
    data = dict(data)
    if data['images']:
        data['images'] = data['images']['url']
    db_ext_organization = ExternalOrganizationCRUD.add(db, data)
    return db_ext_organization


# noqa: MC0001, #pylint:disable=too-many-statements, too-many-locals
def get_audit_log(db: Session, params, from_date: datetime = None,
                  to_date: datetime = None, user: str = None,
                  group_id: str = None, module: str = None, action: str = None):
    membership = UserAuditLogCRUD.membership()
    allowed_orgs = UserAuditLogCRUD.child_organizations(db, membership) + [membership.organization_id]
    subquery = db.query(models.UserAuditLog, models.Membership,
                        func.rank().over(
                            order_by=models.UserAuditLog.created_at.desc(),
                            partition_by=models.UserAuditLog.group_id).label('rnk')
                        ).join(models.Membership)
    subquery = subquery.filter(models.Membership.organization_id.in_(allowed_orgs))
    if from_date:
        subquery = subquery.filter(models.UserAuditLog.created_at >= from_date)
    if to_date:
        subquery = subquery.filter(models.UserAuditLog.created_at <= to_date)
    if user:
        subquery = subquery.filter(func.concat(models.Membership.first_name, ' ',
                                               models.Membership.last_name).ilike(f'%{user}%')
                                   )
    if group_id:
        subquery = subquery.filter(models.UserAuditLog.group_id == group_id)
    if module:
        subquery = subquery.filter(models.UserAuditLog.module.ilike(f'%{module}%'))
    if action:
        subquery = subquery.filter(models.UserAuditLog.action == action)

    subquery = subquery.subquery()
    # rnk_query = db.query(subquery) \
    #     .filter(subquery.c.rnk == 1) \
    #     .order_by(subquery.c.created_at.desc())
    max_rnk_subquery = db.query(
        subquery.c.group_id,
        func.max(subquery.c.rnk).label('max_rnk')
    ).group_by(subquery.c.group_id).subquery()

    rnk_query = db.query(subquery).join(
        max_rnk_subquery,
        (subquery.c.group_id == max_rnk_subquery.c.group_id) &
        (subquery.c.rnk == max_rnk_subquery.c.max_rnk)
    ).order_by(subquery.c.created_at.desc())
    rnk_query = paginate(rnk_query, params)
    group_ids = [rnk['group_id'] for rnk in rnk_query.items]
    query = UserAuditLogCRUD.query(db).filter(models.UserAuditLog.group_id.in_(group_ids)) \
        .order_by(models.UserAuditLog.group_id, models.UserAuditLog.created_at).all()
    grouped_module = defaultdict(list)
    grouped_data_before = defaultdict(list)
    grouped_data_after = defaultdict(list)
    grouped_remarks = defaultdict(list)
    grouped_created_at = defaultdict(list)
    for item in query:
        grouped_module[item.group_id].append(item.module)
        grouped_data_before[item.group_id].append(item.data_before)
        grouped_data_after[item.group_id].append(item.data_after)
        grouped_remarks[item.group_id].append(item.remarks)
        grouped_created_at[item.group_id].append(item.created_at)

    # Convert Row objects to dictionaries
    result = []
    for rnk in rnk_query.items:
        rnk_dict = dict(rnk)
        group_id = rnk_dict['group_id']
        rnk_dict['modules'] = grouped_module.get(group_id)
        rnk_dict['data_before'] = grouped_data_before.get(group_id)
        rnk_dict['data_after'] = grouped_data_after.get(group_id)
        rnk_dict['remarks'] = grouped_remarks.get(group_id)
        created_at = grouped_created_at.get(group_id)
        if created_at:
            rnk_dict['created_at'] = created_at[0]
        first_name = rnk_dict.get('first_name')
        last_name = rnk_dict.get('last_name')
        rnk_dict['membership'] = {'first_name': first_name, 'last_name': last_name}
        result.append(rnk_dict)
    return {'items': result, 'page': rnk_query.page, 'size': rnk_query.size,
            'total': rnk_query.total, 'pages': rnk_query.pages}


def update_preferred_payment_method(db, membership_data, member_id):
    db_membership = MembershipCRUD.update(db, member_id, membership_data.dict(), check_permission=False)
    return db_membership


def update_preferred_payment_flow(db, membership_data, member_id):
    db_membership = MembershipCRUD.update(db, member_id, membership_data.dict(), check_permission=False)
    return db_membership


def get_all_user_access_by_operator_id(db, operator_id, filters: dict):
    try:
        db_user_access = UserAccessOperatorCRUD.query(db).filter(
            models.UserAccessOperator.operator_id == operator_id
        ).join(models.Membership) \
            .join(models.User, models.Membership.user_id == models.User.id)

        if filters.get('full_name'):
            db_user_access = db_user_access.filter(
                func.concat(models.Membership.first_name, ' ',
                            models.Membership.last_name).ilike(f"%{filters.get('full_name')}%")
            )
        if filters.get('email'):
            db_user_access = db_user_access.filter(
                models.User.email.ilike(f"%{filters.get('email')}%")
            )
        if filters.get('phone_number'):
            db_user_access = db_user_access.filter(
                models.User.phone_number.ilike(
                    f"%{filters.get('phone_number')}%")
            )
        db_user_access = db_user_access.order_by(desc(models.UserAccessOperator.created_at))
        return db_user_access
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='UserAccessOperator')


def get_user_access_operator_detail(db, operator_id, user_access_id):
    try:
        db_user_access = UserAccessOperatorCRUD.query(db).filter(
            models.UserAccessOperator.id == user_access_id,
            models.UserAccessOperator.operator_id == operator_id
        ).one()
        return db_user_access
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='UserAccessOperator')


def create_user_access_operator(db, data: schema.UserAccessChargerCreate):
    try:
        data = dict(data)
        db_user_access = UserAccessOperatorCRUD.add(db, data)
        return db_user_access
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloDuplicateUserAccessOperatorError


def delete_user_access_operator(db, user_access_id):
    try:
        UserAccessOperatorCRUD.delete(db, user_access_id)
        return user_access_id
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='UserAccessOperator')


def get_all_user_phone_number(db, operator_id, phone_number, allowed_operators):
    try:
        # Avoid adding same user
        subquery = db.query(models.UserAccessOperator.membership_id) \
            .filter(models.UserAccessOperator.operator_id == operator_id,
                    models.UserAccessOperator.is_deleted.is_(False)) \
            .distinct()

        # Limit visibility to it's user only
        subquery_op = db.query(models.Operator.organization_id) \
            .filter(models.Operator.id.in_(allowed_operators),
                    models.Operator.is_deleted.is_(False)) \
            .distinct()

        db_member = MembershipCRUD.query(db, check_permission=False).join(models.User) \
            .filter(~models.Membership.id.in_(subquery), models.User.phone_number.ilike(f"%{phone_number}%"),
                    models.Membership.organization_id.in_(subquery_op))
        db_member = db_member.filter(models.Membership.membership_type == schema.MembershipType.regular_user)

        return db_member
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_membership_by_user_id(db, user_id):
    try:
        db_member = MembershipCRUD.query(db).filter(
            models.Membership.user_id == user_id
        ).one()
        return db_member
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist
