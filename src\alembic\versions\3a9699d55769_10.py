"""10

Revision ID: 3a9699d55769
Revises: ed5b9be9da8a
Create Date: 2022-09-14 12:24:40.543101

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3a9699d55769'
down_revision = 'ed5b9be9da8a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_promo_code', 'value', existing_type=sa.Numeric(), existing_nullable=False, new_column_name='amount', nullable=True, type_=sa.Numeric(scale=2))
    op.alter_column('main_promo_code', 'min_amount', existing_type=sa.NUMERIC(), existing_nullable=True, new_column_name='limit', type_=sa.Integer(), nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_promo_code', 'limit', existing_type=sa.Integer(), existing_nullable=False, new_column_name='min_amount', nullable=True, type_=sa.NUMERIC())
    op.alter_column('main_promo_code', 'amount', existing_type=sa.Numeric(scale=2), existing_nullable=True, new_column_name='value', type_=sa.Numeric(), nullable=False)
    # ### end Alembic commands ###
