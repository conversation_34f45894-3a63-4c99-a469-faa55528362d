<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link href='https://fonts.googleapis.com/css?family=Lato' rel='stylesheet'>
    <link href="https://fonts.googleapis.com/css?family=Calibri" rel='stylesheet'>
    <title>Pre Authorization</title>
    <style>
        body {
            font-family: Calibri, sans-serif;
            background-color: #eaeaea;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 700px;
            margin: 20px auto;
            padding-top: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
            background-color: white;
            overflow: hidden;
        }
        .header, .footer {
            text-align: center;
            margin-bottom: 20px;
            font-size: 14px;
            padding-top: 1.5vh;
            color: #686c80;
        }
        .footer {
            padding-top: 0.5vh;
        }
        .header img, .footer img {
            max-width: 100px;
        }
        .section {
            margin-bottom: 20px;
        }
        .section h2 {
            margin-bottom: 10px;
            font-size: 18px;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            color: #686c80;
            margin-bottom: 5px;
            font-size: 14px;
        }
        .form-group input, .form-group select {
            width: calc(100% - 20px);
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
        }
        .form-group input[type="submit"] {
            width: 75%;
            background-color: #F47920;
            color: white;
            border: none;
            cursor: pointer;
            padding: 1.5vh;
            border-radius: 50px;
            display: block;
            margin: 20px auto 0;
            font-size: 14px;
        }
        button {
            width: 75%;
            background-color: #F47920;
            color: white;
            border: none;
            cursor: pointer;
            padding: 1.5vh;
            border-radius: 50px;
            display: block;
            margin: 20px auto 0;
            font-size: 14px;
        }
        .message {
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
            font-size: 14px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }

        #logo-img {
            max-width: 55%;
        }

        #ssl-img {
            max-width: 9.5%;
        }

        #logo-text {
            font-size: 12px;
        }
        .header {
            text-align: center;
        }
        .ssl-container {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
        }
        .ssl-container img {
            vertical-align: middle;
            margin-right: 10px;
        }
        .row {
            display: flex;
            justify-content: space-between;
        }
        .row .form-group {
            flex: 1;
        }
        .row .form-group:first-child {
            margin-right: 10px;
        }
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: pointer;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px 0;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        .body-section {
            background-color: #f3f3f3;
            padding: 20px;
            border-radius: 0 0 8px 8px;
            box-sizing: border-box;
            width: 100%;
        }
        input {
            padding-left: 10%;
        }
        #number-container, #securityCode-container {
            height: 38px;
        }
        .flex-microform {
            width: calc(100% - 20px);
            padding-left: 10px;
            padding-right: 10px;
            margin: 0px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
        }
        .flex-microform-focused {
            background-color: #fff;
            border-color: #686c80;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(244, 121, 32, 1);
        }


    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <div>
            <img id="logo-img"
                 src="data:text/plain;base64,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"
                 alt="Logo">
        </div>
        <div class="ssl-container">
            <img id="ssl-img"
                 src="data:text/plain;base64,iVBORw0KGgoAAAANSUhEUgAAAE4AAABUCAMAAAAbOfHSAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA2UExURUdwTPV6IPh8JfZ7IvV6IPV6IPV6IPV7IPZ6IfV5IPV6IfV6IPZ6If+FKf+KNfl9JPZ8IvR5IGh5QfcAAAARdFJOUwDnGknWqPRegsiVvHAQByY1dhMKRAAAAplJREFUWMOtmdm2gyAMRUHmQcH//9mrol5bwiDNeeyqW4ZwEiIhHYqCcj0RFM3KrLukYD+zJiXkeotatgyPKnrB129JY1WoMpmR1DhhvdrEmFJeW2HkWhE1wurt/2x/ZP+745SKY3VD9cE34vuo7Yqmfa8MHk5vOImHE1so4NFWs0U8Ik4SohBx60w8Ji4QjYljRGDiFHGYON8RxZIn0Z445g2WjrdXLKy1MpbU3ynmT/cJ/BecztxscfVTJutnMHdVPoqjM+i2ozh/TXA+dPHcIC6k2V3Jh554P4aj6WnxDPoj647hTJrp4xeXkmUNVw6U9HB45pbsBRmOY+Js5cwO4HRl2wdwvuJ3AzhVydoDOFYyd+fjWdBND522wnRhAyOcyUxs1VcKDLAJjHHRUcVNHMyzU2nRmrx8fByqUWBf6nGq3R95yZeacpB5i3w9kw85U9BVc6s87HL7MpkvQeEKWksA7Eu0XOiO5+9ll1CQ22ZG2JRwFIoIg4VLadRi4Ri0QeO4FK8zEs6A4TiMu8Lf4+DCdZhRcOY+fAYD919t+bc4CeDCv3XlsT1X71anIwLnLjvvvFnYrDRmWel0BZJXMeeoo9cFqSl/I11KTmg73XPhRc9l8KJW9Ryc/EoIzzfxrmQRZWVGHz7AO8bHZG1CHwuxXU4amXYSwEmqpDkp9NEIgeT1l+PKuSPNvbnZ5Rrvp5iltfGvVKiPBjsqurBdY9M1S38R0xattAkH+gzVHuHrRoOqu4TD2Yau2y9wy2n2KA0m7Q2vz2d710/3Fr098SJfdJFV83zwQF4oNBZQzOSVltqE6UC7PBYHKMa6+fA1rn39K4agzraEq5++LmiKBzv2xN+n2P3+weLYFLvNGe1zyj7E2Bm1f7VgEqJ6JbgWAAAAAElFTkSuQmCC"
                 alt="ic_secure" text="This is a SSL encrypted payment">
            <span id="logo-text">This is a SSL encrypted payment</span>
        </div>
    </div>
    <div class="body-section">
        <form id="save-card-form" method="post" target="_self">
            <h2 style="margin-top: 1.0vh;">Personal Details</h2>
            <div class="form-group">
                <label for="firstName">First Name (Optional)</label>
                <input type="text" id="firstName" name="firstName" placeholder="First Name"
                       >
            </div>
            <div class="form-group">
                <label for="lastName">Last Name (Optional)</label>
                <input type="text" id="lastName" name="lastName" placeholder="Last Name"
                       >
            </div>
            <div class="form-group">
                <label for="email">Email (Optional)</label>
                <input type="email" id="email" name="email" placeholder="Email" value="{{user_information.email}}"
                       >
            </div>
            <div class="form-group">
                <label for="phoneNumber">Phone Number (Optional)</label>
                <input type="tel" id="phoneNumber" name="phoneNumber" placeholder="Phone Number"
                       >
            </div>
            <div class="section">
                <h2>Credit Card Details</h2>
                <div class="form-group">
                    <label for="cardNumber">Card Number</label>
                    <div id="number-container" class="flex-microform"></div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <label for="expMonth">Expiry month</label>
                        <select id="expMonth" class="form-control">
                            <option>01</option>
                            <option>02</option>
                            <option>03</option>
                            <option>04</option>
                            <option>05</option>
                            <option>06</option>
                            <option>07</option>
                            <option>08</option>
                            <option>09</option>
                            <option>10</option>
                            <option>11</option>
                            <option>12</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="expYear">Expiry year</label>
                        <select id="expYear" class="form-control"></select>
                    </div>
                </div>
                <div class="form-group tooltip">
                    <label for="securityCode-container">CCV</label>
                    <div id="securityCode-container" class="flex-microform"></div>
                    <span class="tooltiptext">Security code on your card</span>
                </div>

            </div>

            <div id="errors-output" class="error"></div>

            <div class="form-group">
                <button type="button" id="pay-button" class="btn btn-primary">Pay</button>
                <input type="hidden" id="flexresponse" name="flexresponse">
                <div class="footer">
                    <p>By clicking the button you confirm that you have read and agreed to our <a
                            href="https://www.comfortdelgro.com/sustainablity/policies/" style="color:#F47920">Privacy
                        Policy</a></p>
                </div>
                <input type="hidden" id="member_id" name="member_id" value="{{user_information.member_id}}">
                <input type="hidden" id="session_token" name="session_token" value="{{session_token}}">
            </div>
        </form>
    </div>
</div>

{% if client_library_integrity %}
    <script src="{{ client_library_url }}" integrity="{{ client_library_integrity }}" crossorigin="anonymous"></script>
{% else %}
    <script src="{{ client_library_url }}"></script>
{% endif %}

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const captureContext = "{{ capture_context|safe }}";

        var form = document.querySelector('#save-card-form');
        var payButton = document.querySelector('#pay-button');
        var flexResponse = document.querySelector('#flexresponse');
        var expMonth = document.querySelector('#expMonth');
        var expYear = document.querySelector('#expYear');
        var errorsOutput = document.querySelector('#errors-output');

        var currentPath = window.location.pathname;
        var newPath = currentPath.substring(0, currentPath.lastIndexOf('/')) + '/pre-auth-cc';
        form.action = newPath;

        var myStyles = {
            'input': {
                'font-size': '14px',
                'font-family': 'helvetica, tahoma, calibri, sans-serif',
                'color': '#555'
            },
            ':focus': { 'color': '#686c80' },
            ':disabled': { 'cursor': 'not-allowed' },
            'valid': { 'color': '#3c763d' },
            'invalid': { 'color': '#a94442' }
        };

        var flex = new Flex(captureContext);
        var microform = flex.microform({ styles: myStyles });
        var number = microform.createField('number', { placeholder: 'Enter card number' });
        var securityCode = microform.createField('securityCode', { placeholder: '•••' });

        number.load('#number-container');
        securityCode.load('#securityCode-container');

        payButton.addEventListener('click', function() {
            var options = {
                expirationMonth: document.querySelector('#expMonth').value,
                expirationYear: document.querySelector('#expYear').value
            };

            microform.createToken(options, function (err, token) {
                if (err) {
                    console.error('Token creation error:', err);
                    errorsOutput.textContent = err.message;
                } else {
                    console.log('Token created:', token);
                    flexResponse.value = JSON.stringify(token);
                    form.target = '_self';
                    form.submit();
                }
            });
        });

        var currentYear = new Date().getFullYear();
        var currentMonth = ("0" + (new Date().getMonth() + 1)).slice(-2);

        // Populate the options with the next 10 years
        var selectYear = document.getElementById("expYear");
        for (var i = 0; i < 10; i++) {
            var option = document.createElement("option");
            option.text = currentYear + i;
            selectYear.add(option);
        }

        // Set the current month as the default selected option
        var selectMonth = document.getElementById("expMonth");
        selectMonth.value = currentMonth;
    });


</script>
</body>
</html>
