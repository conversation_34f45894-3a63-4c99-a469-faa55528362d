"""181

Revision ID: 1a48f9e5370d
Revises: e7663f12b887
Create Date: 2025-07-01 16:54:03.335103

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1a48f9e5370d'
down_revision = 'e7663f12b887'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_pre_auth_info', sa.Column('charger_type', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_pre_auth_info', 'charger_type')
    # ### end Alembic commands ###
