"""155

Revision ID: 166dee06933a
Revises: f3f188fde3f8
Create Date: 2025-03-18 07:48:10.342740

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '166dee06933a'
down_revision = 'f3f188fde3f8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_subscription_ocpi_custom_plan',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('amount', sa.Numeric(scale=5), nullable=True),
    sa.Column('connector_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('subscription_plan_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('evse_id', sa.String(), nullable=True),
    sa.Column('evse_number', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['subscription_plan_id'], ['main_subscription_plan.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_subscription_ocpi_custom_plan')
    # ### end Alembic commands ###
