"""133

Revision ID: fda37e1cffc6
Revises: 37f9cb0958c4
Create Date: 2024-11-14 18:19:00.623510

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fda37e1cffc6'
down_revision = '37f9cb0958c4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_token_check_pre_auth_payment',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('transaction_type', sa.String(), nullable=True),
    sa.Column('transaction_channel', sa.String(), nullable=True),
    sa.Column('transaction_id', sa.String(), nullable=True),
    sa.Column('reference_id', sa.String(), nullable=True),
    sa.Column('expired_date', sa.String(), nullable=True),
    sa.Column('token', sa.String(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('amount', sa.String(), nullable=True),
    sa.Column('bill_name', sa.String(), nullable=True),
    sa.Column('bill_email', sa.String(), nullable=True),
    sa.Column('bill_mobile', sa.String(), nullable=True),
    sa.Column('bill_desc', sa.String(), nullable=True),
    sa.Column('response', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('callback_response', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('payment_request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('is_3ds', sa.Boolean(), nullable=True),
    sa.Column('is_successful', sa.Boolean(), nullable=True),
    sa.Column('is_binded', sa.Boolean(), nullable=True),
    sa.Column('is_used', sa.Boolean(), nullable=True),
    sa.Column('is_refunded', sa.Boolean(), nullable=True),
    sa.Column('failed_refund', sa.Boolean(), nullable=True),
    sa.Column('used_amount', sa.String(), nullable=True),
    sa.Column('payment_type', sa.String(), nullable=True),
    sa.Column('charging_session_id', sa.String(), nullable=True),
    sa.Column('payment_gateway', sa.String(), nullable=True),
    sa.Column('stat_code', sa.String(), nullable=True),
    sa.Column('error_description', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['payment_request_id'], ['main_payment_request.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_token_check_pre_auth_payment_refund',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('transaction_id', sa.String(), nullable=True),
    sa.Column('domain', sa.String(), nullable=True),
    sa.Column('response', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('stat_code', sa.String(), nullable=True),
    sa.Column('token_check_pre_auth_payment_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['token_check_pre_auth_payment_id'], ['main_token_check_pre_auth_payment.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_token_check_pre_auth_payment_refund')
    op.drop_table('main_token_check_pre_auth_payment')
    # ### end Alembic commands ###
