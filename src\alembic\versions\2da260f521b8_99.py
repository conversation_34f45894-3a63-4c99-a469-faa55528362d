"""99

Revision ID: 2da260f521b8
Revises: 1b4fffcea4de
Create Date: 2024-05-30 17:33:50.698388

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2da260f521b8'
down_revision = '1b4fffcea4de'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_pre_auth_payment', sa.Column('payment_gateway', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_pre_auth_payment', 'payment_gateway')
    # ### end Alembic commands ###
