from contextlib import contextmanager
import pytest

from faker import Faker
from fastapi.testclient import TestClient

from app.constants import AUTHORIZATION_METHOD_ERRORS, AUTHORIZATION_METHOD_SUCCESS_MSG
from app.main import app, ROOT_PATH
from app.models import Membership
from app.tests.factories import (UserFactory, VerificationTokenFactory, OrganizationFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory,
                                 RoleFactory, )
from app.database import SessionLoc<PERSON>, create_session, Base, engine
from app.schema import MembershipType


fake = Faker()
client = TestClient(app)

SUCCESS_MSG = AUTHORIZATION_METHOD_SUCCESS_MSG
ERRORS = AUTHORIZATION_METHOD_ERRORS


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VerificationTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


def create_user_with_auth_token(include_user_id=False, include_membership_id=False, include_organization_id=False):
    data = {}

    signin_data = {
        'email': '',
        'password': 'password',
    }
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=str(organization.id)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        if include_user_id:
            data['user_id'] = str(user.id)

        if include_membership_id:
            data['membership_id'] = str(membership.id)

        if include_organization_id:
            data['organization_id'] = str(organization.id)

        signin_data['email'] = str(user.email)
        signin_data['organization_id'] = str(organization.id)

    url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
    response = client.post(url, json=signin_data)

    data['auth_token'] = response.json()['auth_token']

    return data


def test_auth_user_use_endpoint_requiring_resource_without_proper_role_fails(test_db):
    data = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = data.pop('organization_id')
    data.pop('user_id')
    membership_id = data.pop('membership_id')

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/?',
            scope='get,update,post',
        )
        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)

        # DO NOT ATTACH RESOURCE TO ROLE FOR THIS TEST
        # role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role'
    response = client.get(url, headers={'authorization': data['auth_token']})

    assert response.status_code == 403


def test_auth_user_use_endpoint_requiring_resource_with_proper_role_succeeds(test_db):
    data = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = data.pop('organization_id')
    data.pop('user_id')
    membership_id = data.pop('membership_id')

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/?',
            scope='get,update,post',
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role'
    response = client.get(url, headers={'authorization': data['auth_token']})

    assert response.status_code == 200
