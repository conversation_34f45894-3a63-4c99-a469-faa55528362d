# pylint:disable=too-many-lines

# import json
import json
import logging
import re
import urllib

from fastapi import APIRouter, Depends, Request, HTTPException, BackgroundTasks, Security
from fastapi.responses import JSONResponse
from fastapi.security.api_key import APIKeyHeader
from sqlalchemy.exc import NoResultFound

from app import schema, settings, models, cybersource_payment_utils as cyber_utils, crud
from app.crud import get_or_create_ocpi_app_token, ExternalOrganizationCRUD, ExternalTokenCRUD, \
    get_pre_auth_payment_by_id, get_preauth_by_id, update_wallet_balance, \
    get_non_binded_pre_auth_by_member_id_with_payment_type, get_or_create_pre_auth_info_cc, get_member_primary_cc, \
    get_token_check_pre_auth_payment_by_id, get_membership_by_id, get_or_create_partial_pre_auth_info_cc, \
    update_preferred_payment_flow, get_user_by_id
from app.database import create_session, SessionLocal
from app.middlewares import set_admin_as_context_user
from app.permissions import x_api_key
from app.ruby_proxy_utils import (
    validate_platform_type, billing_request_ruby_proxy,
    # start_charging_ruby_proxy, stop_charging_ruby_proxy, current_session_ruby_proxy, get_stop_charging_response,
    update_foreign_session_proxy,
    update_ocpp_status_ruby_proxy, start_transaction_ruby_proxy,
)
from app.schema import OCPITokenResponse, ExternalToken, PreAuthPaymentResponseToMobile, PreAuthRequest, \
    StartPreAuthRequest
from app.schema.v2 import BillingRequestRuby, ForeignSessionUpdate, TransactionDataRuby, RubyConnectorStatusUpdate, \
    RubyBillingUpdate, PreferredPaymentFlowEnums, UpdatePreferredPaymentFlow
from app.settings import MY_PRE_AUTH_MAX_AMOUNT, SG_PRE_AUTH_MAX_AMOUNT, BN_PRE_AUTH_MAX_AMOUNT, KH_PRE_AUTH_MAX_AMOUNT, \
    ENABLE_MOBILE_OCPI_DISCOUNT_API
from app.utils import (
    apply_subscription_discount_connector,
    decode_auth_token_from_headers,
    process_cybersource_pre_auth_payment_response,
    send_request, generate_charger_header, PreChargingFlow, RouteErrorHandlerProxy, OCPIPreChargingFlow,
    map_charging_session_id_tag_with_member,
    get_charge_history_record,
    generate_charger_header_unauthorized, decode_base64_to_token, post_ocpi_token_to_party, calculate_tax_amount,
    make_pre_auth_payment, create_pre_auth_payment_request,
    process_pre_auth_payment_response, get_pre_auth_url_and_value_from_object,
    send_pre_auth_request_to_pg_gateway, get_wallet_by_member_id_currency, create_wallet_pre_auth_payment_request,
    make_pre_auth_payment_wallet, process_ocpi_command_module, map_pg_error_to_friendly_message, handle_wallet_pre_auth,
    handle_credit_card_pre_auth, safe_get, charging_history_merge_operator_details,
    handle_credit_card_token_check_via_pre_auth, perform_token_check_pre_auth_refund, check_if_low_wallet_balance,
    check_if_low_wallet_balance_by_membership_id
)

# from datetime import datetime

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix=f"/{settings.MAIN_ROOT_PATH}/api/v1/charger",
    tags=['v2 charging', ],
    dependencies=[Depends(x_api_key)],
    route_class=RouteErrorHandlerProxy
)

OCPI_URL = f'{settings.MAIN_OCPI_DOMAIN}/{settings.OCPI_PREFIX}'

CHARGER_URL_PREFIX_V2 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'
CHARGER_URL_PREFIX_V1 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'

API_KEY_NAME = "x-api-key"
SID_KEY_NAME = "x-api-sid"

api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)
api_sid_header = APIKeyHeader(name=SID_KEY_NAME, auto_error=False)

unauthorized_path = [
    'charge_point/serial/',
    'connector/charge_points/',
]


async def check_if_session_free(dbsession, pre_charging_flow, membership_id) -> bool:
    # Assuming the pre_charging_flow is already initiated with necessary details.
    return pre_charging_flow.is_charging_free(dbsession, membership_id)


async def log_activity(user_id: str, table_name: str, data: dict,
                       log_type: schema.ActivityLogType, dbsession: SessionLocal = Depends(create_session)):
    db_activity = models.ActivityLog(user_id=user_id, table_name=table_name, data=data, type=log_type)
    dbsession.add(db_activity)
    dbsession.commit()


@router.post('/start-ocpi-charging/{connector_id}')
async def start_ocpi_charging(request: Request, connector_id: str,  # noqa
                              dbsession: SessionLocal = Depends(create_session)):
    """
    Start charging based on connector, this is for OCPI API.
    Ideally, is only meant for App User's EMSP external charging.
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)

    membership_id = auth_token_data.get('membership_id')

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    headers = generate_charger_header(dbsession, membership_id)
    ocpi_token = get_or_create_ocpi_app_token(dbsession, membership_id)

    pre_charging_flow = OCPIPreChargingFlow(request_headers=headers, connector_id=connector_id)
    await pre_charging_flow.ocpi_start_flow(dbsession, membership_id)

    path = f'ocpi/emsp/{connector_id}/remote-start'
    url = f'{CHARGER_URL_PREFIX_V1}/{path}'
    ocpi_command_url = f'{OCPI_URL}/emsp/2.2.1/commands/'

    data = {
        'ocpi_token_id': str(ocpi_token.id),
        'command': "START_SESSION",
        'response_url': ocpi_command_url,
        'status': "PENDING"
    }
    try:
        response_emsp = await send_request('POST', url, data=json.dumps(data), headers=headers)
    except Exception as e:
        logger.info('There is an error while requesting partner command with start-command, error: %s:', str(e))
        raise HTTPException(status_code=403, detail='Partner initialization failed.')

    if response_emsp.status_code == 200:
        ocpi_token_dict = OCPITokenResponse.from_orm(ocpi_token).dict(exclude_unset=True, exclude_none=True)
        ocpi_token_dict['uid'] = str(ocpi_token_dict.pop('id'))
        try:
            ocpi_token_dict['last_updated'] = str(ocpi_token_dict.pop('updated_at'))
        except KeyError:
            ocpi_token_dict['last_updated'] = str(ocpi_token_dict.pop('created_at'))
        ocpi_token_dict.pop('member_id')
        response_emsp_json = response_emsp.json()
        operator_id = response_emsp_json['cp']['operator_id']
        command_data = {
            'response_url': response_emsp_json['command']['response_url'],
            'token': ocpi_token_dict,
            'location_id': str(response_emsp_json['location']['ocpi_partner_location_id']),
            'evse_uid': str(response_emsp_json['cp']['ocpi_partner_evse_uid']),
            'connector_id': str(response_emsp_json['connector']['ocpi_partner_connector_id']),
        }

        external_org = ExternalOrganizationCRUD.query(dbsession).filter(
            models.ExternalOrganization.id == operator_id).one()

        external_auths = ExternalTokenCRUD.query(dbsession).filter(
            models.ExternalToken.external_organization_id == external_org.id).one()
        external_auths = ExternalToken.from_orm(external_auths).dict()

        end_points = json.loads(external_auths['endpoints'])

        await post_ocpi_token_to_party(ocpi_token_dict, end_points, external_auths)
        for endpoint in end_points['endpoints']:
            if endpoint['role'] == 'RECEIVER' and endpoint['identifier'] == 'commands':
                receiver_command_url = endpoint['url']

        if receiver_command_url.endswith('/'):
            receiver_command_url += 'START_SESSION'
        else:
            receiver_command_url += '/START_SESSION'
        external_service_token = decode_base64_to_token(external_auths['external_service_token'])
        authorization_token = f'Token {external_service_token}'
        ocpi_header = {'Authorization': authorization_token, 'Content-Type': 'application/json'}

        logger.info('start_session as emsp data %s', str(json.dumps(command_data)))
        try:
            response_command = await send_request('POST', receiver_command_url, data=json.dumps(command_data),
                                                  headers=ocpi_header)
            response_command_json = response_command.json()
            logger.info('OCPI EMSP remote-start command response: %s', response_command_json)
        except Exception as e:
            logger.info('There is an error while requesting partner command with start-command, error: %s:', str(e))
            raise HTTPException(status_code=403, detail='Partner initialization failed.')

        command_id = response_emsp_json['command']['id']
        path = f'ocpi/command/{command_id}'
        url = f'{CHARGER_URL_PREFIX_V1}/{path}'

        result_data = {
            'response_data': response_command.json()['data']
        }
        response_command = await send_request('PUT', url, data=json.dumps(result_data),
                                              headers=ocpi_header)

        if response_command_json.get('data'):
            if response_command_json.get('data').get('result') != 'ACCEPTED':
                raise HTTPException(status_code=403, detail='Partner initialization failed.')
            response_status = 'ACCEPTED'
        else:
            raise HTTPException(status_code=403, detail='Partner initialization failed.')

        logger.info('ocpi main start-charging info: %s', response_command.json())

        response_command_json = {
            'id': response_emsp_json['command']['id'],
            'created_at': response_emsp_json['operation_request']['created_at'],
            'requester_type': response_emsp_json['operation_request']['requester_type'],
            'requester_id': response_emsp_json['operation_request']['requester_id'],
            'operation': response_emsp_json['operation_request']['operation'],
            'charge_point_id': response_emsp_json['operation_request']['charge_point_id'],
            'connector_id:': response_emsp_json['operation_request']['connector_id'],
            'status': str(response_status).capitalize()
        }

    return JSONResponse(response_command_json, status_code=response_command.status_code)


@router.post('/stop-ocpi-charging/{transaction_id}')
async def stop_ocpi_charging(request: Request, transaction_id: str,  # noqa
                             dbsession: SessionLocal = Depends(create_session)):
    """
    Start charging based on connector, this is for OCPI API.
    Ideally, is only meant for App User's EMSP external charging.

    """
    auth_token_data = decode_auth_token_from_headers(request.headers)

    membership_id = auth_token_data.get('membership_id')

    headers = generate_charger_header(dbsession, membership_id)
    ocpi_token = get_or_create_ocpi_app_token(dbsession, membership_id)

    # To add pre-charging flow.

    path = f'ocpi/emsp/{transaction_id}/remote-stop'
    url = f'{CHARGER_URL_PREFIX_V1}/{path}'
    ocpi_command_url = f'{OCPI_URL}/emsp/2.2.1/commands/'

    data = {
        'ocpi_token_id': str(ocpi_token.id),
        'command': "STOP_SESSION",
        'response_url': ocpi_command_url,
        'status': "PENDING"
    }
    response_emsp = await send_request('POST', url, data=json.dumps(data), headers=headers)
    if response_emsp.status_code == 200:
        ocpi_token_dict = OCPITokenResponse.from_orm(ocpi_token).dict(exclude_unset=True, exclude_none=True)
        ocpi_token_dict['uid'] = str(ocpi_token_dict.pop('id'))
        ocpi_token_dict['party_id'] = str(ocpi_token_dict.pop('party_id')).upper()
        ocpi_token_dict['country_code'] = str(ocpi_token_dict.pop('country_code')).upper()
        try:
            ocpi_token_dict['last_updated'] = str(ocpi_token_dict.pop('updated_at'))
        except KeyError:
            ocpi_token_dict['last_updated'] = str(ocpi_token_dict.pop('created_at'))
        ocpi_token_dict.pop('member_id')
        response_emsp_json = response_emsp.json()
        operator_id = response_emsp_json['cp']['operator_id']
        command_data = {
            'response_url': response_emsp_json['command']['response_url'],
            'token': ocpi_token_dict,
            'location_id': str(response_emsp_json['location']['ocpi_partner_location_id']),
            'evse_uid': str(response_emsp_json['cp']['ocpi_partner_evse_uid']),
            'connector_id': str(response_emsp_json['connector']['ocpi_partner_connector_id']),
            'session_id': str(response_emsp_json['session']['partner_session_uid'])
        }

        external_org = ExternalOrganizationCRUD.query(dbsession).filter(
            models.ExternalOrganization.id == operator_id).one()

        external_auths = ExternalTokenCRUD.query(dbsession).filter(
            models.ExternalToken.external_organization_id == external_org.id).one()
        external_auths = ExternalToken.from_orm(external_auths).dict()

        end_points = json.loads(external_auths['endpoints'])
        for endpoint in end_points['endpoints']:
            if endpoint['role'] == 'RECEIVER' and endpoint['identifier'] == 'commands':
                receiver_command_url = endpoint['url']

        if receiver_command_url.endswith('/'):
            receiver_command_url += 'STOP_SESSION'
        else:
            receiver_command_url += '/STOP_SESSION'
        external_service_token = decode_base64_to_token(external_auths['external_service_token'])
        authorization_token = f'Token {external_service_token}'
        ocpi_header = {'Authorization': authorization_token, 'Content-Type': 'application/json'}

        try:
            response_receiver = await send_request('POST', receiver_command_url, data=json.dumps(command_data),
                                                   headers=ocpi_header)
        except Exception as e:
            logger.info('There is an error while requesting partner command with stop charging command, error: %s:',
                        str(e))
            raise HTTPException(status_code=403, detail='Partner initialization failed.')

        response_receiver_json = response_receiver.json()
        command_id = response_emsp_json['command']['id']
        path = f'ocpi/command/{command_id}'
        url = f'{CHARGER_URL_PREFIX_V1}/{path}'

        result_data = {
            'response_data': response_receiver_json,
            'operation_request': response_emsp_json['operation_request']
        }
        response_command = await send_request('PUT', url, data=json.dumps(result_data),
                                              headers=ocpi_header)

        if response_receiver_json.get('data'):
            if response_receiver_json.get('data').get('result') == 'REJECTED':
                if response_receiver_json.get('data').get('message') and len(
                        response_receiver_json.get('data').get('message')) >= 1:
                    try:
                        message = response_receiver_json.get('data').get('message')[0]
                        message = message.get('text')
                        raise HTTPException(status_code=403,
                                            detail='Partner rejected for the following reason: ' + message)
                    except (KeyError, ValueError):  # pylint: disable=broad-except
                        raise HTTPException(status_code=403, detail='Partner initialization failed.')


                else:
                    raise HTTPException(status_code=403, detail='Partner initialization failed.')
            if response_receiver_json.get('data').get('result') != 'ACCEPTED':
                raise HTTPException(status_code=403, detail='Partner initialization failed.')
            # response_status = 'REJECTED'
        else:
            # response_status = 'REJECTED'
            raise HTTPException(status_code=403, detail='Partner initialization failed.')

        logger.info('OCPI main stop-charging info: %s', response_command.json())

        response_command_json = {
            'id': response_emsp_json['operation_request']['id'],
            'created_at': response_emsp_json['operation_request']['created_at'],
            'requester_type': response_emsp_json['operation_request']['requester_type'],
            'requester_id': response_emsp_json['operation_request']['requester_id'],
            'operation': response_emsp_json['operation_request']['operation'],
            'charge_point_id': response_emsp_json['operation_request']['charge_point_id'],
            'connector_id:': response_emsp_json['operation_request']['connector_id'],
            'status': response_emsp_json['operation_request']['status']
        }

    return JSONResponse(response_command_json, status_code=response_command.status_code)


@router.post('/start-charging/{connector_id}')
async def start_charging_proxy(request: Request, connector_id: str,
                               dbsession: SessionLocal = Depends(create_session)):
    """
    Start charging based on connector, this is a proxy to the charger API but with additional addons
    For instance: charging flow, which check connector avaiablility, connector reservation, user id, permission
    and etc.

    :param str connector_id
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)

    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    data = await request.body()
    query_params = request.query_params

    pre_charging_flow = PreChargingFlow(request_headers=headers, connector_id=connector_id)
    await pre_charging_flow.start_flow(dbsession, membership_id)

    if settings.ENABLE_TOKEN_CHECK_ON_START_CHARGING:
        connector_currency = pre_charging_flow._connector['billing_currency']
        if connector_currency in settings.TOKEN_CHECK_CURRENCY_LIST:
            use_3ds = False
            pre_auth_amount = 1.01
            if not pre_charging_flow._check_charging_fee(dbsession, membership_id):
                no_3ds_message = 'Sorry, the card was declined, please try again or change the card.'
                db_credit_card = get_member_primary_cc(dbsession, membership_id,
                                                       connector_currency)
                if not db_credit_card:
                    db_credit_card = get_member_primary_cc(dbsession, membership_id)
                    if db_credit_card:
                        if db_credit_card.payment_gateway != schema.CreditCardPaymentGateway.cybersource:
                            raise HTTPException(status_code=422,
                                                detail=f'EC: {422}. {str(no_3ds_message)}')
                    else:
                        raise HTTPException(status_code=422,
                                            detail=f'EC: {422}. {str(no_3ds_message)}')
                processed_response_code, db_token_check_pre_auth_payment = handle_credit_card_token_check_via_pre_auth(
                    request,
                    dbsession,
                    membership_id,
                    use_3ds,
                    pre_auth_amount,
                    connector_currency
                )
                if processed_response_code != 200:
                    no_3ds_message = 'Sorry, the card was declined, please try again or change the card.'
                    # If its 301 (Internally redirect to 3DS), and the PG is Fiuu,
                    # we reject them as requirement are to only perform
                    # Non-3DS Flow.
                    if processed_response_code == 301:
                        db_credit_card = get_member_primary_cc(dbsession, membership_id, connector_currency)
                        if not db_credit_card:
                            db_credit_card = get_member_primary_cc(dbsession, membership_id)
                            if db_credit_card.payment_gateway != schema.CreditCardPaymentGateway.cybersource:
                                raise HTTPException(status_code=422,
                                                    detail=f'EC: {processed_response_code}. {str(no_3ds_message)}')
                        else:
                            if db_credit_card.payment_gateway != schema.CreditCardPaymentGateway.cybersource:
                                raise HTTPException(status_code=422,
                                                    detail=f'EC: {processed_response_code}. {str(no_3ds_message)}')
                    else:
                        db_token_check_pre_auth = get_token_check_pre_auth_payment_by_id(dbsession,
                                                                                         db_token_check_pre_auth_payment.id)
                        if db_token_check_pre_auth.error_description is not None:
                            mapped_error_message = map_pg_error_to_friendly_message(
                                str(db_token_check_pre_auth.error_description),
                                str(processed_response_code))
                        else:
                            mapped_error_message = map_pg_error_to_friendly_message(str(processed_response_code),
                                                                                    str(processed_response_code))
                        if mapped_error_message == '':
                            raise HTTPException(status_code=422, detail=str(
                                f'EC: {processed_response_code}. Transaction failed by card processor. '
                                f'Try another card or contact your bank for more information.'))
                        else:
                            raise HTTPException(status_code=422,
                                                detail=str(f'EC: {processed_response_code}. {mapped_error_message}'))

                else:
                    try:
                        perform_token_check_pre_auth_refund(dbsession, db_token_check_pre_auth_payment)
                    except Exception as exc:  # pylint: disable=broad-except
                        logger.error('Error refunding db_token_check_pre_auth_payment id: %s, with error as %s',
                                     str(db_token_check_pre_auth_payment.id), str(exc))

    # db_cp = await validate_platform_type(request, dbsession, url_path=request.url.path, path_id=connector_id,
    #                                      headers=headers)
    # if db_cp['cp_platform'] == 'Ruby':
    #     for connector in db_cp['connectors']:
    #         if connector['id'] == connector_id:
    #             connect_number = connector['number']
    #
    #     result = await start_charging_ruby_proxy(dbsession, str(membership_id), db_cp['serial_number'],
    #                                              connect_number,
    #                                              str(connector_id), str(db_cp['id']), headers)
    #
    #     return result

    # start charging
    # calling v1 to avoid the issue of v2 not working / not updated
    path = f'connector/{connector_id}/remote-start'
    url = f'{CHARGER_URL_PREFIX_V1}/{path}'
    response = await send_request('POST', url, data=data, headers=headers, query_params=query_params, timeout=9)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/reservation/reserve/{connector_id}')
async def reserve_connector(request: Request, connector_id: str,
                            dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    data = await request.body()
    query_params = request.query_params
    dict_params = dict(query_params)
    dict_params['is_mobile_api'] = True
    pre_charging_flow = PreChargingFlow(request_headers=headers, connector_id=connector_id)
    await pre_charging_flow.start_flow(dbsession, membership_id)
    await pre_charging_flow.check_if_user_do_have_active_reservation()

    # start charging
    path = f'connector/{connector_id}/reserve'
    url = f'{CHARGER_URL_PREFIX_V1}/{path}'
    response = await send_request('POST', url, data=data, headers=headers, query_params=dict_params)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/reservation/cancel-reservation/{reservation_id}')
async def request_cancel_reservation(request: Request, reservation_id: str,
                                     dbsession: SessionLocal = Depends(create_session)):
    """
    To trigger cancel reservation based on reservation id (running number), it will be triggering charger server
    and charger service are to trigger background service (rabbitmq)

    :param str reservation_id
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)

    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    logger.debug('header for request to charger service: %s', headers)
    url = f'{CHARGER_URL_PREFIX_V1}/connector/cancel_reservation/{reservation_id}'
    method = request.method
    query_params = request.query_params
    data = await request.body()

    # stop charging
    # calling v1 to avoid the issue of v2 not working / not updated
    response = await send_request(method, url, data=data, headers=headers, query_params=query_params, timeout=9)
    return JSONResponse(response.json(), status_code=response.status_code)


async def pre_charging_flow(dbsession, membership_id, connector_id, use_wallet, is_ocpi, headers,
                            require_cc_check: bool = True):
    if not is_ocpi:
        flow = PreChargingFlow(request_headers=headers, connector_id=connector_id, use_wallet=use_wallet,
                               validate_cc=require_cc_check)
        await flow.start_flow(dbsession, membership_id)
        if await check_if_session_free(dbsession, flow, membership_id):
            return schema.BasicMessage(detail='Free session, can start charging.')
    else:
        flow = OCPIPreChargingFlow(request_headers=headers, connector_id=connector_id, use_wallet=use_wallet,
                                   validate_cc=require_cc_check)
        await flow.ocpi_start_flow(dbsession, membership_id)
        free_session_due_to_subscription = flow.check_charging_free_due_to_subscription(dbsession, membership_id)
        if free_session_due_to_subscription is True:
            return schema.BasicMessage(detail='Free session, can start charging.')

    return flow


@router.post('/pre-auth/{connector_id}')
# pylint:disable-all # noqa
async def perform_pre_auth(request: Request, connector_id: str, filters: PreAuthRequest,
                           dbsession: SessionLocal = Depends(create_session)):
    # WARNING: Please do note that alot of thing are shared with id-tag check (request_authorization in tasks.py).
    # Please confirm thing are working!
    use_3ds, is_ocpi, use_wallet = filters.use_3ds, filters.is_ocpi, filters.use_wallet
    payment_type = 'Wallet' if use_wallet else 'Credit-Card'
    guest_mode = False
    if settings.PRE_AUTH_FORCE_3DS:
        use_3ds = True

    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')
    db_member = get_membership_by_id(dbsession, membership_id)
    db_user = get_user_by_id(dbsession, db_member.user_id)
    require_cc_check = True
    # To cater for where we use guest checkout everytime we charge
    # We do not have CC info
    if settings.GUEST_PRE_AUTH_3DS_EVERY_CHARGE:
        if db_user.is_guest:
            require_cc_check = False
            guest_mode = True
            use_3ds = True

    pre_charging_flow_response = await pre_charging_flow(dbsession, membership_id, connector_id, use_wallet, is_ocpi,
                                                         headers, require_cc_check=require_cc_check)
    # If something is wrong (Pre-charging flow failed, continue to return the message.
    # Example:
    # (a) Free-charging, no need pre-auth
    # (b) Connector not available / CP not connected
    if isinstance(pre_charging_flow_response, schema.BasicMessage):
        return pre_charging_flow_response

    connector_currency = pre_charging_flow_response.get_connector()['billing_currency']
    connector_fee = pre_charging_flow_response.get_connector()['billing_unit_fee']
    db_pre_auth_info = get_or_create_pre_auth_info_cc(dbsession, membership_id, connector_currency)
    pre_auth_amount = db_pre_auth_info.pre_auth_amount

    # Check if the wallet is not used and if the pre-auth amount is invalid
    if not use_wallet:
        raise_exception = False
        # Condition 1: pre_auth_amount is not provided or is 10.0 or less
        # if pre_auth_amount is None or pre_auth_amount <= 10.0:
        #     raise_exception = True
        # Condition 2: Currency is MYR and pre_auth_amount is greater than MY_PRE_AUTH_MAX_AMOUNT
        if connector_currency == schema.Currency.myr and pre_auth_amount > MY_PRE_AUTH_MAX_AMOUNT:
            raise_exception = True
        # Condition 3: Currency is SGD and pre_auth_amount is greater than SG_PRE_AUTH_MAX_AMOUNT
        if connector_currency == schema.Currency.sgd and pre_auth_amount > SG_PRE_AUTH_MAX_AMOUNT:
            raise_exception = True
        # Condition 4: Currency is BND and pre_auth_amount is greater than BN_PRE_AUTH_MAX_AMOUNT
        if connector_currency == schema.Currency.bnd and pre_auth_amount > BN_PRE_AUTH_MAX_AMOUNT:
            raise_exception = True
        # Condition 5: Currency is KHR and pre_auth_amount is greater than KH PRE_AUTH_MAX_AMOUNT
        if connector_currency == schema.Currency.khr and pre_auth_amount > KH_PRE_AUTH_MAX_AMOUNT:
            raise_exception = True
        if raise_exception:
            raise HTTPException(status_code=401, detail='No pre-auth amount found or amount is over the allowed limit.')

    if use_wallet:
        # We always perform wallet value update as its a function that re-compute all the matching record for user.
        db_wallet = get_wallet_by_member_id_currency(dbsession, membership_id, connector_currency)
        db_wallet = update_wallet_balance(dbsession, db_wallet.id)

        # If the amount on the wallet balance is less than connector fee, for example, 1KwH at 1.00,
        # but you only have 0.99, you will not be able to start
        if float(db_wallet.balance) < round(float(connector_fee), 2) or float(db_wallet.balance) <= 0.1:
            raise HTTPException(status_code=422,
                                detail='Wallet balance is insufficient. Please use a credit card instead')
        pre_auth_amount = float(db_wallet.balance)

    # If there is old pre-auth that is valid (successful) pre-auth, and not binded to any charging session,
    # Return directly, else, continue to perform pre-auth

    # If use wallet, we check if its redirect to cc as partial will redirect to cc
    if use_wallet:
        db_member = get_membership_by_id(dbsession, membership_id)
        if settings.ENABLE_PARTIAL_PAYMENT_FLOW:
            is_low_wallet_balance = check_if_low_wallet_balance_by_membership_id(dbsession, membership_id,
                                                                                 connector_currency)
            if is_low_wallet_balance:
                if (db_member.preferred_payment_flow == PreferredPaymentFlowEnums.partial or
                        db_member.preferred_payment_flow == PreferredPaymentFlowEnums.credit_card):
                    # use_wallet = False
                    payment_type = 'Credit-Card'

    old_pre_auth_payment = get_non_binded_pre_auth_by_member_id_with_payment_type(dbsession, membership_id,
                                                                                  connector_currency, payment_type)
    if old_pre_auth_payment:
        return PreAuthPaymentResponseToMobile.from_orm(old_pre_auth_payment)

    # If perform wallet transaction, continue to perform Wallet's pre-auth, basically just create pre-auth value.
    if use_wallet:
        db_member = get_membership_by_id(dbsession, membership_id)
        # If it is wallet transaction and partial payment is enabled, we check and direct to cc if partial or
        # cc is enabled
        if settings.ENABLE_PARTIAL_PAYMENT_FLOW:
            is_low_wallet_balance = check_if_low_wallet_balance_by_membership_id(dbsession, membership_id,
                                                                                 connector_currency)
            if is_low_wallet_balance:
                if settings.CHANGE_PARTIAL_FLOW_BY_DEFAULT:
                    if settings.PAYMENT_GATEWAY_TYPE == 'CyberSource':
                        db_credit_card = get_member_primary_cc(dbsession, membership_id)
                    else:
                        db_credit_card = get_member_primary_cc(dbsession, membership_id, connector_currency)
                    if db_credit_card:
                        update_data = UpdatePreferredPaymentFlow(
                            preferred_payment_flow=PreferredPaymentFlowEnums.partial)
                        db_member = update_preferred_payment_flow(dbsession, update_data, membership_id)
                    else:
                        update_data = UpdatePreferredPaymentFlow(
                            preferred_payment_flow=PreferredPaymentFlowEnums.wallet)
                        db_member = update_preferred_payment_flow(dbsession, update_data, membership_id)

                if (db_member.preferred_payment_flow == PreferredPaymentFlowEnums.partial or
                        db_member.preferred_payment_flow == PreferredPaymentFlowEnums.credit_card):
                    _ = ''
                    pre_auth_info = get_or_create_partial_pre_auth_info_cc(dbsession, membership_id, connector_currency)
                    pre_auth_amount = float(pre_auth_info.pre_auth_amount)
                else:
                    return handle_wallet_pre_auth(dbsession, membership_id, pre_auth_amount, connector_currency)
            else:
                return handle_wallet_pre_auth(dbsession, membership_id, pre_auth_amount, connector_currency)
        else:
            return handle_wallet_pre_auth(dbsession, membership_id, pre_auth_amount, connector_currency)

    # Else, perform Credit-Card Pre-Auth.
    processed_response_code, db_pre_auth_payment = handle_credit_card_pre_auth(request, dbsession, membership_id,
                                                                               use_3ds, pre_auth_amount,
                                                                               connector_currency,
                                                                               guest_mode=guest_mode)
    if processed_response_code == 301 and guest_mode:
        db_pre_auth = get_pre_auth_payment_by_id(dbsession, db_pre_auth_payment.id)
        pre_auth_object = PreAuthPaymentResponseToMobile.from_orm(db_pre_auth)

        action_url, action_method, action_data = get_pre_auth_url_and_value_from_object(
            db_pre_auth_payment.response)

        pre_auth_object.action_method = action_method
        pre_auth_object.action_url = action_url
        pre_auth_object.action_data = action_data
        return pre_auth_object

    # The response code should actually be in "Integer", these integer is returned from internal system after mapping.
    # Error Code: 301 - Redirect to 3DS
    # If is not valid, do not allow them to charge, if is 301 and not CyberSource Payment Gateway, Reject Them
    if processed_response_code != 200:
        no_3ds_message = 'Sorry, the card was declined, please try again or change the card.'
        # If its 301 (Internally redirect to 3DS), and the PG is Fiuu, we reject them as requirement are to only perform
        # Non-3DS Flow.
        if processed_response_code == 301:
            db_credit_card = get_member_primary_cc(dbsession, membership_id, connector_currency)
            if not db_credit_card:
                db_credit_card = get_member_primary_cc(dbsession, membership_id)
                if db_credit_card.payment_gateway != schema.CreditCardPaymentGateway.cybersource:
                    raise HTTPException(status_code=422,
                                        detail=f'EC: {processed_response_code}. {str(no_3ds_message)}')
            else:
                if db_credit_card.payment_gateway != schema.CreditCardPaymentGateway.cybersource:
                    raise HTTPException(status_code=422, detail=f'EC: {processed_response_code}. {str(no_3ds_message)}')

            # Change from Non-3DS to 3DS
            _, db_pre_auth_payment = handle_credit_card_pre_auth(request, dbsession, membership_id, True,
                                                                 pre_auth_amount, connector_currency)

        # If the error message is 301xx (We try to map and return the message accordingly).
        else:
            db_pre_auth = get_pre_auth_payment_by_id(dbsession, db_pre_auth_payment.id)
            if db_pre_auth.error_description is not None:
                mapped_error_message = map_pg_error_to_friendly_message(str(db_pre_auth.error_description),
                                                                        str(processed_response_code))
            else:
                mapped_error_message = map_pg_error_to_friendly_message(str(processed_response_code),
                                                                        str(processed_response_code))
            if mapped_error_message == '':
                raise HTTPException(status_code=422, detail=str(
                    f'EC: {processed_response_code}. Transaction failed by card processor. '
                    f'Try another card or contact your bank for more information.'))
            else:
                raise HTTPException(status_code=422,
                                    detail=str(f'EC: {processed_response_code}. {mapped_error_message}'))

    # Actually, only CyberSource with 301 will come here, as the previous logic already blocked Razer
    # From performing 3DS flow.
    if processed_response_code in [200, 301]:
        db_pre_auth = get_pre_auth_payment_by_id(dbsession, db_pre_auth_payment.id)
        pre_auth_object = PreAuthPaymentResponseToMobile.from_orm(db_pre_auth)

        if db_pre_auth_payment.payment_gateway == schema.CreditCardPaymentGateway.fiuu and (
                use_3ds or processed_response_code == 301):
            action_url, action_method, action_data = get_pre_auth_url_and_value_from_object(
                db_pre_auth_payment.response)

            pre_auth_object.action_method = action_method
            pre_auth_object.action_url = action_url
            pre_auth_object.action_data = action_data
        if db_pre_auth_payment.payment_gateway == schema.CreditCardPaymentGateway.cybersource and (
                use_3ds or processed_response_code == 301):
            pre_auth_object.action_method = db_pre_auth_payment.response.get('action_method')
            pre_auth_object.action_url = db_pre_auth_payment.response.get('action_url')
            pre_auth_object.action_data = db_pre_auth_payment.response.get('action_data')

        return pre_auth_object

    raise HTTPException(status_code=422, detail='Card not allowed to pre-auth, please contact administrator.')


@router.post('/pre-auth/start-charging/{connector_id}')
async def start_charging_after_pre_auth(request: Request, connector_id: str,
                                        filters: StartPreAuthRequest,
                                        dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    use_wallet = filters.use_wallet
    promo_code = filters.promo_code
    payment_type = 'Credit-Card'
    if use_wallet:
        payment_type = 'Wallet'

    query_params = request.query_params
    query_params = dict(query_params)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')
    db_member = get_membership_by_id(dbsession, membership_id)
    db_user = get_user_by_id(dbsession, db_member.user_id)

    require_cc_check = True
    # To cater for where we use guest checkout everytime we charge
    # We do not have CC info
    if settings.GUEST_PRE_AUTH_3DS_EVERY_CHARGE:
        if db_user.is_guest:
            require_cc_check = False

    pre_charging_flow = PreChargingFlow(request_headers=headers, connector_id=connector_id, use_wallet=use_wallet,
                                        validate_cc=require_cc_check)
    await pre_charging_flow.start_flow(dbsession, membership_id,
                                       promo_code)  # Assume this is necessary for initializing the flow
    connector_currency = pre_charging_flow.get_connector()['billing_currency']

    # If use wallet, we check if its redirect to cc as partial will redirect to cc
    if use_wallet:
        db_member = get_membership_by_id(dbsession, membership_id)
        if settings.ENABLE_PARTIAL_PAYMENT_FLOW:
            is_low_wallet_balance = check_if_low_wallet_balance_by_membership_id(dbsession, membership_id,
                                                                                 connector_currency)
            if is_low_wallet_balance:
                if (db_member.preferred_payment_flow == PreferredPaymentFlowEnums.partial or
                        db_member.preferred_payment_flow == PreferredPaymentFlowEnums.credit_card):
                    # use_wallet = False
                    payment_type = 'Credit-Card'

    is_session_free = await check_if_session_free(dbsession, pre_charging_flow, membership_id)

    if not is_session_free:
        pre_auth_payment = get_non_binded_pre_auth_by_member_id_with_payment_type(dbsession, membership_id,
                                                                                  connector_currency, payment_type)
        if pre_auth_payment:
            path = f'connector/{connector_id}/remote-start'
            url = f'{CHARGER_URL_PREFIX_V1}/{path}'
            response = await send_request('POST', url, data={}, headers=headers, query_params=query_params, timeout=9)
            return JSONResponse(response.json(), status_code=response.status_code)
        raise HTTPException(status_code=422, detail='No binded pre-auth found.')

    path = f'connector/{connector_id}/remote-start'
    url = f'{CHARGER_URL_PREFIX_V1}/{path}'
    response = await send_request('POST', url, data={}, headers=headers, query_params=query_params, timeout=9)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/pre-auth/start-ocpi-charging/{connector_id}')
async def start_ocpi_charging_after_pre_auth(request: Request, connector_id: str,  # noqa: MC0001
                                             filters: StartPreAuthRequest,
                                             dbsession: SessionLocal = Depends(create_session)):
    """
    Start charging based on connector, this is for OCPI API.
    Ideally, is only meant for App User's EMSP external charging.
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)

    membership_id = auth_token_data.get('membership_id')

    use_wallet = filters.use_wallet
    payment_type = 'Credit-Card'
    if use_wallet:
        payment_type = 'Wallet'

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')
    db_member = get_membership_by_id(dbsession, membership_id)
    db_user = get_user_by_id(dbsession, db_member.user_id)

    require_cc_check = True
    # To cater for where we use guest checkout everytime we charge
    # We do not have CC info
    if settings.GUEST_PRE_AUTH_3DS_EVERY_CHARGE:
        if db_user.is_guest:
            require_cc_check = False

    headers = generate_charger_header(dbsession, membership_id)
    ocpi_token = get_or_create_ocpi_app_token(dbsession, membership_id)

    pre_charging_flow = OCPIPreChargingFlow(request_headers=headers, connector_id=connector_id, use_wallet=use_wallet,
                                            validate_cc=require_cc_check)
    await pre_charging_flow.ocpi_start_flow(dbsession, membership_id)
    connector_currency = pre_charging_flow.get_connector()['billing_currency']
    free_session_due_to_subscription = pre_charging_flow.check_charging_free_due_to_subscription(dbsession,
                                                                                                 membership_id)
    if use_wallet:
        db_member = get_membership_by_id(dbsession, membership_id)
        if settings.ENABLE_PARTIAL_PAYMENT_FLOW:
            is_low_wallet_balance = check_if_low_wallet_balance_by_membership_id(dbsession, membership_id,
                                                                                 connector_currency)
            if is_low_wallet_balance:
                if (db_member.preferred_payment_flow == PreferredPaymentFlowEnums.partial or
                        db_member.preferred_payment_flow == PreferredPaymentFlowEnums.credit_card):
                    # use_wallet = False
                    payment_type = 'Credit-Card'

    pre_auth_payment = get_non_binded_pre_auth_by_member_id_with_payment_type(dbsession, membership_id,
                                                                              connector_currency, payment_type)

    if not free_session_due_to_subscription:
        if not pre_auth_payment:
            raise HTTPException(status_code=422, detail='No binded pre-auth found.')

    path = f'ocpi/emsp/{connector_id}/remote-start'
    url = f'{CHARGER_URL_PREFIX_V1}/{path}'
    ocpi_command_url = f'{OCPI_URL}/emsp/2.2.1/commands/'

    data = {
        'ocpi_token_id': str(ocpi_token.id),
        'command': "START_SESSION",
        'response_url': ocpi_command_url,
        'status': "PENDING"
    }
    try:
        response_emsp = await send_request('POST', url, data=json.dumps(data), headers=headers)
    except Exception as e:
        logger.info('There is an error while requesting partner command with start-command, error: %s:', str(e))
        raise HTTPException(status_code=403, detail='Partner initialization failed.')

    if response_emsp.status_code == 200:
        ocpi_token_dict = OCPITokenResponse.from_orm(ocpi_token).dict(exclude_unset=True, exclude_none=True)
        ocpi_token_dict['uid'] = str(ocpi_token_dict.pop('id'))
        try:
            ocpi_token_dict['last_updated'] = str(ocpi_token_dict.pop('updated_at'))
        except KeyError:
            ocpi_token_dict['last_updated'] = str(ocpi_token_dict.pop('created_at'))
        ocpi_token_dict.pop('member_id')
        response_emsp_json = response_emsp.json()
        operator_id = response_emsp_json['cp']['operator_id']
        command_data = {
            'response_url': response_emsp_json['command']['response_url'],
            'token': ocpi_token_dict,
            'location_id': str(response_emsp_json['location']['ocpi_partner_location_id']),
            'evse_uid': str(response_emsp_json['cp']['ocpi_partner_evse_uid']),
            'connector_id': str(response_emsp_json['connector']['ocpi_partner_connector_id']),
        }

        external_org = ExternalOrganizationCRUD.query(dbsession).filter(
            models.ExternalOrganization.id == operator_id).one()

        external_auths = ExternalTokenCRUD.query(dbsession).filter(
            models.ExternalToken.external_organization_id == external_org.id).one()
        external_auths = ExternalToken.from_orm(external_auths).dict()

        end_points = json.loads(external_auths['endpoints'])

        await post_ocpi_token_to_party(ocpi_token_dict, end_points, external_auths)
        for endpoint in end_points['endpoints']:
            if endpoint['role'] == 'RECEIVER' and endpoint['identifier'] == 'commands':
                receiver_command_url = endpoint['url']

        if receiver_command_url.endswith('/'):
            receiver_command_url += 'START_SESSION'
        else:
            receiver_command_url += '/START_SESSION'
        external_service_token = decode_base64_to_token(external_auths['external_service_token'])
        authorization_token = f'Token {external_service_token}'
        ocpi_header = {'Authorization': authorization_token, 'Content-Type': 'application/json'}

        logger.info('start_session as emsp data %s', str(json.dumps(command_data)))
        try:
            response_command = await send_request('POST', receiver_command_url, data=json.dumps(command_data),
                                                  headers=ocpi_header)
            response_command_json = response_command.json()
            logger.info('OCPI EMSP remote-start command response: %s', response_command_json)
        except Exception as e:
            logger.info('There is an error while requesting partner command with start-command, error: %s:', str(e))
            raise HTTPException(status_code=403, detail='Partner initialization failed.')

        command_id = response_emsp_json['command']['id']
        path = f'ocpi/command/{command_id}'
        url = f'{CHARGER_URL_PREFIX_V1}/{path}'

        result_data = {
            'response_data': response_command.json()['data']
        }
        response_command = await send_request('PUT', url, data=json.dumps(result_data),
                                              headers=ocpi_header)

        if response_command_json.get('data'):
            if response_command_json.get('data').get('result') != 'ACCEPTED':
                raise HTTPException(status_code=403, detail='Partner initialization failed.')
            response_status = 'ACCEPTED'
        else:
            raise HTTPException(status_code=403, detail='Partner initialization failed.')

        logger.info('ocpi main start-charging info: %s', response_command.json())

        response_command_json = {
            'id': response_emsp_json['operation_request']['id'],
            'created_at': response_emsp_json['operation_request']['created_at'],
            'requester_type': response_emsp_json['operation_request']['requester_type'],
            'requester_id': response_emsp_json['operation_request']['requester_id'],
            'operation': response_emsp_json['operation_request']['operation'],
            'charge_point_id': response_emsp_json['operation_request']['charge_point_id'],
            'connector_id:': response_emsp_json['operation_request']['connector_id'],
            'status': str(response_status).capitalize()
        }

    return JSONResponse(response_command_json, status_code=response_command.status_code)


@router.get('/pre-auth/check/{pre_auth_id}', response_model=PreAuthPaymentResponseToMobile)
async def check_pre_auth_status(request: Request, pre_auth_id: str,
                                dbsession: SessionLocal = Depends(create_session)):
    try:
        db_pre_auth = get_preauth_by_id(dbsession, pre_auth_id)
    except NoResultFound:
        raise HTTPException(status_code=404, detail='No Pre-Auth Found.')

    return db_pre_auth


@router.post('/stop-charging/{transaction_id}')
async def stop_charging_session(request: Request, transaction_id: str, background_tasks: BackgroundTasks,
                                dbsession: SessionLocal = Depends(create_session)):
    """
    Stop charging services based on transaction id, it will trigger to stop the charger activity, as-well as updating
    the charging session data

    :param str transaction_id
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)

    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    # db_cp = await validate_platform_type(request, dbsession, request.url.path, transaction_id)
    #
    # if db_cp.get('charging_session_type') == 'Foreign':
    #     stop_charging_response = await get_stop_charging_response(dbsession, headers, transaction_id)
    #     background_tasks.add_task(stop_charging_ruby_proxy, dbsession, membership_id,
    #                               db_cp['connectors']['charge_point']['serial_number'],
    #                               db_cp['connectors']['number'], session_id=db_cp['id'],
    #                               foreign_session_id=db_cp['foreign_session_id'])
    #     return JSONResponse(stop_charging_response.json(), status_code=stop_charging_response.status_code)

    logger.debug('header for request to charger service: %s', headers)
    url = f'{CHARGER_URL_PREFIX_V1}/connector/remote-stop/{transaction_id}'
    method = request.method
    query_params = request.query_params
    data = await request.body()

    # stop charging
    # calling v1 to avoid the issue of v2 not working / not updated
    response = await send_request(method, url, data=data, headers=headers, query_params=query_params, timeout=9)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/ocpp-status/ruby/serial-number/{serial_number}')
async def update_ocpp_status_ruby(request: Request, serial_number: str,
                                  connector: RubyConnectorStatusUpdate,
                                  dbsession: SessionLocal = Depends(create_session)):
    connector_data = connector.dict()

    logger.info('Received ocpp-status update call to %s, with status as: %s', str(serial_number), str(connector_data))
    result = await update_ocpp_status_ruby_proxy(dbsession, serial_number,
                                                 connector_data.get('connector_number'),
                                                 connector_data.get('ocpp_status'),
                                                 connector_data.get('status'))
    return result


@router.get('/charge-usage/{charging_session_id}')
async def get_charge_usage(request: Request, charging_session_id: str,
                           dbsession: SessionLocal = Depends(create_session)):
    """
    Get the charge usage (usage history) for the particular session

    :param str charging_session_id
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)

    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    logger.debug('header for request to charger service: %s', headers)
    url = f'{CHARGER_URL_PREFIX_V2}/charging/{charging_session_id}'
    method = request.method
    data = await request.body()

    # stop charging
    # calling v1 to avoid the issue of v2 not working / not updated
    response = await send_request(method, url, data=data, headers=headers)
    if response.status_code == 200:
        response_json = response.json()
        operators = []
        operators_by_name = []
        operator_id = safe_get(response_json, 'meta', 'operator', 'id')
        operator_name = safe_get(response_json, 'meta', 'location', 'ocpi_partner_operator', 'name')
        if operator_name is not None:
            operators_by_name.append(operator_name)
        if operator_id is not None:
            operators.append(operator_id)

        charging_record = get_charge_history_record(response_json, dbsession)
        charging_record = map_charging_session_id_tag_with_member(dbsession, charging_record)
        operators = crud.get_external_organization_by_operator_list(dbsession, operators)
        operators = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in operators]

        operators_dict = {}
        for l2 in operators:
            operator_details = l2.dict()
            operators_dict[str(operator_details['id'])] = {k: v for k, v in operator_details.items() if k != 'id'}

        if len(operators_by_name) > 0:
            operators_by_name = crud.get_external_organization_by_operator_name(dbsession,
                                                                                operators_by_name)
            operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                                 operators_by_name]
            for l2 in operators_by_name:
                operator_details = l2.dict()
                operator_details['id'] = str(operator_details['id'])  # Convert the ID to a string
                operators_dict[str(operator_details['original_name'])] = {k: v for k, v in
                                                                          operator_details.items()
                                                                          if k != 'original_name'}

        response_json_merged = charging_history_merge_operator_details(charging_record, operators_dict)
        return JSONResponse(response_json_merged, status_code=response.status_code)

    if response.status_code == 500:
        raise HTTPException(status_code=404, detail='No charging session found')

    return JSONResponse(response.json(), status_code=response.status_code)


# @router.get('/connector/charge_points/{serial_number}')
# async def get_connector(request: Request, serial_number: str,
#                         dbsession: SessionLocal = Depends(create_session)):
#     auth_token_data = decode_auth_token_from_headers(request.headers)
#
#     membership_id = auth_token_data.get('membership_id')
#     headers = generate_charger_header(dbsession, membership_id)
#     url = f'{CHARGER_URL_PREFIX_V2}/connector/charge_points/{serial_number}'
#     response = await send_request('GET', url, headers=headers)
#
#     if response.status_code == 200:
#         response_json = response.json()
#         for i in range(len(response_json)):
#             cpc_id = response_json[i].get('id')
#             billing_price = response_json[i].get('billing_unit_fee')
#             discounted_amount = apply_subscription_discount_connector(dbsession, membership_id, cpc_id, billing_price)
#
#             response_json[i]['discounted_billing_unit_fee'] = discounted_amount
#
#         return JSONResponse(response_json, status_code=response.status_code)
#     return JSONResponse(response.json(), status_code=response.status_code)

@router.get('/location/discount/{location_id}')
async def get_cp_discounted_price(request: Request, location_id: str,
                                  api_sid_header: str = Security(api_sid_header),
                                  dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)

    membership_id = auth_token_data.get('membership_id')
    if membership_id:
        headers = generate_charger_header(dbsession, membership_id)
    else:
        headers = generate_charger_header_unauthorized(dbsession, api_sid_header)
    url = f'{CHARGER_URL_PREFIX_V2}/location/charge_points/raw/{location_id}'
    response = await send_request('GET', url, headers=headers)
    rounding_number = settings.DECIMAL_TO_ROUND_IN_DISCOUNTED_UNIT_FEE
    if response.status_code == 200:
        response_json = response.json()
        for i in range(len(response_json)):
            cpc_id = response_json[i].get('id')
            billing_price = response_json[i].get('billing_unit_fee')
            billing_price_after_vat = response_json[i].get('billing_unit_fee_after_vat')
            billing_currency = response_json[i].get('billing_currency')
            if membership_id:
                discounted_amount = apply_subscription_discount_connector(dbsession, membership_id, cpc_id,
                                                                          billing_price)
            else:
                discounted_amount = billing_price
            if discounted_amount == billing_price:
                discounted_amount_after_vat = {
                    'total_amount': billing_price_after_vat
                }
            else:

                discounted_amount_after_vat = calculate_tax_amount(dbsession, cpc_id, discounted_amount,
                                                                   billing_currency, always_tax=True,
                                                                   rounding_number=rounding_number)

            response_json[i]['discounted_billing_unit_fee'] = discounted_amount
            response_json[i]['discounted_billing_unit_fee_after_vat'] = discounted_amount_after_vat['total_amount']

        return JSONResponse(response_json, status_code=response.status_code)

    if ENABLE_MOBILE_OCPI_DISCOUNT_API:
        if response.status_code == 404:
            url = f'{CHARGER_URL_PREFIX_V2}/location/ocpi/charge_points/raw/{location_id}'
            response = await send_request('GET', url, headers=headers)
            if response.status_code == 200:
                response_json = response.json()
                for i in range(len(response_json)):
                    cpc_id = response_json[i].get('id')
                    billing_price = response_json[i].get('billing_unit_fee')
                    billing_currency = response_json[i].get('billing_currency')
                    if membership_id:
                        discounted_amount = apply_subscription_discount_connector(dbsession, membership_id, cpc_id,
                                                                                  billing_price, is_ocpi=True)
                    else:
                        discounted_amount = billing_price
                    discounted_amount_after_vat = calculate_tax_amount(dbsession, cpc_id, discounted_amount,
                                                                       billing_currency, always_tax=True,
                                                                       rounding_number=rounding_number)

                    response_json[i]['discounted_billing_unit_fee'] = discounted_amount
                    response_json[i]['discounted_billing_unit_fee_after_vat'] = discounted_amount_after_vat['total_amount']

                return JSONResponse(response_json, status_code=response.status_code)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/location/discount')
async def get_cps_discounted_price(request: Request, locations: list[str],
                                   api_sid_header: str = Security(api_sid_header),
                                   dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)

    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header_unauthorized(dbsession, api_sid_header)
    url = f'{CHARGER_URL_PREFIX_V2}/location/charge_points/raw'
    response = await send_request('POST', url, headers=headers, data=json.dumps(locations))
    rounding_number = settings.DECIMAL_TO_ROUND_IN_DISCOUNTED_UNIT_FEE
    final_list = []
    total_vehicle_returned = 0
    total_vehicle_required = len(locations)
    if response.status_code == 200:
        response_json = response.json()
        total_vehicle_returned = len(response_json)
        for i in range(len(response_json)):
            for x in range(len(response_json[i]['charge_points'])):
                for z in range(len(response_json[i]['charge_points'][x]['connectors'])):
                    connector = response_json[i]['charge_points'][x]['connectors'][z]
                    cpc_id = connector.get('id')
                    billing_price = connector.get('billing_unit_fee')
                    billing_price_after_vat = connector.get('billing_unit_fee_after_vat')
                    billing_currency = connector.get('billing_currency')
                    discounted_amount = apply_subscription_discount_connector(dbsession, membership_id, cpc_id,
                                                                              billing_price)
                    if discounted_amount == billing_price:
                        discounted_amount_after_vat = {
                            'total_amount': billing_price_after_vat
                        }
                    else:
                        discounted_amount_after_vat = calculate_tax_amount(dbsession, cpc_id, discounted_amount,
                                                                           billing_currency, always_tax=True,
                                                                           rounding_number=rounding_number)

                    response_json[i]['charge_points'][x]['connectors'][z][
                        'discounted_billing_unit_fee'] = discounted_amount
                    response_json[i]['charge_points'][x]['connectors'][z][
                        'discounted_billing_unit_fee_after_vat'] = discounted_amount_after_vat['total_amount']
        final_list.extend(response_json)
        # return JSONResponse(response_json, status_code=response.status_code)
    if ENABLE_MOBILE_OCPI_DISCOUNT_API:
        if total_vehicle_required > total_vehicle_returned:
            url = f'{CHARGER_URL_PREFIX_V2}/location/ocpi/charge_points/raw'
            response = await send_request('POST', url, headers=headers, data=json.dumps(locations))
            response_json = response.json()
            for i in range(len(response_json)):
                for x in range(len(response_json[i]['charge_points'])):
                    for z in range(len(response_json[i]['charge_points'][x]['connectors'])):
                        connector = response_json[i]['charge_points'][x]['connectors'][z]
                        cpc_id = connector.get('id')
                        billing_price = connector.get('billing_unit_fee')
                        billing_currency = connector.get('billing_currency')
                        discounted_amount = apply_subscription_discount_connector(dbsession, membership_id, cpc_id,
                                                                                  billing_price, is_ocpi=True)
                        discounted_amount_after_vat = calculate_tax_amount(dbsession, cpc_id, discounted_amount,
                                                                           billing_currency, always_tax=True,
                                                                           rounding_number=rounding_number
                                                                           )

                        response_json[i]['charge_points'][x]['connectors'][z][
                            'discounted_billing_unit_fee'] = discounted_amount
                        response_json[i]['charge_points'][x]['connectors'][z][
                            'discounted_billing_unit_fee_after_vat'] = discounted_amount_after_vat['total_amount']
            final_list.extend(response_json)
            return JSONResponse(final_list, status_code=response.status_code)

    return JSONResponse(final_list, status_code=response.status_code)


@router.get('/connector/charge_points/discount/{serial_number}')
async def get_connector_by_serial_number(request: Request, serial_number: str,
                                         api_sid_header: str = Security(api_sid_header),
                                         dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)

    membership_id = auth_token_data.get('membership_id')
    if membership_id:
        headers = generate_charger_header(dbsession, membership_id)
    else:
        headers = generate_charger_header_unauthorized(dbsession, api_sid_header)

    url = f'{CHARGER_URL_PREFIX_V2}/connector/charge_points/raw/{serial_number}'
    response = await send_request('GET', url, headers=headers)

    rounding_number = settings.DECIMAL_TO_ROUND_IN_DISCOUNTED_UNIT_FEE

    if response.status_code == 200:
        response_json = response.json()
        for i in range(len(response_json)):
            cpc_id = response_json[i].get('id')
            billing_price = response_json[i].get('billing_unit_fee')
            billing_price_after_vat = response_json[i].get('billing_unit_fee_after_vat')
            billing_currency = response_json[i].get('billing_currency')
            if membership_id:
                discounted_amount = apply_subscription_discount_connector(dbsession, membership_id, cpc_id,
                                                                          billing_price)
            else:
                discounted_amount = billing_price

            if discounted_amount == billing_price:
                discounted_amount_after_vat = {
                    'total_amount': billing_price_after_vat
                }
            else:
                discounted_amount_after_vat = calculate_tax_amount(dbsession, cpc_id, discounted_amount,
                                                                   billing_currency, always_tax=True,
                                                                   rounding_number=rounding_number)

            response_json[i]['discounted_billing_unit_fee'] = discounted_amount
            response_json[i]['discounted_billing_unit_fee_after_vat'] = discounted_amount_after_vat['total_amount']

        return JSONResponse(response_json, status_code=response.status_code)
    if ENABLE_MOBILE_OCPI_DISCOUNT_API:
        if response.status_code == 404:
            url = f'{CHARGER_URL_PREFIX_V2}/connector/ocpi/charge_points/raw/{serial_number}'
            response = await send_request('GET', url, headers=headers)
            response_json = response.json()

            for i in range(len(response_json)):
                cpc_id = response_json[i].get('id')
                billing_price = response_json[i].get('billing_unit_fee')
                billing_currency = response_json[i].get('billing_currency')
                if membership_id:
                    discounted_amount = apply_subscription_discount_connector(dbsession, membership_id, cpc_id,
                                                                              billing_price, is_ocpi=True)
                else:
                    discounted_amount = billing_price

                discounted_amount_after_vat = calculate_tax_amount(dbsession, cpc_id, discounted_amount,
                                                                   billing_currency, always_tax=True,
                                                                   rounding_number=rounding_number)

                response_json[i]['discounted_billing_unit_fee'] = discounted_amount
                response_json[i]['discounted_billing_unit_fee_after_vat'] = discounted_amount_after_vat['total_amount']

            return JSONResponse(response_json, status_code=response.status_code)

    return JSONResponse(response.json(), status_code=response.status_code)


# this api is for calling ruby api when charge_point is not connected to python apollo
@router.post('/billing-request/ruby/{foreign_session_id}')
async def billing_request_ruby(request: Request, foreign_session_id: str,
                               data: BillingRequestRuby, background_tasks: BackgroundTasks,
                               dbsession: SessionLocal = Depends(create_session)):
    set_admin_as_context_user(dbsession)

    charging_data = data.dict()

    logger.info('Received billing-request call from ruby with foreign_session_id %s, with data as: %s',
                str(foreign_session_id), str(charging_data))

    meter_start = charging_data.get('meter_start')
    meter_stop = charging_data.get('meter_stop')
    session_start = charging_data.get('session_start')
    session_end = charging_data.get('session_end')

    charging_session = RubyBillingUpdate(
        meter_start=meter_start,
        meter_stop=meter_stop,
        session_start=session_start,
        session_end=session_end,
        status='Completed'
    )
    db_charging_session = await validate_platform_type(request, dbsession, url_path=request.url.path,
                                                       path_id=foreign_session_id,
                                                       headers=None)

    await update_foreign_session_proxy(dbsession, None, db_charging_session['id'], charging_session.dict())

    billing_type = db_charging_session.get('meta', {}).get('billing_info', {}).get('billing_type')
    billing_unit_fee = db_charging_session.get('meta', {}).get('billing_info', {}).get('billing_unit_fee')
    billing_cycle = db_charging_session.get('meta', {}).get('billing_info', {}).get('billing_cycle')
    billing_currency = db_charging_session.get('meta', {}).get('billing_info', {}).get('billing_currency')
    connection_fee = db_charging_session.get('meta', {}).get('billing_info', {}).get('connection_fee')
    billing_discounted_type = db_charging_session.get('meta', {}).get('billing_info', {}).get('billing_discounted_type')
    billing_discounted_amount = db_charging_session.get('meta', {}).get('billing_info', {}) \
        .get('billing_discounted_amount')
    transaction_id = db_charging_session.get('transaction_id')

    # await update_foreign_session_proxy(dbsession, None, db_charging_session['id'], charging_session.dict())
    result = await billing_request_ruby_proxy(dbsession, db_charging_session['id_tag'], billing_type,
                                              billing_cycle, billing_unit_fee, connection_fee,
                                              meter_start, meter_stop, charging_session.dict().get('session_start'),
                                              charging_session.dict().get('session_end'), db_charging_session['id'],
                                              db_charging_session['connectors']['charge_point']['id'],
                                              billing_currency, db_charging_session['connectors']['id'],
                                              billing_discounted_type, billing_discounted_amount, transaction_id)
    if not result:
        raise HTTPException(400, 'Record Not Found')
    charging_session.charging_session_bill_id = str(result['charging_session_bill_id'])
    background_tasks.add_task(update_foreign_session_proxy, dbsession, None, db_charging_session['id'],
                              charging_session.dict())
    return result


# this api is for calling ruby api when charge_point is not connected to python apollo
@router.patch('/update-session/ruby/{foreign_session_id}', )
async def update_foreign_session(request: Request, foreign_session_id: str,
                                 data: ForeignSessionUpdate,
                                 dbsession: SessionLocal = Depends(create_session)):
    set_admin_as_context_user(dbsession)

    charging_data = data.dict()

    db_charging_session = await validate_platform_type(request, dbsession, url_path=request.url.path,
                                                       path_id=foreign_session_id,
                                                       headers=None)
    logger.info('Received session update for session_id %s, with data as: %s', str(db_charging_session['id']),
                str(charging_data))

    db_charging_session = await update_foreign_session_proxy(dbsession, None, db_charging_session['id'], charging_data)
    return db_charging_session


@router.post('/start-transaction/ruby')
async def start_transaction_ruby(request: Request, serial_number: str, connector_number: str,
                                 transaction_data: TransactionDataRuby,
                                 dbsession: SessionLocal = Depends(create_session)):
    admin = set_admin_as_context_user(dbsession)
    headers = generate_charger_header(dbsession, admin.id)
    transaction_data_dict = transaction_data.dict()
    db_cp = await validate_platform_type(request, dbsession, url_path=request.url.path, path_id=serial_number,
                                         headers=headers)

    logger.info('Received transaction start for charger with serial-number %s, with data as: %s', str(serial_number),
                str(transaction_data_dict))

    connector_id = None
    for connector in db_cp['connectors']:
        if str(connector['number']) == connector_number:
            connector_id = connector['id']

    if connector_id is not None:
        json_response = await start_transaction_ruby_proxy(dbsession, serial_number, connector_number,
                                                           connector_id,
                                                           db_cp['id'], transaction_data_dict, headers)
        return json_response


@router.get('/command/native/{command_id}')
async def native_command_mapper(request: Request, command_id: str, api_sid_header: str = Security(api_sid_header),
                                dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    if membership_id:
        headers = generate_charger_header(dbsession, membership_id)
    else:
        headers = generate_charger_header_unauthorized(dbsession, api_sid_header)

    url = f'{CHARGER_URL_PREFIX_V2}/command/native/{command_id}'
    method = request.method
    query_params = request.query_params
    data = await request.body()
    response = await send_request(method, url, data=data, headers=headers, query_params=query_params)
    response_json = response.json()
    if response.status_code in [200, 201, 202, 203, 204]:
        response_json['result_message'] = None
        response_json['display_support'] = False

    return JSONResponse(response_json, status_code=response.status_code)


@router.get('/command/ocpi/{command_id}')
async def ocpi_command_mapper(request: Request, command_id: str, api_sid_header: str = Security(api_sid_header),
                              dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    if membership_id:
        headers = generate_charger_header(dbsession, membership_id)
    else:
        headers = generate_charger_header_unauthorized(dbsession, api_sid_header)

    url = f'{CHARGER_URL_PREFIX_V2}/command/ocpi/{command_id}'
    method = request.method
    query_params = request.query_params
    data = await request.body()
    response = await send_request(method, url, data=data, headers=headers, query_params=query_params)
    response_json = response.json()

    if response.status_code in [200, 201, 202, 203, 204]:
        response_json['display_support'] = False
        response_json = process_ocpi_command_module(response_json)

    return JSONResponse(response_json, status_code=response.status_code)


@router.api_route('/{path:path}', methods=['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
                  include_in_schema=False)
async def proxy_request(request: Request, path: str,  # pylint:disable=too-many-branches
                        api_sid_header: str = Security(api_sid_header),
                        dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    unauthorized_headers = generate_charger_header_unauthorized(dbsession, api_sid_header)

    logger.debug('header for request to charger service: %s', headers)
    url = f'{CHARGER_URL_PREFIX_V2}/{path}'
    method = request.method
    query_params = request.query_params
    data = await request.body()

    # Check whether the api require auth token
    if any([path.startswith(item) for item in unauthorized_path]):
        headers = unauthorized_headers

    # Get object data that is being deleted in order to log delete activity in charger service
    if method == 'DELETE':
        response_delete = await send_request('GET', url, data=data, headers=headers, query_params=query_params)

    response = await send_request(method, url, data=data, headers=headers, query_params=query_params)

    # Log activity
    table_name = re.match(r'^[^/]*', path).group(0)
    if response.status_code < 300 and method == 'PATCH':
        await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.update, dbsession)
    elif response.status_code < 300 and method == 'POST':
        await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)
    elif response.status_code < 300 and method == 'DELETE':
        await log_activity(user_id, table_name, response_delete.json(), schema.ActivityLogType.delete, dbsession)

    if path == 'charging/current' and response.status_code == 200:
        # db_cp = await validate_platform_type(request, dbsession, url_path=request.url.path, path_id=membership_id,
        #                                      headers=headers)
        #
        # if db_cp.get('charging_session_type') == 'Foreign':
        #     foreign_result = await current_session_ruby_proxy(dbsession, membership_id, headers,
        #                                                       serial_number=db_cp['connectors']['charge_point']
        #                                                       ['serial_number'], session_id=db_cp['id'],
        #                                                       foreign_session_id=db_cp['foreign_session_id'])
        #
        #     foreign_result_json = map_charging_session_id_tag_with_member(dbsession, foreign_result)
        #     return JSONResponse(foreign_result_json, status_code=response.status_code)

        response_json = response.json()
        response_json = map_charging_session_id_tag_with_member(dbsession, response_json)
        return JSONResponse(response_json, status_code=response.status_code)
    if re.fullmatch('charging/reservation/.+', path) and response.status_code == 200 and method == 'GET':
        response_json = response.json()
        operators = []
        operators_by_name = []
        operator_id = safe_get(response_json, 'meta', 'operator', 'id')
        operator_name = safe_get(response_json, 'meta', 'location', 'ocpi_partner_operator', 'name')
        if operator_name is not None:
            operators_by_name.append(operator_name)
        if operator_id is not None:
            operators.append(operator_id)
        operators = crud.get_external_organization_by_operator_list(dbsession, operators)
        operators = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in operators]

        operators_dict = {}
        for l2 in operators:
            operator_details = l2.dict()
            operators_dict[str(operator_details['id'])] = {k: v for k, v in operator_details.items() if k != 'id'}

        if len(operators_by_name) > 0:
            operators_by_name = crud.get_external_organization_by_operator_name(dbsession,
                                                                                operators_by_name)
            operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                                 operators_by_name]
            for l2 in operators_by_name:
                operator_details = l2.dict()
                operators_dict[str(operator_details['original_name'])] = {k: v for k, v in
                                                                          operator_details.items()
                                                                          if k != 'original_name'}
        response_json = charging_history_merge_operator_details(response_json, operators_dict)
        return JSONResponse(response_json, status_code=response.status_code)

    if re.fullmatch('charging/charging_sessions/connector/.+', path) and response.status_code == 200:
        response_json = response.json()
        for i in range(len(response_json)):
            response_json[i] = map_charging_session_id_tag_with_member(dbsession, response_json[i])

        return JSONResponse(response_json, status_code=response.status_code)

    return JSONResponse(response.json(), status_code=response.status_code)
