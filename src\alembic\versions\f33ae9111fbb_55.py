"""55

Revision ID: f33ae9111fbb
Revises: 30c5fe5a2827
Create Date: 2023-09-07 16:50:40.544405

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f33ae9111fbb'
down_revision = '30c5fe5a2827'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_auth_user', sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.drop_index('unique_phone_number', table_name='main_auth_user')
    op.drop_index('unique_user_email', table_name='main_auth_user')
    op.create_index('unique_phone_number_organization_id', 'main_auth_user', ['phone_number', 'organization_id', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted'))
    op.create_index('unique_email_organization_id', 'main_auth_user', ['email', 'organization_id', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted'))
    op.create_foreign_key('main_auth_user_organization_fk', 'main_auth_user', 'main_organization', ['organization_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('main_auth_user_organization_fk', 'main_auth_user', type_='foreignkey')
    op.drop_index('unique_email_organization_id', table_name='main_auth_user', postgresql_where=sa.text('NOT is_deleted'))
    op.drop_index('unique_phone_number_organization_id', table_name='main_auth_user', postgresql_where=sa.text('NOT is_deleted'))
    op.create_index('unique_user_email', 'main_auth_user', ['email', 'is_deleted'], unique=False)
    op.create_index('unique_phone_number', 'main_auth_user', ['phone_number', 'is_deleted'], unique=False)
    op.drop_column('main_auth_user', 'organization_id')
    # ### end Alembic commands ###
