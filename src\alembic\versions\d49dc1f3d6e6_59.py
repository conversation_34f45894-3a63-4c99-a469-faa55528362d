"""59

Revision ID: d49dc1f3d6e6
Revises: 18cd41021497
Create Date: 2023-09-29 14:20:15.548480

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd49dc1f3d6e6'
down_revision = '18cd41021497'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_subscription_invitation', sa.Column('custom_id_tag', sa.String(), nullable=True))
    op.add_column('main_subscription_invitation', sa.Column('custom_number', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_subscription_invitation', 'custom_number')
    op.drop_column('main_subscription_invitation', 'custom_id_tag')
    # ### end Alembic commands ###
