import logging
from datetime import datetime

from typing import List, Union
from uuid import UUID

import pytz
from fastapi import APIRouter, HTTPException, Depends, Request, UploadFile, File, Response
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate

from app import settings, schema, crud, exceptions
from app.crud.reporting_task import create_reporting_task_record
from app.database import create_session, SessionLocal
from app.permissions import permission, decode_auth_token_from_headers
from app.schema import ReportingTaskCreate
from app.utils import GenerateReport, is_disallowed_image
from app.lta_tasks import send_customer_list_via_email_sync

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/organization",
    tags=['organization', ],
    dependencies=[Depends(permission)]
)


def filter_operator(operators: list):
    new_operators = []
    for operator in operators:
        if not operator.is_deleted:
            new_operators.append(operator)
    return new_operators


# Need to clean up: already exists in organization_memberships.py
async def membership_filters(name: str = None, id_tag: str = None, organization: str = None):
    return {'name': name, 'user_id_tag': id_tag, 'organization': organization}


async def user_filters(
        email: str = None,
        phone_number: str = None,
        is_verified: bool = None,
        migrated_only: bool = False,
):
    return {
        'email': email,
        'phone_number': phone_number,
        'is_verified': is_verified,
        'migrated_only': migrated_only,
    }


@router.get("", response_model=List[schema.OrganizationResponse])
async def list_organizations(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Lists Organization objects
    """
    organizations = crud.get_organization_list(dbsession)
    for org in organizations:
        org.operators = filter_operator(org.operators)
    return organizations


@router.post("", response_model=schema.OrganizationResponse, status_code=201)
async def create_organization(request: Request, data: schema.Organization = Depends(),  # noqa: MC0001
                              file: UploadFile = File(None),
                              dbsession: SessionLocal = Depends(create_session)):
    """
    Creates an Organization object
    """
    try:
        try:
            auth_token_data = decode_auth_token_from_headers(request.headers)
            is_superuser = crud.get_user_by_id(dbsession, auth_token_data['user_id']).is_superuser
            parent = crud.get_organization_by_id(dbsession, data.parent_id)

            if not is_superuser:
                if parent.parent_id is not None and parent.parent.parent_id is not None:
                    raise HTTPException(400,
                                        'You are not permitted to create organization in this level, '
                                        'please contact Administrator.')
        except exceptions.ApolloObjectDoesNotExist:
            pass

        logo = None
        content_type = None
        if file:
            if file.content_type not in ['image/png', 'image/jpeg', 'image/jpg']:
                raise HTTPException(400, 'The uploaded file is not in PNG, JPEG, or JPG format .')
            content_type = file.content_type.split('/')[1]
            file.filename = f'temp.{content_type}'
            logo = await file.read()
            is_svg = is_disallowed_image(logo, file.filename)
            if is_svg:
                raise HTTPException(400, 'The uploaded file is not in PNG, JPEG, or JPG format .')

        org = crud.create_organization(dbsession, data)
        if file:
            org = crud.update_organization_logo(dbsession, logo, str(org.id), content_type)

        # secret_key = crud.create_organization_authentication_service(dbsession, org.id)
        # organization_response = schema.OrganizationResponse.from_orm(org)
        # organization_response.secret_key = secret_key
        country_code = 'my'
        is_taxable = False
        if str(settings.OCPI_PARTY_ID).upper() == 'CDG':
            is_taxable = True
            country_code = 'sg'
        elif str(settings.OCPI_PARTY_ID).upper() == 'HGM':
            is_taxable = False
            country_code = 'my'

        operator = crud.create_operator(dbsession, schema.Operator(
            name=data.name,
            organization_id=str(org.id),
            is_taxable=is_taxable,
        ))

        external_organization_org = {
            'id': operator.id,
            'party_id': operator.name[:3].lower(),
            'country_code': country_code,
            'images': None,
            'website': None,
            'description': operator.name + ' CPO',
            'friendly_name': operator.name,
            'name': operator.name,
            'organization_type': 'Normal'
        }

        crud.create_external_organization(dbsession, external_organization_org)

        return org
    except (exceptions.ApolloRootOrganizationError, exceptions.ApolloOrganizationDuplication) as e:
        raise HTTPException(400, e.__str__())


# Get all memberships of organization with membership type filter
@router.get("/memberships", status_code=200,
            response_model=Union[List[schema.MembershipResponse], Page[schema.MembershipResponse]])  # noqa
async def list_all_organization_members(organization_id: UUID = None, membership_type: str = None,
                                        user_filter: dict = Depends(user_filters),
                                        mem_filter: dict = Depends(membership_filters),
                                        pagination: bool = True, params: Params = Depends(),
                                        dbsession: SessionLocal = Depends(create_session)):
    """
    Lists all memberships

    :param uuid organization_id: Organization ID
    :param str membership_type: Membership type
    """
    membership_types = [membership.name for membership in schema.MembershipType]
    try:
        if membership_type is not None:
            if membership_type not in membership_types:
                raise HTTPException(400, f"Membership type must be one of {membership_types}")

        membership_list = crud.get_organization_membership_list(dbsession, organization_id,
                                                                membership_type, user_filter=user_filter,
                                                                mem_filter=mem_filter)
        if pagination:
            return paginate(membership_list, params)
        return membership_list.all()
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/memberships/download")  # noqa
async def download_all_organization_members(request: Request, organization_id: UUID = None,
                                            membership_type: str = None, include_child_orgs: bool = True,
                                            user_filter: dict = Depends(user_filters),
                                            mem_filter: dict = Depends(membership_filters),
                                            dbsession: SessionLocal = Depends(create_session),
                                            filename: str = None, send_to: str = None):
    """
    Download all memberships

    :param uuid organization_id: Organization ID
    :param str membership_type: Membership type
    """

    headers = ['NAME', 'ID TAG', 'EMAIL', 'PHONE NUMBER', 'ORGANIZATION', 'SIGNUP DATE', 'VERIFIED']

    columns = ['first_name', 'last_name', 'user_id_tag', 'user.email', 'user.phone_number', 'organization.name',
               'created_at', 'user.is_verified']

    # columns = ['full_name', 'user_id_tag', 'user.email', 'user.phone_number', 'organization.name',
    #            'created_at']
    reorder_columns = ['full_name', 'user_id_tag', 'user.email', 'user.phone_number', 'organization.name',
                       'created_at', 'user.is_verified']

    request_data = {
        "headers": dict(request.headers)
    }

    if not filename:
        tz = pytz.timezone('Asia/Kuala_Lumpur')
        current_datetime = datetime.now(tz)
        format_datetime = current_datetime.strftime("%d.%m.%Y_(%H:%M)")
        filename = 'customer_' + format_datetime

    if send_to:
        message = {
            'user_filter': user_filter,
            'mem_filter': mem_filter,
            'include_child_orgs': include_child_orgs,
            'organization_id': str(organization_id),
            'membership_type': membership_type,
            'request_data': request_data,
            'headers': headers,
            'columns': columns,
            'reorder_columns': reorder_columns,
            'filename': filename,
            'email': send_to,
        }

        task_data = ReportingTaskCreate(
            task_name="customer_list",
            json_payload=message,
            retry_count=0
        )

        reporting_task = create_reporting_task_record(dbsession, task_data)
        task_uid = reporting_task.id
        message['task_uid'] = task_uid
        send_customer_list_via_email_sync.delay(message=message)

        return {'message': 'The report will be ready in 15 minutes.'}

    query = crud.get_organization_membership_list(dbsession, organization_id, membership_type,
                                                  include_child_orgs, user_filter, mem_filter)

    report = GenerateReport('customer_list', headers, columns, reorder_columns, query=query)
    await report.generate_dataframe_with_query(schema.MembershipDownloadResponse,
                                               multiple_join=['organization', 'user'])
    await report.operation('full_name', lambda x, y: f'{x} {y}', 'first_name', 'last_name')
    await report.datetime_reformat('created_at')
    res = await report.generate_report()
    return res


@router.get("/logo/{organization_id}", status_code=200)
async def get_organization_logo(organization_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Function enables admin to get logo of particular organization
    """
    try:
        db_org = crud.get_organization_logo(dbsession, organization_id)
        return Response(content=db_org.logo, media_type=f'image/{db_org.logo_format_type}')
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/logo/{organization_id}", response_model=schema.OrganizationResponse, status_code=200)
async def update_organization_logo(organization_id: UUID, file: UploadFile = File(...),
                                   dbsession: SessionLocal = Depends(create_session)):
    """
    Function enables admin to update logo of particular organization
    """
    try:
        if file.content_type not in ['image/png', 'image/jpeg', 'image/jpg']:
            raise HTTPException(400, 'The uploaded file is not in PNG, JPEG, or JPG format .')
        content_type = file.content_type.split('/')[1]
        file.filename = f'{organization_id}.{content_type}'
        logo = await file.read()
        is_svg = is_disallowed_image(logo, file.filename)
        if is_svg:
            raise HTTPException(400, 'The uploaded file is not in PNG, JPEG, or JPG format .')
        org = crud.update_organization_logo(dbsession, logo, organization_id, content_type)
        return org
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.patch("/{organization_id}/regenerate-api-key", response_model=schema.OrganizationAuthenticationService)
async def regenerate_api_key(request: Request, organization_id: UUID,
                             dbsession: SessionLocal = Depends(create_session)):
    """
    Superuser regenerate API Key for the specific organization
    :param str organization_id: Target Organization ID
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        is_superuser = crud.get_user_by_id(dbsession, auth_token_data['user_id']).is_superuser
        if not is_superuser:
            raise HTTPException(400,
                                'You do not have rights to regenerate API Key, '
                                'please contact Administrator.')
        new_org_auth_svc = crud.regenerate_api_key(dbsession, organization_id)
        return new_org_auth_svc
    except (exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloNonStaffMembershipError,
            exceptions.ApolloOperationOnRootOrganization) as e:
        raise HTTPException(400, e.__str__())


@router.delete("/logo/{organization_id}", status_code=204)
async def delete_organization_logo(organization_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Function enables admin to delete logo of particular organization
    """
    try:
        db_org = crud.get_organization_by_id(dbsession, organization_id)
        if db_org.logo is None:
            raise HTTPException(400, 'Organization does not have its logo set.')
        crud.update_organization_logo(dbsession, None, organization_id, None)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Special endpoint to retrieve all roles to not cause path error
@router.get("/roles", response_model=List[schema.RoleResponse])
async def list_all_roles(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Fetches all accessible roles depending on privileges of accessing user
    """
    roles = crud.get_all_roles_list(dbsession)
    for role in roles:
        role.user_count = sum(1 for membership in role.memberships if not membership.is_deleted)
    return roles


@router.get("/{organization_id}", response_model=schema.OrganizationResponse)
async def get_organization(request: Request, organization_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Fetches a single organization object

    :param str organization_id: Target Organization ID
    """
    try:
        res = crud.get_organization_by_id(dbsession, organization_id)
        return res
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.patch("/{organization_id}", response_model=schema.OrganizationResponse)
async def update_organization(request: Request, organization_id: UUID, data: schema.OrganizationUpdate,
                              dbsession: SessionLocal = Depends(create_session)):
    """
    Partially updates an Organization object

    :param str organization_id: Target Organization ID
    """
    try:
        org = crud.update_organization(dbsession, data, organization_id)
        return org
    except (exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloNonStaffMembershipError,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloOrganizationDuplication) as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{organization_id}", status_code=204)
async def delete_organization(request: Request, organization_id: UUID,
                              dbsession: SessionLocal = Depends(create_session)):
    """
    Deletes an Organization object

    :param str organization_id: Target Organization ID
    """
    try:
        operator_id = crud.get_operator_by_organization_id(dbsession, organization_id)
        if operator_id:
            crud.delete_operator(dbsession, operator_id.id)
        memberships = crud.get_organization_membership_list(dbsession, organization_id)
        if memberships:
            crud.delete_all_memberships_from_organization(dbsession, organization_id)
        crud.delete_organization(dbsession, organization_id)
        return None
    except (exceptions.ApolloObjectDoesNotExist, exceptions.ApolloOperationOnRootOrganization) as e:
        raise HTTPException(400, e.__str__())


@router.get("/audit/log", response_model=Page[schema.UserAuditLogResponse])
async def get_user_audit_logs(request: Request, user: str = None,
                              from_date: datetime = None, to_date: datetime = None,
                              module: str = None, action: str = None,
                              params: Params = Depends(), dbsession: SessionLocal = Depends(create_session)):
    """
    User Audit Log
    """
    query = crud.get_audit_log(dbsession, params, from_date, to_date, user, module=module, action=action)
    return query


@router.get("/audit/log/{group_id}", response_model=schema.UserAuditLogResponse)
async def get_user_audit_log(request: Request, group_id: str,
                             dbsession: SessionLocal = Depends(create_session)):
    """
    User Audit Log by group id
    """
    params = Params()
    query = crud.get_audit_log(dbsession, params, group_id=group_id)
    items = query.get('items', [{}])
    return items[0]
