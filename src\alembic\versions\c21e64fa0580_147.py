"""147

Revision ID: c21e64fa0580
Revises: 47108ae7b6c5
Create Date: 2025-02-19 14:59:52.319247

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c21e64fa0580'
down_revision = '47108ae7b6c5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('unique_autocharge_mac_address', 'main_autocharge', ['mac_address', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('unique_autocharge_mac_address', table_name='main_autocharge', postgresql_where=sa.text('NOT is_deleted'))
    # ### end Alembic commands ###
