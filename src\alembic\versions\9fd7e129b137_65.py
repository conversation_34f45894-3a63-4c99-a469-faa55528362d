"""65

Revision ID: 9fd7e129b137
Revises: 540a5fbafb27
Create Date: 2023-11-21 15:41:11.941651

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9fd7e129b137'
down_revision = '540a5fbafb27'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_credit_card', sa.Column('bill_name', sa.String(), nullable=True))
    op.add_column('main_credit_card', sa.Column('bill_email', sa.String(), nullable=True))
    op.add_column('main_credit_card', sa.Column('bill_mobile', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_credit_card', 'bill_mobile')
    op.drop_column('main_credit_card', 'bill_email')
    op.drop_column('main_credit_card', 'bill_name')
    # ### end Alembic commands ###
