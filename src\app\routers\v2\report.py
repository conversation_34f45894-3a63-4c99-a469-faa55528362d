import logging
import json
import os

from tempfile import NamedTemporaryFile
import requests

from fastapi import APIRouter, HTTPException, Depends, Request, status, BackgroundTasks
from fastapi.responses import FileResponse
from jose import jwt
from starlette.responses import JSONResponse

from app import settings, crud, exceptions, schema, models
from app.crud import get_payment_request_by_charging_session_bill_id, get_charging_session_bill_by_charging_session, \
    get_e_credit_note_by_id, get_payment_refund_by_refund_id, get_payment_request, get_charging_session_bill, UserCRUD
from app.database import create_session, SessionLocal
from app.schema import v2 as mobile_schema, EInvoiceStatus, ECreditNoteStatus
from app.middlewares import set_member_as_context_user
from app.utils import generate_charger_header, \
    get_invoice_template_name, get_invoice_html, remove_table_borders, convert_html_to_pdf, GetResponseBase

from app.e_invoice_utils import validate_e_invoice_status_for_mobile_display, login_to_rmp, \
    validate_e_credit_note_status_for_mobile_display

logger = logging.getLogger(__name__)

CHARGER_URL_PREFIX_V2 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'
ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/invoice",
    tags=['v2 invoice'],
)


def cleanup_temp_file(path: str):
    try:
        os.remove(path)
    except Exception as e:  # pylint: disable=broad-except
        print(f"Failed to delete temp file: {e}")


@router.get("/ocpi/pdf/download/", status_code=status.HTTP_200_OK)
async def get_invoice(request: Request, key: str, generate_pdf: bool = False,  # noqa: MC0001
                      dbsession: SessionLocal = Depends(create_session)):
    """
    Get an Invoice, return PDF if its required

    :param str key: Bearer token that consists of information needed
    """
    # pylint: disable=too-many-nested-blocks
    if key:
        try:
            auth_token_data = jwt.decode(key, key=settings.JWT_REPORT_SECRET,
                                         algorithms=[schema.JWT_ALGORITHM, ],
                                         options={"verify_signature": True, "verify_exp": True})

            membership_id = auth_token_data.get('membership_id')
            headers = generate_charger_header(dbsession, membership_id)
            logger.debug('header for request to charger service: %s', headers)
            set_member_as_context_user(dbsession, membership_id)
            charging_session_id = auth_token_data.get('charging_session_id')
            try:
                invoice = await crud.get_invoice(dbsession, charging_session_id, headers)
                if invoice is None:
                    raise HTTPException(status_code=404, detail=f"Invoice {charging_session_id} not found")
                if generate_pdf:
                    try:
                        template_name = get_invoice_template_name(invoice, None)  # Pass appropriate reference if needed
                        # Determine organization-specific templates
                        db_member = crud.get_membership_by_id(dbsession, str(invoice.member.id))
                        if db_member.organization.name.lower() == settings.YGT_ORGANIZATION_NAME.lower():
                            if "_sg" in template_name:
                                template_name = template_name.replace("_sg", "_sg_ygt")
                            else:
                                template_name = template_name.replace(".html", "_ygt.html")
                        html = get_invoice_html(invoice.dict(), template_name)

                    except Exception:  # pylint: disable=broad-except
                        # A simplified exception fallback
                        if invoice.connector.billing_currency == 'SGD':
                            html = get_invoice_html(invoice.dict(), 'invoice_v2_sg.html')
                        elif invoice.connector.billing_currency == 'KHR':
                            html = get_invoice_html(invoice.dict(), 'invoice_v2_kh.html')
                        elif invoice.connector.billing_currency == 'BND':
                            html = get_invoice_html(invoice.dict(), 'invoice_v2_bn.html')
                        elif invoice.connector.billing_currency == 'IDR':
                            html = get_invoice_html(invoice.dict(), 'invoice_v2_idr.html')
                        else:
                            html = get_invoice_html(invoice.dict(), 'invoice_v2.html')

                    try:
                        # Remove borders from all tables in the HTML
                        html = remove_table_borders(html)

                        generated_pdf = convert_html_to_pdf(
                            html,
                            charging_session_id,
                            invoice.connector.billing_currency
                        )
                        return generated_pdf

                    except Exception as e:
                        logger.error('Error generating PDF: %s', e)
                        raise HTTPException(status_code=500, detail="Failed to generate PDF")

                return invoice

            except exceptions.ApolloObjectDoesNotExist:
                data = {"error": {"code": 4040, "message": 'Charging Session Not Found'}}
                generic_response = GetResponseBase[str](**data)
                return JSONResponse(content=generic_response.return_dict())
        except jwt.JWTError:
            return JSONResponse(status_code=400, content={'detail': 'Token validation error'})
    else:
        raise HTTPException(status_code=400, detail="No key provided")


@router.get("/order/pdf/download/", status_code=status.HTTP_200_OK)
async def get_commercial_invoice(request: Request, key: str, generate_pdf: bool = False,  # noqa: MC0001
                                 dbsession: SessionLocal = Depends(create_session)):
    """
    Get a Commercial Invoice, return PDF if its required

    :param str key: Bearer token that consists of information needed
    """
    # pylint: disable=too-many-nested-blocks
    if key:
        try:
            auth_token_data = jwt.decode(key, key=settings.JWT_REPORT_SECRET,
                                         algorithms=[schema.JWT_ALGORITHM, ],
                                         options={"verify_signature": True, "verify_exp": True})

            membership_id = auth_token_data.get('membership_id')
            set_member_as_context_user(dbsession, membership_id)
            subscription_invoice_id = auth_token_data.get('subscription_invoice_id')
            try:
                db_commercial_invoice = crud.CommercialInvoiceCRUD.query(dbsession) \
                    .filter(models.CommercialInvoice.id == subscription_invoice_id).one()
                commercial_invoice = mobile_schema.MobileSubscriptionInvoice.from_orm(db_commercial_invoice).dict()
                if generate_pdf:
                    template_name = "subscription_invoice.html"
                    html = get_invoice_html(commercial_invoice, template_name)

                    try:
                        # Remove borders from all tables in the HTML
                        html = remove_table_borders(html)

                        generated_pdf = convert_html_to_pdf(
                            html,
                            subscription_invoice_id,
                            db_commercial_invoice.currency
                        )
                        return generated_pdf

                    except Exception as e:
                        logger.error('Error generating PDF: %s', e)
                        raise HTTPException(status_code=500, detail="Failed to generate PDF")

                return db_commercial_invoice

            except exceptions.ApolloObjectDoesNotExist:
                data = {"error": {"code": 4040, "message": 'Subscription Invoice Not Found'}}
                generic_response = GetResponseBase[str](**data)
                return JSONResponse(content=generic_response.return_dict())
        except jwt.JWTError:
            return JSONResponse(status_code=400, content={'detail': 'Token validation error'})
    else:
        raise HTTPException(status_code=400, detail="No key provided")


@router.get("/e_invoice/pdf/download/", status_code=status.HTTP_200_OK)
async def download_validated_e_invoice_pdf(key: str,  # noqa: MC0001
                                           background_tasks: BackgroundTasks,
                                           dbsession: SessionLocal = Depends(create_session)):
    """
    Download validated e-Invoice

    :param str key: Bearer token that consists of information needed
    """
    if key:
        try:
            auth_token_data = jwt.decode(key, key=settings.JWT_REPORT_SECRET,
                                         algorithms=[schema.JWT_ALGORITHM, ],
                                         options={"verify_signature": True, "verify_exp": True})

            membership_id = auth_token_data.get('membership_id')
            set_member_as_context_user(dbsession, membership_id)
            charging_session_id = auth_token_data.get('charging_session_id')

            e_invoice = None
            charging_session_bill = get_charging_session_bill_by_charging_session(dbsession, charging_session_id)
            if not charging_session_bill:
                raise HTTPException(403, "No invoice generated, cannot request for e-invoice.")

            if charging_session_bill:
                charging_session_bill = validate_e_invoice_status_for_mobile_display(dbsession, charging_session_bill,
                                                                                     update_data=False)
                e_invoice = charging_session_bill.e_invoice
            db_pr = get_payment_request_by_charging_session_bill_id(dbsession, charging_session_bill.id)
            db_member_id = str(db_pr.member_id)

            if db_member_id != membership_id:
                raise HTTPException(403, "Invoice owner missmatch, rejecting.")

            if e_invoice is not None:
                if e_invoice.status != EInvoiceStatus.validated:
                    raise HTTPException(403, "E-Invoice is not ready to be downloaded.")

                base_url = settings.E_INVOICE_API_BASE_URL
                token = await login_to_rmp()
                token_headers = {
                    'Authorization': f'Bearer {token}',
                    'Content-Type': 'application/json'
                }

                pdf_retrieval_url = f"{base_url}/api/pdfretrieval"

                e_invoice_number = e_invoice.e_invoice_number
                e_invoice_datetime = e_invoice.e_invoice_date_time.strftime("%Y-%m-%dT%H:%M:%S")

                payload = {
                    "eInvoiceNumber": e_invoice_number,
                    "eInvoiceDateTime": e_invoice_datetime,
                    "lhdnId": e_invoice.lhdn_uid
                }
                response = requests.request("POST", pdf_retrieval_url, headers=token_headers,
                                            data=json.dumps(payload))

                # After response = requests.post(...)
                if response.status_code != 200:
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"PDF retrieval failed: {response.text}"
                    )

                # Check for valid PDF
                if not response.content.startswith(b"%PDF-"):
                    raise HTTPException(status_code=400, detail="E-Invoice cannot be downloaded at this time.")

                if response.headers.get("Content-Type") != "application/pdf":
                    raise HTTPException(status_code=400, detail="E-Invoice cannot be downloaded at this time.")

                # Proceed to save as before
                with NamedTemporaryFile(suffix=".pdf", delete=False) as temp_pdf:
                    temp_pdf.write(response.content)
                    temp_pdf_path = temp_pdf.name

                background_tasks.add_task(cleanup_temp_file, temp_pdf_path)
                return FileResponse(
                    path=temp_pdf_path,
                    media_type="application/pdf",
                    filename=f"{e_invoice_number}.pdf",
                    headers={"Content-Disposition": f"inline; filename={e_invoice_number}.pdf"}
                )

            raise HTTPException(403, "No e-invoice can be downloaded at this time.")
        except exceptions.ApolloObjectDoesNotExist as e:
            raise HTTPException(400, e.__str__())
        except jwt.JWTError:
            return JSONResponse(status_code=400, content={'detail': 'Token validation error'})
    else:
        raise HTTPException(status_code=400, detail="No key provided")


@router.get("/e_credit_note/pdf/download/", status_code=status.HTTP_200_OK)
async def download_validated_e_cn_pdf(key: str,  # noqa: MC0001
                                      background_tasks: BackgroundTasks,
                                      dbsession: SessionLocal = Depends(create_session)):
    """
    Download validated e-Credit Note

    :param str key: Bearer token that consists of information needed
    """
    if key:
        try:
            auth_token_data = jwt.decode(key, key=settings.JWT_REPORT_SECRET,
                                         algorithms=[schema.JWT_ALGORITHM, ],
                                         options={"verify_signature": True, "verify_exp": True})

            membership_id = auth_token_data.get('membership_id')
            set_member_as_context_user(dbsession, membership_id)
            credit_note_id = auth_token_data.get('credit_note_id')

            db_credit_note = get_e_credit_note_by_id(dbsession, credit_note_id)

            if not db_credit_note:
                raise HTTPException(403, "No e-credit note available")

            db_payment_refund = get_payment_refund_by_refund_id(dbsession, str(db_credit_note.payment_refund_id))
            db_pr = get_payment_request(dbsession, str(db_payment_refund.payment_request_id))
            charging_session_bill = get_charging_session_bill(dbsession, str(db_pr.charging_session_bill_id))
            db_member_id = str(db_pr.member_id)

            if db_member_id != membership_id:
                raise HTTPException(403, "E-Credit Note owner missmatch, rejecting.")

            # db_credit_note = schema.ECreditNoteWithDownloadURLResponse.from_orm(db_credit_note)

            e_credit_note = validate_e_credit_note_status_for_mobile_display(dbsession,
                                                                             charging_session_bill,
                                                                             db_credit_note,
                                                                             update_data=False)

            if e_credit_note.status != ECreditNoteStatus.validated:
                raise HTTPException(403, "E-Credit Note is not ready to be downloaded.")

            base_url = settings.E_INVOICE_API_BASE_URL
            token = await login_to_rmp()
            token_headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }

            pdf_retrieval_url = f"{base_url}/api/pdfretrieval"

            e_credit_note_number = e_credit_note.e_credit_note_number
            e_credit_note_date_time = e_credit_note.e_credit_note_date_time.strftime("%Y-%m-%dT%H:%M:%S")

            payload = {
                "eInvoiceNumber": e_credit_note_number,
                "eInvoiceDateTime": e_credit_note_date_time,
                "lhdnId": e_credit_note.lhdn_uid
            }
            response = requests.request("POST", pdf_retrieval_url, headers=token_headers,
                                        data=json.dumps(payload))

            if response.status_code != 200:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"PDF retrieval failed: {response.text}"
                )

            # Check for valid PDF
            if not response.content.startswith(b"%PDF-"):
                raise HTTPException(status_code=400, detail="E-Credit Note cannot be downloaded at this time.")

            if response.headers.get("Content-Type") != "application/pdf":
                raise HTTPException(status_code=400, detail="E-Credit Note cannot be downloaded at this time.")

            # Proceed to save as before
            with NamedTemporaryFile(suffix=".pdf", delete=False) as temp_pdf:
                temp_pdf.write(response.content)
                temp_pdf_path = temp_pdf.name

            background_tasks.add_task(cleanup_temp_file, temp_pdf_path)
            return FileResponse(
                path=temp_pdf_path,
                media_type="application/pdf",
                filename=f"{e_credit_note_number}.pdf",
                headers={"Content-Disposition": f"inline; filename={e_credit_note_number}.pdf"}
            )

        except exceptions.ApolloObjectDoesNotExist as e:
            raise HTTPException(400, e.__str__())
        except jwt.JWTError:
            return JSONResponse(status_code=400, content={'detail': 'Token validation error'})
    else:
        raise HTTPException(status_code=400, detail="No key provided")


@router.get("/pdf/refund/download/", status_code=status.HTTP_200_OK)
async def get_commercial_invoice(request: Request, key: str, generate_pdf: bool = False,  # noqa: MC0001
                                 dbsession: SessionLocal = Depends(create_session)):
    """
    Get a Commercial Invoice, return PDF if its required

    :param str key: Bearer token that consists of information needed
    """
    # pylint: disable=too-many-nested-blocks
    if key:
        try:
            auth_token_data = jwt.decode(key, key=settings.JWT_REPORT_SECRET,
                                         algorithms=[schema.JWT_ALGORITHM, ],
                                         options={"verify_signature": True, "verify_exp": True})

            membership_id = auth_token_data.get('membership_id')
            set_member_as_context_user(dbsession, membership_id)
            db_member = crud.get_membership_by_id(dbsession, membership_id)
            payment_refund_id = auth_token_data.get('payment_refund_id')

            login_user = UserCRUD.get(dbsession, db_member.user_id)
            headers = generate_charger_header(dbsession, membership_id)
            logger.debug('header for request to charger service: %s', headers)

            db_payment_refund = get_payment_refund_by_refund_id(dbsession, str(payment_refund_id))
            db_pr = get_payment_request(dbsession, str(db_payment_refund.payment_request_id))
            db_member_id = str(db_pr.member_id)

            if db_member_id != membership_id:
                raise HTTPException(403, "E-Credit Note owner missmatch, rejecting.")
            try:
                credit_note = await crud.get_single_credit_note(dbsession, payment_refund_id, headers,
                                                                login_user)
                if generate_pdf:
                    try:
                        template_name = 'credit_note.html'
                        html = get_invoice_html(credit_note.dict(), template_name)
                    except Exception:  # pylint: disable=broad-except
                        html = get_invoice_html(credit_note.dict(), 'credit_note.html')
                    try:
                        # Remove borders from all tables in the HTML
                        html = remove_table_borders(html)

                        generated_pdf = convert_html_to_pdf(
                            html,
                            payment_refund_id,
                            db_pr.currency
                        )
                        return generated_pdf

                    except Exception as e:
                        logger.error('Error generating PDF: %s', e)
                        raise HTTPException(status_code=500, detail="Failed to generate PDF")

                return credit_note

            except exceptions.ApolloObjectDoesNotExist:
                data = {"error": {"code": 4040, "message": 'Subscription Invoice Not Found'}}
                generic_response = GetResponseBase[str](**data)
                return JSONResponse(content=generic_response.return_dict())
        except jwt.JWTError:
            return JSONResponse(status_code=400, content={'detail': 'Token validation error'})
    else:
        raise HTTPException(status_code=400, detail="No key provided")
