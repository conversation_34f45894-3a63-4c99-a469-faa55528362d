"""117

Revision ID: 0745b66dab3b
Revises: 4b7d6c8132a9
Create Date: 2024-08-22 15:40:06.247311

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0745b66dab3b'
down_revision = '4b7d6c8132a9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('provider_log',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('url', sa.String(), nullable=True),
    sa.Column('http_method', sa.String(), nullable=True),
    sa.Column('request_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('request_header', sa.String(), nullable=True),
    sa.Column('request_body', sa.String(), nullable=True),
    sa.Column('response_status_code', sa.String(), nullable=True),
    sa.Column('response_body', sa.String(), nullable=True),
    sa.Column('response_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('provider_log_created_at_idx', 'provider_log', ['created_at'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('provider_log_created_at_idx', table_name='provider_log')
    op.drop_table('provider_log')
    # ### end Alembic commands ###
