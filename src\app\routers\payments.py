# pylint:disable=too-many-lines
from datetime import datetime, timedelta
import json
import logging
import re
import urllib
from decimal import ROUND_HALF_UP, Decimal
from typing import List, Union
from uuid import UUID
from functools import partial

import pytz
import httpx
from fastapi import APIRouter, Depends, Request, HTTPException, Query, status
from fastapi.responses import RedirectResponse
from fastapi.responses import J<PERSON>NResponse, HTMLResponse
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.orm import Session

from app import settings, schema, crud, exceptions, models
from app.celery import app
from app.crud import log_audit_trail, PaymentRequestCRUD
from app.crud.reporting_task import create_reporting_task_record
from app.database import create_session, SessionLocal
from app.constants import (
    PAYMENT_IPN_URL, PAYMENT_DIRECT_API_URL, SG_PAYMENT_IPN_URL
    # BN_PAYMENT_IPN_URL, KH_PAYMENT_IPN_URL
)
from app.e_invoice_utils import construct_and_submit_credit_note_to_rmp
from app.schema import ReportingTaskCreate
from app.schema.payment import (
    DashboardType,
    Currency)
from app.utils import (
    calculate_md5_hash, decode_auth_token_from_headers, generate_charger_header,
    # send_invoice_mail,
    send_request,
    get_cc_html_template,
    get_charger_utilization_rate,
    get_kw_report,
    get_total_transactions,
    CHARGER_URL_PREFIX, send_recurring_payment_my, send_recurring_payment_sg,
    GenerateReport,
    # send_welcome_email_for_bmw_and_mini_sub_plan, get_wallet_by_member_id_currency,
    nested_dict, process_razerpay_reconcilation, update_pr_status_on_failed, update_pr_status_on_success,
    send_recurring_payment_bn, send_recurring_payment_kh, payment_request_done_partial_cc,
    get_all_emsp_cdr, update_payment_request
)
# from app.ruby_proxy_utils import sync_ruby_membership
from app.permissions import permission
from app.middlewares import set_admin_as_context_user, deactivate_audit
from app.lta_tasks import send_emsp_cdr_via_email_sync

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/payment",
    tags=['payment', ],
)


def is_valid_uuid(val):
    try:
        UUID(str(val))
        return True
    except ValueError:
        return False


def get_current_date_start():
    return datetime.now(pytz.timezone('Asia/Kuala_Lumpur')).replace(hour=0, minute=0, second=0, microsecond=0)


def get_current_date_end():
    return datetime.now(pytz.timezone('Asia/Kuala_Lumpur')).replace(hour=23, minute=59, second=59, microsecond=0)


async def filter_dashboard_energy(types: DashboardType = Query(default=DashboardType.Day),  # noqa
                                  date_start: datetime = Query(default=None),  # noqa
                                  date_end: datetime = Query(default=None)):  # noqa  # noqa
    if not date_start:
        date_start = get_current_date_start()
    if not date_end:
        date_end = get_current_date_end()
    return {'type': types, 'date_start': date_start, 'date_end': date_end}


async def charger_utilization_filters(
        type: schema.ChargerUtilizationFilterType = Query(schema.ChargerUtilizationFilterType.days),  # noqa
        date_start: datetime = Query(default=None),  # noqa
        date_end: datetime = Query(default=None)):  # noqa
    if not date_start:
        date_start = get_current_date_start()
    if not date_end:
        date_end = get_current_date_end()
    return {'type': type, 'date_start': date_start, 'date_end': date_end}


async def total_transaction_filters(
        types: schema.TotalTransactionType = Query(default=schema.TotalTransactionType.days),  # noqa
        date_start: datetime = Query(default=None),  # noqa
        date_end: datetime = Query(default=None)):  # noqa
    if not date_start:
        date_start = get_current_date_start()
    if not date_end:
        date_end = get_current_date_end()
    return {'type': types, 'date_start': date_start, 'date_end': date_end}


async def date_filters(
        date_start: datetime = Query(default=None),  # noqa
        date_end: datetime = Query(default=None)):  # noqa
    if not date_start:
        date_start = get_current_date_start()
    if not date_end:
        date_end = get_current_date_end()
    return {'date_start': date_start, 'date_end': date_end}


def emsp_cdr_filters(date_from: datetime = None, date_to: datetime = None, invoice: str = None,
                     party_id: str = None, evse_id: str = None, country_code: str = None,
                     token: str = None, id: str = None, currency: str = None, partner_name: str = None,
                     cdr_id: str = None, partner_session_uid: str = None,
                     native_session_id: str = None):
    return {'date_from': date_from, 'date_to': date_to, 'transaction_id': invoice,
            'party_id': party_id, 'evse_id': evse_id, 'country_code': country_code,
            'cdr_token': token, 'id': id, 'currency': currency, 'partner_name': partner_name, 'cdr_id': cdr_id,
            'partner_session_uid': partner_session_uid, 'native_session_id': native_session_id}


def reconcilation_filter(payment_type: str = None, type: str = None, invoice_generated: bool = None,
                         billing_status: str = None, currency: str = None,
                         partner: str = None, operator: str = None, country: str = None,
                         order_id: str = None, username: str = None, charge_point_id: str = None,
                         date_from: datetime = None, date_to: datetime = None):
    return {'payment_type': payment_type, 'type': type, 'invoice_generated': invoice_generated,
            'billing_status': billing_status, 'currency': currency,
            'partner': partner, 'operator': operator, 'country': country, 'order_id': order_id,
            'username': username, 'charge_point_id': charge_point_id,
            'date_from': date_from, 'date_to': date_to}


def payment_gateway_reconcilation_filter(invoice_number: str = None, email: str = None, phone_number: str = None,
                                         order_id: str = None, tran_id: str = None, channel: str = None,
                                         stat_code: str = None, stat_name: str = None, currency: str = None,
                                         billing_status: str = None, date_from: datetime = None,
                                         date_to: datetime = None):
    return {'invoice_number': invoice_number, 'email': email, 'phone_number': phone_number, 'order_id': order_id,
            'tran_id': tran_id, 'channel': channel, 'stat_code': stat_code, 'stat_name': stat_name,
            'currency': currency, 'billing_status': billing_status, 'date_from': date_from, 'date_to': date_to}


def filter_emsp(query, filters):
    date_from = filters.pop('date_from', None)
    date_to = filters.pop('date_to', None)
    if date_from:
        query = models.filter_query(query, models.OCPICPOCdr.start_date_time >= date_from)
    if date_to:
        query = models.filter_query(query, models.OCPICPOCdr.start_date_time <= date_to)
    if filters:
        for key, value in filters.items():
            attr = getattr(models.OCPICPOCdr, key)
            if value:
                if key == 'cdr_token':
                    query = models.filter_query(query, attr["uid"].astext.ilike(f'%{value}%'))
                else:
                    query = models.filter_query(query, attr.ilike(f'%{value}%'))
    return query


@router.post('/payment-return-url', include_in_schema=False)
async def payment_return_url(request: Request, dbsession: SessionLocal = Depends(create_session)):  # noqa
    """
    Payment Return URL, return for payment gateway to Mobile App
    """
    logger.info('payment-return-url')
    set_admin_as_context_user(dbsession)
    deactivate_audit()
    form_data = await request.form()
    data = dict(form_data)
    payment_response = schema.PaymentReturnURL(**data)

    pre_skey = calculate_md5_hash(
        f'{payment_response.tran_id}{payment_response.order_id}{payment_response.status}'
        f'{payment_response.domain}{payment_response.amount}{payment_response.currency}'
    )
    skey = calculate_md5_hash(
        f'{payment_response.paydate}{payment_response.domain}'
        f'{pre_skey}{payment_response.appcode}{settings.SECRET_KEY}'
    )
    if payment_response.skey != skey:
        logger.error('skey mismatch for order id: %s',
                     payment_response.order_id)
        raise HTTPException(400, 'skey mismatch')

    async with httpx.AsyncClient() as client:
        data = payment_response.dict(by_alias=True)
        data['treq'] = 1
        await client.post(PAYMENT_IPN_URL, data=data)
        # if KH_PAYMENT_IPN_URL is not None:
        #     await client.post(KH_PAYMENT_IPN_URL, data=data)

    if is_valid_uuid(payment_response.order_id):
        db_pr = crud.get_payment_request(dbsession, payment_response.order_id)
    else:
        db_pr = crud.get_payment_reqeust_by_invoice_number(dbsession, payment_response.order_id)

    if payment_response.status == schema.PaymentStatus.successful:
        try:
            if db_pr.update_token:
                db_member = crud.get_membership_by_id(dbsession, db_pr.member_id)
                db_user = crud.get_user_by_id(dbsession, db_member.user_id)
                try:
                    crud.check_member_cc_token_is_not_blacklisted(dbsession, db_pr.member_id,
                                                                  payment_response.extra_p,
                                                                  db_pr.currency, db_user)
                except exceptions.ApolloBlacklistedCreditCard:
                    return HTMLResponse(content=get_cc_html_template(False, db_pr.reason, reason='Blacklisted'),
                                        status_code=200)

        except exceptions.ApolloObjectDoesNotExist:
            logger.error('Member credit card information not found')

        return HTMLResponse(content=get_cc_html_template(True, db_pr.reason, save_cc=db_pr.save_credit_card),
                            status_code=200, )
    if payment_response.status == schema.PaymentStatus.failure:
        if db_pr.update_token:
            return HTMLResponse(content=get_cc_html_template(False, db_pr.reason, reason=payment_response.error_desc,
                                                             reason_code=payment_response.error_code,
                                                             save_cc=db_pr.save_credit_card),
                                status_code=200)

        return HTMLResponse(
            content=get_cc_html_template(False, schema.PaymentRequestReason.payment, save_cc=db_pr.save_credit_card
                                         ), status_code=200)


@router.post("/payment-callback", include_in_schema=False)
async def payment_callback(request: Request, dbsession: SessionLocal = Depends(create_session)):  # noqa: MC0001
    """
    Get Payment Response Callback
    """
    logger.info('payment-callback')
    # Log form data
    admin = set_admin_as_context_user(dbsession)  # noqa
    deactivate_audit()
    form_data = await request.form()
    data = dict(form_data)
    payment_response = schema.PaymentCallback(**data)
    crud.create_payment_callback(dbsession, payment_response)
    pre_skey = calculate_md5_hash(
        f'{payment_response.tran_id}{payment_response.order_id}{payment_response.status}'
        f'{payment_response.domain}{payment_response.amount}{payment_response.currency}'
    )
    skey = calculate_md5_hash(
        f'{payment_response.paydate}{payment_response.domain}'
        f'{pre_skey}{payment_response.appcode}{settings.SECRET_KEY}'
    )
    if payment_response.skey != skey:
        logger.error('skey mismatch for order id: %s',
                     payment_response.order_id)
        raise HTTPException(400, 'skey mismatch')

    async with httpx.AsyncClient() as client:
        data = payment_response.dict(by_alias=True)
        data['treq'] = 1
        await client.post(PAYMENT_IPN_URL, data=data)
        # if KH_PAYMENT_IPN_URL is not None:
        #     await client.post(KH_PAYMENT_IPN_URL, data=data)

    if payment_response.status == schema.PaymentStatus.successful:
        if re.match(r'^[A-Za-z0-9]+-[A-Za-z0-9]+-[A-Za-z0-9]+-[A-Za-z0-9]+$', payment_response.order_id):
            db_pr = crud.get_payment_request_by_outstanding_order_id(dbsession, payment_response.order_id)
            payment_request_outstanding_update = schema.PaymentRequestOutstandingUpdate(
                pre_auth_outstanding_capture_status=schema.PaymentRequestStatus.done
            )
            update_payment_request(dbsession, payment_request_outstanding_update, db_pr.id)
            db_charging_bill = crud.get_charging_session_bill(dbsession, db_pr.charging_session_bill_id)

            # this if statement is to prevent negative outstanding balance where pg sometimes callback more than
            # once for each transaction
            if db_charging_bill.outstanding_balance > 0.00:
                outstanding_balance = Decimal(db_charging_bill.outstanding_balance) - Decimal(payment_response.amount)
                crud.update_charging_bill_outstanding(dbsession, db_pr.charging_session_bill_id, outstanding_balance)
                crud.payment_request_done(dbsession, db_pr.id)
                crud.charging_bill_done(dbsession, db_pr.charging_session_bill_id)
        else:
            if is_valid_uuid(payment_response.order_id):
                db_pr = crud.get_payment_request(dbsession, payment_response.order_id)
            else:
                db_pr = crud.get_payment_reqeust_by_invoice_number(dbsession, payment_response.order_id)

            if db_pr.status == schema.PaymentRequestStatus.done:
                return db_pr
            if db_pr.type == schema.PaymentRequestType.partial or \
                    db_pr.type == schema.PaymentRequestType.partial_direct:
                payment_request_done_partial_cc(dbsession, db_pr.id)
            db_pr = crud.payment_request_done(dbsession, db_pr.id)
            await update_pr_status_on_success(dbsession, db_pr, payment_response)

    elif payment_response.status == schema.PaymentStatus.failure:
        if re.match(r'^[A-Za-z0-9]+-[A-Za-z0-9]+-[A-Za-z0-9]+-[A-Za-z0-9]+$', payment_response.order_id):
            db_pr = crud.get_payment_request_by_outstanding_order_id(dbsession, payment_response.order_id)
            payment_request_outstanding_update = schema.PaymentRequestOutstandingUpdate(
                pre_auth_outstanding_capture_status=schema.PaymentRequestStatus.failed
            )
            update_payment_request(dbsession, payment_request_outstanding_update, db_pr.id)
            crud.payment_request_failed(dbsession, db_pr.id)
            crud.charging_bill_failed(dbsession, db_pr.charging_session_bill_id)
        else:
            if is_valid_uuid(payment_response.order_id):
                db_pr = crud.get_payment_request(dbsession, payment_response.order_id)
            else:
                db_pr = crud.get_payment_reqeust_by_invoice_number(dbsession, payment_response.order_id)

            if db_pr.status == schema.PaymentRequestStatus.done:
                return db_pr
            await update_pr_status_on_failed(dbsession, db_pr, payment_response)


@router.get('/pre-auth/payment-return-url', include_in_schema=True)
async def pre_auth_payment_return_url(request: Request, payment_response: schema.PaymentPreAuthReturnURL = Depends(),
                                      dbsession: SessionLocal = Depends(create_session)):  # noqa
    """
    Payment Return URL, return for payment gateway to Mobile App
    """
    logger.info('payment-return-url')
    set_admin_as_context_user(dbsession)
    deactivate_audit()
    # payment_response = schema.PaymentReturnURL()

    pre_skey = calculate_md5_hash(
        f'{payment_response.tran_id}{payment_response.order_id}{payment_response.status}'
        f'{payment_response.domain}{payment_response.amount}{payment_response.currency}'
    )
    skey = calculate_md5_hash(
        f'{payment_response.paydate}{payment_response.domain}'
        f'{pre_skey}{payment_response.appcode}{settings.SECRET_KEY}'
    )
    if payment_response.skey != skey:
        logger.error('skey mismatch for order id: %s',
                     payment_response.order_id)
        raise HTTPException(400, 'skey mismatch')

    async with httpx.AsyncClient() as client:
        data = payment_response.dict(by_alias=True)
        data['treq'] = 1
        await client.post(PAYMENT_IPN_URL, data=data)
        # if KH_PAYMENT_IPN_URL is not None:
        #     await client.post(KH_PAYMENT_IPN_URL, data=data)

    if is_valid_uuid(payment_response.order_id):
        db_pr = crud.get_payment_request(dbsession, payment_response.order_id)
    else:
        db_pr = crud.get_payment_reqeust_by_invoice_number(dbsession, payment_response.order_id)

    if payment_response.status == schema.PaymentStatus.successful:
        try:
            if db_pr.reason == schema.PaymentRequestReason.pre_auth:
                crud.payment_request_done(dbsession, db_pr.id)
                db_pre_auth_payment = crud.get_pre_auth_payment_by_pr_id(dbsession, db_pr.id)
                pre_auth_payment_update = schema.PreAuthPaymentResponse(
                    is_successful=True,
                    transaction_id=payment_response.tran_id,
                    callback_response=json.loads(payment_response.json())
                )
                crud.update_pre_auth_payment(dbsession, db_pre_auth_payment.id, pre_auth_payment_update)
                message = {
                    'message_type': 'SUCCESS',
                    'pre_auth_id': db_pre_auth_payment.id
                }
                # send_charging_history_via_email_sync.delay(message)
                app.send_task(f'{settings.MAIN_SERVICE_TASKS_PREFIX}.notify_pre_auth',
                              kwargs={'message': message},
                              queue=settings.MAIN_SERVICE_WORKER_QUEUE,
                              routing_key=settings.MAIN_SERVICE_WORKER_ROUTE)

        except exceptions.ApolloObjectDoesNotExist:
            logger.error('Member credit card information not found')

        return HTMLResponse(content=get_cc_html_template(True, db_pr.reason, save_cc=db_pr.save_credit_card),
                            status_code=200)
    if payment_response.status == schema.PaymentStatus.failure:
        if db_pr.reason == schema.PaymentRequestReason.payment:
            # Return just saying Payment Failed
            return HTMLResponse(content=get_cc_html_template(False, schema.PaymentRequestReason.subscription,
                                                             reason=payment_response.error_desc,
                                                             reason_code=payment_response.error_code,
                                                             save_cc=db_pr.save_credit_card
                                                             ), status_code=200)

        return HTMLResponse(content=get_cc_html_template(False, schema.PaymentRequestReason.pre_auth,
                                                         reason=payment_response.error_desc,
                                                         reason_code=payment_response.error_code,
                                                         save_cc=db_pr.save_credit_card
                                                         ), status_code=200)


@router.post('/sg/payment-return-url', include_in_schema=False)
async def sg_payment_return_url(request: Request, dbsession: SessionLocal = Depends(create_session)):  # noqa
    """
    Payment Return URL, return for payment gateway to Mobile App
    """
    logger.info('payment-return-url')
    set_admin_as_context_user(dbsession)
    deactivate_audit()
    form_data = await request.form()
    data = dict(form_data)
    payment_response = schema.PaymentReturnURL(**data)

    pre_skey = calculate_md5_hash(
        f'{payment_response.tran_id}{payment_response.order_id}{payment_response.status}'
        f'{payment_response.domain}{payment_response.amount}{payment_response.currency}'
    )
    skey = calculate_md5_hash(
        f'{payment_response.paydate}{payment_response.domain}'
        f'{pre_skey}{payment_response.appcode}{settings.SG_SECRET_KEY}'
    )
    if payment_response.skey != skey:
        logger.error('skey mismatch for order id: %s',
                     payment_response.order_id)
        raise HTTPException(400, 'skey mismatch')

    async with httpx.AsyncClient() as client:
        data = payment_response.dict(by_alias=True)
        data['treq'] = 1
        await client.post(SG_PAYMENT_IPN_URL, data=data)
        # if BN_PAYMENT_IPN_URL is not None:
        #     await client.post(BN_PAYMENT_IPN_URL, data=data)

    if is_valid_uuid(payment_response.order_id):
        db_pr = crud.get_payment_request(dbsession, payment_response.order_id)
    else:
        db_pr = crud.get_payment_reqeust_by_invoice_number(dbsession, payment_response.order_id)

    if payment_response.status == schema.PaymentStatus.successful:
        if db_pr.update_token:
            db_member = crud.get_membership_by_id(dbsession, db_pr.member_id)
            db_user = crud.get_user_by_id(dbsession, db_member.user_id)
            try:
                crud.check_member_cc_token_is_not_blacklisted(dbsession, db_pr.member_id,
                                                              payment_response.extra_p,
                                                              db_pr.currency, db_user)
            except exceptions.ApolloBlacklistedCreditCard:
                return HTMLResponse(content=get_cc_html_template(False, db_pr.reason, reason='Blacklisted',
                                                                 save_cc=db_pr.save_credit_card),
                                    status_code=200)
        return HTMLResponse(content=get_cc_html_template(True, db_pr.reason,
                                                         save_cc=db_pr.save_credit_card),
                            status_code=200)
    if payment_response.status == schema.PaymentStatus.failure:
        return HTMLResponse(content=get_cc_html_template(False, schema.PaymentRequestReason.payment,
                                                         save_cc=db_pr.save_credit_card
                                                         ), status_code=200)


@router.post("/sg/payment-callback", include_in_schema=True)
async def sg_payment_callback(request: Request, dbsession: SessionLocal = Depends(create_session)):  # noqa: MC0001
    """
    Get Payment Response Callback
    """
    logger.info('payment-callback')
    admin = set_admin_as_context_user(dbsession)  # noqa
    deactivate_audit()
    form_data = await request.form()
    data = dict(form_data)
    payment_response = schema.PaymentCallback(**data)
    crud.create_payment_callback(dbsession, payment_response)
    pre_skey = calculate_md5_hash(
        f'{payment_response.tran_id}{payment_response.order_id}{payment_response.status}'
        f'{payment_response.domain}{payment_response.amount}{payment_response.currency}'
    )
    skey = calculate_md5_hash(
        f'{payment_response.paydate}{payment_response.domain}'
        f'{pre_skey}{payment_response.appcode}{settings.SG_SECRET_KEY}'
    )
    if payment_response.skey != skey:
        logger.error('skey mismatch for order id: %s',
                     payment_response.order_id)
        raise HTTPException(400, 'skey mismatch')

    async with httpx.AsyncClient() as client:
        data = payment_response.dict(by_alias=True)
        data['treq'] = 1
        await client.post(SG_PAYMENT_IPN_URL, data=data)
        # if BN_PAYMENT_IPN_URL is not None:
        #     await client.post(BN_PAYMENT_IPN_URL, data=data)

    if payment_response.status == schema.PaymentStatus.successful:
        if is_valid_uuid(payment_response.order_id):
            db_pr = crud.get_payment_request(dbsession, payment_response.order_id)
        else:
            db_pr = crud.get_payment_reqeust_by_invoice_number(dbsession, payment_response.order_id)

        if db_pr.status == schema.PaymentRequestStatus.done:
            return db_pr
        if db_pr.type == schema.PaymentRequestType.partial or db_pr.type == schema.PaymentRequestType.partial_direct:
            payment_request_done_partial_cc(dbsession, db_pr.id)
        db_pr = crud.payment_request_done(dbsession, db_pr.id)

        await update_pr_status_on_success(dbsession, db_pr, payment_response)

    elif payment_response.status == schema.PaymentStatus.failure:
        if is_valid_uuid(payment_response.order_id):
            db_pr = crud.get_payment_request(dbsession, payment_response.order_id)
        else:
            db_pr = crud.get_payment_reqeust_by_invoice_number(dbsession, payment_response.order_id)

        if db_pr.status == schema.PaymentRequestStatus.done:
            return db_pr

        await update_pr_status_on_failed(dbsession, db_pr, payment_response)


@router.post("/payment-request/init-charging", status_code=201,
             dependencies=[Depends(permission)], response_model=schema.PaymentRequestResponse)
async def create_pre_charging_payment_request(request: Request, pre_pr: schema.PaymentRequestPreCharging,
                                              dbsession: SessionLocal = Depends(create_session)):
    """
    Create Pre-Charging Payment Request

    :param PaymentRequestPreCharging pre_pr: Payment Request Data
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_member = crud.get_membership_by_id(dbsession, membership_id)
        pr = schema.PaymentRequest(
            amount='0.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=pre_pr.connector_id,
            meta=json.loads(schema.MembershipResponse.from_orm(db_member).json()),
        )
        if pre_pr.promo_code:
            promo_usage = crud.create_promo_usage(dbsession, pre_pr.promo_code, membership_id,
                                                  pre_pr.charge_point_id, pr.amount)
            pr.promo_usage_id = promo_usage.id

        db_pr = crud.create_pre_payment_request(dbsession, pr, membership_id)

        return db_pr
    except (
            exceptions.ApolloPaymentRequestError,
            exceptions.ApolloPromoCodeUsageError
    ) as e:
        raise HTTPException(400, e.__str__())


@router.post("/payment-request/deposit", status_code=201,
             dependencies=[Depends(permission)], response_model=schema.PaymentRequestResponse)
async def create_pre_auth_payment_request(request: Request, pr: schema.PaymentRequestDeposit,
                                          dbsession: SessionLocal = Depends(create_session)):
    """
    Create Deposit Payment Request

    :param PaymentRequestDeposit pr: Payment Request Data
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_member = crud.get_membership_by_id(dbsession, membership_id)
        pr = schema.PaymentRequest(
            **pr.dict(),
            meta=json.loads(schema.MembershipResponse.from_orm(db_member).json()),
            reason=schema.PaymentRequestReason.deposit
        )
        db_pr = crud.create_payment_request(dbsession, pr, membership_id)

        vkey = calculate_md5_hash(
            f'{db_pr.amount}{settings.MERCHANT_ID}{db_pr.id}{settings.VERIFY_KEY}')
        params = {
            'amount': db_pr.amount,
            'orderid': db_pr.id,
            'bill_name': f'{db_pr.member.first_name} {db_pr.member.last_name}',
            'bill_email': db_pr.member.user.email,
            'bill_mobile': db_pr.member.user.phone_number,
            'bill_desc': db_pr.billing_description,
            'vcode': vkey,
            'channel': 'credit',
            'username': settings.MERCHANT_ID,
            'app_name': 'Apollo',
        }
        url = f'{PAYMENT_DIRECT_API_URL}?' + urllib.parse.urlencode(params)
        return RedirectResponse(url)

    except (
            exceptions.ApolloPaymentRequestError,
    ) as e:
        raise HTTPException(400, e.__str__())


@router.get("/payment-request/pending", status_code=200, response_model=List[schema.PaymentRequestResponse])
async def list_pending_direct_payment_requests(request: Request,
                                               dbsession: SessionLocal = Depends(
                                                   create_session)
                                               ) -> List[schema.PaymentRequestResponse]:
    """
    Get Pending Direct Payment Requests
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    db_pr_list = crud.get_pending_payment_requests(
        dbsession, membership_id, schema.PaymentRequestType.direct)
    return db_pr_list


@router.post("/payment-request/{payment_request_id}", status_code=200, response_model=schema.PaymentRequestResponse)
async def send_payment_request(request: Request, payment_request_id: UUID,
                               dbsession: SessionLocal = Depends(
                                   create_session)
                               ) -> schema.PaymentRequestResponse:
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_pr = crud.get_payment_request(dbsession, payment_request_id)

        if db_pr.status != schema.PaymentRequestStatus.pending:
            raise HTTPException(400, 'Payment must have "Pending" status')

        if db_pr.type == schema.PaymentRequestType.recurring:
            cc = crud.get_member_primary_cc(dbsession, membership_id)
            if not cc:
                raise HTTPException(400, 'Primary Credit Card not found')
            recurring_payment = schema.RecurringPayment(
                amount=db_pr.amount,
                token=cc.token,
                order_id=str(db_pr.invoice_number),
                bill_name=f'{db_pr.member.first_name} {db_pr.member.last_name}',
                bill_email=db_pr.member.user.email,
                bill_mobile=db_pr.member.user.phone_number,
                bill_desc=db_pr.billing_description,
                response={},
                payment_request_id=str(db_pr.id),
            )
            if db_pr.currency == Currency.sgd:
                logger.info('Calling Singapore Payment Gateway')
                # response = await send_recurring_payment_sg(recurring_payment)
                response = send_recurring_payment_sg(recurring_payment)
            elif db_pr.currency == Currency.bnd:
                logger.info('Calling Brunei Payment Gateway')
                # response = await send_recurring_payment_sg(recurring_payment)
                response = send_recurring_payment_bn(recurring_payment)
            elif db_pr.currency == Currency.khr:
                logger.info('Calling Cambodia Payment Gateway')
                # response = await send_recurring_payment_sg(recurring_payment)
                response = send_recurring_payment_kh(recurring_payment)
            else:
                logger.info('Calling Malaysia Payment Gateway')
                response = send_recurring_payment_my(recurring_payment)

            recurring_payment_response = schema.RecurringPaymentResponse(
                **response.json()[0])
            recurring_payment.response = recurring_payment_response
            crud.create_recurring_payment(dbsession, recurring_payment)

        elif db_pr.type == schema.PaymentRequestType.direct:
            vkey = calculate_md5_hash(
                f'{db_pr.amount}{settings.MERCHANT_ID}{db_pr.id}{settings.VERIFY_KEY}')
            params = {
                'amount': db_pr.amount,
                'orderid': db_pr.id,
                'bill_name': f'{db_pr.member.first_name} {db_pr.member.last_name}',
                'bill_email': db_pr.member.user.email,
                'bill_mobile': db_pr.member.user.phone_number,
                'bill_desc': db_pr.billing_description,
                'vcode': vkey,
                'channel': 'credit',
                'username': settings.MERCHANT_ID,
                'app_name': 'Apollo',
            }
            url = f'{PAYMENT_DIRECT_API_URL}?' + urllib.parse.urlencode(params)
            return RedirectResponse(url)

        return db_pr

    except (
            exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloRecurringPaymentError,
    ) as e:
        raise HTTPException(400, e.__str__())


@router.get("/payment-request/{payment_request_id}", status_code=200, response_model=schema.PaymentRequestResponse)
async def get_payment_request(payment_request_id: UUID,
                              dbsession: SessionLocal = Depends(create_session)
                              ) -> schema.PaymentRequestResponse:
    """
    Get Payment Request

    :param str payment_request_id: Target Payment Request ID
    """

    try:
        db_pr = crud.get_payment_request(dbsession, payment_request_id)
        return db_pr
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Reporting module

# @router.get("/monthly-revenue", status_code=200)
# async def get_monthly_revenue(request: Request,
#                               year: int = Query(default=datetime.now().year, ge=2012, le=datetime.now().year),  # noqa
#                               timezone: str = 'Asia/Kuala_Lumpur',  # noqa
#                               dbsession: SessionLocal = Depends(create_session)) -> dict:
#     """
#     Get monthly revenue based on provided year and month
#
#     :param year:
#     :param dbsession:
#     :return:
#     """
#     auth_token_data = decode_auth_token_from_headers(request.headers)
#     membership_id = auth_token_data.get('membership_id')
#     headers = generate_charger_header(dbsession, membership_id)
#
#     query_params = request.query_params
#     query_params = dict(query_params)
#     # query_params['return_bill_id_only'] = True
#     # url = f'{CHARGER_URL_PREFIX}/charging/stats/get-charging-session-list-by-year'
#     if year is not None:
#         # todo: rework logic
#         raise HTTPException(status_code=401, detail="Temporary disable due to computational expensive")
#     response = await send_request('GET', url=url, headers=headers, query_params=query_params)
#     data = response.json()
#     return crud.get_monthly_revenue(dbsession, data)


@router.get("/monthly-revenue", status_code=200)
async def get_monthly_revenue(request: Request,
                              year: int = Query(default=datetime.now().year, ge=2012, le=datetime.now().year),  # noqa
                              timezone: str = 'Asia/Kuala_Lumpur',  # noqa
                              dbsession: SessionLocal = Depends(create_session)) -> dict:
    """
    Get monthly revenue based on provided year and month

    :param year:
    :param dbsession:
    :return:
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    is_superuser = headers.get('is_superuser', 'False') == 'True'
    connectors = []
    if not is_superuser:
        url = f'{CHARGER_URL_PREFIX}/connector/list-connector'
        response = await send_request('GET', url=url, headers=headers)
        connectors = response.json()
    return crud.get_monthly_revenue_v2(dbsession, connectors, year, timezone, is_superuser, headers=headers)


@router.post("/rebilling-issued-invoice", status_code=200)
async def rebilling_invoices(request: Request,
                             transaction_ids: list,
                             # otp: str,
                             dbsession: SessionLocal = Depends(create_session)) -> dict:
    """
    Rebill invoices

    :return:
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    db_member = crud.get_membership_by_id(dbsession, membership_id)
    db_user = crud.get_user_by_id(dbsession, db_member.user_id)
    headers = generate_charger_header(dbsession, membership_id)

    # if otp != settings.REBILLING_OTP:
    #     raise HTTPException(status_code=401, detail="Wrong OTP")

    if not db_user.is_superuser and db_member.membership_type not in [schema.MembershipType.staff,
                                                                      schema.MembershipType.sub_staff,
                                                                      schema.MembershipType.custom]:
        raise HTTPException(status_code=401, detail="Non-staff are not able to perform rebilling")

    url = f'{CHARGER_URL_PREFIX}/charging/rebilling-issued-invoice'

    response = await send_request('POST', url=url, headers=headers, data=json.dumps(transaction_ids))
    data = response.json()
    audit_data = {
        'membership_id': membership_id,
        'transaction_ids': transaction_ids,
        'activity': 'rebilling',
    }
    log_audit_trail(PaymentRequestCRUD, dbsession, schema.AuditLogType.create, data_before=audit_data,
                    data_after={}, object_id=membership_id)
    return data


@router.get("/report/unique-users", status_code=200)
async def get_number_of_unique_user_report(request: Request, filters: dict = Depends(date_filters),
                                           dbsession: SessionLocal = Depends(create_session)) -> int:
    """
    Get number of unique users based on provided date range
    :param date_start:
    :param date_end:
    :return:
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    url = f'{CHARGER_URL_PREFIX}/charging/charging-sessions-unique-id-tags'
    response = await send_request('GET', url=url, headers=headers, query_params=filters)
    data = response.json()
    return crud.number_of_unique_users(dbsession, data['id_tags_list'])


@router.get("/report/number-of-transactions", status_code=200)
async def get_number_of_transactions_report(request: Request, filters: dict = Depends(date_filters),
                                            dbsession: SessionLocal = Depends(create_session)) -> int:
    """
    Get number of transactions based on provided date range
    :param date_start:
    :param date_end:
    :return:
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    url = f'{CHARGER_URL_PREFIX}/charging/total-charging-transaction'
    response = await send_request('GET', url=url, headers=headers, query_params=filters)

    data = response.json()
    return data


@router.get("/report/avg-energy-consumption", status_code=200)
async def get_avg_energy_consumption_report(request: Request, filters: dict = Depends(date_filters),
                                            dbsession: SessionLocal = Depends(create_session)) -> float:
    """
    Get avg energy consumption based on provided date range
    :param date_start:
    :param date_end:
    :return:
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    # url = f'{CHARGER_URL_PREFIX}/charging/charging-sessions-dashboard'
    url = f'{CHARGER_URL_PREFIX}/charging/dashboard-meter-values'
    response = await send_request('GET', url=url, headers=headers, query_params=filters)
    data = response.json()
    print(len(data))
    # _, avg = crud.energy_consumption(data)
    _, avg = crud.energy_consumption_v2(data)
    return avg


@router.get("/report/total-energy-consumed", status_code=200)
async def get_total_energy_consumed_report(request: Request, filters: dict = Depends(date_filters),
                                           dbsession: SessionLocal = Depends(create_session)) -> float:
    """
    Get total energy consumed based on provided date range
    :param date_start:
    :param date_end:
    :return:
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    # url = f'{CHARGER_URL_PREFIX}/charging/charging-sessions-dashboard'
    url = f'{CHARGER_URL_PREFIX}/charging/dashboard-meter-values'
    response = await send_request('GET', url=url, headers=headers, query_params=filters)
    data = response.json()
    # total, _ = crud.energy_consumption(data)
    total, _ = crud.energy_consumption_v2(data)
    return total


# @router.get("/report/total-payments", status_code=200)
# async def get_total_payments(request: Request, filters: dict = Depends(date_filters),
#                              currency: str = None,
#                              dbsession: SessionLocal = Depends(create_session)) -> float:
#     """
#     Get number of paid transactions based on provided date range
#     :param date_start:
#     :param date_end:
#     :return:
#     """
#     auth_token_data = decode_auth_token_from_headers(request.headers)
#     membership_id = auth_token_data.get('membership_id')
#     headers = generate_charger_header(dbsession, membership_id)
#
#     url = f'{CHARGER_URL_PREFIX}/charging/charging-sessions-dashboard'
#     response = await send_request('GET', url=url, headers=headers, query_params=filters)
#     data = response.json()
#     return crud.total_payments(dbsession, data, currency)


@router.get("/report/total-payments", status_code=200)
async def get_total_payments_v2(request: Request, filters: dict = Depends(date_filters),
                                currency: str = None,
                                dbsession: SessionLocal = Depends(create_session)) -> float:
    """
    Get number of paid transactions based on provided date range
    :param date_start:
    :param date_end:
    :return:
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    is_superuser = headers.get('is_superuser', 'False') == 'True'
    data = []
    if not is_superuser:
        url = f'{CHARGER_URL_PREFIX}/connector/list-connector'
        response = await send_request('GET', url=url, headers=headers)
        data = response.json()
    return crud.total_payments_v2(dbsession, data, currency, filters, is_superuser, headers=headers)


# @router.get("/report/avg-operation-duration", status_code=200)
# async def get_avg_operation_duration(request: Request, filters: dict = Depends(date_filters),
#                                      dbsession: SessionLocal = Depends(create_session)) -> float:
#     """
#     Get average of operation_duration based on provided date range
#     :param date_start:
#     :param date_end:
#     :return:
#     """
#     auth_token_data = decode_auth_token_from_headers(request.headers)
#     membership_id = auth_token_data.get('membership_id')
#     headers = generate_charger_header(dbsession, membership_id)
#
#     url = f'{CHARGER_URL_PREFIX}/charging/charging-sessions-dashboard'
#     response = await send_request('GET', url=url, headers=headers, query_params=filters)
#     data = response.json()
#     _, avg = crud.operation_duration(data)
#     return avg


@router.get("/report/avg-operation-duration", status_code=200)
async def get_avg_operation_duration(request: Request, filters: dict = Depends(date_filters),
                                     dbsession: SessionLocal = Depends(create_session)) -> float:
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    url = f'{CHARGER_URL_PREFIX}/charge_point/report/operation-duration'
    filters['which'] = 'average'
    response = await send_request('GET', url=url, headers=headers, query_params=filters)
    data = response.json()
    return data


# @router.get("/report/total-operation-duration", status_code=200)
# async def get_total_operation_duration(request: Request, filters: dict = Depends(date_filters),
#                                        dbsession: SessionLocal = Depends(create_session)) -> float:
#     """
#     Get total operation duration based on provided date range
#     :param date_start:
#     :param date_end:
#     :return:
#     """
#     auth_token_data = decode_auth_token_from_headers(request.headers)
#     membership_id = auth_token_data.get('membership_id')
#     headers = generate_charger_header(dbsession, membership_id)
#
#     url = f'{CHARGER_URL_PREFIX}/charging/charging-sessions-dashboard'
#     response = await send_request('GET', url=url, headers=headers, query_params=filters)
#     data = response.json()
#     total, _ = crud.operation_duration(data)
#     return total


@router.get("/report/total-operation-duration", status_code=200)
async def get_total_operation_duration(request: Request, filters: dict = Depends(date_filters),
                                       dbsession: SessionLocal = Depends(create_session)) -> float:
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    url = f'{CHARGER_URL_PREFIX}/charge_point/report/operation-duration'
    response = await send_request('GET', url=url, headers=headers, query_params=filters)
    data = response.json()
    return data


@router.get("/report/total-greenhouse-gas-saved", status_code=200)
async def get_total_greenhouse_gas_saved(request: Request, filters: dict = Depends(date_filters),
                                         dbsession: SessionLocal = Depends(create_session)) -> float:
    """
    Get total greenhouse gas saved based on provided date range
    :param date_start:
    :param date_end:
    :return:
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    # url = f'{CHARGER_URL_PREFIX}/charging/charging-sessions-dashboard'
    url = f'{CHARGER_URL_PREFIX}/charging/dashboard-meter-values'
    response = await send_request('GET', url=url, headers=headers, query_params=filters)
    data = response.json()
    # return crud.total_greenhouse_saved(data)
    return crud.total_greenhouse_saved_v2(data)


@router.get("/report/total-charged-mileage", status_code=200)
async def get_total_charged_mileage(request: Request, filters: dict = Depends(date_filters),
                                    dbsession: SessionLocal = Depends(create_session)) -> float:
    """
    Get total charged mileage saved based on provided date range
    :param date_start:
    :param date_end:
    :return:
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    # url = f'{CHARGER_URL_PREFIX}/charging/charging-sessions-dashboard'
    url = f'{CHARGER_URL_PREFIX}/charging/dashboard-meter-values'
    response = await send_request('GET', url=url, headers=headers, query_params=filters)
    data = response.json()
    # total_milage = crud.total_mileage(data)
    total_milage, _ = crud.total_mileage_v2(data)
    return total_milage


@router.get("/report/get-energy-transaction", status_code=status.HTTP_200_OK,
            response_model=List[schema.DashboardEnergyResponse])
async def get_energy_transaction(request: Request,
                                 filter_dashboard_energy: dict = Depends(filter_dashboard_energy),
                                 dbsession: SessionLocal =
                                 Depends(create_session)) -> List[schema.DashboardEnergyResponse]:
    """
    Get  Energy Transaction

    :param type: Enums
    :param date_start: datetime
    :param date_end: datetime
    :return:
    """
    if filter_dashboard_energy["type"] == schema.DashboardType.Week and filter_dashboard_energy["date_end"] is None:
        raise HTTPException(
            status_code=401, detail="'date_end' must be provided for weeks type filter.")

    if filter_dashboard_energy["type"] == schema.DashboardType.Year:
        # todo: rework logic
        raise HTTPException(status_code=401, detail="Temporary disable due to computational expensive")
    path = 'charging/dashboard/get_dashboard_energy'

    url = f'{CHARGER_URL_PREFIX}/{path}'

    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    query_params = request.query_params
    try:
        response = await send_request('GET', url, headers=headers, query_params=query_params)
        return JSONResponse(response.json(), status_code=response.status_code)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/report/avg-charger-power-output", status_code=200)
async def get_avg_charger_power_output(request: Request, filters: dict = Depends(date_filters),
                                       dbsession: SessionLocal = Depends(create_session)):
    """
    Get average charger power output based on provided date range

    :param date_start:
    :param date_end:
    :return:
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    bills = await get_kw_report(dbsession, headers, filters)

    return bills


async def filter_dashboard_charging(types: schema.DashboardType = Query(schema.DashboardType.Day),  # noqa
                                    date_start: datetime = Query(
                                        default=datetime.now(pytz.timezone('Asia/Kuala_Lumpur')).replace(hour=0,
                                                                                                         minute=0,
                                                                                                         second=0,
                                                                                                         microsecond=0)
                                    ),
                                    date_end: datetime = Query(
                                        default=datetime.now(pytz.timezone('Asia/Kuala_Lumpur')).replace(hour=23,
                                                                                                         minute=59,
                                                                                                         second=59,
                                                                                                         microsecond=0))
                                    ):
    return {'type': types, 'date_start': date_start, 'date_end': date_end}


@router.get("/report/get-energy-transactions-charging-duration/", status_code=200)
async def get_energy_transactions_charging_duration(request: Request,
                                                    filter_dashboard_charging: dict =
                                                    Depends(filter_dashboard_charging),
                                                    dbsession: SessionLocal = Depends(create_session)):
    """
    Get Energy Transactions Charging Duration

    :param type : Enums
    :param date_start : datetime
    :param date_end : datetime
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)
    query_params = request.query_params
    try:
        path = 'charging/stats/charging-duration'
        if (filter_dashboard_charging["type"] == schema.DashboardType.Week or
            filter_dashboard_charging["type"] == schema.DashboardType.Month) and \
                filter_dashboard_charging["date_end"] is None:
            raise HTTPException(
                status_code=401, detail="'date_end' must be provided for weeks/month type filter.")

        charging_result = await send_request('GET', f'{CHARGER_URL_PREFIX}/{path}', headers=headers,
                                             query_params=query_params)
        if charging_result.status_code == 200:
            return charging_result.json()

        logger.debug('charger response: %s', charging_result.json())
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSession')
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/report/get-energy-transactions-by-session/",
            status_code=200)
async def get_energy_transactions_by_session(request: Request,
                                             filter_dashboard_charging: dict = Depends(filter_dashboard_charging),
                                             dbsession: SessionLocal = Depends(create_session)):  # noqa
    """
    Get Energy Transactions Charging Duration
    Note that there's no implementation for days/years yet
    :param type : Enums
    :param date_start : datetime
    :param date_end : datetime
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)
    query_params = request.query_params
    try:
        path = 'charging/stats/energy-transactions-start-end-charging'
        if (filter_dashboard_charging["type"] == schema.DashboardType.Week or
            filter_dashboard_charging["type"] == schema.DashboardType.Month) and \
                filter_dashboard_charging["date_end"] is None:
            raise HTTPException(
                status_code=401, detail="'date_end' must be provided for weeks/month type filter.")

        charging_result = await send_request('GET', f'{CHARGER_URL_PREFIX}/{path}', headers=headers,
                                             query_params=query_params)
        if charging_result.status_code == 200:
            return charging_result.json()

        logger.debug('charger response: %s', charging_result.json())
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSession')
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/report/energy-total-transaction",
            status_code=status.HTTP_200_OK, response_model=List[schema.TotalTransactionResponse])
async def get_energy_total_transaction(request: Request,
                                       filter_total_transactions: dict = Depends(total_transaction_filters),
                                       dbsession: SessionLocal = Depends(create_session)) \
        -> List[schema.TotalTransactionResponse]:
    """
    Get Energy Total Transaction
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    query_params = request.query_params

    try:
        transaction_list = await get_total_transactions(query_params, headers)
        return transaction_list
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/report/charger-utilization",
            status_code=status.HTTP_200_OK, response_model=schema.ChargePointConnectorUtilization)
async def get_charger_utilization(request: Request,
                                  filter_charger_utilization: dict = Depends(charger_utilization_filters),
                                  dbsession: SessionLocal = Depends(create_session)):
    """
    Get Charger Utilization for Dashboard
    :param type : Enums
    :param date_start: datetime
    :param end_date : datetime
    """

    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)
    query_params = request.query_params

    try:
        charger_utilization = await get_charger_utilization_rate(query_params, headers)
        return charger_utilization
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get(
    "/emsp-cdr",
    status_code=status.HTTP_200_OK,
    response_model=Union[  # pylint: disable=unsubscriptable-object
        List[schema.OCPICPOCdrResponse],
        Page[schema.OCPICPOCdrResponse],
    ],
)
async def get_all_emsp_cdr_session(
        request: Request,
        params: Params = Depends(),
        filters: dict = Depends(emsp_cdr_filters),
        report: bool = False,
        pagination: bool = True,
        dbsession: Session = Depends(create_session),
) -> Union[  # pylint: disable=unsubscriptable-object
    List[schema.OCPICPOCdrResponse],
    Page[schema.OCPICPOCdrResponse],
]:
    """
    Get list of eMSP CDR Session
    """
    return await get_all_emsp_cdr(
        dbsession=dbsession,
        request=request,
        params=params,
        filters=filters,
        report=report,
        pagination=pagination,
    )


@router.api_route('/emsp-cdr/download/', methods=['GET'])
async def download_emsp_cdr_session(request: Request, params: Params = Depends(),
                                    dbsession: SessionLocal = Depends(create_session),
                                    filename: str = None, send_to: str = None,
                                    filters: dict = Depends(emsp_cdr_filters)):
    headers = ['INVOICE NUMBER', 'CDR ID', 'EVSE ID', 'CURRENCY', 'PARTY ID', 'COUNTRY CODE', 'TOKEN ID',
               'TOKEN CONTRACT ID', 'CREDIT', 'CREDIT REFERENCE ID',
               'TOTAL AMOUNT', 'USAGE', 'DURATION', 'START TIME', 'END TIME']

    columns = ['transaction_id', 'partner_ocpi_cpo_cdr_id', 'cdr_location.evse_id', 'currency', 'party_id',
               'country_code', 'cdr_token.uid', 'cdr_token.contract_id', 'credit', 'credit_reference_id',
               'total_cost.incl_vat', 'total_energy', 'duration', 'start_date_time', 'end_date_time']

    if 'date_from' in filters and isinstance(filters['date_from'], datetime):
        filters['date_from'] = filters['date_from'].isoformat()  # Convert to string
    if 'date_to' in filters and isinstance(filters['date_to'], datetime):
        filters['date_to'] = filters['date_to'].isoformat()  # Convert to string

    if not filename:
        tz = pytz.timezone('Asia/Kuala_Lumpur')
        current_datetime = datetime.now(tz)
        format_datetime = current_datetime.strftime("%d.%m.%Y_(%H:%M)")
        filename = 'emsp_cdr' + format_datetime

    if send_to:
        request_data = {
            "headers": dict(request.headers),
            "query_params": dict(request.query_params)
        }

        message = {
            'headers': headers,
            'filename': filename,
            'columns': columns,
            'email': send_to,
            'filters': filters,
            'request_data': request_data
        }
        task_data = ReportingTaskCreate(
            task_name="emsp_cdr",
            json_payload=message,
            retry_count=0
        )

        reporting_task = create_reporting_task_record(dbsession, task_data)
        task_uid = reporting_task.id
        message['task_uid'] = task_uid
        send_emsp_cdr_via_email_sync.delay(message=message)
        return {'message': 'The report will be ready in 15 minutes.'}

    get_data = partial(get_all_emsp_cdr, request=request, filters=filters,
                       report=True, dbsession=dbsession)
    report = GenerateReport('emsp_cdr', headers, columns, function=get_data)
    await report.generate_dataframe()
    await report.datetime_reformat('start_date_time')
    await report.datetime_reformat('end_date_time')
    res = await report.generate_report()
    return res


@router.get('/reconcilation/')
async def get_charging_reconcilation(request: Request,  # noqa: MC0001
                                     dbsession: SessionLocal = Depends(create_session),  # noqa: MC0001
                                     params: Params = Depends(),  # noqa: MC0001
                                     status: str = None, filters: dict = Depends(reconcilation_filter)):  # noqa: MC0001
    # TODO: Optimize reduce api call
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    url = f'{CHARGER_URL_PREFIX}/charging/reconcilation'
    cq_params = {
        'size': params.size,
        'page': params.page,
    }
    c_json = {}
    cp_op_ids = []
    partner_ids = []
    query_from_inv = any(value is not None for value in filters.values())
    if filters['partner']:
        ext_org_filter = dbsession.query(models.ExternalOrganization) \
            .filter(models.ExternalOrganization.friendly_name.ilike(f'%{filters["partner"]}%'))
        partner_ids = [str(ext.id) for ext in ext_org_filter]
        if not partner_ids:
            return {'items': [], 'total': 0, 'size': params.size, 'page': params.page}
    if filters['operator']:
        operators = crud.get_operator_by_operator_name(dbsession, filters['operator'])
        operator_ids = [str(op.id) for op in operators]
        chargepoint = dbsession.query(models.OperatorChargepoint.charge_point_id) \
            .filter(models.OperatorChargepoint.operator_id.in_(operator_ids)).all()
        cp_op_ids = [str(cp.charge_point_id) for cp in chargepoint]
        c_json['operator_ids'] = operator_ids
        if not cp_op_ids:
            return {"items": [], "total": 0, "page": 1, "size": params.size}
    url_cp = f'{CHARGER_URL_PREFIX}/charge_point/small/'
    cp_params = {'size': 50, 'no_update': True}
    if filters['charge_point_id']:
        cp_params['serial_number'] = filters['charge_point_id']
    if filters['country']:
        cp_params['country'] = filters['country']
    charge_point_ids = []
    if filters['charge_point_id'] or filters['country']:
        cp_response = await send_request('GET', url=url_cp, headers=headers, query_params=cp_params)
        cp_data = cp_response.json()
        charge_point_ids = [c.get('id') for c in cp_data]
    charge_point_ids = set(charge_point_ids) | set(cp_op_ids)
    cp_ids = list(charge_point_ids)
    inv_pagination = None
    if query_from_inv:
        cs_ids = crud.reconcilation_invoice_filtered(dbsession, filters, params, cp_ids, partner_ids)
        if cs_ids is not None:
            if cs_ids.total > 0:
                c_json['ids'] = cs_ids.items
                inv_pagination = {}
                inv_pagination['page'] = cs_ids.page
                inv_pagination['size'] = cs_ids.size
                inv_pagination['total'] = cs_ids.total
                cq_params['page'] = 1
            else:
                return {"items": [], "total": 0, "page": 1, "size": params.size}
    if status:
        cq_params['status'] = status
    response = await send_request('POST', url=url, headers=headers, data=json.dumps(c_json),
                                  query_params=cq_params)
    data = response.json()
    c_items = data['items']
    if len(c_items) <= 0:
        return {"items": [], "total": 0, "page": 1, "size": params.size}
    cs_ids = [cs['id'] for cs in c_items]
    # Can optimize this if we optimize charging_status
    bill_data, pr_data = crud.simplified_invoice_reconcilation(dbsession, cs_ids)
    for cs in c_items:
        session_start = cs.get('session_start')
        session_end = cs.get('session_end')
        duration = 0
        if session_start and session_end:
            try:
                session_start = datetime.fromisoformat(session_start)
                session_end = datetime.fromisoformat(session_end)
                duration = session_end - session_start
            except Exception as e:  # pylint: disable=broad-except
                logger.error('Error calculating duration: %s', str(e))
        cs['duration'] = str(duration)
        bill = bill_data.get(cs['id'])
        native = ['Local', 'Foreign']
        meter_start = cs.get('meter_start', 0)
        meter_stop = cs.get('meter_stop', 0)
        cs['usage'] = (meter_stop - meter_start) / 1000
        if cs['charging_session_type'] in native:
            cs['charging_session_type'] = 'Native'
        if bill:
            cs['billing_status'] = bill.status
            cs['discount'] = bill.discount
            cs['total_price'] = bill.usage_amount
            if isinstance(bill.discount, dict):
                cs['total_discount'] = sum(bill.discount.values())
        else:
            cs['billing_status'] = 'Not Billed'
            cs['discount'] = 0
        pr = pr_data.get(cs['id'])
        fullname = None
        if pr:
            cs['invoice_generated'] = True
            cs['payment_type'] = pr.type
            if pr.currency:
                cs['payment_currency'] = pr.currency
            else:
                cs['payment_currency'] = cs.get('charge_point_connector')['billing_currency']
            # cs['payment_currency'] = cs.get('meta')
            # print(cs.get('meta'))

            try:
                fullname = f'{pr.member.first_name} {pr.member.last_name}'
            except Exception as e:  # pylint: disable=broad-except
                logger.error('Error getting username: %s', str(e))
        else:
            cs['invoice_generated'] = False
            cs['payment_type'] = None
            cs['payment_currency'] = None
        if fullname is None:
            try:
                mem_detail = crud.get_member_by_id_tag(dbsession, cs['id_tag'])
                if mem_detail:
                    fullname = f"{mem_detail.first_name} {mem_detail.last_name}"
            except Exception as e:  # pylint: disable=broad-except
                logger.error('Error: %s', str(e))
        cs['username'] = fullname
        operator_name = None
        if cs.get('charge_point_connector') is not None:
            operator_id = nested_dict(cs, 'charge_point_connector', 'charge_point', 'operator_id')
            if operator_id:
                operator = crud.get_operator_by_id(dbsession, operator_id)
                operator_name = operator.name if operator else None
        cs['operator'] = operator_name
        partner_name = None
        if cs.get('ocpi_evse_connector') is not None:
            operator_id = nested_dict(cs, 'ocpi_evse_connector', 'ocpi_evse', 'operator_id')
            if operator_id:
                ext_org = dbsession.query(models.ExternalOrganization) \
                    .filter(models.ExternalOrganization.id == operator_id).first()
                if ext_org:
                    partner_name = ext_org.friendly_name
        cs['partner'] = partner_name
    if inv_pagination is not None:
        data.update(inv_pagination)

    for bill in data['items']:
        payment_type_filtered = None
        if bill['payment_type']:
            if bill['payment_type'] == schema.PaymentRequestType.wallet:
                payment_type_filtered = "Wallet"
            elif bill['payment_type'] == schema.PaymentRequestType.recurring:
                payment_type_filtered = 'Credit-Card'
            elif bill['payment_type'] == schema.PaymentRequestType.payment_terminal:
                payment_type_filtered = 'Payment Terminal'
            elif bill['payment_type'] == schema.PaymentRequestType.direct:
                payment_type_filtered = 'Credit-Card'
            elif bill['payment_type'] == schema.PaymentRequestType.partial:
                payment_type_filtered = 'Partial'
            elif bill['payment_type'] == schema.PaymentRequestType.partial_direct:
                payment_type_filtered = 'Partial'
            else:
                payment_type_filtered = None
        if payment_type_filtered is not None:
            bill['payment_type'] = payment_type_filtered

    return data


@router.get('/payment-gateway/reconcilation/', response_model=Page[schema.PaymentGatewayReconcilationResponse])
async def get_payment_gateway_reconcilation(request: Request,
                                            filters: dict = Depends(payment_gateway_reconcilation_filter),
                                            params: Params = Depends(),
                                            dbsession: SessionLocal = Depends(create_session)):
    query = crud.get_all_payment_gateway_reconcilation(dbsession, filters)

    reconn = paginate(query, params)
    reconn_items = reconn.items

    for recon in reconn_items:
        if not recon.charging_session_bill:
            recon.discounted_price = 0
        elif isinstance(recon.charging_session_bill.discount, dict):
            recon.discounted_price = recon.charging_session_bill.discount['subscription_discount']
        elif isinstance(recon.charging_session_bill.discount, float):
            recon.discounted_price = recon.charging_session_bill.discount

    return reconn


@router.get('/payment-gateway/reconcilation/download/')
async def download_payment_gateway_reconcilation(request: Request,
                                                 filters: dict = Depends(payment_gateway_reconcilation_filter),
                                                 dbsession: SessionLocal = Depends(create_session)):
    headers = ['INVOICE NUMBER', 'EMAIL', 'PHONE NUMBER', 'TOTAL PRICE', 'BILLING DATE', 'TRAN ID',
               'CHANNEL', 'STAT CODE', 'BANK NAME', 'NET AMOUNT', 'BILLING STATUS', 'SETTLEMENT DATE']

    columns = ['order_id', 'billing_email', 'member.user.phone_number', 'amount', 'billing_date',
               'tran_id', 'channel', 'stat_code', 'bank_name', 'net_amount',
               'payment_request.status', 'settlement_date']

    query = crud.get_all_payment_gateway_reconcilation(dbsession, filters)
    report = GenerateReport('pg_reconcilation', header=headers, columns=columns, query=query)
    await report.generate_dataframe_with_query(schema=schema.PaymentGatewayReconcilationResponse,
                                               multiple_join=['member', 'payment_request'])
    await report.datetime_reformat('settlement_date')
    await report.datetime_reformat('billing_date')
    res = await report.generate_report()
    return res


@router.get('/payment-gateway/generate-reconciliation/')
async def generate_payment_gateway_reconcilation(request: Request, start_date: datetime = None,  # noqa: MC0001
                                                 use_background: bool = False,
                                                 lookback_duration: int = 1,
                                                 dbsession: SessionLocal = Depends(create_session)):  # noqa: MC0001
    if use_background:
        message = {'start_date': start_date.isoformat()}

        app.send_task(f'{settings.MAIN_SERVICE_TASKS_PREFIX}.process_razerpay_reconciliation',
                      kwargs={'message': message},
                      queue=settings.MAIN_SERVICE_WORKER_QUEUE,
                      routing_key=settings.MAIN_SERVICE_WORKER_ROUTE)
        return 'Task running on background'
    lookback_duration = timedelta(days=lookback_duration).total_seconds()
    await process_razerpay_reconcilation(dbsession, 'SGD', start_date, lookback_duration)
    return await process_razerpay_reconcilation(dbsession, 'MYR', start_date, lookback_duration)


@router.post("/refund-callback", include_in_schema=False)
async def refund_callback(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Get Refund Response Callback
    """
    logger.info('refund-callback')
    admin = set_admin_as_context_user(dbsession)  # noqa
    deactivate_audit()
    refund_body = await request.json()
    logger.info("Refund Callback Body: %s", str(refund_body))
    reference_id = refund_body.get('RefID')
    refund_status = refund_body.get('Status')
    signature_received = refund_body.get('Signature')
    refund_id = refund_body.get('RefundID')

    db_payment_refund = crud.get_payment_refund_by_reference_id(dbsession, reference_id)
    refund_amount = db_payment_refund.refund_amount
    refund_amount = Decimal(str(refund_amount)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    signature_key = calculate_md5_hash(
        f'P{settings.MERCHANT_ID}'
        f'{db_payment_refund.reference_id}{refund_id}{db_payment_refund.pg_transaction_id}'
        f'{refund_amount}{refund_status}{settings.SECRET_KEY}'
    )
    if signature_received != signature_key:
        logger.error('skey mismatch for reference id: %s', db_payment_refund.reference_id)
        raise HTTPException(400, 'skey mismatch')

    if refund_status == '00':
        refund_status = schema.RefundStatus.success
    elif refund_status == '11':
        refund_status = schema.RefundStatus.rejected
    else:
        refund_status = schema.RefundStatus.pending

    refund_update_schema = schema.UpdatePaymentRefund(refund_status=refund_status,
                                                      pg_refund_callback=refund_body)
    db_payment_refund = crud.update_payment_refund(dbsession, db_payment_refund.id, refund_update_schema)
    if refund_status == schema.RefundStatus.success:
        try:

            db_pr = crud.get_payment_request(dbsession, db_payment_refund.payment_request_id)
            charging_session_bill = crud.get_charging_session_bill(dbsession, db_pr.charging_session_bill_id)
            charging_session_id = charging_session_bill.charging_session_id
            headers = generate_charger_header(dbsession, str(db_pr.member_id))
            db_cs = await crud.get_raw_charging_session(dbsession, charging_session_id, headers)

            result = await construct_and_submit_credit_note_to_rmp(
                dbsession, db_cs, charging_session_bill, str(db_pr.member_id), db_payment_refund.id
            )
            logger.info("E-Credit Note submission result: %s", result)
        except Exception as e:  # pylint: disable=broad-except
            logger.exception("Failed to construct/submit E-Credit Note: %s", str(e))

    return db_payment_refund
