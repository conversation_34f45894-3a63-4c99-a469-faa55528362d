import json
import logging
import random
import string
from datetime import datetime
from typing import Union, List

from fastapi import HTT<PERSON>Exception
from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy import Float, func, or_
from sqlalchemy.exc import IntegrityError, NoResultFound
from sqlalchemy.orm import Session, Query

from app import models, schema, exceptions
from app.crud.payment import create_payment_request_redeem_package, create_payment_request, update_payment_request
# from app.crud import BaseCRUD
from .base import BaseCRUD, log_audit_trail

logger = logging.getLogger(__name__)


def generate_invitation_code(length, prefix: str = None):
    letters_and_digits = string.ascii_uppercase + string.digits

    if prefix is not None:
        number_of_length = length - len(prefix) - 1
        code = ''.join(random.choice(letters_and_digits) for _ in range(number_of_length))  # nosec
        code = prefix.upper() + '-' + code
        return code
    return ''.join(random.choice(letters_and_digits) for _ in range(length))  # nosec


class PaymentRequestCRUD(BaseCRUD):  # noqa
    model = models.PaymentRequest


class WalletCRUD(BaseCRUD):
    model = models.Wallet

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        db_wallet = dbsession.query(cls.model).get(object_id)
        if not db_wallet:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_wallet.member.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_wallet.member.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        if not check_permission:
            return super().query(dbsession, *columns)
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession, *columns).join(models.Membership).filter(
                models.Membership.organization_id.in_(allowed_orgs)
            )
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     return super().query(dbsession, *columns).join(models.Membership).filter(
        #         models.Membership.organization_id == membership.organization_id)
        if membership.membership_type == schema.MembershipType.regular_user:
            return super().query(dbsession, *columns).filter(cls.model.member_id == membership.id)
        raise exceptions.ApolloPermissionError()


class WalletPackageCRUD(BaseCRUD):
    model = models.WalletPackage

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        db_wallet = dbsession.query(cls.model).get(object_id)
        if not db_wallet:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_wallet.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_wallet.member.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        membership = cls.membership()
        query = super().query(dbsession, *columns) \
            .order_by(models.WalletPackage.updated_at.desc().nullslast(),
                      models.WalletPackage.created_at.desc())
        if not check_permission:
            return query
        if membership.user.is_superuser:
            return query
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return query.filter(cls.model.organization_id.in_(allowed_orgs))
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     return super().query(dbsession, *columns).join(models.Membership).filter(
        #         models.Membership.organization_id == membership.organization_id)
        if membership.membership_type == schema.MembershipType.regular_user:
            return query.filter(cls.model.member_id == membership.id)
        raise exceptions.ApolloPermissionError()


class WalletTransactionCRUD(BaseCRUD):
    model = models.WalletTransaction

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        return super().query(dbsession, *columns) \
            .order_by(models.WalletTransaction.transaction_id.desc(),
                      models.WalletTransaction.updated_at.desc().nullslast(),
                      models.WalletTransaction.created_at.desc())


class WalletPackageInvitationCRUD(BaseCRUD):
    model = models.WalletPackageInvitation

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        return True

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        return super().query(dbsession, *columns) \
            .order_by(models.WalletPackageInvitation.updated_at.desc().nullslast(),
                      models.WalletPackageInvitation.created_at.desc())

    @classmethod
    def bulk_insert(cls, dbsession: Session, data: list[dict], *args, check_permission=True, **kwargs):
        """create an object
        """
        db_object_list = []
        for i in range(len(data)):
            db_object = cls.model(**data[i])
            dbsession.add(db_object)
        dbsession.bulk_save_objects(db_object_list)
        dbsession.commit()
        cls.log_activity(dbsession, {'data': data}, schema.ActivityLogType.create)
        return db_object_list


class WalletPackageOrderCRUD(BaseCRUD):
    model = models.WalletPackageOrder

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        db_wallet = dbsession.query(cls.model).get(object_id)
        if not db_wallet:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_wallet.member.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_wallet.member.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        if not check_permission:
            return super().query(dbsession, *columns)
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession, *columns).join(models.Membership).filter(
                models.Membership.organization_id.in_(allowed_orgs)
            )
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     return super().query(dbsession, *columns).join(models.Membership).filter(
        #         models.Membership.organization_id == membership.organization_id)
        if membership.membership_type == schema.MembershipType.regular_user:
            return super().query(dbsession, *columns).filter(cls.model.member_id == membership.id)
        raise exceptions.ApolloPermissionError()


def get_wallet(db: Session, wallet_id: str) -> models.Wallet:
    try:
        db_wallet = WalletCRUD.get(db, wallet_id)
        return db_wallet
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Wallet')


def get_wallet_by_member_and_currency(db: Session, membership_id: str, currency: str) -> models.Wallet:
    try:
        db_wallet = WalletCRUD.query(db).filter(
            models.Wallet.member_id == membership_id,
            models.Wallet.currency == currency,
        ).first()
        return db_wallet
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Wallet')


def get_wallet_by_member(db: Session, membership_id: str) -> List[models.Wallet]:
    db_wallet = WalletCRUD.query(db).filter(
        models.Wallet.member_id == membership_id,
    ).all()
    return db_wallet


def update_wallet(db: Session, wallet_id: str, wallet_data: schema.Wallet) -> models.Wallet:
    try:
        db_wallet = WalletCRUD.update(
            db,
            wallet_id,
            wallet_data.dict(exclude_unset=True, exclude_defaults=True)
        )
        return db_wallet.first()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Wallet')


def create_wallet(db: Session, wallet_data: schema.Wallet, member_id: str) -> models.Wallet:
    try:
        db_wallet = WalletCRUD.add(db, dict(wallet_data.dict(), member_id=member_id))
        return db_wallet
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloWalletError()


def delete_wallet(db: Session, wallet_id: str) -> models.Wallet:
    try:
        WalletCRUD.delete(db, wallet_id)
        return wallet_id
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Wallet')


def get_wallet_list(db: Session) -> models.Wallet:
    db_wallet_list = WalletCRUD.query(db).all()
    for wallet in db_wallet_list:
        update_wallet_balance(db, wallet.id)
    return db_wallet_list


def get_wallet_list_with_filter(db: Session, wallet_filters: dict = None, params: Params = None,  # noqa: MC0001
                                query_only: bool = False) -> Union[models.Wallet, Query]:  # noqa
    query = WalletCRUD.query(db)
    membership = WalletCRUD.membership()
    is_superuser = membership.user.is_superuser
    if is_superuser:
        query = query.join(models.Membership)
    if wallet_filters['organization_id']:
        query = query.filter(
            models.Membership.organization_id == wallet_filters['organization_id'])
    if wallet_filters['wallet_id']:
        query = query.filter(models.Wallet.id == wallet_filters['wallet_id'])
    if wallet_filters['username']:
        query = query.filter(
            func.concat(models.Membership.first_name, ' ',
                        models.Membership.last_name).ilike(f'%{wallet_filters["username"]}%')
        )
        if wallet_filters['phone_number']:
            query = query.join(models.User).filter(
                models.User.phone_number.ilike(f'%{wallet_filters["phone_number"]}%'))
    if wallet_filters['phone_number'] and not wallet_filters['username']:
        query = query.join(models.User) \
            .filter(models.User.phone_number.ilike(f'%{wallet_filters["phone_number"]}%'))
    if wallet_filters['status']:
        query = query.filter(func.lower(models.Wallet.status) == wallet_filters['status'].lower())
    if wallet_filters['currency']:
        query = query.filter(func.lower(models.Wallet.currency) == wallet_filters['currency'])
    if params:
        db_wallet_list = paginate(query, params)
        for wallet in db_wallet_list.items:
            update_wallet_balance(db, wallet.id)
    else:
        db_wallet_list = query.all()
    if query_only:
        return query
    return db_wallet_list


def get_wallet_package_by_id(db: Session, package_id: str) -> models.WalletPackage:
    try:
        wallet_package = WalletPackageCRUD.get(db, package_id)
        return wallet_package
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='WalletPackage')


def update_wallet_package(db: Session, package_id: str, data: schema.UpdateWalletPackage) -> models.WalletPackage:
    try:
        wallet_package = WalletPackageCRUD.update(db, package_id, data.dict(exclude_unset=True))
        return wallet_package
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='WalletPackage')


def update_wallet_package_invitation(db: Session, wallet_package_invitation_id: str,
                                     data: schema.UpdateWalletPackageInvitation) -> models.WalletPackageInvitation:
    try:
        wallet_package = WalletPackageInvitationCRUD.update(db, wallet_package_invitation_id,
                                                            data.dict(exclude_unset=True))
        return wallet_package
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='WalletPackageInvitation')


def delete_wallet_package_invitation(db: Session, wallet_package_invitation_id: str):
    try:
        WalletPackageInvitationCRUD.delete(db, wallet_package_invitation_id)
        return wallet_package_invitation_id
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='WalletPackageInvitation')


def delete_wallet_package(db: Session, package_id: str):
    try:
        WalletPackageCRUD.delete(db, package_id)
        return package_id
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='WalletPackage')


def get_wallet_invitation_by_package_id(db: Session, package_id: str,
                                        params: Params = None) -> models.WalletPackageInvitation:
    try:
        wallet_package = WalletPackageCRUD.get(db, package_id)
        query = WalletPackageInvitationCRUD.query(db, check_permission=False)

        # if is_used is not None:
        query = query.filter(models.WalletPackageInvitation.wallet_package_id == wallet_package.id)

        if params:
            db_package_invitation = paginate(query, params)
        else:
            db_package_invitation = query.all()
        return db_package_invitation

    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='WalletPackage')


def get_wallet_pacakge_list_with_filter(db: Session, wallet_filters: dict = None,  # noqa: MC0001
                                        params: Params = None,
                                        query_only: bool = False) -> Union[models.WalletPackage, Query]:  # noqa
    query = WalletPackageCRUD.query(db)
    membership = WalletPackageCRUD.membership()
    _ = membership.user.is_superuser
    if wallet_filters['organization_id']:
        query = query.filter(models.WalletPackage.organization_id == wallet_filters['organization_id'])
    if wallet_filters['wallet_package_id']:
        query = query.filter(models.WalletPackage.id == wallet_filters['wallet_package_id'])
    if wallet_filters['name']:
        query = query.filter(models.WalletPackage.name.ilike(f'%{wallet_filters["name"]}%'))
    if wallet_filters['description']:
        query = query.filter(models.WalletPackage.description.ilike(f'%{wallet_filters["description"]}%'))
    if wallet_filters['is_redeemable'] is not None:
        query = query.filter(models.WalletPackage.is_redeemable == wallet_filters['is_redeemable'])
    if wallet_filters['package_category'] is not None:
        query = query.filter(models.WalletPackage.package_category == wallet_filters['package_category'])
    if wallet_filters['is_private'] is not None:
        query = query.filter(models.WalletPackage.is_private == wallet_filters['is_private'])
    if wallet_filters['is_active'] is not None:
        query = query.filter(models.WalletPackage.is_active == wallet_filters['is_active'])
    if wallet_filters['currency']:
        query = query.filter(func.lower(models.WalletPackage.currency) == wallet_filters['currency'].lower())
    if query_only:
        return query
    if params:
        db_wallet_package = paginate(query, params)
    else:
        db_wallet_package = query.all()
    return db_wallet_package


def update_wallet_balance(db: Session, wallet_id: str) -> models.Wallet:
    db_wallet = get_wallet(db, wallet_id)

    total_deposit = PaymentRequestCRUD.query(db, func.sum(models.PaymentRequest.amount.cast(Float))).filter(
        models.PaymentRequest.reason == schema.PaymentRequestReason.deposit,
        models.PaymentRequest.status == schema.PaymentRequestStatus.done,
        models.PaymentRequest.member_id == db_wallet.member_id,
        models.PaymentRequest.currency == db_wallet.currency,
    ).one()[0] or 0

    total_prepaid_wallet_topup = PaymentRequestCRUD.query(
        db, func.sum(models.PaymentRequest.amount.cast(Float))).filter(
        models.PaymentRequest.reason == schema.PaymentRequestReason.prepaid_wallet_topup,
        models.PaymentRequest.status == schema.PaymentRequestStatus.done,
        models.PaymentRequest.member_id == db_wallet.member_id,
        models.PaymentRequest.currency == db_wallet.currency,
    ).one()[0] or 0

    total_payment = PaymentRequestCRUD.query(db, func.sum(models.PaymentRequest.amount.cast(Float))).filter(
        models.PaymentRequest.reason == schema.PaymentRequestReason.payment,
        models.PaymentRequest.type == schema.PaymentRequestType.wallet,
        models.PaymentRequest.status == schema.PaymentRequestStatus.done,
        models.PaymentRequest.member_id == db_wallet.member_id,
        models.PaymentRequest.currency == db_wallet.currency,
    ).one()[0] or 0

    total_prepaid_wallet_deduction = PaymentRequestCRUD.query(
        db, func.sum(models.PaymentRequest.amount.cast(Float))).filter(
        models.PaymentRequest.reason == schema.PaymentRequestReason.prepaid_wallet_deduction,
        models.PaymentRequest.status == schema.PaymentRequestStatus.done,
        models.PaymentRequest.member_id == db_wallet.member_id,
        models.PaymentRequest.currency == db_wallet.currency,
    ).one()[0] or 0

    total_promo_usage = \
        PaymentRequestCRUD.query(db, func.sum(models.PromoCodeUsage.amount.cast(Float))).join(
            models.PromoCodeUsage).filter(
            models.PaymentRequest.reason == schema.PaymentRequestReason.payment,
            models.PaymentRequest.type == schema.PaymentRequestType.wallet,
            models.PaymentRequest.status == schema.PaymentRequestStatus.done,
            models.PaymentRequest.member_id == db_wallet.member_id,
            models.PaymentRequest.currency == db_wallet.currency,
        ).one()[0] or 0
    total_partial_payment = db.query(func.sum(models.PaymentRequest.wallet_deduct_amount.cast(Float))).select_from(
        models.PaymentRequest
    ).filter(
        models.PaymentRequest.reason == schema.PaymentRequestReason.payment,
        models.PaymentRequest.type == schema.PaymentRequestType.partial,
        models.PaymentRequest.wallet_deduct_status == schema.PaymentRequestStatus.done,
        models.PaymentRequest.member_id == db_wallet.member_id,
        models.PaymentRequest.currency == db_wallet.currency
    ).one()[0] or 0

    total_partial_payment_via_pre_auth = \
        db.query(func.sum(models.PaymentRequest.wallet_deduct_amount.cast(Float))).select_from(
            models.PaymentRequest
        ).filter(
            models.PaymentRequest.reason == schema.PaymentRequestReason.payment,
            models.PaymentRequest.type == schema.PaymentRequestType.partial_direct,
            models.PaymentRequest.wallet_deduct_status == schema.PaymentRequestStatus.done,
            models.PaymentRequest.member_id == db_wallet.member_id,
            models.PaymentRequest.currency == db_wallet.currency
        ).one()[0] or 0

    total_refunded_wallet = db.query(func.sum(models.PaymentRefund.refund_amount.cast(Float))).select_from(
        models.PaymentRefund
    ).join(models.PaymentRequest).filter(
        models.PaymentRequest.member_id == db_wallet.member_id,
        models.PaymentRequest.currency == db_wallet.currency,
        models.PaymentRefund.refund_type == schema.RefundType.wallet,
        models.PaymentRefund.refund_status == schema.RefundStatus.success
    ).one()[0] or 0

    total_credits = total_deposit + total_prepaid_wallet_topup + total_refunded_wallet
    total_debits = (
        total_payment
        + total_prepaid_wallet_deduction
        + total_partial_payment
        + total_partial_payment_via_pre_auth
        - total_promo_usage
    )
    total_balance = total_credits - total_debits

    db_wallet = WalletCRUD.update(db, wallet_id, {'balance': total_balance}, check_permission=False)

    return db_wallet


def get_wallet_package(db: Session, package_id: str):
    try:
        wallet_package = WalletPackageCRUD.query(db, check_permission=False).filter(
            models.WalletPackage.id == package_id).one()
        return wallet_package
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='WalletInvitation')


def get_wallet_packages(db: Session, is_active: bool = True, is_redeemable: bool = None,
                        is_private: bool = None, currency: str = None,
                        query_only: bool = False):
    query = WalletPackageCRUD.query(db, check_permission=False)

    if currency is not None:
        query = query.filter(models.WalletPackage.currency == currency)

    if is_active is not None:
        query = query.filter(models.WalletPackage.is_active == is_active)

    if is_redeemable is not None:
        query = query.filter(models.WalletPackage.is_redeemable == is_redeemable)

    if is_private is not None:
        query = query.filter(models.WalletPackage.is_private == is_private)

    if query_only:
        return query

    return query.all()


# deprecated
def get_wallet_package_based_on_pacakge_invitation_code(db: Session, code: str, wallet_ids: list):
    try:
        invitation_query = WalletPackageInvitationCRUD.query(db, check_permission=False)
        db_invitation = invitation_query.filter(models.WalletPackageInvitation.code == code,
                                                or_(models.WalletPackageInvitation.wallet_id.in_(wallet_ids),
                                                    models.WalletPackageInvitation.wallet_id.is_(None))).one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='WalletPackageInvitationCode')

    if db_invitation.linked_at is not None:
        raise HTTPException(400, "Redeem code is redeemed.")

    wallet_package = WalletPackageCRUD.query(db, check_permission=False).filter(
        models.WalletPackage.id == db_invitation.wallet_package_id).one()

    return wallet_package


def get_wallet_package_invitation_by_code(db: Session, code: str):
    try:
        invitation_query = WalletPackageInvitationCRUD.query(db, check_permission=False)
        db_invitation = invitation_query.filter(models.WalletPackageInvitation.code == code).one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='WalletPackageInvitationCode')

    return db_invitation


def create_wallet_package(db: Session, wallet_package: schema.CreateWalletPackage) -> models.WalletPackage:
    try:
        db_wallet_package = WalletPackageCRUD.add(db, dict(wallet_package.dict()))
        return db_wallet_package
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloWalletError()


def create_wallet_package_order(db: Session, db_wallet_package: models.WalletPackage,
                                currency: schema.Currency, wallet_id: str,
                                wallet_package_id: str,
                                db_wallet_package_amount: float = 0,
                                db_wallet_package_discount_fee: float = 0,
                                ) -> models.WalletPackageOrder:
    try:
        final_amount = float(db_wallet_package_amount - db_wallet_package_discount_fee)

        # If final_amount is 0 (for eg: BMW customer, no need pay, triggert ValueError)
        # if final_amount == 0:
        #     raise ValueError

        # If final amount is less than 1, we will reject from creating subscribing， razerpay requires 1 ringgit or more
        if final_amount <= 1 and final_amount != 0:
            db.rollback()
            raise exceptions.ApolloSubscriptionOrderError()

        pacakge_meta = json.loads(schema.WalletPackageResponse.from_orm(db_wallet_package).json())

        package_order = schema.WalletPackageOrder(
            amount=round(final_amount, 2),
            discount_amount=round(db_wallet_package_discount_fee, 2),
            credit_amount=db_wallet_package.credit_amount,
            currency=currency,
            wallet_id=wallet_id,
            wallet_package_id=wallet_package_id,
            status=schema.WalletPackageOrderStatus.pending,
            package_meta=pacakge_meta,
        )

        # for decimal issue
        if final_amount <= 1e-9:
            package_order.status = schema.WalletPackageOrderStatus.success
        db_wallet_package_order = WalletPackageOrderCRUD.add(db, package_order.dict(), check_permission=False)
        return db_wallet_package_order
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloSubscriptionOrderError()


def use_package_invitation(db: Session, wallet_id: str,
                           wallet_package_invitation_id: str) -> models.WalletPackageInvitation:
    db_wallet_pacakge = WalletPackageInvitationCRUD.update(
        db, wallet_package_invitation_id,
        {'linked_at': datetime.now(), 'wallet_id': wallet_id},
        check_permission=False
    )
    return db_wallet_pacakge


def topup_wallet_from_redeemed_package(db: Session, redeem_code: str, member_id: str, wallet_id: str, currency: str,
                                       credit_amount: float,
                                       wallet_amount: str,
                                       pacakge_id: str = None,
                                       package_meta: dict = None):
    if package_meta is None:
        package_meta = {}
    wallet_transaction_obj = {
        'amount': credit_amount,
        'currency': currency,
        'transaction_type': 'Redeem',
        'status': 'Pending',
        'package_meta': package_meta,
        'wallet_package_id': pacakge_id,
        'wallet_id': wallet_id,
        'remarks': f'Top-up {float(credit_amount):.2f} from package redeemed code: #{redeem_code}',
        'description': f'Redeemed {float(credit_amount):.2f} credit with code: #{redeem_code}'
    }

    wallet_transaction = WalletTransactionCRUD.add(db, wallet_transaction_obj, check_permission=False)
    _ = WalletCRUD.update(db, wallet_id, {'balance': wallet_amount}, check_permission=False)
    WalletTransactionCRUD.update(db, wallet_transaction.id, {'status': 'Success'}, check_permission=False)
    pr = schema.PaymentRequestRedeemPackage(
        type=schema.PaymentRequestType.wallet,
        amount=credit_amount,
        currency=currency,
        billing_description=f'topup_wallet-{wallet_transaction.id}',
        reason=schema.PaymentRequestReason.deposit,
        meta=package_meta,
        status=schema.PaymentRequestStatus.done
    )
    _ = create_payment_request_redeem_package(db, pr, member_id)
    db_wallet = update_wallet_balance(db, wallet_id)
    return db_wallet


def manual_topup_to_wallet(db: Session, remarks: str, member_id: str, wallet_id: str, currency: str,
                           credit_amount: float, wallet_amount: str):
    wallet_transaction_obj = {
        'amount': credit_amount,
        'currency': currency,
        'transaction_type': 'Redeem',
        'status': 'Pending',
        'wallet_id': wallet_id,
        'remarks': remarks,
        'description': f'Manual top-up {credit_amount} to wallet.'
    }

    wallet_transaction = WalletTransactionCRUD.add(db, wallet_transaction_obj, check_permission=False)
    _ = WalletCRUD.update(db, wallet_id, {'balance': wallet_amount}, check_permission=False)
    WalletTransactionCRUD.update(db, wallet_transaction.id, {'status': 'Success'}, check_permission=False)
    pr = schema.PaymentRequestRedeemPackage(
        type=schema.PaymentRequestType.wallet,
        amount=credit_amount,
        currency=currency,
        billing_description=f'topup_wallet-{wallet_transaction.id}',
        reason=schema.PaymentRequestReason.deposit,
        status=schema.PaymentRequestStatus.done
    )
    _ = create_payment_request_redeem_package(db, pr, member_id)
    db_wallet = update_wallet_balance(db, wallet_id)
    return db_wallet


def manual_deduct_from_wallet(db: Session, remarks: str, member_id: str, wallet_id: str, currency: str,
                              debit_amount: float, wallet_amount: str):
    wallet_transaction_obj = {
        'amount': debit_amount,
        'currency': currency,
        'transaction_type': 'Deduct',
        'status': 'Pending',
        'wallet_id': wallet_id,
        'remarks': remarks,
        'description': f'Manual deduct {debit_amount} from wallet.'
    }

    wallet_transaction = WalletTransactionCRUD.add(db, wallet_transaction_obj, check_permission=False)
    _ = WalletCRUD.update(db, wallet_id, {'balance': wallet_amount}, check_permission=False)
    WalletTransactionCRUD.update(db, wallet_transaction.id, {'status': 'Success'}, check_permission=False)
    payment_request = schema.PaymentRequest(
        type=schema.PaymentRequestType.wallet,
        amount=debit_amount,
        currency=currency,
        billing_description=f'deduct_wallet-{wallet_transaction.id}',
        reason=schema.PaymentRequestReason.payment,
        update_token=False,
        meta={},
    )

    db_pr = create_payment_request(db, payment_request, member_id)
    pr_update = schema.PaymentRequestStatusUpdate(
        status=schema.PaymentRequestStatus.done
    )

    update_payment_request(db, pr_update, db_pr.id)

    db_wallet = update_wallet_balance(db, wallet_id)
    return db_wallet


def get_or_create_wallet(db: Session, member_id: str, currency: schema.Currency) -> models.Wallet:
    try:
        db_wallet = WalletCRUD.query(db).filter(
            models.Wallet.member_id == member_id,
            models.Wallet.currency == currency,
        ).one()
        return db_wallet
    except NoResultFound:
        my_wallet = schema.Wallet(currency=currency)
        db_wallet = create_wallet(db, my_wallet, str(member_id))
        return db_wallet


def generate_redeem_codes(db: Session, discount_amount: float, wallet_package_id: str, number_of_code: int = 1,
                          prefix: str = None):
    try:
        bulk_invitation_code = []
        for _ in range(number_of_code):
            package_redeem_schema = schema.WalletPackageInvitation(
                code=generate_invitation_code(12, prefix),
                wallet_package_id=wallet_package_id,
                discount_amount=discount_amount
            )

            bulk_invitation_code.append(package_redeem_schema.dict())

        data = {
            'discount_amount': discount_amount,
            'wallet_package_id': str(wallet_package_id),
            'number_of_code': number_of_code,
        }

        log_audit_trail(WalletPackageInvitationCRUD, db, schema.AuditLogType.create, data_before={},
                        data_after=data, object_id=str(wallet_package_id))
        return WalletPackageInvitationCRUD.bulk_insert(db, bulk_invitation_code)
    except Exception as e:  # pylint: disable=broad-except
        logger.error("Error bulk inserting redeem code, error: %s", str(e))
        raise Exception


def get_wallet_invitation_with_filter(db: Session, wallet_package_id: str = None, is_used: bool = None,
                                      code: str = None, username: str = None, id: str = None,
                                      query_only: bool = False, params: Params = None):
    query = WalletPackageInvitationCRUD.query(db, check_permission=False)
    query.join(models.Wallet).join(models.Membership)
    # if is_used is not None:

    if wallet_package_id:
        query = query.filter(models.WalletPackageInvitation.wallet_package_id == wallet_package_id)

    if is_used is not None:
        query = query.filter(models.WalletPackageInvitation.linked_at.isnot(None))

    if code:
        query = query.filter(models.WalletPackageInvitation.code.ilike(f'%{code}%'))

    if username:
        query = query.join(models.Wallet).join(models.Membership) \
            .filter(func.concat(models.Membership.first_name, ' ',
                                models.Membership.last_name).ilike(f'%{username}%'))

    if id:
        query = query.filter(models.WalletPackageInvitation.id == id)

    if query_only:
        return query

    if params:
        db_package_invitation = paginate(query, params)
    else:
        db_package_invitation = query.all()
    return db_package_invitation


def get_wallet_transaction_with_filter(db: Session, transaction_filter: dict,  # noqa: MC0001
                                       wallet_id: str = None,
                                       query_only: bool = False, params: Params = None):  # noqa mccabe: MC0001
    query = WalletTransactionCRUD.query(db)

    # if is_used is not None:

    query = query.join(models.Wallet).join(models.Membership)

    if wallet_id is not None:
        query = query.filter(models.WalletTransaction.wallet_id == wallet_id)

    if transaction_filter['transaction_id'] is not None:
        query = query.filter(models.WalletTransaction.transaction_id == int(transaction_filter['transaction_id']))

    if transaction_filter['username']:
        query = query.filter(
            func.concat(models.Membership.first_name, ' ',
                        models.Membership.last_name).ilike(f'%{transaction_filter["username"]}%')
        )

    if transaction_filter['remarks'] is not None:
        query = query.filter(models.WalletTransaction.remarks.ilike(f'%{transaction_filter["remarks"]}%'))

    if transaction_filter['package_name'] is not None:
        query = query.filter(
            func.jsonb_extract_path_text(models.WalletTransaction.package_meta, 'name')
            .ilike(f'%{transaction_filter["package_name"]}%')
        )
    # if transaction_filter['package'] is not None:
    #     query = query.filter(models.WalletTransaction.remarks.ilike(f'%{transaction_filter["remarks"]}%'))

    if transaction_filter['currency'] is not None:
        query = query.filter(
            func.lower(models.WalletTransaction.currency) == transaction_filter['currency'].lower())

    if transaction_filter['amount'] is not None:
        query = query.filter(models.WalletTransaction.amount - transaction_filter['amount'] <= 1e-9)

    if transaction_filter['status'] is not None:
        query = query.filter(models.WalletTransaction.status == transaction_filter['status'])

    if transaction_filter['transaction_id'] is not None:
        query = query.filter(models.WalletTransaction.transaction_id == int(transaction_filter['transaction_id']))

    if transaction_filter['transaction_type'] is not None:
        query = query.filter(models.WalletTransaction.transaction_type.
                             ilike(f'%{transaction_filter["transaction_type"]}%'))

    # todo for filtering wallet package type
    # if transaction_filter['package_type'] is not None:
    #     query = query.filter(models.WalletTransaction.package_meta['package_name'].
    #     ilike(f'%{transaction_filter["package_type"]}%'))

    if transaction_filter['session_id'] is not None:
        query = query.filter(models.WalletTransaction.
                             charging_session_id.ilike(f'%{transaction_filter["session_id"]}%'))

    if query_only:
        return query

    if params:
        db_transaction = paginate(query, params)
    else:
        db_transaction = query.all()
    return db_transaction
