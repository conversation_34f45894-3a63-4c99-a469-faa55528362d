from enum import Enum
from uuid import UUID
import re
from typing import Optional
import bcrypt
from pydantic import BaseModel, validator, constr, EmailStr

from app.schema.payment import Currency

from ..auth import User, MembershipType


class PreAuthPaymentMethodEnums(str, Enum):
    credit_card = 'Credit-Card'
    wallet = 'Wallet'


class PreferredPaymentFlowEnums(str, Enum):
    credit_card = 'Credit-Card'
    wallet = 'Wallet'
    partial = 'Partial'


class PreAuthChargerTypeEnums(str, Enum):
    ac = 'AC'
    dc = 'DC'


def validator_non_empty_string(val: str):
    if val is not None and len(val) < 1:
        raise ValueError('Value must not be empty.')
    return val


class V2MembershipResponse(BaseModel):
    id: UUID
    first_name: str
    last_name: str
    organization_id: UUID
    user: User
    membership_type: MembershipType

    class Config:
        orm_mode = True


class MobileMembershipAllResponse(BaseModel):
    id: UUID
    first_name: str
    last_name: str
    organization_id: UUID
    user: User
    favorite_charge_points: str
    favorite_locations: str
    membership_type: MembershipType

    class Config:
        orm_mode = True


# pylint: disable=unsubscriptable-object
class MobileMembershipUpdate(BaseModel):
    first_name: Optional[constr(min_length=1)]
    last_name: Optional[constr(min_length=1)]
    email: Optional[EmailStr]
    password: Optional[constr(min_length=8, max_length=128)]
    confirm_password: Optional[constr(min_length=8, max_length=128)]
    ruby_user_id: Optional[UUID]
    vehicle_model: Optional[str]
    preferred_payment_method: Optional[str]
    preferred_payment_flow: Optional[str]
    pre_auth_amount: Optional[float]

    @validator('first_name')
    def prevent_first_name_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)

    @validator('last_name')
    def prevent_last_name_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)

    @validator('email')
    def lower_case_email(cls, email: str):
        return email.lower()

    @validator('password')
    def validate_password(cls, password: str):
        error_message = ''
        if not len(password) >= 8:
            error_message += "Password must be at least 8 characters.\n"
        if not re.search(r'.*\d.*', password):
            error_message += "Password must contain digit.\n"
        if not re.search(r'.*[A-Z].*', password):
            error_message += "Password must contain capital letter.\n"
        if not re.search(r'.*[a-z].*', password):
            error_message += "Password must contain lower letter.\n"
        # if not re.search(r'.*\W.*', password):
        #     error_message += "Password must contain special character.\n"
        if error_message:
            raise ValueError(error_message)
        return password

    @validator('confirm_password')
    def validate_password_confirmation(cls, confirm_password: str, values: dict):
        if values.get('password', '') != confirm_password:
            raise ValueError('Passwords must match.')
        return confirm_password


class MobileMembershipUpdateResponse(BaseModel):
    first_name: Optional[constr(min_length=1)]
    last_name: Optional[constr(min_length=1)]
    email: Optional[EmailStr]
    password: Optional[constr(min_length=8, max_length=128)]
    ruby_user_id: Optional[UUID]
    preferred_payment_method: Optional[str]

    @validator('password')
    def hash_password(cls, password: str, values: dict):
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt(11, b'2a')).decode('utf-8')


class PreferredPreAuthInfo(BaseModel):
    pre_auth_amount: float
    pre_auth_currency: Currency
    charger_type: PreAuthChargerTypeEnums


class UpdatePreferredPaymentMethod(BaseModel):
    preferred_payment_method: Optional[PreAuthPaymentMethodEnums]
    preferred_pre_auth_info: Optional[PreferredPreAuthInfo]


class UpdatePreferredPaymentFlow(BaseModel):
    preferred_payment_flow: PreferredPaymentFlowEnums
