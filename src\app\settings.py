import os
import json
import logging
from datetime import timezone
from pathlib import Path

import pydantic
from dotenv import load_dotenv

load_dotenv(override=True)
env = os.environ

BASE_DIR = Path(__file__).resolve().parent.parent

json_conf = {}
json_path = os.path.join(BASE_DIR, 'config/settings.json')

with open(json_path) as f:
    json_conf = json.load(f)


def get_secret(file_path_env_var: str):
    secret_path = os.getenv(file_path_env_var)

    if not secret_path:
        return None

    path_exists = os.path.exists(secret_path)

    if path_exists:
        with open(secret_path, 'r', encoding='utf-8') as secret_file:
            secret_var = secret_file.read().rstrip('\n')
        return secret_var

    return None


def get_env_or_secret(env_var_name: str):
    env_var = os.getenv(env_var_name)
    if env_var:
        return env_var

    file_path_env_var = env_var_name + '_FILE'
    secret_var = get_secret(file_path_env_var)
    return secret_var


TIMEZONE = timezone.utc

APOLLO_MAIN_DOMAIN = env.get('APOLLO_MAIN_DOMAIN')
CHARGER_DOMAIN = env.get('CHARGER_DOMAIN')
MIGRATION_RAILS_DOMAIN = env.get('MIGRATION_RAILS_DOMAIN')

MAIN_ROOT_PATH = env.get('MAIN_ROOT_PATH', 'apollo-main')
CHARGER_ROOT_PATH = env.get('CHARGER_ROOT_PATH', 'apollo-charger/api/v1/csms')
CHARGER_ROOT_PATH_V2 = env.get('CHARGER_ROOT_PATH_V2', 'apollo-charger/api/v1')

PLUG_AND_CHARGE_SECRET = env.get('PLUG_AND_CHARGE_SECRET', '')

# logger
LOG_FORMAT = ('%(levelname) -10s %(asctime)s %(name) -30s %(funcName) '
              '-35s %(lineno) -5d: %(message)s')
LOG_LEVEL = env.get('LOG_LEVEL')
logging.basicConfig(format=LOG_FORMAT, level=LOG_LEVEL)

JWT_SECRET = env.get('JWT_SECRET')

# postgres
POSTGRES_HOST = env.get('POSTGRES_HOST')
POSTGRES_PORT = env.get('POSTGRES_PORT')
POSTGRES_APOLLO_MAIN_DATABASE = get_env_or_secret('POSTGRES_APOLLO_MAIN_DATABASE')
POSTGRES_USER = get_env_or_secret('POSTGRES_USER')
POSTGRES_PASSWORD = get_env_or_secret('POSTGRES_PASSWORD')
DATABASE_SSL = env.get('DATABASE_SSL') == 'True'
DATABASE_POOL_SIZE = get_env_or_secret('DATABASE_POOL_SIZE') or 5
DATABASE_MAX_OVERFLOW = get_env_or_secret('DATABASE_MAX_OVERFLOW') or 10
DATABASE_POOL_RECYCLE = get_env_or_secret('DATABASE_POOL_RECYCLE') or 1800
DATABASE_POOL_TIMEOUT = get_env_or_secret('DATABASE_POOL_TIMEOUT') or 60.0

SQLALCHEMY_DATABASE_URL = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}\
@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_APOLLO_MAIN_DATABASE}"

CORS_ORIGINS = env.get('CORS_ORIGINS', '').split(',')
ALLOWED_HOSTS = env.get('ALLOWED_HOSTS', '').split(',')

DEBUG = bool(int(env.get('DEBUG', 0)))

SENDGRID_API_KEY = env.get('SENDGRID_API_KEY')
SENDER_EMAIL = env.get('SENDER_EMAIL')
SENDER_NAME = env.get('SENDER_NAME', 'chargEV Billing')
NOTIFICATION_SENDER_NAME = env.get('NOTIFICATION_SENDER_NAME', 'chargEV Alerts')

VERIFICATION = {
    'TOKEN_BYTES_LENGTH': int(env.get('VERIFICATION_TOKEN_BYTES_LENGTH', 6)),
    'TOKEN_DURATION_MINUTES': int(env.get('VERIFICATION_TOKEN_DURATION_MINUTES', 5))
}

MANUAL_LINK_TOKEN = {
    'TOKEN_BYTES_LENGTH': int(env.get('MANUAL_LINK_TOKEN_BYTES_LENGTH', 6)),
    'TOKEN_DURATION_MINUTES': int(env.get('MANUAL_LINK_TOKEN_DURATION_MINUTES', 5))
}

RESET_PASSWORD = {
    'TOKEN_BYTES_LENGTH': int(env.get('RESET_PASSWORD_TOKEN_BYTES_LENGTH', 6)),
    'TOKEN_DURATION_MINUTES': int(env.get('RESET_PASSWORD_DURATION_MINUTES', 5))
}

TWILIO = {
    'ACCOUNT_SID': env.get('TWILIO_ACCOUNT_SID'),
    'AUTH_TOKEN': env.get('TWILIO_AUTH_TOKEN'),
    'VERIFY_SERVICE_SID': env.get('TWILIO_VERIFY_SERVICE_SID'),
}

API_TAGS_METADATA = [
    {
        "name": "auth",
        "description": "Operations with authentication and user management.",
    },
]

# To be validated against only if OTP_ENABLE_WHATSAPP is True
ALLOWED_OTP_COUNTRIES = {
    # East Asia
    "CN",  # China
    "HK",  # Hong Kong
    "MO",  # Macao
    "JP",  # Japan
    "KR",  # South Korea
    "MN",  # Mongolia
    "TW",  # Taiwan

    # Southeast Asia
    "BN",  # Brunei
    "ID",  # Indonesia
    "KH",  # Cambodia
    "MY",  # Malaysia
    "MM",  # Myanmar (Burma)
    "PH",  # Philippines
    "SG",  # Singapore
    "TH",  # Thailand
    "VN",  # Vietnam

    # Oceania (Pacific)
    "AU",  # Australia
    "NZ",  # New Zealand

    # North America
    "US",  # United States
    "CA",  # Canada

    # Western Europe
    "AT",  # Austria
    "BE",  # Belgium
    "FR",  # France
    "DE",  # Germany
    "LI",  # Liechtenstein
    "LU",  # Luxembourg
    "MC",  # Monaco
    "NL",  # Netherlands
    "CH",  # Switzerland

    # Northern Europe
    "DK",  # Denmark
    "EE",  # Estonia
    "FI",  # Finland
    "IS",  # Iceland
    "IE",  # Ireland
    "LV",  # Latvia
    "LT",  # Lithuania
    "NO",  # Norway
    "SE",  # Sweden
    "GB",  # United Kingdom

    # Southern Europe
    "AD",  # Andorra
    "GR",  # Greece
    "IT",  # Italy
    "MT",  # Malta
    "PT",  # Portugal
    "SM",  # San Marino
    "ES",  # Spain
    "VA",  # Vatican City
}

OTP_VALIDATE_COUNTRY = pydantic.parse_obj_as(bool, env.get('OTP_VALIDATE_COUNTRY', False))
OTP_ENABLE_WHATSAPP = pydantic.parse_obj_as(bool, env.get('OTP_ENABLE_WHATSAPP', False))
OTP_CHANNEL_SMS = 'sms'
OTP_CHANNEL_WA = 'whatsapp'

# Razerpay
MERCHANT_ID = get_env_or_secret('MERCHANT_ID')
VERIFY_KEY = get_env_or_secret('VERIFY_KEY')
SECRET_KEY = get_env_or_secret('SECRET_KEY')

RAZER_PAYMENT_DIRECT_API_URL = get_env_or_secret('PAYMENT_DIRECT_API_URL')
RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL = get_env_or_secret('PAYMENT_PRE_AUTH_REQUEST_API_URL')
RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL = get_env_or_secret('PAYMENT_PRE_AUTH_REFUND_API_URL')
RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL = get_env_or_secret('PAYMENT_PRE_AUTH_CAPTURE_API_URL')
RAZER_PAYMENT_RECURRING_API_URL = get_env_or_secret('PAYMENT_RECURRING_API_URL')
RAZER_PAYMENT_IPN_URL = get_env_or_secret('PAYMENT_IPN_URL')
RAZER_PAYMENT_RECONCILATION_API_URL = get_env_or_secret('PAYMENT_RECONCILATION_API_URL')
RAZER_PAYMENT_RECONCILATION_BY_OID_URL = get_env_or_secret('PAYMENT_RECONCILATION_BY_OID_URL')
RAZER_PAYMENT_RECONCILATION_BY_TID_URL = get_env_or_secret('PAYMENT_RECONCILATION_BY_TID_URL')

RAZER_TOKEN_API_URL = get_env_or_secret('TOKEN_API_URL')
RAZER_REFUND_API_URL = get_env_or_secret('REFUND_API_URL')
RAZER_REFUND_STATUS_API_URL = get_env_or_secret('REFUND_STATUS_API_URL')

# SG Razerpay
SG_MERCHANT_ID = get_env_or_secret('SG_MERCHANT_ID')
SG_VERIFY_KEY = get_env_or_secret('SG_VERIFY_KEY')
SG_SECRET_KEY = get_env_or_secret('SG_SECRET_KEY')

SG_RAZER_PAYMENT_DIRECT_API_URL = get_env_or_secret('SG_PAYMENT_DIRECT_API_URL')
SG_RAZER_PAYMENT_RECURRING_API_URL = get_env_or_secret('SG_PAYMENT_RECURRING_API_URL')
SG_RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL = get_env_or_secret('SG_PAYMENT_PRE_AUTH_REQUEST_API_URL')
SG_RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL = get_env_or_secret('SG_PAYMENT_PRE_AUTH_CAPTURE_API_URL')
SG_RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL = get_env_or_secret('SG_PAYMENT_PRE_AUTH_REFUND_API_URL')
SG_RAZER_PAYMENT_IPN_URL = get_env_or_secret('SG_PAYMENT_IPN_URL')
SG_RAZER_PAYMENT_RECONCILATION_API_URL = get_env_or_secret('SG_PAYMENT_RECONCILATION_API_URL')
SG_RAZER_TOKEN_API_URL = get_env_or_secret('SG_TOKEN_API_URL')
SG_RAZER_PAYMENT_RECONCILATION_BY_OID_URL = get_env_or_secret('SG_PAYMENT_RECONCILATION_BY_OID_URL')
SG_RAZER_PAYMENT_RECONCILATION_BY_TID_URL = get_env_or_secret('SG_PAYMENT_RECONCILATION_BY_TID_URL')

# rabbitmq
RABBITMQ_USER = get_env_or_secret('RABBITMQ_USER')
RABBITMQ_PASS = get_env_or_secret('RABBITMQ_PASS')
RABBITMQ_HOST = env.get('RABBITMQ_HOST')
RABBITMQ_PORT = int(env.get('RABBITMQ_PORT', '5672'))
EXCHANGE = 'message'
EXCHANGE_TYPE = 'topic'
WORKER_EXCHANGE_TYPE = 'direct'
WORKER_EXCHANGE = 'main_worker'
CHARGER_SERVICE_WORKER_ROUTE = 'charger_worker'
CHARGER_SERVICE_WORKER_QUEUE = 'charger_worker'
CHARGER_LTA_SERVICE_WORKER_QUEUE = 'lta_worker'

CHARGER_REPORTING_SERVICE_WORKER_QUEUE = 'reporting_worker'

MAIN_SERVICE_WORKER_ROUTE = 'main_worker'
MAIN_SERVICE_WORKER_QUEUE = 'main_worker'
MAIN_SERVICE_TASKS_PREFIX = 'apollo.main.tasks'
LTA_MAIN_SERVICE_TASKS_PREFIX = 'apollo.main.lta_main_tasks'
LTA_MAIN_WORKER_EXCHANGE = 'main_lta_worker'
LTA_MAIN_WORKER_QUEUE = 'main_lta_worker'
PARTNER_WORKER_EXCHANGE = 'main_partner_worker'

# celery
RABBITMQ_IS_SECURE = env.get('RABBITMQ_IS_SECURE', False)
RABBITMQ_PROTOCOL = 'amqp'
if RABBITMQ_IS_SECURE:
    RABBITMQ_PROTOCOL = 'amqps'
CELERY_BROKER_URL = f'{RABBITMQ_PROTOCOL}://{RABBITMQ_USER}:{RABBITMQ_PASS}@{RABBITMQ_HOST}:{RABBITMQ_PORT}'

# sentry
SENTRY_DSN = env.get('SENTRY_DSN')

# callback
CALL_BACK_API_KEY = get_env_or_secret('CALL_BACK_API_KEY')
CALL_BACK_URL = get_env_or_secret('CALL_BACK_URL')
PRE_AUTH_CALL_BACK_URL = env.get('PRE_AUTH_CALL_BACK_URL', None)
COMMAND_CALL_BACK_URL = env.get('COMMAND_CALL_BACK_URL', None)

# reporting module constants
ICE_EMISSION_FACTOR = 0.17  # kg / km
EV_EMISSION_FACTOR = 0.694  # kg / km
EV_EFFICIENCY_FACTOR = 6.0  # km / kWh

# ruby migration key
MIGRATION_API_KEY = env.get('MIGRATION_API_KEY')
MIGRATION_PARTNER_NAME = env.get('MIGRATION_PARTNER_NAME')

# S3 Bucket
S3_BUCKET = env.get('S3_BUCKET')
S3_REGION = env.get('S3_REGION', "ap-southeast-1")
S3_ACCESS_KEY = env.get('AWS_ACCESS_KEY')
S3_SECRET_ACCESS_KEY = env.get('AWS_SECRET_ACCESS_KEY')
LTA_S3_PREFIX = get_env_or_secret('LTA_S3_PREFIX') or 'static/LTA_Static'

# Init Root Information
ROOT_EMAIL = env.get('ROOT_EMAIL')
ROOT_PASSWORD = env.get('ROOT_PASSWORD')
ROOT_PHONE_NUMBER = env.get('ROOT_PHONE_NUMBER')

# OCPI Related
OCPI_HOST = env.get('OCPI_HOST')
OCPI_PREFIX = env.get('OCPI_PREFIX')
CHARGER_OCPI_DOMAIN = env.get('CHARGER_OCPI_DOMAIN')
MAIN_OCPI_DOMAIN = env.get('MAIN_OCPI_DOMAIN')

DOWNLOAD_TIMEOUT = 20

IS_DEVELOP_ENV = env.get("IS_DEVELOP_ENV", False)

YGT_ORGANIZATION_NAME = env.get("YGT_ORGANIZATION_NAME", "Handal Green Mobility")
YGT_APP_SENDER_NAME = env.get('YGT_APP_SENDER_NAME', 'YGT')

# This is Pre-Authorization (RFID Flow) instead of Credit-Card's Pre-Authorization
PRE_AUTH_CC_CHECK = pydantic.parse_obj_as(bool, env.get('PRE_AUTH_CC_CHECK', False))

REBILLING_OTP = env.get('REBILLING_OTP', 'ascomcaosmcoanscorn124oaosdjoasdjncasdokomc')

BLACKLIST_CHECK_MEMBER_ID_LAST_4 = env.get('BLACKLIST_CHECK_MEMBER_ID_LAST_4', True)
BLACKLIST_CHECK_PHONE_NUMBER_LAST_4 = env.get('BLACKLIST_CHECK_PHONE_NUMBER_LAST_4', False)
BLACKLIST_CHECK_EMAIL_LAST_4 = env.get('BLACKLIST_CHECK_EMAIL_LAST_4', False)
BLACKLIST_CHECK_CURRENCY_LAST_4 = env.get('BLACKLIST_CHECK_CURRENCY_LAST_4', False)

POWERCABLE_DOMAIN = env.get('POWERCABLE_DOMAIN')
POWERCABLE_CSMS_ROOT_PATH = env.get('POWERCABLE_CSMS_ROOT_PATH', 'yinson-powercable/api/csms')
VEHICLE_CALL_BACK_URL = env.get('VEHICLE_CALL_BACK_URL', None)
VEHICLE_AUTOCHARGE_CALL_BACK_URL = env.get('VEHICLE_AUTOCHARGE_CALL_BACK_URL', None)

MY_PRE_AUTH_MAX_AMOUNT = int(env.get('MY_PRE_AUTH_MAX_AMOUNT', 250))
MY_PRE_AUTH_DEFAULT_AMOUNT = int(env.get('MY_PRE_AUTH_DEFAULT_AMOUNT', 250))
MY_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT = int(env.get('MY_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT', 150))

SG_PRE_AUTH_MAX_AMOUNT = int(env.get('SG_PRE_AUTH_MAX_AMOUNT', 150))
SG_PRE_AUTH_DEFAULT_AMOUNT = int(env.get('SG_PRE_AUTH_DEFAULT_AMOUNT', 150))
SG_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT = int(env.get('SG_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT', 75))

BN_PRE_AUTH_MAX_AMOUNT = int(env.get('BN_PRE_AUTH_MAX_AMOUNT', 150))
BN_PRE_AUTH_DEFAULT_AMOUNT = int(env.get('BN_PRE_AUTH_DEFAULT_AMOUNT', 150))
BN_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT = int(env.get('BN_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT', 75))

KH_PRE_AUTH_MAX_AMOUNT = int(env.get('KH_PRE_AUTH_MAX_AMOUNT', 170000))
KH_PRE_AUTH_DEFAULT_AMOUNT = int(env.get('KH_PRE_AUTH_DEFAULT_AMOUNT', 170000))
KH_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT = int(env.get('KH_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT', 85000))

RAZER_RECONCILIATION_LOOKBACK_DURATION = int(env.get('RAZER_RECONCILIATION_LOOKBACK_DURATION', 8))

DEEPLINK_URL = env.get('DEEPLINK_URL')

DYNAMIC_LTA_URL = env.get('DYNAMIC_LTA_URL')
GET_LTA_URL_FROM_ENV = env.get('GET_LTA_URL_FROM_ENV', False)

# cybersource
CYBERSOURCE_MERCHANT_ID = env.get('CYBERSOURCE_MERCHANT_ID')
CYBERSOURCE_AUTHENTICATION_TYPE = env.get('CYBERSOURCE_AUTHENTICATION_TYPE')
CYBERSOURCE_KEY_ID = env.get('CYBERSOURCE_KEY_ID')
CYBERSOURCE_SECRET_KEY = env.get('CYBERSOURCE_SECRET_KEY')
CYBERSOURCE_RUN_ENVIRONMENT = env.get('CYBERSOURCE_RUN_ENVIRONMENT')

CYBERSOURCE_MERCHANT_ID_3DS = env.get('CYBERSOURCE_MERCHANT_ID_3DS')
CYBERSOURCE_AUTHENTICATION_TYPE_3DS = env.get('CYBERSOURCE_AUTHENTICATION_TYPE_3DS')
CYBERSOURCE_KEY_ID_3DS = env.get('CYBERSOURCE_KEY_ID_3DS')
CYBERSOURCE_SECRET_KEY_3DS = env.get('CYBERSOURCE_SECRET_KEY_3DS')
CYBERSOURCE_RUN_ENVIRONMENT_3DS = env.get('CYBERSOURCE_RUN_ENVIRONMENT_3DS')

CYBERSOURCE_MERCHANT_ID_N3DS = env.get('CYBERSOURCE_MERCHANT_ID_N3DS')
CYBERSOURCE_AUTHENTICATION_TYPE_N3DS = env.get('CYBERSOURCE_AUTHENTICATION_TYPE_N3DS')
CYBERSOURCE_KEY_ID_N3DS = env.get('CYBERSOURCE_KEY_ID_N3DS')
CYBERSOURCE_SECRET_KEY_N3DS = env.get('CYBERSOURCE_SECRET_KEY_N3DS')
CYBERSOURCE_RUN_ENVIRONMENT_N3DS = env.get('CYBERSOURCE_RUN_ENVIRONMENT_N3DS')

# cybersource MYR MID
CYBERSOURCE_MERCHANT_ID_MYR = env.get('CYBERSOURCE_MERCHANT_ID_MYR')
CYBERSOURCE_AUTHENTICATION_TYPE_MYR = env.get('CYBERSOURCE_AUTHENTICATION_TYPE_MYR')
CYBERSOURCE_KEY_ID_MYR = env.get('CYBERSOURCE_KEY_ID_MYR')
CYBERSOURCE_SECRET_KEY_MYR = env.get('CYBERSOURCE_SECRET_KEY_MYR')
CYBERSOURCE_RUN_ENVIRONMENT_MYR = env.get('CYBERSOURCE_RUN_ENVIRONMENT_MYR')

CYBERSOURCE_MERCHANT_ID_3DS_MYR = env.get('CYBERSOURCE_MERCHANT_ID_3DS_MYR')
CYBERSOURCE_AUTHENTICATION_TYPE_3DS_MYR = env.get('CYBERSOURCE_AUTHENTICATION_TYPE_3DS_MYR')
CYBERSOURCE_KEY_ID_3DS_MYR = env.get('CYBERSOURCE_KEY_ID_3DS_MYR')
CYBERSOURCE_SECRET_KEY_3DS_MYR = env.get('CYBERSOURCE_SECRET_KEY_3DS_MYR')
CYBERSOURCE_RUN_ENVIRONMENT_3DS_MYR = env.get('CYBERSOURCE_RUN_ENVIRONMENT_3DS_MYR')

CYBERSOURCE_MERCHANT_ID_N3DS_MYR = env.get('CYBERSOURCE_MERCHANT_ID_N3DS_MYR')
CYBERSOURCE_AUTHENTICATION_TYPE_N3DS_MYR = env.get('CYBERSOURCE_AUTHENTICATION_TYPE_N3DS_MYR')
CYBERSOURCE_KEY_ID_N3DS_MYR = env.get('CYBERSOURCE_KEY_ID_N3DS_MYR')
CYBERSOURCE_SECRET_KEY_N3DS_MYR = env.get('CYBERSOURCE_SECRET_KEY_N3DS_MYR')
CYBERSOURCE_RUN_ENVIRONMENT_N3DS_MYR = env.get('CYBERSOURCE_RUN_ENVIRONMENT_N3DS_MYR')

CYBERSOURCE_CLIENT_SECRET_KEY = env.get('CYBERSOURCE_CLIENT_SECRET_KEY', 'APOLLO_MAIN')
PAYMENT_GATEWAY_TYPE = env.get('PAYMENT_GATEWAY_TYPE', 'CyberSource')

OCPI_COUNTRY_CODE = env.get('OCPI_COUNTRY_CODE')
OCPI_PARTY_ID = env.get('OCPI_PARTY_ID')

APPLY_FREE_CHARGING = pydantic.parse_obj_as(bool, env.get('APPLY_FREE_CHARGING', True))
HOGGING_START_GRACE_PERIOD_SECONDS = env.get('HOGGING_START_GRACE_PERIOD_SECONDS', 900)
HOGGING_CALL_BACK_URL = env.get('HOGGING_CALL_BACK_URL', None)
AUTHORIZATION_MESSAGE_WEBHOOK_URL = env.get('AUTHORIZATION_MESSAGE_WEBHOOK_URL', None)
AUTHORIZATION_MESSAGE_WEBHOOK_API_KEY = env.get('AUTHORIZATION_MESSAGE_WEBHOOK_API_KEY', None)
AUTHORIZE_MESSAGE_ENABLED = pydantic.parse_obj_as(bool, env.get('AUTHORIZE_MESSAGE_ENABLED', False))

JWT_REPORT_SECRET = env.get('JWT_REPORT_SECRET', 'APOLLO_REPORT')

VEHICLES_PER_USER_LIMIT = int(env.get('VEHICLES_PER_USER_LIMIT', 5))
CPO_TOKEN_URL = env.get('CPO_TOKEN_URL', 'https://auth.eu.plugncharge.hubject.com')
CPO_AUDIENCE = env.get('CPO_AUDIENCE', 'https://eu.plugncharge-qa.hubject.com')
CPO_OPCP_URL = env.get('CPO_OPCP_URL', 'https://eu.plugncharge-qa.hubject.com')
CPO_CLIENT_ID = env.get('CPO_CLIENT_ID', 'szjwFboxt5NCCV1E65TbNAdCkydspl01')
CPO_CLIENT_SECRET = env.get('CPO_CLIENT_SECRET', 'kkzG4D2Gn1Gra7_5sFIjdnc9gGWSsL4baD3d2FR7faBSubDEM59YVaWS__NHWSyV')

MO_TOKEN_URL = env.get('MO_TOKEN_URL', 'https://auth.eu.plugncharge.hubject.com')
MO_AUDIENCE = env.get('MO_AUDIENCE', 'https://eu.plugncharge-qa.hubject.com')
MO_OPCP_URL = env.get('MO_OPCP_URL', 'https://eu.plugncharge-qa.hubject.com')
MO_CLIENT_ID = env.get('MO_CLIENT_ID', 'jQAGuZTRiHf8ZDu3rTZHo6AA2znLiS0N')
MO_CLIENT_SECRET = env.get('MO_CLIENT_SECRET', 'Vv6YEn1Fv085bdXLKU1PZEUwq_pfvhOJCNpA0MM8kQ3LPe405uevqCz60fAWta6i')
EMAID_COUNTRY_CODE = env.get('EMAID_COUNTRY_CODE', 'EM')
EMAID_PROVIDER_ID = env.get('EMAID_PROVIDER_ID', 'P99')
AUTOCHARGE_VALIDITY_DURATION_YEARS = int(env.get('AUTOCHARGE_VALIDITY_DURATION_YEARS', 30))
HUBJECT_WEBHOOK_SECRET = env.get('HUBJECT_WEBHOOK_SECRET', 'VJMQd6BxYlfsXpwg16pLO0SijE0LetAYsQRy4NvGCp4=')

LTA_REPORT_RECEIVER_EMAIL = env.get('LTA_REPORT_RECEIVER_EMAIL', ['<EMAIL>', ])
if isinstance(LTA_REPORT_RECEIVER_EMAIL, list):
    LTA_REPORT_RECEIVER_EMAIL_LIST = LTA_REPORT_RECEIVER_EMAIL
else:
    LTA_REPORT_RECEIVER_EMAIL_LIST = json.loads(LTA_REPORT_RECEIVER_EMAIL)

# This will be inheritted to the LTA Static Reports and LTA Dynamic Data Report name.
LTA_COMPANY_NAME = env.get('LTA_COMPANY_NAME', 'Yinson EV Charge Pte Ltd')
# Company website, image url, thumbnail is used for LTA Dynamic Data Report
LTA_COMPANY_WEBSITE = env.get('LTA_COMPANY_WEBSITE', '')
LTA_COMPANY_IMAGE_URL = env.get('LTA_COMPANY_IMAGE_URL', '')
LTA_COMPANY_THUMBNAIL_URL = env.get('LTA_COMPANY_THUMBNAIL_URL', '')
# This is usedd to split LTA Static Data Reports to multiple sheet, 500 represents 500 chargers per sheet at max
LTA_REPORT_CHUNK_SIZE = env.get('LTA_REPORT_CHUNK_SIZE', 500)

# KH Razerpay
KH_MERCHANT_ID = get_env_or_secret('KH_MERCHANT_ID')
KH_VERIFY_KEY = get_env_or_secret('KH_VERIFY_KEY')
KH_SECRET_KEY = get_env_or_secret('KH_SECRET_KEY')

KH_RAZER_PAYMENT_DIRECT_API_URL = get_env_or_secret('KH_PAYMENT_DIRECT_API_URL')
KH_RAZER_PAYMENT_RECURRING_API_URL = get_env_or_secret('KH_PAYMENT_RECURRING_API_URL')
KH_RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL = get_env_or_secret('KH_PAYMENT_PRE_AUTH_REQUEST_API_URL')
KH_RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL = get_env_or_secret('KH_PAYMENT_PRE_AUTH_CAPTURE_API_URL')
KH_RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL = get_env_or_secret('KH_PAYMENT_PRE_AUTH_REFUND_API_URL')
KH_RAZER_PAYMENT_IPN_URL = get_env_or_secret('KH_PAYMENT_IPN_URL')
KH_RAZER_PAYMENT_RECONCILATION_API_URL = get_env_or_secret('KH_PAYMENT_RECONCILATION_API_URL')
KH_RAZER_TOKEN_API_URL = get_env_or_secret('KH_TOKEN_API_URL')
KH_RAZER_PAYMENT_RECONCILATION_BY_OID_URL = get_env_or_secret('KH_PAYMENT_RECONCILATION_BY_OID_URL')
KH_RAZER_PAYMENT_RECONCILATION_BY_TID_URL = get_env_or_secret('KH_PAYMENT_RECONCILATION_BY_TID_URL')

# BN Razerpay
BN_MERCHANT_ID = get_env_or_secret('BN_MERCHANT_ID')
BN_VERIFY_KEY = get_env_or_secret('BN_VERIFY_KEY')
BN_SECRET_KEY = get_env_or_secret('BN_SECRET_KEY')

BN_RAZER_PAYMENT_DIRECT_API_URL = get_env_or_secret('BN_PAYMENT_DIRECT_API_URL')
BN_RAZER_PAYMENT_RECURRING_API_URL = get_env_or_secret('BN_PAYMENT_RECURRING_API_URL')
BN_RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL = get_env_or_secret('BN_PAYMENT_PRE_AUTH_REQUEST_API_URL')
BN_RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL = get_env_or_secret('BN_PAYMENT_PRE_AUTH_CAPTURE_API_URL')
BN_RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL = get_env_or_secret('BN_PAYMENT_PRE_AUTH_REFUND_API_URL')
BN_RAZER_PAYMENT_IPN_URL = get_env_or_secret('BN_PAYMENT_IPN_URL')
BN_RAZER_PAYMENT_RECONCILATION_API_URL = get_env_or_secret('BN_PAYMENT_RECONCILATION_API_URL')
BN_RAZER_TOKEN_API_URL = get_env_or_secret('BN_TOKEN_API_URL')
BN_RAZER_PAYMENT_RECONCILATION_BY_OID_URL = get_env_or_secret('BN_PAYMENT_RECONCILATION_BY_OID_URL')
BN_RAZER_PAYMENT_RECONCILATION_BY_TID_URL = get_env_or_secret('BN_PAYMENT_RECONCILATION_BY_TID_URL')

TRACEMALLOC_TRACK_BOOL = env.get('TRACEMALLOC_TRACK', False)
TRACEMALLOC_TRACK = pydantic.parse_obj_as(bool, TRACEMALLOC_TRACK_BOOL)

# S3 Bucket for Upload Report
S3_BUCKET_REPORT = env.get('S3_BUCKET')
REPORT_DOWNLOAD_EXPIRY_DAYS = int(env.get('REPORT_DOWNLOAD_EXPIRY_DAYS', 14))
LTA_REPORT_EXPIRY_DAYS = int(env.get('LTA_REPORT_EXPIRY_DAYS', 90))

# Branding Settings
BRANDING_NAME = env.get('BRANDING_NAME', 'CDG Engie')
BRANDING_URL = env.get('BRANDING_URL', 'https://www.cdgengie.com/')
BRANDING_LOGO_URL = env.get(
    'BRANDING_LOGO_URL',
    'https://cdg-python-logo.s3.ap-southeast-1.amazonaws.com/' +
    'cdg_engie_logo.png'
)
BRANDING_SUPPORT_EMAIL = env.get('BRANDING_SUPPORT_EMAIL', '<EMAIL>')
BRANDING_SUPPORT_PHONE_NUMBER = env.get('BRANDING_SUPPORT_PHONE_NUMBER', '+6565531111')
BRANDING_ADDRESS = env.get('BRANDING_ADDRESS', 'ComfortDelGro ENGIE Pte Ltd\n205 Braddell Road\nSingapore 579701')
BRANDING_TAX_REG_NUMBER = env.get('BRANDING_TAX_REG_NUMBER', '202134785K')
BRANDING_QR_LOGO = env.get('BRANDING_QR_LOGO', 'chargev-logo.png')

RATE_LIMIT_LOGIN = pydantic.parse_obj_as(bool, env.get('RATE_LIMIT_LOGIN', False))
RATE_LOGIN_ATTEMPTS = env.get('RATE_LOGIN_ATTEMPTS', 5)

JWT_EMAIL_REPORT_SECRET = env.get('JWT_EMAIL_REPORT_SECRET', 'APOLLO_EMAIL_REPORT')

DURATION_MINUTE_TILL_PRE_AUTH_EXPIRY = int(env.get('DURATION_MINUTE_TILL_PRE_AUTH_EXPIRY', 60))
LENGTH_TILL_PRE_AUTH_UNABLE_TO_CAPTURE = int(env.get('LENGTH_TILL_PRE_AUTH_UNABLE_TO_CAPTURE', 7))

CAN_VIEW_SUB_ORG = pydantic.parse_obj_as(bool, env.get('CAN_VIEW_SUB_ORG', False))

MARK_FEE_LESS_THAN_101_FREE = pydantic.parse_obj_as(bool, env.get('MARK_FEE_LESS_THAN_101_FREE', True))
MARK_OCPI_FEE_LESS_THAN_101_FREE = pydantic.parse_obj_as(bool, env.get('MARK_OCPI_FEE_LESS_THAN_101_FREE', False))

REQUEST_AUTH_PERFORM_PRE_AUTH = pydantic.parse_obj_as(bool, env.get('REQUEST_AUTH_PERFORM_PRE_AUTH', False))
REQUEST_AUTH_PRE_AUTH_CURRENCY = env.get('REQUEST_AUTH_PRE_AUTH_CURRENCY', ['MYR', 'SGD'])
if isinstance(REQUEST_AUTH_PRE_AUTH_CURRENCY, list):
    REQUEST_AUTH_PRE_AUTH_CURRENCY_LIST = REQUEST_AUTH_PRE_AUTH_CURRENCY
else:
    REQUEST_AUTH_PRE_AUTH_CURRENCY_LIST = json.loads(REQUEST_AUTH_PRE_AUTH_CURRENCY)

CHECK_CSMS_TOKEN_EXPIRY = pydantic.parse_obj_as(bool, env.get('CHECK_CSMS_TOKEN_EXPIRY', False))

ENABLE_PARTIAL_PAYMENT_FLOW = pydantic.parse_obj_as(bool, env.get('ENABLE_PARTIAL_PAYMENT_FLOW', False))
LOW_WALLET_BALANCE = float(env.get('LOW_WALLET_BALANCE', 30))

ENABLE_TOKEN_CHECK_ON_START_CHARGING = pydantic.parse_obj_as(bool,
                                                             env.get('ENABLE_TOKEN_CHECK_ON_START_CHARGING', False))

ENABLE_PARENT_ORG_FOR_CHARGING_STATION_BOOL = env.get('ENABLE_PARENT_ORG_FOR_CHARGING_STATION', True)
ENABLE_PARENT_ORG_FOR_CHARGING_STATION = pydantic.parse_obj_as(bool, ENABLE_PARENT_ORG_FOR_CHARGING_STATION_BOOL)

TOKEN_CHECK_CURRENCY = env.get('TOKEN_CHECK_CURRENCY', ['MYR', 'SGD'])
if isinstance(TOKEN_CHECK_CURRENCY, list):
    TOKEN_CHECK_CURRENCY_LIST = TOKEN_CHECK_CURRENCY
else:
    TOKEN_CHECK_CURRENCY_LIST = json.loads(TOKEN_CHECK_CURRENCY)

ENABLE_SENTRY = pydantic.parse_obj_as(bool, env.get('ENABLE_SENTRY', True))

WALLET_CURRENCY = os.getenv('WALLET_CURRENCY', '["MYR", "SGD", "BND", "KHR"]')

# Parse CURRENCY_LIST to ensure it's a Python list, to enable dynamic creation of wallet
if isinstance(WALLET_CURRENCY, list):
    WALLET_CURRENCY_LIST = WALLET_CURRENCY
else:
    try:
        WALLET_CURRENCY_LIST = json.loads(WALLET_CURRENCY)
    except json.JSONDecodeError:
        raise ValueError("CURRENCY_LIST must be a valid JSON array or list.")

# DUNNING SETTINGS
DUNNING_OUTSTANDING_THRESHOLD = env.get('DUNNING_OUTSTANDING_THRESHOLD', 100)
DUNNING_START_DATE = env.get('DUNNING_START_DATE', '2024-06-01 15:59:59')
DUNNING_TRANSACTION_THRESHOLD = int(env.get('DUNNING_TRANSACTION_THRESHOLD', 3))
DUNNING_GRACE_PERIOD = int(env.get('DUNNING_GRACE_PERIOD', 24))
DUNNING_ENABLED = pydantic.parse_obj_as(bool, env.get('DUNNING_ENABLED', False))

OPERATOR_CONNECTOR_BULK_UPDATE = pydantic.parse_obj_as(bool, env.get('OPERATOR_CONNECTOR_BULK_UPDATE', False))

# Extra subscription plans
SUBSCRIPTION_OPERATOR_PLAN_ENABLED = pydantic.parse_obj_as(bool, env.get("SUBSCRIPTION_OPERATOR_PLAN_ENABLED", False))
SUBSCRIPTION_TARIFF_PLAN_ENABLED = pydantic.parse_obj_as(bool, env.get("SUBSCRIPTION_TARIFF_PLAN_ENABLED", False))

CAMPAIGN_PROMO_CODE_GENERATION_LIMIT = int(env.get('CAMPAIGN_PROMO_CODE_GENERATION_LIMIT') or 1000)

# refund pre-auth
PRE_AUTH_INSTANT_VOID = pydantic.parse_obj_as(bool, env.get('PRE_AUTH_INSTANT_VOID', True))
PRE_AUTH_VOID_AFTER_MINUTES = int(env.get('PRE_AUTH_VOID_AFTER_MINUTES') or 720)  # 12hrs
DECIMAL_TO_ROUND_IN_DISCOUNTED_UNIT_FEE = int(env.get('DECIMAL_TO_ROUND_IN_DISCOUNTED_UNIT_FEE') or 2)
REFUND_PRE_AUTH_WALLET_UPON_INCOMPLETE = pydantic.parse_obj_as(bool, env.get('REFUND_PRE_AUTH_WALLET_UPON_INCOMPLETE',
                                                                             True))

USE_CSB_INSTEAD_OF_PR_FOR_INVOICES = pydantic.parse_obj_as(bool, env.get('USE_CSB_INSTEAD_OF_PR_FOR_INVOICES', False))
USE_V2_MICROFORM = pydantic.parse_obj_as(bool, env.get('USE_V2_MICROFORM', False))
COMPLY_UOB_COMPLIANCE = pydantic.parse_obj_as(bool, env.get('COMPLY_UOB_COMPLIANCE', False))

ALLOW_OCPI_CHARGER_MORE_THAN_ORI = pydantic.parse_obj_as(bool, env.get('ALLOW_OCPI_CHARGER_MORE_THAN_ORI', True))
ENABLE_MOBILE_OCPI_DISCOUNT_API = pydantic.parse_obj_as(bool, env.get('ENABLE_MOBILE_OCPI_DISCOUNT_API', False))

ENABLE_RESTRICT_OTP_FAILURE = pydantic.parse_obj_as(bool, env.get('ENABLE_RESTRICT_OTP_FAILURE', False))
CHANGE_PARTIAL_FLOW_BY_DEFAULT = pydantic.parse_obj_as(bool, env.get('CHANGE_PARTIAL_FLOW_BY_DEFAULT', False))
PRE_AUTH_FORCE_3DS = pydantic.parse_obj_as(bool, env.get('PRE_AUTH_FORCE_3DS', False))
GUEST_PRE_AUTH_3DS_EVERY_CHARGE = pydantic.parse_obj_as(bool, env.get('GUEST_PRE_AUTH_3DS_EVERY_CHARGE', False))

CUSTOMIZED_FAILED_OTP_MESSAGE = env.get('CUSTOMIZED_FAILED_OTP_MESSAGE')
if CUSTOMIZED_FAILED_OTP_MESSAGE:
    FAILED_OTP_MESSAGE = CUSTOMIZED_FAILED_OTP_MESSAGE
else:
    FAILED_OTP_MESSAGE = (
        '⚠️ Unable to send OTP after multiple attempts.\n'
        'We are experiencing difficulties sending the verification code to your number.\n\n'
        'Check your network connection.\n'
        'Confirm your phone number is correct.\n'
        'Whatsapp Technical Hotline: +601123373387'
    )

CUSTOMIZED_INVALID_OTP_CHANNEL_MESSAGE = env.get('CUSTOMIZED_INVALID_OTP_CHANNEL_MESSAGE')
if CUSTOMIZED_INVALID_OTP_CHANNEL_MESSAGE:
    INVALID_OTP_CHANNEL_MESSAGE = CUSTOMIZED_INVALID_OTP_CHANNEL_MESSAGE
else:
    INVALID_OTP_CHANNEL_MESSAGE = 'Invalid Channel selected. Please contact customer support'

CUSTOMIZED_UNSUPPORTED_COUNTRY_PHONE_NUMBER_MESSAGE = env.get('CUSTOMIZED_UNSUPPORTED_COUNTRY_PHONE_NUMBER_MESSAGE')
if CUSTOMIZED_UNSUPPORTED_COUNTRY_PHONE_NUMBER_MESSAGE:
    UNSUPPORTED_COUNTRY_PHONE_NUMBER_MESSAGE = CUSTOMIZED_UNSUPPORTED_COUNTRY_PHONE_NUMBER_MESSAGE
else:
    UNSUPPORTED_COUNTRY_PHONE_NUMBER_MESSAGE = 'Unsupported phone number. Please contact customer support'

TRIGGER_REFUND_STATUS_TASK_VIA_CELERY = pydantic.parse_obj_as(bool,
                                                              env.get('TRIGGER_REFUND_STATUS_TASK_VIA_CELERY', True))

OUTLIER_KW_THRESHOLD = int(env.get('OUTLIER_KW_THRESHOLD') or 500)
OUTLIER_DURATION_THRESHOLD = int(env.get('OUTLIER_DURATION_THRESHOLD') or 86400)  # value in seconds (86400 = 1 day)
OUTLIER_AMOUNT_THRESHOLD = int(env.get('OUTLIER_AMOUNT_THRESHOLD') or 600)
OUTLIER_SGD_AMOUNT_THRESHOLD = int(env.get('OUTLIER_SGD_AMOUNT_THRESHOLD') or 1000)
OUTLIER_BND_AMOUNT_THRESHOLD = int(env.get('OUTLIER_BND_AMOUNT_THRESHOLD') or 1000)
OUTLIER_KHR_AMOUNT_THRESHOLD = int(env.get('OUTLIER_KHR_AMOUNT_THRESHOLD') or 1400000)
OUTLIER_IDR_AMOUNT_THRESHOLD = int(env.get('OUTLIER_IDR_AMOUNT_THRESHOLD') or 5700000)
ENABLE_OUTLIER_VALIDATION = pydantic.parse_obj_as(bool,
                                                  env.get('ENABLE_OUTLIER_VALIDATION', False))
OUTLIER_RECEIVER_EMAIL = env.get('OUTLIER_RECEIVER_EMAIL', ['<EMAIL>', ])
if isinstance(OUTLIER_RECEIVER_EMAIL, list):
    OUTLIER_RECEIVER_EMAIL_LIST = OUTLIER_RECEIVER_EMAIL
else:
    OUTLIER_RECEIVER_EMAIL_LIST = json.loads(OUTLIER_RECEIVER_EMAIL)

# redis
REDIS_URL = get_env_or_secret('REDIS_URL') or ''
REDIS_HOST = get_env_or_secret('REDIS_HOST') or ''
REDIS_PASSWORD = get_env_or_secret('REDIS_PASSWORD') or ''
REDIS_PORT = get_env_or_secret('REDIS_PORT') or '6379'
REDIS_IS_SECURE = get_env_or_secret('REDIS_IS_SECURE') or 'False'
REDIS_USER_DB = get_env_or_secret('REDIS_USER_DB') or '10'
REDIS_PROTOCOL = 'redis'
REDIS_REQUIRE_PASSWORD = pydantic.parse_obj_as(bool, env.get('REDIS_REQUIRE_PASSWORD', True))

if pydantic.parse_obj_as(bool, REDIS_IS_SECURE):
    REDIS_PROTOCOL = 'rediss'

REDIS_SSL_CERT = os.getenv('REDIS_SSL_CERT') or ''

if REDIS_REQUIRE_PASSWORD:
    REDIS_MASTER_USER_URL = (f"{REDIS_PROTOCOL}://:{str(REDIS_PASSWORD)}@{REDIS_HOST}:{str(REDIS_PORT)}/"
                             f"{str(REDIS_USER_DB)}")
else:
    REDIS_MASTER_USER_URL = f"{REDIS_PROTOCOL}://:@{REDIS_HOST}:{str(REDIS_PORT)}/{str(REDIS_USER_DB)}"

DISABLE_SIMULTANEOUS_LOGIN = pydantic.parse_obj_as(bool, env.get('DISABLE_SIMULTANEOUS_LOGIN', False))
REDIS_VALIDATE_USER_TOKEN_TIMEOUT = float(env.get('REDIS_VALIDATE_USER_TOKEN_TIMEOUT', 1.5))

ENFORCE_MFA = pydantic.parse_obj_as(bool, env.get('ENFORCE_MFA', False))
MFA_ISSUER_NAME = env.get('MFA_ISSUER_NAME', 'CDG-Apollo')
MFA_EXPIRY_MINUTES = int(env.get('MFA_EXPIRY_MINUTES') or 30)
MFA_JWT_SECRET = env.get('MFA_JWT_SECRET')
MFA_VALID_WINDOWS = int(env.get('MFA_VALID_WINDOWS', 3))

SEND_OCPI_ROAMING_OPERATING_HOURS = pydantic.parse_obj_as(bool, env.get('SEND_OCPI_ROAMING_OPERATING_HOURS', False))

E_INVOICE_REQUEST_BASE_URL = env.get('E_INVOICE_REQUEST_BASE_URL', None)
E_INVOICE_REQUEST_COMPANY_NAME = env.get('E_INVOICE_REQUEST_COMPANY_NAME', None)
E_INVOICE_API_BASE_URL = env.get('E_INVOICE_API_BASE_URL', None)
E_INVOICE_COMPANY_ID = env.get('E_INVOICE_COMPANY_ID', None)
E_INVOICE_CLIENT_ID = env.get('E_INVOICE_CLIENT_ID', None)
E_INVOICE_CLIENT_SECRET = env.get('E_INVOICE_CLIENT_SECRET', None)
E_INVOICE_ADD_SUPPLIER_INFO = pydantic.parse_obj_as(bool, env.get('E_INVOICE_ADD_SUPPLIER_INFO', False))
E_INVOICE_ADD_REDIRECT_URL = pydantic.parse_obj_as(bool, env.get('E_INVOICE_ADD_REDIRECT_URL', False))
E_INVOICE_CALL_BACK_EXPIRY_MINUTES = int(env.get('E_INVOICE_CALL_BACK_EXPIRY_MINUTES', 30))
E_INVOICE_JWT_SECRET = env.get('E_INVOICE_JWT_SECRET', 'APOLLO_E_INVOICE_CSMS_SECRET')

ENABLE_E_INVOICE = pydantic.parse_obj_as(bool, env.get('ENABLE_E_INVOICE', False))
ENABLE_EMSP_E_INVOICE = pydantic.parse_obj_as(bool, env.get('ENABLE_EMSP_E_INVOICE', False))
ENABLE_NATIVE_E_INVOICE = pydantic.parse_obj_as(bool, env.get('ENABLE_NATIVE_E_INVOICE', False))
ENABLE_PARTNER_E_INVOICE = pydantic.parse_obj_as(bool, env.get('ENABLE_PARTNER_E_INVOICE', False))

E_INVOICE_MEMBER_ID = env.get('E_INVOICE_MEMBER_ID', '[]')
if isinstance(E_INVOICE_MEMBER_ID, str):
    E_INVOICE_MEMBER_ID_LIST = json.loads(E_INVOICE_MEMBER_ID)
else:
    E_INVOICE_MEMBER_ID_LIST = E_INVOICE_MEMBER_ID

LTA_TASK_SPLIT_MODE = env.get('LTA_TASK_SPLIT_MODE', 'MONOLITH')
SAVE_LTA_DYNAMIC_LOG_TO_DB = pydantic.parse_obj_as(bool, env.get('SAVE_LTA_DYNAMIC_LOG_TO_DB', False))
LTA_DYNAMIC_RETRY = int(env.get('LTA_DYNAMIC_RETRY', 1))

PRE_AUTH_CAPTURE_OUTSTANDING_BALANCE = pydantic.parse_obj_as(bool, env.get('PRE_AUTH_CAPTURE_OUTSTANDING_BALANCE',
                                                                           False))
E_INVOICE_USE_RUNNING_NUMBER = pydantic.parse_obj_as(bool, env.get('E_INVOICE_USE_RUNNING_NUMBER', True))
UPDATE_RECURRING_TO_SUCCESS_ON_ON_ACCEPTED = pydantic.parse_obj_as(bool, env.get(
    'UPDATE_RECURRING_TO_SUCCESS_ON_ON_ACCEPTED', False))
