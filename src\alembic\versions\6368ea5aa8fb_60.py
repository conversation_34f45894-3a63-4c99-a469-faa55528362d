"""60

Revision ID: 6368ea5aa8fb
Revises: d49dc1f3d6e6
Create Date: 2023-10-13 10:49:26.905034

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6368ea5aa8fb'
down_revision = 'd49dc1f3d6e6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_external_organization', sa.Column('display_as_operator', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_external_organization', 'display_as_operator')
    # ### end Alembic commands ###
