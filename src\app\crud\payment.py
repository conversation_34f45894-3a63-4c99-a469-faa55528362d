# pylint:disable=too-many-lines
from decimal import Decimal
import json
import logging
from datetime import datetime
from fastapi import HTTP<PERSON>x<PERSON>
import pytz

from sqlalchemy import func, Float, or_, and_, cast, Numeric
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, NoResultFound

from app import models, schema, exceptions, settings
from app.schema import PaymentRequestReason
from app.settings import EV_EFFICIENCY_FACTOR, ICE_EMISSION_FACTOR, EV_EMISSION_FACTOR
from .base import BaseCRUD
from .billing import BillingCRUD

CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'

logger = logging.getLogger(__name__)

CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'


class PaymentCallbackCRUD(BaseCRUD):
    model = models.PaymentCallback


class PartnerPaymentNotificationCRUD(BaseCRUD):
    model = models.PartnerPaymentNotification


class OCPICPOCdrCRUD(BaseCRUD):
    model = models.OCPICPOCdr


class PaymentRequestCRUD(BaseCRUD):
    model = models.PaymentRequest

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        db_pr = dbsession.query(cls.model).get(object_id)
        if not db_pr:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_pr.member.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_pr.member.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        membership = cls.membership()
        if membership.user.is_superuser or not check_permission:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession, *columns).join(models.Membership).filter(
                models.Membership.organization_id.in_(allowed_orgs)
            )
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     return super().query(dbsession, *columns).join(models.Membership).filter(
        #         models.Membership.organization_id == membership.organization_id)
        if membership.membership_type == schema.MembershipType.regular_user:
            return super().query(dbsession, *columns).filter(cls.model.member_id == membership.id)
        raise exceptions.ApolloPermissionError()


class RecurringPaymentCRUD(BaseCRUD):
    model = models.RecurringPayment


class TokenCheckPreAuthPaymentCRUD(BaseCRUD):
    model = models.TokenCheckPreAuthPayment


class PreAuthPaymentCRUD(BaseCRUD):
    model = models.PreAuthPayment


class TokenCheckPreAuthPaymentRefundCRUD(BaseCRUD):
    model = models.TokenCheckPreAuthPaymentRefund


class PreAuthPaymentRefundCRUD(BaseCRUD):
    model = models.PreAuthPaymentRefund


class PreAuthPaymentCaptureCRUD(BaseCRUD):
    model = models.PreAuthPaymentCapture


class PaymentGatewayReconcilationCRUD(BaseCRUD):
    model = models.PaymentGatewayReconcilation

    @classmethod
    def add(cls, dbsession: Session, data: dict):
        """create an object
        """
        db_object = cls.model(**data)
        is_exist = dbsession.query(cls.model).filter(
            cls.model.order_id == db_object.order_id,
        ).first()
        if is_exist:
            db_object = dbsession.query(cls.model).filter(
                cls.model.order_id == data.get('order_id')
            ).first()
            cls.update(dbsession, db_object, data)
            return db_object
        dbsession.add(db_object)
        return db_object

    @classmethod
    def bulk_insert(cls, dbsession: Session, data: list[dict]):
        """create an object
        """
        for d in data:
            cls.add(dbsession, d)
        dbsession.commit()
        return len(data)

    @classmethod
    def update(cls, dbsession: Session, db_object: models.BaseModel, data: dict):
        """update an object
        """
        for key, value in data.items():
            try:
                setattr(db_object, key, value)
            except Exception as e:  # pylint: disable=broad-except
                print("Error getting value from model: %s", str(e))
                setattr(db_object, key, value)
        dbsession.commit()
        dbsession.refresh(db_object)
        return db_object


class CommercialInvoiceCRUD(BaseCRUD):
    model = models.CommercialInvoice

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        return True

    @classmethod
    def can_create(cls, dbsession: Session, data: dict, *args, **kwargs):
        return True


class PaymentRefundCRUD(BaseCRUD):
    model = models.PaymentRefund


class OutlierSessionCRUD(BaseCRUD):
    model = models.OutlierSession


def create_payment_callback(db: Session, payment_callback: schema.PaymentCallback) -> models.PaymentCallback:
    try:
        db_payment_callback = PaymentCallbackCRUD.add(db, payment_callback.dict())
        return db_payment_callback
    except IntegrityError:
        db.rollback()
        logger.error('Payment callback with duplicated order_id % received ', payment_callback.order_id)


def create_payment_request(db: Session, payment_request: schema.PaymentRequest,
                           member_id: str) -> models.PaymentRequest:
    try:
        db_payment_request = PaymentRequestCRUD.add(db,
                                                    dict(payment_request.dict(), member_id=member_id),
                                                    check_permission=False)
        return db_payment_request
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloPaymentRequestError()


def create_payment_request_no_member(db: Session, payment_request: schema.PaymentRequest) -> models.PaymentRequest:
    try:
        db_payment_request = PaymentRequestCRUD.add(db, dict(payment_request.dict()),
                                                    check_permission=False)
        return db_payment_request
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloPaymentRequestError()


def create_payment_request_redeem_package(db: Session, payment_request: schema.PaymentRequestRedeemPackage,
                                          member_id: str) -> models.PaymentRequest:
    try:
        db_payment_request = PaymentRequestCRUD.add(db,
                                                    dict(payment_request.dict(), member_id=member_id),
                                                    check_permission=False)
        return db_payment_request
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloPaymentRequestError()


def create_pre_payment_request(db: Session, payment_request: schema.PaymentRequest,
                               member_id: str) -> models.PaymentRequest:
    try:
        db_payment_request = PaymentRequestCRUD.add(db,
                                                    dict(payment_request.dict(),
                                                         member_id=member_id,
                                                         status=schema.PaymentRequestStatus.pre_charging))
        return db_payment_request
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloPaymentRequestError()


def get_pre_charging_payment_request(db: Session, member_id: str, connector_id: str) -> models.PaymentRequest:
    query = PaymentRequestCRUD.query(db).filter(
        models.PaymentRequest.member_id == member_id,
        models.PaymentRequest.connector_id == connector_id,
        models.PaymentRequest.status == schema.PaymentRequestStatus.pre_charging,
    )
    return query.first()


def get_pending_payment_requests(db: Session, member_id: str, type: schema.PaymentRequestType):
    query = PaymentRequestCRUD.query(db).filter(
        models.PaymentRequest.status == schema.PaymentRequestStatus.pending,
        models.PaymentRequest.type == type
    )
    return query.all()


def get_charging_session_pending_payment_requests(db: Session, charging_session_bill_id: str):
    query = PaymentRequestCRUD.query(db).filter(
        models.PaymentRequest.status == schema.PaymentRequestStatus.pending,
        models.PaymentRequest.charging_session_bill_id == charging_session_bill_id,
        models.PaymentRequest.type == schema.PaymentRequestType.recurring
    )
    return query.first()


def get_payment_reqeust_by_invoice_number(db: Session, invoice_number: str) -> models.PaymentRequest:
    try:
        query = PaymentRequestCRUD.query(db).filter(
            models.PaymentRequest.invoice_number == invoice_number,
        )
        return query.one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def get_payment_request_by_outstanding_order_id(db: Session, order_id: str) -> models.PaymentRequest:
    try:
        query = PaymentRequestCRUD.query(db).filter(
            models.PaymentRequest.pre_auth_outstanding_order_id == order_id,
        )
        return query.one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def get_payment_request_by_charging_session_bill_id(db: Session,
                                                    charging_session_bill_id: str) -> models.PaymentRequest:
    try:
        query = PaymentRequestCRUD.query(db).filter(
            models.PaymentRequest.charging_session_bill_id == charging_session_bill_id,
        )
        return query.one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def get_first_payment_request_by_charging_session_bill_sort_by_updated(db: Session,
                                                                       charging_session_bill_id: str) -> \
        models.PaymentRequest:
    try:
        query = PaymentRequestCRUD.query(db).filter(
            models.PaymentRequest.charging_session_bill_id == charging_session_bill_id,
        ).order_by(models.PaymentRequest.updated_at.desc().nullslast(), models.PaymentRequest.created_at.desc())
        pr = query.first()
        if not pr:
            raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')
        return pr
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def get_payment_request(db: Session, payment_request_id: str) -> models.PaymentRequest:
    try:
        db_payment_request = PaymentRequestCRUD.get(db, payment_request_id)
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def get_payment_request_by_invoice_numbers(db: Session, invoice_numbers: list[str]) -> list[models.PaymentRequest]:
    try:
        query = PaymentRequestCRUD.query(db).filter(
            or_(
                and_(
                    models.PaymentRequest.invoice_number.in_(invoice_numbers),
                    models.PaymentRequest.reason != PaymentRequestReason.pre_auth
                ),
                models.PaymentRequest.reference_number.in_(invoice_numbers),
            )
        )
        return query.all()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def get_payment_request_by_invoice_number(db: Session, invoice_numbers: str) -> models.PaymentRequest:
    try:
        query = PaymentRequestCRUD.query(db).filter(
            or_(
                and_(
                    models.PaymentRequest.invoice_number == invoice_numbers,
                    models.PaymentRequest.reason != PaymentRequestReason.pre_auth
                ),
                models.PaymentRequest.reference_number == invoice_numbers,
            )
        )
        return query.one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def update_payment_request(db: Session, payment_request: schema.PaymentRequestUpdate,
                           payment_request_id: str) -> models.PaymentRequest:
    try:
        db_payment_request = PaymentRequestCRUD.update(db,
                                                       payment_request_id,
                                                       payment_request.dict(exclude_unset=True,
                                                                            exclude_defaults=True),
                                                       check_permission=False
                                                       )
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def payment_request_done(db: Session, payment_request_id: str) -> models.PaymentRequest:
    try:
        db_payment_request = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.id == payment_request_id).one()
        db_payment_request.status = schema.PaymentRequestStatus.done
        db.commit()
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def payment_request_pending(db: Session, payment_request_id: str) -> models.PaymentRequest:
    try:
        db_payment_request = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.id == payment_request_id).one()
        db_payment_request.status = schema.PaymentRequestStatus.pending
        db.commit()
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def payment_request_failed(db: Session, payment_request_id: str) -> models.PaymentRequest:
    try:
        db_payment_request = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.id == payment_request_id).one()
        db_payment_request.status = schema.PaymentRequestStatus.failed
        db.commit()
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def payment_request_failed_partial_cc(db: Session, payment_request_id: str) -> models.PaymentRequest:
    try:
        db_payment_request = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.id == payment_request_id).one()
        db_payment_request.non_wallet_deduct_status = schema.PaymentRequestStatus.rejected
        db.commit()
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def payment_request_rejected(db: Session, payment_request_id: str) -> models.PaymentRequest:
    try:
        db_payment_request = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.id == payment_request_id).one()
        db_payment_request.status = schema.PaymentRequestStatus.rejected
        db.commit()
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def payment_request_rejected_partial_cc(db: Session, payment_request_id: str) -> models.PaymentRequest:
    try:
        db_payment_request = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.id == payment_request_id).one()
        db_payment_request.non_wallet_deduct_status = schema.PaymentRequestStatus.rejected
        db.commit()
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def create_recurring_payment(db: Session, recurring_payment: schema.RecurringPayment) -> models.RecurringPayment:
    try:
        db_recurring_payment = RecurringPaymentCRUD.add(db, recurring_payment.dict())
        return db_recurring_payment
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloRecurringPaymentError()


def get_recurring_payment_by_payment_request(db: Session, payment_request_id: str) -> models.RecurringPayment:
    query = RecurringPaymentCRUD.query(db).filter(models.RecurringPayment.order_id == str(payment_request_id))
    return query.first()


def create_token_check_pre_auth_payment(db: Session, token_check_pre_auth_payment: schema.TokenCheckPreAuthPayment) -> (
        models.TokenCheckPreAuthPayment):
    try:
        db_token_check_pre_auth_payment = TokenCheckPreAuthPaymentCRUD.add(db, token_check_pre_auth_payment.dict())
        return db_token_check_pre_auth_payment
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloPreAuthPaymentError()


def create_pre_auth_payment(db: Session, pre_auth_payment: schema.PreAuthPayment) -> models.PreAuthPayment:
    try:
        db_pre_auth_payment = PreAuthPaymentCRUD.add(db, pre_auth_payment.dict())
        return db_pre_auth_payment
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloPreAuthPaymentError()


def create_token_check_pre_auth_payment_refund(db: Session,
                                               token_check_pre_auth_payment_refund:
                                               schema.TokenCheckPreAuthPaymentRefundCreate) -> \
        models.TokenCheckPreAuthPaymentRefund:
    try:
        token_check_dict = token_check_pre_auth_payment_refund
        db_token_check_pre_auth_payment_refund = TokenCheckPreAuthPaymentRefundCRUD.add(db,
                                                                                        token_check_dict.dict(),
                                                                                        check_permission=False)
        return db_token_check_pre_auth_payment_refund
    except IntegrityError:
        raise exceptions.ApolloPreAuthPaymentError()


def update_token_check_pre_auth_payment_refund(db: Session, token_check_pre_auth_payment_refund_id: str,
                                               payment_response:
                                               schema.TokenCheckPreAuthPaymentRefundUpdateAfterResponse) -> \
        models.TokenCheckPreAuthPaymentRefund:
    try:
        db_payment_request = TokenCheckPreAuthPaymentRefundCRUD.update(db,
                                                                       token_check_pre_auth_payment_refund_id,
                                                                       payment_response.dict(exclude_unset=True,
                                                                                             exclude_defaults=True),
                                                                       check_permission=False
                                                                       )
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='TokenCheckPreAuthPaymentRefund')


def create_pre_auth_payment_refund(db: Session,
                                   pre_auth_payment_refund: schema.PreAuthPaymentRefundCreate) -> \
        models.PreAuthPaymentRefund:
    try:
        db_pre_auth_payment_refund = PreAuthPaymentRefundCRUD.add(db, pre_auth_payment_refund.dict(),
                                                                  check_permission=False)
        return db_pre_auth_payment_refund
    except IntegrityError:
        raise exceptions.ApolloPreAuthPaymentError()


def update_pre_auth_payment_refund(db: Session, pre_auth_payment_refund_id: str,
                                   payment_response: schema.PreAuthPaymentRefundUpdateAfterResponse) -> \
        models.PreAuthPaymentRefund:
    try:
        db_payment_request = PreAuthPaymentRefundCRUD.update(db,
                                                             pre_auth_payment_refund_id,
                                                             payment_response.dict(exclude_unset=True,
                                                                                   exclude_defaults=True),
                                                             check_permission=False
                                                             )
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PreAuthPaymentRefund')


def create_pre_auth_payment_capture(db: Session,
                                    payment_capture: schema.PreAuthPaymentCapture) -> models.PreAuthPaymentCapture:
    try:
        db_pre_auth_payment_capture = PreAuthPaymentCaptureCRUD.add(db, payment_capture.dict())
        return db_pre_auth_payment_capture
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloPreAuthPaymentError()


def get_pre_auth_payment_by_id(db: Session, pre_auth_payment_id: str) -> models.PreAuthPayment:
    db_pre_auth_payment = PreAuthPaymentCRUD.get(db, pre_auth_payment_id)
    return db_pre_auth_payment


def get_pre_auth_payment_by_pr_id(db: Session, payment_request_id: str) -> models.PreAuthPayment:
    db_pre_auth_payment = PreAuthPaymentCRUD.query(db).filter(
        models.PreAuthPayment.payment_request_id == payment_request_id)

    return db_pre_auth_payment.one()


def get_token_check_pre_auth_payment_by_id(db: Session,
                                           token_check_pre_auth_payment_id: str) -> models.TokenCheckPreAuthPayment:
    db_token_check_pre_auth_payment = TokenCheckPreAuthPaymentCRUD.get(db, token_check_pre_auth_payment_id)
    return db_token_check_pre_auth_payment


def get_token_check_pre_auth_payment_by_pr_id(db: Session, payment_request_id: str) -> models.TokenCheckPreAuthPayment:
    db_token_check_pre_auth_payment = TokenCheckPreAuthPaymentCRUD.query(db).filter(
        models.TokenCheckPreAuthPayment.payment_request_id == payment_request_id)

    return db_token_check_pre_auth_payment.one()


def get_non_binded_pre_auth_by_member_id(db: Session, member_id: str, currency: str) -> models.PreAuthPayment:
    db_pre_auth_payment = PreAuthPaymentCRUD.query(db).filter(
        models.PreAuthPayment.is_successful.is_(True),
        models.PreAuthPayment.is_binded.is_(False),
        models.PreAuthPayment.is_used.is_(False),
        models.PreAuthPayment.is_refunded.is_(False),
        models.PreAuthPayment.failed_refund.is_(False),
        models.PreAuthPayment.currency == currency
    ).join(models.PaymentRequest).filter(
        models.PaymentRequest.member_id == member_id)

    return db_pre_auth_payment.first()


def get_all_non_binded_pre_auth_more_than_created_at(db: Session, created_at: datetime,
                                                     currency: str = None) -> list[models.PreAuthPayment]:
    db_pre_auth_payment = PreAuthPaymentCRUD.query(db).filter(
        models.PreAuthPayment.is_successful.is_(True),
        models.PreAuthPayment.is_binded.is_(False),
        models.PreAuthPayment.is_used.is_(False),
        models.PreAuthPayment.is_refunded.is_(False),
        models.PreAuthPayment.failed_refund.is_(False),
        models.PreAuthPayment.created_at <= created_at
    )
    if currency:
        db_pre_auth_payment.filter(models.PreAuthPayment.currency == currency)

    return db_pre_auth_payment.all()


def get_non_binded_pre_auth_by_member_id_with_payment_type(db: Session, member_id: str, currency: str,
                                                           payment_type: str) -> models.PreAuthPayment:
    db_pre_auth_payment = PreAuthPaymentCRUD.query(db).filter(
        models.PreAuthPayment.is_successful.is_(True),
        models.PreAuthPayment.is_binded.is_(False),
        models.PreAuthPayment.is_used.is_(False),
        models.PreAuthPayment.is_refunded.is_(False),
        models.PreAuthPayment.failed_refund.is_(False),
        models.PreAuthPayment.currency == currency,
        models.PreAuthPayment.payment_type == payment_type,
    ).join(models.PaymentRequest).filter(
        models.PaymentRequest.member_id == member_id)

    return db_pre_auth_payment.first()


def get_preauth_by_id(db: Session, pre_auth_id) -> models.PreAuthPayment:
    db_pre_auth_payment = PreAuthPaymentCRUD.query(db).filter(
        models.PreAuthPayment.id == pre_auth_id
    ).join(models.PaymentRequest)

    return db_pre_auth_payment.one()


def get_pre_auth_payment_by_charging_session_id(db: Session, charging_session_id: str) -> models.PreAuthPayment:
    db_pre_auth_payment = PreAuthPaymentCRUD.query(db).filter(
        models.PreAuthPayment.charging_session_id == charging_session_id)

    return db_pre_auth_payment.one_or_none()


def update_token_check_pre_auth_payment(db: Session, token_check_pre_auth_payment_id: str,
                                        payment_request: schema.TokenCheckPreAuthPayment) \
        -> models.TokenCheckPreAuthPayment:
    try:
        db_payment_request = TokenCheckPreAuthPaymentCRUD.update(db,
                                                                 token_check_pre_auth_payment_id,
                                                                 payment_request.dict(exclude_unset=True,
                                                                                      exclude_defaults=True),
                                                                 check_permission=False
                                                                 )
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def update_pre_auth_payment(db: Session, pre_auth_payment_id: str,
                            payment_request: schema.PreAuthPayment) -> models.PreAuthPayment:
    try:
        db_payment_request = PreAuthPaymentCRUD.update(db,
                                                       pre_auth_payment_id,
                                                       payment_request.dict(exclude_unset=True,
                                                                            exclude_defaults=True),
                                                       check_permission=False
                                                       )
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


# Reporting module


def get_monthly_revenue(db: Session, charging_session: dict) -> dict:
    monthly_revenue = {}
    for key, value in charging_session.items():
        sum_of_paid_amounts = BillingCRUD.query(db,
                                                models.PaymentRequest.currency,
                                                func.sum(models.PaymentRequest.amount.cast(Float))) \
            .join(models.PaymentRequest) \
            .filter(models.ChargingSessionBill.status == 'Paid', models.ChargingSessionBill.id.in_(value)) \
            .group_by(models.PaymentRequest.currency).all()
        res = {row[0]: round(row[1], 2) for row in sum_of_paid_amounts}
        monthly_revenue[key] = {}
        monthly_revenue[key]['SGD'] = res.get('SGD', 0)
        monthly_revenue[key]['MYR'] = res.get('MYR', 0)
    return monthly_revenue


def get_monthly_revenue_v2(db: Session, connectors: list, year: int, timezone: str, is_superuser: bool,
                           headers: dict) -> dict:
    tz = pytz.timezone(timezone)
    date_start = datetime(year, 1, 1, tzinfo=tz)
    date_end = datetime(year, 12, 31, 23, 59, 59, tzinfo=tz)

    extract_mnt = func.extract('month', func.timezone(timezone, models.PaymentRequest.created_at))

    query = BillingCRUD.query(db,
                              models.PaymentRequest.currency,
                              func.sum(models.PaymentRequest.amount.cast(Float)),
                              extract_mnt) \
        .join(models.PaymentRequest) \
        .filter(models.ChargingSessionBill.status == 'Paid',
                models.ChargingSessionBill.created_at.between(date_start, date_end))

    if not is_superuser:
        connectors = connectors + json.loads(headers.get('accessible_charge_points', '[]'))
        query = query.filter(models.PaymentRequest.connector_id.in_(connectors))

    results = query.group_by(models.PaymentRequest.currency, extract_mnt).all()
    list_curr = ('MYR', 'SGD')
    monthly_data = {str(i): {key: 0 for key in list_curr} for i in range(1, 13)}
    for res in results:
        key, val, idx = res
        monthly_data[str(int(idx))][key] = round(val, 2)
    return monthly_data


def number_of_unique_users(db: Session, unique_id_tags: list) -> int:
    if unique_id_tags:
        query_users = db.query(models.Membership).filter(func.lower(models.Membership.user_id_tag).in_(unique_id_tags))
        unique_users = query_users.distinct(models.Membership.id).count()
        return unique_users
    return 0


def energy_consumption(data: dict):
    number_of_charging_sessions = len(data)
    # Calculate total energy consumed
    power_usage_list = [(charging_session['meter_stop'] - charging_session['meter_start'])
                        for charging_session in data]
    sum_of_power_usage = round((float(sum(power_usage_list)) / 1000), 3)

    # Calculate average energy consumption
    try:
        avg_energy_consumption = round(sum_of_power_usage / number_of_charging_sessions, 3)
    except ZeroDivisionError:
        avg_energy_consumption = 0
    return sum_of_power_usage, avg_energy_consumption


def energy_consumption_v2(data: list):
    number_of_charging_sessions = len(data)
    # Calculate total energy consumed
    sum_of_power_usage = round((float(sum(data)) / 1000), 3)

    # Calculate average energy consumption
    try:
        avg_energy_consumption = round(sum_of_power_usage / number_of_charging_sessions, 3)
    except ZeroDivisionError:
        avg_energy_consumption = 0
    return sum_of_power_usage, avg_energy_consumption


def total_payments(db: Session, data: dict, currency: str) -> float:
    charging_sessions_bill_id_list = [charging_session['charging_session_bill_id'] for charging_session in data]
    query = BillingCRUD.query(db, func.sum(models.PaymentRequest.amount.cast(Float))) \
        .join(models.PaymentRequest).filter(
        models.ChargingSessionBill.status == 'Paid', models.ChargingSessionBill.id.in_(charging_sessions_bill_id_list),
    )
    if currency:
        query = query.filter(models.PaymentRequest.currency.ilike(currency))
    sum_of_paid_amounts = query.one()[0] or 0
    return sum_of_paid_amounts


def total_payments_v2(db: Session, data: list, currency: str, date_filter: dict, is_superuser: bool,
                      headers: dict) -> float:
    if not data and not is_superuser:
        return 0
    query = BillingCRUD.query(db, func.sum(models.PaymentRequest.amount.cast(Float))) \
        .join(models.PaymentRequest).filter(models.ChargingSessionBill.status == 'Paid')

    if not is_superuser:
        data = data + json.loads(headers.get('accessible_charge_points', '[]'))
        query = query.filter(models.PaymentRequest.connector_id.in_(data))
    if currency:
        query = query.filter(models.PaymentRequest.currency.ilike(currency))
    if date_filter:
        query = query.filter(models.PaymentRequest.created_at >= date_filter['date_start'])
        query = query.filter(models.PaymentRequest.created_at <= date_filter['date_end'])
    sum_of_paid_amounts = query.one()[0] or 0
    return sum_of_paid_amounts


def operation_duration(data: dict):
    number_of_charging_sessions = len(data)
    total_duration_list = [(datetime.fromisoformat(charging_session['session_end']) -
                            datetime.fromisoformat(charging_session['session_start'])
                            ).total_seconds() for charging_session in data]

    total_duration = round(sum(total_duration_list), 2)

    # Calculate average operation duration
    try:
        avg_operation_duration = round(total_duration / number_of_charging_sessions, 2)
    except ZeroDivisionError:
        avg_operation_duration = 0
    return total_duration, avg_operation_duration


def total_mileage(data: dict):
    sum_of_power_usage, _ = energy_consumption(data)
    total_charged_mileage = round(float(sum_of_power_usage * EV_EFFICIENCY_FACTOR), 2)
    return total_charged_mileage


def total_greenhouse_saved(data: dict) -> float:
    sum_of_power_usage, _ = energy_consumption(data)
    total_charged_mileage = total_mileage(data)
    total_greenhouse_gas_saved = round(((total_charged_mileage * ICE_EMISSION_FACTOR)
                                        - (sum_of_power_usage * EV_EMISSION_FACTOR)), 2)
    return total_greenhouse_gas_saved


def total_mileage_v2(data: dict):
    sum_of_power_usage, _ = energy_consumption_v2(data)
    total_charged_mileage = round(float(sum_of_power_usage * EV_EFFICIENCY_FACTOR), 2)
    return total_charged_mileage, sum_of_power_usage


def total_greenhouse_saved_v2(data: dict) -> float:
    total_charged_mileage, sum_of_power_usage = total_mileage_v2(data)
    total_greenhouse_gas_saved = round(((total_charged_mileage * ICE_EMISSION_FACTOR)
                                        - (sum_of_power_usage * EV_EMISSION_FACTOR)), 2)
    return total_greenhouse_gas_saved


def create_cpo_cdr_session(db: Session, ocpi_cdr: schema.OCPICPOCdrCreate) -> models.OCPICPOCdr:
    try:
        ocpi_cdr = OCPICPOCdrCRUD.add(db, ocpi_cdr.dict())
        return ocpi_cdr
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICdr')


def update_cpo_cdr_session(db: Session, cdr_id, ocpi_cdr: schema.OCPICPOCdrUpdate) -> models.OCPICPOCdr:
    try:
        ocpi_cpo_cdr = OCPICPOCdrCRUD.query(db).filter(
            models.OCPICPOCdr.partner_ocpi_cpo_cdr_id == cdr_id).one()

        ocpi_cpo_cdr = OCPICPOCdrCRUD.update(db, ocpi_cpo_cdr.id, ocpi_cdr.dict())
        return ocpi_cpo_cdr
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICdr')


def update_cpo_cdr_session_by_id(db: Session, id: str, ocpi_cdr: schema.OCPICPOCdrUpdate) -> models.OCPICPOCdr:
    try:
        ocpi_cpo_cdr = OCPICPOCdrCRUD.query(db).filter(
            models.OCPICPOCdr.id == id).one()

        ocpi_cpo_cdr = OCPICPOCdrCRUD.update(db, ocpi_cpo_cdr.id, ocpi_cdr.dict(exclude_unset=True,
                                                                                exclude_defaults=True))
        return ocpi_cpo_cdr
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICdr')


def get_cpo_cdr_session(db: Session, cdr_id: str) -> models.OCPICPOToken:
    try:
        ocpi_cpo_token = OCPICPOCdrCRUD.query(db).filter(models.OCPICPOCdr.partner_ocpi_cpo_cdr_id == cdr_id).one()
        return ocpi_cpo_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICPOCdr')


def get_all_cpo_cdr_session(db: Session):
    try:
        ocpi_cpo_token = OCPICPOCdrCRUD.query(db)
        return ocpi_cpo_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICPOCdr')


def get_all_payment_gateway_reconcilation(db: Session, filters: dict) -> list[models.PaymentGatewayReconcilation]:  # noqa: MC0001
    query = PaymentGatewayReconcilationCRUD.query(db)
    if filters['invoice_number']:
        query = query.filter(models.PaymentGatewayReconcilation.order_id.ilike(f'%{filters["invoice_number"]}%'))
    if filters['email']:
        query = query.filter(models.PaymentGatewayReconcilation.billing_email.ilike(f'%{filters["email"]}%'))
    if filters['order_id']:
        query = query.filter(models.PaymentGatewayReconcilation.order_id.ilike(f'%{filters["order_id"]}%'))
    if filters['tran_id']:
        query = query.filter(models.PaymentGatewayReconcilation.tran_id == filters['tran_id'])
    if filters['channel']:
        query = query.filter(models.PaymentGatewayReconcilation.channel.ilike(f'%{filters["channel"]}%'))
    if filters['stat_code']:
        query = query.filter(models.PaymentGatewayReconcilation.stat_code == filters['stat_code'])
    if filters['stat_name']:
        query = query.filter(models.PaymentGatewayReconcilation.stat_name == filters['stat_name'])
    if filters['date_from']:
        query = query.filter(models.PaymentGatewayReconcilation.billing_date >= filters['date_from'])
    if filters['date_to']:
        query = query.filter(models.PaymentGatewayReconcilation.billing_date < filters['date_to'])
    if filters['currency']:
        query = query.join(models.PaymentRequest)
        query = query.filter(models.PaymentRequest.currency == filters['currency'])
    if filters['billing_status']:
        query = query.join(models.PaymentRequest)
        query = query.filter(models.PaymentRequest.status == filters['billing_status'])
    if filters['phone_number']:
        query = query.join(models.Membership).join(models.User)
        query = query.filter(models.User.phone_number.ilike(f'%{filters["phone_number"]}%'))
    query = query.order_by(models.PaymentGatewayReconcilation.billing_date.desc())

    return query


def create_commercial_invoice(db: Session, commercial_invoice: schema.CommercialInvoice,
                              member_id: str) -> models.CommercialInvoice:
    try:
        db_commercial_invoice = CommercialInvoiceCRUD.add(db,
                                                          dict(commercial_invoice.dict(), member_id=member_id),
                                                          check_permission=False)
        return db_commercial_invoice
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloCommercialInvoiceError()


def update_commercial_invoice(db: Session, commercial_invoice: schema.CommercialInvoice,
                              commercial_invoice_id: str) -> models.CommercialInvoice:
    try:
        db_commercial_invoice = CommercialInvoiceCRUD.update(db,
                                                             commercial_invoice_id,
                                                             commercial_invoice.dict(exclude_unset=True,
                                                                                     exclude_defaults=True),
                                                             check_permission=False
                                                             )
        return db_commercial_invoice
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='CommercialInvoice')


def get_total_payment_refund_by_payment_request_id(db: Session, payment_request_id: str,
                                                   refund_type: schema.RefundType) -> Decimal:
    try:
        total_refund = PaymentRefundCRUD.query(db, func.sum(cast(models.PaymentRefund.refund_amount, Numeric)))\
            .filter(
                models.PaymentRefund.payment_request_id == payment_request_id,
                models.PaymentRefund.refund_status != schema.RefundStatus.rejected,
                models.PaymentRefund.refund_type == refund_type).scalar() or '0'
        return Decimal(str(total_refund))
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRefund')


def get_all_payment_refund_by_invoice_number(db: Session, invoice_number: str):
    query = PaymentRefundCRUD.query(db)
    query = query.join(models.PaymentRequest).filter(models.PaymentRequest.invoice_number == invoice_number) \
        .order_by(models.PaymentRefund.created_at.desc())
    return query


def get_payment_refund_by_id(db: Session, refund_id: str):
    query = PaymentRefundCRUD.query(db)
    query = query.filter(models.PaymentRefund.id == refund_id)
    return query.first()


def validate_payment_status(status, expected_status=schema.PaymentRequestStatus.done):
    if status != expected_status:
        raise HTTPException(status_code=400, detail='Payment request status must be done')


def get_refund_details(payment_request, refund_data):
    payment_type = payment_request.type

    if payment_type in [schema.PaymentRequestType.wallet, schema.PaymentRequestType.recurring,
                        schema.PaymentRequestType.direct]:
        validate_payment_status(payment_request.status)

        # Validate wallet refund type matches wallet payment type
        if (refund_data.get('refund_type') == schema.RefundType.wallet and
                payment_type != schema.PaymentRequestType.wallet):
            raise HTTPException(
                status_code=400,
                detail='Cannot process wallet refund for non-wallet payment'
            )

        # Validate credit card refund type matches recurring payment type
        if (refund_data.get('refund_type') == schema.RefundType.credit_card and
                payment_type not in [schema.PaymentRequestType.direct, schema.PaymentRequestType.recurring]):
            raise HTTPException(
                status_code=400,
                detail='Cannot process credit card refund for wallet payment'
            )

        return {
            'refund_type': (schema.RefundType.wallet if payment_type == schema.PaymentRequestType.wallet
                            else schema.RefundType.credit_card),
            'paid_amount': Decimal(str(payment_request.amount))
        }

    # Validate partial payment type
    if payment_type in [schema.PaymentRequestType.partial_direct, schema.PaymentRequestType.partial]:
        is_wallet_refund = refund_data.get('refund_type') == schema.RefundType.wallet
        status = (payment_request.wallet_deduct_status if is_wallet_refund
                  else payment_request.non_wallet_deduct_status)
        amount = (payment_request.wallet_deduct_amount if is_wallet_refund
                  else payment_request.non_wallet_deduct_amount)

        validate_payment_status(status)
        return {
            'refund_type': schema.RefundType.wallet if is_wallet_refund else schema.RefundType.credit_card,
            'paid_amount': Decimal(str(amount))
        }
    raise HTTPException(status_code=400, detail='Payment type not supported for refund')


def create_payment_refund(db: Session, refund_payment: schema.CreatePaymentRefund,
                          ) -> models.PaymentRefund:
    try:
        db_refund_payment = PaymentRefundCRUD.add(db,
                                                  dict(refund_payment.dict()))
        return db_refund_payment
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloPaymentRefundError()


def get_payment_refund_by_pg_refund_id(dbsession: Session, pg_refund_id: str
                                       ) -> models.PaymentRefund:
    try:
        db_refund_payment = PaymentRefundCRUD.query(dbsession).filter(
            models.PaymentRefund.pg_refund_id == pg_refund_id).one()
        return db_refund_payment
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRefund')


def get_payment_refund_by_refund_id(dbsession: Session, refund_id: str
                                    ) -> models.PaymentRefund:
    try:
        db_refund_payment = PaymentRefundCRUD.query(dbsession).filter(
            models.PaymentRefund.id == refund_id).one()
        return db_refund_payment
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRefund')


def update_payment_refund(db: Session, refund_id: str,
                          refund_payment: schema.UpdatePaymentRefund,
                          ) -> models.PaymentRefund:
    try:
        db_payment_request = PaymentRefundCRUD.update(db,
                                                      refund_id,
                                                      refund_payment.dict(exclude_unset=True,
                                                                          exclude_defaults=True),
                                                      check_permission=False)
        return db_payment_request
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')


def get_payment_refund_by_reference_id(dbsession: Session, reference_id: str
                                       ) -> models.PaymentRefund:
    try:
        db_refund_payment = PaymentRefundCRUD.query(dbsession).filter(
            models.PaymentRefund.reference_id == reference_id).one()
        return db_refund_payment
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRefund')
