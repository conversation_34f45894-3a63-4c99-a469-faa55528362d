# pylint: disable=too-many-lines
import logging
import re
import json
import traceback
from typing import List, Optional
from uuid import UUID
from datetime import datetime, timezone, timedelta
import hashlib
import hmac
import ccy
import macaddress
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi_pagination import Params, Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.orm import Session

from app import settings, schema, crud, exceptions
from app.database import create_session, SessionLocal
from app.permissions import permission, decode_auth_token_from_headers
from app.utils import GenerateReport, lookup_pcid, push_emaid, delete_contract_certificate, \
    provision_contract_certificate, delete_emaid_for_pcid_locally, recreate_emaid_for_pcid, send_request
from app.crud import (IDTagCRUD, EmaidCRUD, AutochargeCRUD, get_contract_cert_orders, create_id_tag,
                      create_random_emaid_tag, update_vehicle_history)
from app.schema.v2 import (AutochargeCreate, AutochargeStatus, EmaidUpdate,
                           EmaidStatus, AutochargeUpdate, DeleteVehicleResponse)
from app.schema import ContractCertificateFeeCreate, ContractCertificateFeeUpdate, IDTagUpdate, IDTagType, IDTag, \
    EmaidCreate, VehicleUpdate, ProviderLogCreate as ProviderLogCreateSchema
from app.middlewares import set_admin_as_context_user, deactivate_audit

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH


router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/vehicle",
    tags=['vehicle', ],
    dependencies=[Depends(permission)]
)

router_webhook = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/plug_and_charge/webhook",
    tags=['vehicle', ]
)


async def send_webhook_event_notification(member_id: UUID, pcid: str, emaid: str, webhook_event_type: str):
    '''
    Background task to send notifications to users impacted by a Hubject Webhook Event
    Each event in the event list should have the format (member_id, emaid)
    '''
    mobile_webhook_url = settings.AUTHORIZATION_MESSAGE_WEBHOOK_URL
    mobile_webhook_api_key = settings.AUTHORIZATION_MESSAGE_WEBHOOK_API_KEY
    headers = {
        'X-API-Key': mobile_webhook_api_key,
        'content-type': 'application/json'
    }

    message_data = {
        "event": "Message Callback",
        "payload": {"member_id": str(member_id)}
    }
    if webhook_event_type in ['mo.prov.cert.deleted', 'mo.prov.cert.factory.reset']:
        message_data["event_banner_title"] = "Plug and Charge Contract Certificate Deletion"
        message_data["event_banner"] = f'Your contract certificate {emaid} has ' + \
            'been deleted by the OEM/EV manufacturer'
    elif webhook_event_type == 'mo.prov.cert.updated':
        message_data["event_banner_title"] = "Plug and Charge Contract Certificate Recreated"
        message_data["event_banner"] = f'Your contract certificate {emaid} has ' + \
            'been recreated due to an update in you vehicle by the OEM/EV manufacturer'
    else:
        return
    try:
        await send_request('POST', mobile_webhook_url, data=json.dumps(message_data), headers=headers)
    except Exception as e:  # pylint: disable=broad-except
        logger.error("Error calling message webhook on mobile app with error: %s", str(e))


@router.get("/contract_certificate_fee_list", status_code=status.HTTP_200_OK)
async def get_contract_certificate_fee_list(request: Request,
                                            dbsession: SessionLocal = Depends(create_session)):
    """
    Get Contract Certificate Fee List
    """
    try:
        db_cc_fee_list = crud.get_contract_certificate_fee_list(dbsession)
        return db_cc_fee_list
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/contract_certificate_fee/{iso3166_country_code}", status_code=status.HTTP_200_OK)
async def get_contract_certificate_fee_by_country(request: Request, iso3166_country_code: str,
                                                  dbsession: SessionLocal = Depends(create_session)):
    """
    Get Contract Certificate Fee for a particular country
    """
    try:
        db_cc_fee = crud.get_contract_certificate_fee_by_country(dbsession, iso3166_country_code)
        return db_cc_fee
    except exceptions.NoResultFound as e:
        raise HTTPException(400, e.__str__())


@router.post("/contract_certificate_fee", status_code=status.HTTP_201_CREATED)
async def create_contract_certificate_fee(request: Request, cc_fee: ContractCertificateFeeCreate,  # noqa: MC0001
                                          dbsession: SessionLocal = Depends(create_session)):
    """
    Create Contract Certificate Fee for a country. There should be only one fee record per country
    """
    try:
        # Only superuser or user in the highest org can set the fee
        try:
            auth_token_data = decode_auth_token_from_headers(request.headers)
            user_obj = crud.get_user_by_id(dbsession, auth_token_data['user_id'])
            is_superuser = user_obj.is_superuser
            org_obj = crud.get_organization_by_id(dbsession, user_obj.id)
            parent = crud.get_organization_by_id(dbsession, org_obj.parent_id)

            if not is_superuser:
                if parent.parent_id is not None and parent.parent.parent_id is not None:
                    raise HTTPException(400,
                                        'You are not permitted to set the contract certificate fee, '
                                        'please contact Administrator.')
        except exceptions.ApolloObjectDoesNotExist:
            pass
        try:
            db_cc_fee = crud.get_contract_certificate_fee_by_country(dbsession, cc_fee.iso3166_country_code)
        except exceptions.ApolloObjectDoesNotExist:
            pass

        if db_cc_fee is not None:
            raise HTTPException(400, 'Fee has already been set for this country')
        if cc_fee.activation_fee < 0:
            raise HTTPException(409, 'Activation Fee should not be negative')
        if cc_fee.renewal_fee < 0:
            raise HTTPException(400, 'Renewal Fee should not be negative')
        iso3166_country_code = cc_fee.iso3166_country_code
        entered_currency = cc_fee.payment_currency
        country_to_currency = ccy.countryccy(iso3166_country_code)
        if country_to_currency is None or country_to_currency != entered_currency:
            raise HTTPException(400, 'Invalid country or currency')

        db_cc_fee = crud.create_contract_certificate_fee(dbsession, cc_fee)
        return db_cc_fee
    except exceptions.ApolloContractCertificateFeeError as e:
        raise HTTPException(400, e.__str__())


@router.put("/contract_certificate_fee", status_code=status.HTTP_200_OK)
async def update_contract_certificate_fee(request: Request, cc_fee: ContractCertificateFeeUpdate,  # noqa: MC0001
                                          dbsession: SessionLocal = Depends(create_session)):
    """
    Update Contract Certificate Fee for a country
    """
    try:
        # Only superuser or user in the highest org can update the fee
        try:
            auth_token_data = decode_auth_token_from_headers(request.headers)
            user_obj = crud.get_user_by_id(dbsession, auth_token_data['user_id'])
            is_superuser = user_obj.is_superuser
            org_obj = crud.get_organization_by_id(dbsession, user_obj.id)
            parent = crud.get_organization_by_id(dbsession, org_obj.parent_id)

            if not is_superuser:
                if parent.parent_id is not None and parent.parent.parent_id is not None:
                    raise HTTPException(400,
                                        'You are not permitted to update the contract certificate fee, '
                                        'please contact Administrator.')
        except exceptions.ApolloObjectDoesNotExist:
            pass

        if cc_fee.activation_fee is not None and cc_fee.activation_fee < 0:
            raise HTTPException(409, 'Activation Fee should not be negative')
        if cc_fee.renewal_fee is not None and cc_fee.renewal_fee < 0:
            raise HTTPException(400, 'Renewal Fee should not be negative')
        iso3166_country_code = cc_fee.iso3166_country_code
        entered_currency = cc_fee.payment_currency
        country_to_currency = ccy.countryccy(iso3166_country_code)
        if country_to_currency is None or country_to_currency != entered_currency:
            raise HTTPException(400, 'Invalid country or currency')
        db_cc_fee = crud.update_contract_certificate_fee_by_country(dbsession, cc_fee, cc_fee.iso3166_country_code)
        if db_cc_fee is None:
            raise HTTPException(404, 'Fee record not found')
        return db_cc_fee
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/contract_certificate_fee/{iso3166_country_code}", status_code=status.HTTP_200_OK)
async def delete_contract_certificate_fee(request: Request, iso3166_country_code: str,
                                          dbsession: SessionLocal = Depends(create_session)):
    """
    Create Contract Certificate Fee for a country
    """
    try:
        # Only superuser or user in the highest org can delete the fee
        try:
            auth_token_data = decode_auth_token_from_headers(request.headers)
            user_obj = crud.get_user_by_id(dbsession, auth_token_data['user_id'])
            is_superuser = user_obj.is_superuser
            org_obj = crud.get_organization_by_id(dbsession, user_obj.id)
            parent = crud.get_organization_by_id(dbsession, org_obj.parent_id)

            if not is_superuser:
                if parent.parent_id is not None and parent.parent.parent_id is not None:
                    raise HTTPException(400,
                                        'You are not permitted to delete the contract certificate fee, '
                                        'please contact Administrator.')
        except exceptions.ApolloObjectDoesNotExist:
            pass
        db_cc_fee = crud.delete_contract_certificate_fee_by_country(dbsession, iso3166_country_code)
        return db_cc_fee
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/contract_certificate_orders", status_code=status.HTTP_200_OK,
            response_model=List[schema.CertificateOrderResponse])
async def get_contract_certificate_order(request: Request,
                                         dbsession: SessionLocal = Depends(create_session)):
    """
    Get Contract Certificate Order List
    """
    try:
        db_cc_order_list = get_contract_cert_orders(dbsession)
        return db_cc_order_list
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Hubject Webhook
@router_webhook.post('/', include_in_schema=False, status_code=status.HTTP_200_OK)
async def hubject_webhook(request: Request, background_tasks: BackgroundTasks,  # noqa: MC0001
                          dbsession: SessionLocal = Depends(create_session)):
    """
    Handle Hubject webhook events specified in
    https://support.hubject.com/hc/en-us/articles/9300360706077-1-8-Webhook-Notification-Service
    """
    hubject_signature_key = 'X-Hubject-Signature'
    x_hubject_signature = None
    http_status = 200
    try:
        if hubject_signature_key in request.headers:
            x_hubject_signature = request.headers.get(hubject_signature_key)
        elif hubject_signature_key.lower() in request.headers:
            x_hubject_signature = request.headers.get(hubject_signature_key.lower())

        if x_hubject_signature is None:
            raise HTTPException(status_code=403, detail="Missing signature")

        # Format of the signature is sha256=<hash>. Extracting the hash out.
        x_hubject_signature = x_hubject_signature.split("=")[1]
        body = await request.json()

        body_str = json.dumps(body, separators=(',', ':'))
        # Generate expected HMAC-SHA256 signature
        expected_signature = hmac.new(
            key=settings.HUBJECT_WEBHOOK_SECRET.encode('utf-8'),
            msg=body_str.encode('utf-8'),
            digestmod=hashlib.sha256).hexdigest()

        # Verify the signature matches the expected signature
        if not hmac.compare_digest(expected_signature, x_hubject_signature):
            raise HTTPException(403, 'Invalid Signature')

        event_type = body.get('eventType')
        set_admin_as_context_user(dbsession)
        deactivate_audit()
        payload = body.get('payload')

        if event_type in ('mo.prov.cert.deleted', 'mo.prov.cert.factory.reset'):
            pcid = payload.get('pcid')
            emaid = payload.get('emaid')
            member_id = await delete_emaid_for_pcid_locally(dbsession, pcid, emaid)
        elif event_type == 'mo.prov.cert.updated':
            pcid = payload.get('pcid')
            emaid = payload.get('emaid')
            member_id = await recreate_emaid_for_pcid(dbsession, pcid, emaid)
        if member_id is not None:
            logger.info('#### Scheduling background task')
            background_tasks.add_task(send_webhook_event_notification, member_id, pcid, emaid, event_type)

    except HTTPException as e:
        http_status = e.status_code
        raise
    except Exception as e:
        logger.error(traceback.format_exc())
        http_status = 400
        raise HTTPException(400, e.__str__())
    finally:
        logger.info('Logging Hubject Webhook Event: %s', body_str)
        request_time = datetime.now().astimezone(timezone.utc)
        provider_log = ProviderLogCreateSchema(
            name='Hubject',
            url=str(request.url),
            http_method='POST',
            request_at=request_time,
            request_header=json.dumps(dict(request.headers)),
            request_body=body_str,
            response_status_code=str(http_status),
            response_body='',
            response_at=datetime.now().astimezone(timezone.utc)
        )
        crud.create_provider_log(dbsession, provider_log)


@router.post("/", response_model=schema.VehicleResponse,
             status_code=status.HTTP_201_CREATED)
async def add_vehicle(vehicle_data: schema.Vehicle,
                      dbsession: SessionLocal = Depends(create_session)):
    """
    Add an Vehicle
    :param Vehicle vehicle_data: Vehicle Data
    """
    try:
        db_vehicle = crud.create_vehicle(dbsession, vehicle_data)
        return db_vehicle
    except exceptions.ApolloVehicleError as e:
        raise HTTPException(400, e.__str__())


@router.post("/{membership_id}", response_model=schema.VehicleResponse,
             status_code=status.HTTP_201_CREATED)
async def add_vehicle_with_membership(vehicle_data: schema.VehicleRegistration,  # noqa: MC0001
                                      membership_id: UUID,
                                      mac_address: str = None,
                                      dbsession: SessionLocal = Depends(create_session)):
    """
    Add an Vehicle
    :param Vehicle vehicle_data: Vehicle Data
    :param UUID membership_id: Membership ID
    """
    # First make sure that the user vehicle limit has not been reached.
    try:
        db_vehicle_list = crud.get_membership_vehicle_list(dbsession, membership_id)
        if len(db_vehicle_list) >= settings.VEHICLES_PER_USER_LIMIT:
            raise HTTPException(409, 'Vehicle Limit Reached')
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(404, 'Member not found')

    try:
        ac_status = None
        mac_str = None
        if vehicle_data.pcid is not None:
            # Check if the pcid is already exist
            vehicle_record = crud.get_vehicle_with_pcid(
                dbsession, vehicle_data.pcid)
            if vehicle_record is not None:
                # enhance to handle register error
                raise HTTPException(400, 'PCID is already registered.')

            # Lookup if the pcid is registered in Hubject Prov Cert Pool
            lookup_pcid_status, message = await lookup_pcid(dbsession, vehicle_data.pcid)
            if not lookup_pcid_status:
                raise HTTPException(400, message)

        # If the vehicle name is empty, use the registration number
        if vehicle_data.name is None:
            vehicle_data.name = vehicle_data.registration_number

        if vehicle_data.ac_status is not None:
            if vehicle_data.ac_status == AutochargeStatus.active:
                if mac_address is None:
                    raise HTTPException(400, 'Vehicle MAC Address is missing')
                try:
                    mac_str = str(macaddress.MAC(mac_address)).replace('-', '')
                    if crud.get_autocharge_by_vid(dbsession, mac_str) is not None:
                        raise HTTPException(400, 'Vehicle MAC Address already exists')
                except ValueError:
                    raise HTTPException(400, 'Invalid MAC Address')
            elif vehicle_data.ac_status != AutochargeStatus.pending:
                raise HTTPException(400, f'Autocharge status {vehicle_data.ac_status} not supported')
            ac_status = vehicle_data.ac_status
        del vehicle_data.ac_status

        db_vehicle = crud.create_vehicle_v2(dbsession, vehicle_data)
        _ = crud.add_membership_vehicle(
            dbsession, db_vehicle.id, membership_id)

    except exceptions.ApolloVehicleError:
        raise HTTPException(400, 'Vehicle cannot be created.')

    # Register/Activate Plug and Charge
    try:
        if vehicle_data.pcid is not None:
            emaid_prefix = settings.EMAID_COUNTRY_CODE + settings.EMAID_PROVIDER_ID
            emaid_tag = emaid_prefix + create_random_emaid_tag()
            emaid_data = EmaidCreate(
                emaid=emaid_tag,  # need make it as dynamic
                status=EmaidStatus.pending,
                status_comment='Registration in progress.',
                vehicle_id=str(db_vehicle.id)
            )
            logger.debug('CSMS PnC: Creating Emaid for Vehicle: %s with emaid: %s', str(db_vehicle.id), emaid_tag)
            db_emaid = crud.create_emaid(dbsession, emaid_data)
            update_vehicle_history(dbsession, db_vehicle.id)
            id_tag_data = IDTag(
                id_tag=db_emaid.emaid,
                name=f'Contract for {db_vehicle.pcid}',
                type=IDTagType.pnc,
                expiration=datetime.now(timezone.utc),
                is_active=False,
                description=f'Contract id tag for {db_vehicle.pcid}',
                member_id=membership_id,
                emaid_id=str(db_emaid.id)
            )
            logger.debug('PnC: Creating ID Tag %s for emaid: %s', db_emaid.emaid, db_emaid.id)
            create_id_tag(dbsession, id_tag_data)
            await provision_contract_certificate(db_vehicle, dbsession, "New")
    except exceptions.ApolloEmaidError:
        raise HTTPException(400, 'Error provisioning contract Certificate.')

    # Register/Activate Autocharge is parameter were provided
    try:
        if ac_status is not None:
            autocharge_data = AutochargeCreate(
                status=AutochargeStatus.pending,
                vehicle_id=str(db_vehicle.id)
            )
            db_autocharge = crud.create_autocharge(dbsession, autocharge_data)
            id_tag_data = IDTag(
                id_tag='VID:' + mac_str,
                name=f'VID for {db_vehicle.id}',
                type=IDTagType.vid,
                expiration=datetime.now(timezone.utc) + timedelta(days=365 *
                                                                  settings.AUTOCHARGE_VALIDITY_DURATION_YEARS),
                is_active=True,
                description=f'VID id_tag for {db_vehicle.id}',
                member_id=membership_id,
                autocharge_id=db_autocharge.id
            )
            create_id_tag(dbsession, id_tag_data)
            autocharge_update = AutochargeUpdate(mac_address=mac_str, status=AutochargeStatus.active)
            _ = crud.update_autocharge(dbsession, autocharge_update, db_autocharge.id)

    except exceptions.ApolloAutochargeError:
        raise HTTPException(400, 'Autocharge cannot be created.')

    response_message = {
        "detail_message": "Successfully registered vehicle.",
        "vehicle_id": str(db_vehicle.id)
    }

    return JSONResponse(response_message)


@router.put("/autocharge/activate/{vehicle_id}", status_code=status.HTTP_200_OK)
async def activate_autocharge_for_vehicle(vehicle_id: UUID,
                                          mac_address: str,
                                          request: Request,
                                          dbsession: SessionLocal = Depends(create_session)):
    try:
        try:
            # Validate the MAC Address
            mac_str = str(macaddress.MAC(mac_address)).replace('-', '')
            mac_vid = 'VID:' + mac_str
            # Make sure there is no autocharge object with the same MAC address
            if crud.get_autocharge_by_vid(dbsession, mac_str) is not None:
                raise HTTPException(400, 'Vehicle MAC Address already exists')
        except ValueError:
            raise HTTPException(400, 'Invalid MAC Address')

        db_vehicle = crud.get_vehicle_v2(dbsession, vehicle_id)
        if db_vehicle is None:
            raise HTTPException(404, "Vehicle not found")
        db_membership = db_vehicle.membership
        if db_membership is None:
            raise HTTPException(400, 'Vehicle is not linked to any user')
        if db_vehicle.autocharge is not None and db_vehicle.autocharge.status != AutochargeStatus.pending:
            raise HTTPException(400, "Vehicle has already activated Autocharge")

        db_autocharge = None
        # If there is no autocharge object. Create one and set it to pending state
        if db_vehicle.autocharge is None:
            autocharge_data = AutochargeCreate(
                status=AutochargeStatus.pending,
                vehicle_id=str(db_vehicle.id)
            )
            db_autocharge = crud.create_autocharge(dbsession, autocharge_data)
        else:
            db_autocharge = db_vehicle.autocharge

        # Create an id tag and link it to the autocharge object
        id_tag_data = IDTag(
            id_tag=mac_vid,
            name=f'VID for {db_vehicle.id}',
            type=IDTagType.vid,
            expiration=datetime.now(timezone.utc) + timedelta(days=365 * settings.AUTOCHARGE_VALIDITY_DURATION_YEARS),
            is_active=True,
            description=f'VID id_tag for {db_vehicle.id}',
            member_id=db_membership.id,
            autocharge_id=db_autocharge.id
        )
        create_id_tag(dbsession, id_tag_data)

        # Set the Autocharge Object status to Active
        autocharge_update = AutochargeUpdate(mac_address=mac_str, status=AutochargeStatus.active)
        _ = crud.update_autocharge(dbsession, autocharge_update, db_autocharge.id)
        response_message = {
            "detail_message": "Successfully activated vehicle for Autocharge.",
            "vehicle_id": str(db_vehicle.id)
        }
        return JSONResponse(response_message)

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.put("/autocharge/deactivate/{vehicle_id}", status_code=status.HTTP_200_OK)
async def deactivate_autocharge_for_vehicle(vehicle_id: UUID,
                                            request: Request,
                                            dbsession: SessionLocal = Depends(create_session)):
    try:
        db_vehicle = crud.get_vehicle_v2(dbsession, vehicle_id)
        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')
        db_membership = db_vehicle.membership
        if db_membership is None:
            raise HTTPException(400, 'Vehicle Not linked to any user')

        if db_vehicle.autocharge is None:
            raise HTTPException(400, 'Unable to deactivate as the Vehicle has not been registered for autocharge')
        db_autocharge = db_vehicle.autocharge
        id_tag_record = db_autocharge.id_tag
        if id_tag_record is not None:
            id_tag_update = IDTagUpdate(id_tag=id_tag_record.id_tag, name=id_tag_record.name,
                                        type=id_tag_record.type, is_active=False,
                                        member_id=db_membership.id, expiration=datetime.now(timezone.utc))

            id_tag_record = IDTagCRUD.update(dbsession, id_tag_record.id,
                                             id_tag_update.dict(exclude_unset=False, exclude_defaults=False),
                                             check_permission=False)
            if id_tag_record is not None:
                logger.info('id_tag_record is not None. id is %s', str(id_tag_record.id))
            crud.delete_id_tag(dbsession, id_tag_record.id)

        autocharge_update = AutochargeUpdate(status=AutochargeStatus.deactivated, vehicle_id=None)
        db_autocharge = AutochargeCRUD.update(dbsession, db_autocharge.id,
                                              autocharge_update.dict(exclude_unset=False, exclude_defaults=False))
        crud.delete_autocharge(dbsession, db_autocharge.id)
        response_message = {
            "detail_message": "Successfully deactivated Autocharge for vehicle.",
            "vehicle_id": str(db_vehicle.id)
        }

        return JSONResponse(response_message)
    except exceptions.ApolloAutochargeError as e:
        raise HTTPException(400, e.__str__())


@router.post('/plug_and_charge/{vehicle_id}', status_code=status.HTTP_200_OK)
async def add_vehicle_pcid(request: Request,  # noqa: MC0001
                           vehicle_id: UUID,
                           pcid: str = None,
                           dbsession: SessionLocal = Depends(create_session)):
    if pcid is not None:
        if not bool(re.match(r'^[a-zA-Z0-9]{17,18}$', pcid)):
            raise HTTPException(400, 'Invalid PCID format')

    db_vehicle = crud.get_vehicle_v2(dbsession, vehicle_id)
    if db_vehicle is None:
        raise HTTPException(404, 'Vehicle Not Found')
    db_membership = db_vehicle.membership
    if db_membership is None:
        raise HTTPException(400, 'Vehicle Not linked to any user')
    if pcid is None and db_vehicle.pcid is None:
        raise HTTPException(400, 'No PCID provided')
    try:
        # Sanity checks on the vehicle record
        db_member_vehicle_list = crud.get_membership_vehicle_list(dbsession, db_membership.id)
        for vehicle_record in db_member_vehicle_list:
            # Check if the user has another vehicle with the same PCID
            if str(vehicle_record.id) != str(vehicle_id) and vehicle_record.pcid == pcid:
                raise HTTPException(400, 'User already has a vehicle with this PCID')

        # Step 2: Lookup if the pcid is registered in Hubject Prov Cert Pool
        lookup_pcid_status, message = await lookup_pcid(dbsession, pcid)
        if not lookup_pcid_status:
            raise HTTPException(400, message)
        # Record the PCID. If no contract has been created before allow PCID to be updated.
        if db_vehicle.emaid is None or len(db_vehicle.emaid) == 0:
            crud.update_vehicle(dbsession, VehicleUpdate(pcid=pcid), vehicle_id)
        else:
            if db_vehicle.pcid != pcid:
                response_message = {
                    "detail_message": 'PCID mismatch. Remove the vehicle and retry.',
                    "vehicle_id": pcid
                }
                return JSONResponse(response_message)

        # Check for any existing contracts for this PCID
        emaid_list = db_vehicle.emaid
        if len(emaid_list) > 0:
            if emaid_list[0].status == EmaidStatus.active:
                raise HTTPException(400, 'Vehicle already has a contract')

        db_emaid = None
        emaid_prefix = settings.EMAID_COUNTRY_CODE + settings.EMAID_PROVIDER_ID
        if len(emaid_list) == 0:
            emaid_tag = emaid_prefix + create_random_emaid_tag()
            emaid_data = EmaidCreate(
                emaid=emaid_tag,  # need make it as dynamic
                status=EmaidStatus.payment_pending,
                status_comment='Registration in progress.',
                vehicle_id=str(db_vehicle.id)
            )
            logger.debug('PnC: Creating Emaid for Vehicle: %s with emaid: %s', str(db_vehicle.id), emaid_tag)
            db_emaid = crud.create_emaid(dbsession, emaid_data)
            # db_vehicle = crud.get_vehicle(dbsession, db_vehicle.id)

            update_vehicle_history(dbsession, db_vehicle.id)

            # Create a corresponding id tag for responding to PnC Authorize messages
            # Do not enable the id tag until the certificate generation is actually complete
            id_tag_data = IDTag(
                id_tag=db_emaid.emaid,
                name=f'Contract for {db_vehicle.pcid}',
                type=IDTagType.pnc,
                expiration=datetime.now(timezone.utc),
                is_active=False,
                description=f'Contract id tag for {db_vehicle.pcid}',
                member_id=db_membership.id,
                emaid_id=str(db_emaid.id)
            )
            logger.debug('PnC: Creating ID Tag %s for emaid: %s', db_emaid.emaid, db_emaid.id)
            create_id_tag(dbsession, id_tag_data)

            response_message = {
                "detail_message": "Successfully registered vehicle.",
                "vehicle_id": str(db_vehicle.id)
            }
        else:
            if emaid_list[0].status in (EmaidStatus.pending, EmaidStatus.payment_pending, EmaidStatus.failed):
                db_emaid = emaid_list[0]
                response_message = {
                    "detail_message": "Retrying Contract Creation",
                    "vehicle_id": str(db_vehicle.id)
                }
            else:
                raise HTTPException(400, 'Invalid Operation')
        await provision_contract_certificate(db_vehicle, dbsession, "New")
        return JSONResponse(response_message)
    except exceptions.ApolloEmaidError:
        raise HTTPException(400, 'EMAID cannot be created.')
    except HTTPException as e:
        raise e


@router.put("/plug_and_charge/renew/{vehicle_id}", status_code=status.HTTP_200_OK)
async def renew_vehicle(vehicle_id: UUID, request: Request,
                        dbsession: SessionLocal = Depends(create_session)):
    """
    Renew the contract certificate
    """
    db_vehicle = crud.get_vehicle(dbsession, vehicle_id)
    if db_vehicle is None:
        raise HTTPException(404, 'Vehicle Not Found')

    if db_vehicle.emaid is None or len(db_vehicle.emaid) == 0:
        raise HTTPException(404, 'No contract found.')

    if db_vehicle.emaid[0].status not in (
        EmaidStatus.active,
        EmaidStatus.disabled,
        EmaidStatus.renewal_payment_pending,
        EmaidStatus.renewal_pending,
        EmaidStatus.renewal_rejected
    ):
        raise HTTPException(
            400,
            'Contract is not in active, disabled, renewal_payment_pending, renewal_pending nor renewal_rejected.'
        )
    db_emaid = db_vehicle.emaid[0]
    await provision_contract_certificate(db_vehicle, dbsession, "Renew")
    response_message = {
        "detail_message": "Renewing Contract.",
        "vehicle_id": str(db_vehicle.id),
        "EMAID": db_emaid.emaid
    }
    return JSONResponse(response_message)


@router.put("/plug_and_charge/deactivate/{vehicle_id}", status_code=status.HTTP_200_OK)
async def deactivate_plug_and_charge_for_vehicle(request: Request, vehicle_id: UUID,
                                                 dbsession: SessionLocal = Depends(create_session)):
    """
    This function deactivates the contract certificate for a vehicle.
    It then soft deletes the id tag and the emaid record.
    """
    try:
        db_vehicle = crud.get_vehicle_v2(dbsession, vehicle_id)
        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')
        db_membership = db_vehicle.membership
        if db_membership is None:
            raise HTTPException(400, 'Vehicle Not linked to any user')
        emaid_list = db_vehicle.emaid
        if len(emaid_list) == 0:
            raise HTTPException(404, 'Vehicle has no contract record')
        id_tag_record = emaid_list[0].id_tag
        hubject_delete_status = await delete_contract_certificate(dbsession, emaid_list[0].emaid)
        if not hubject_delete_status:
            raise HTTPException(400, 'Unable to deactivate Plug and Charge for this vehicle.')
        if id_tag_record is not None:
            id_tag_update = IDTagUpdate(id_tag=id_tag_record.id_tag,
                                        name=id_tag_record.name,
                                        type=id_tag_record.type, is_active=False,
                                        member_id=db_membership.id,
                                        expiration=datetime.now(timezone.utc))

            id_tag_record = IDTagCRUD.update(dbsession, id_tag_record.id,
                                             id_tag_update.dict(exclude_unset=False, exclude_defaults=False),
                                             check_permission=False)

            crud.delete_id_tag(dbsession, id_tag_record.id)

        plugandcharge_update = EmaidUpdate(status=EmaidStatus.deactivated, vehicle_id=None)
        db_plugandcharge = EmaidCRUD.update(dbsession, emaid_list[0].id,
                                            plugandcharge_update.dict(exclude_unset=False,
                                                                      exclude_defaults=False))
        crud.delete_emaid(dbsession, db_plugandcharge.id)

        crud.update_vehicle_history(dbsession, db_vehicle.id)
        # Reloading the vehicle with the new Vehicle History
        db_vehicle = crud.get_vehicle(dbsession, db_vehicle.id)
        # Clearing the PCID
        vehicle_update = VehicleUpdate(
            name=db_vehicle.name,
            model=db_vehicle.model,
            brand=db_vehicle.brand,
            registration_number=db_vehicle.registration_number,
            vehicle_histories=db_vehicle.vehicle_histories
        )
        crud.VehicleCRUD.update(dbsession, db_vehicle.id,
                                vehicle_update.dict(exclude_unset=False, exclude_defaults=False))
        response_message = {
            "detail_message": "Successfully deactivated Plug and Charge for vehicle.",
            "vehicle_id": str(db_vehicle.id)
        }
        return JSONResponse(response_message)
    except HTTPException as e:
        logger.info('Exception while deactivating Plug and Charge for Vehicle: %s', str(e))
        raise e


@router.post("/membership/{vehicle_id}", response_model=List[schema.VehicleResponse],
             status_code=status.HTTP_201_CREATED)
async def add_membership_vehicle(vehicle_id: UUID,
                                 membership_id: UUID,
                                 request: Request,
                                 dbsession: SessionLocal = Depends(create_session)):
    """
    Add a Vehicle
    :param str vehicle_id: Target Vehicle ID
    """
    # First make sure that the vehicle limit has not been reached for the user
    try:
        db_vehicle_list = crud.get_membership_vehicle_list(dbsession, membership_id)
        if len(db_vehicle_list) >= settings.VEHICLES_PER_USER_LIMIT:
            raise HTTPException(409, 'Vehicle Limit Reached')
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(404, 'Member not found')

    try:
        db_vehicle_list = crud.add_membership_vehicle(
            dbsession, vehicle_id, membership_id)
        return db_vehicle_list
    except exceptions.ApolloVehicleError as e:
        raise HTTPException(400, e.__str__())


@router.get("/",
            status_code=status.HTTP_200_OK, response_model=Page[schema.VehicleResponse])
async def get_vehicle_list(
    request: Request,
    dbsession: Session = Depends(create_session),
    is_pnc_only: Optional[bool] = Query(None),  # pylint: disable=unsubscriptable-object
    is_ac_only: Optional[bool] = Query(None),  # pylint: disable=unsubscriptable-object
    filters_param: schema.VehicleFiltersParam = Depends(),
    params: Params = Depends(),
):
    """
    Get Vehicle List
    """
    filters = {
        "is_pnc_only": is_pnc_only,
        "is_ac_only": is_ac_only
    }
    vehicle_list_query = crud.get_vehicle_list_with_details(dbsession, filters, filters_param)
    if params:
        return paginate(vehicle_list_query, params)
    return vehicle_list_query.all()
    # return paginate(vehicle_list_query, params)


@router.get("/download", status_code=status.HTTP_200_OK)
async def download_vehicle_list(
    request: Request,
    dbsession: Session = Depends(create_session),
    is_pnc_only: Optional[bool] = Query(None),  # pylint: disable=unsubscriptable-object
    is_ac_only: Optional[bool] = Query(None),  # pylint: disable=unsubscriptable-object
    filters_param: schema.VehicleFiltersParam = Depends()
):
    if is_pnc_only:
        headers = ['REGISTRATION NUMBER', 'PCID', 'EMAID', 'OWNER', 'PNC STATUS', 'PNC EXPIRY DATE']
        columns = ['registration_number', 'pcid', 'emaid', 'membership.first_name',
                   'membership.last_name', 'emaid_status', 'emaid_contract_end']
        reorder_columns = ['registration_number', 'pcid', 'emaid', 'full_name',
                           'emaid_status', 'emaid_contract_end']
        report_name = 'vehicle_pnc_contracts'
    elif is_ac_only:
        headers = ['REGISTRATION NUMBER', 'MAC ADDRESS', 'AC STATUS', 'OWNER', 'EXPIRY DATE']
        columns = ['registration_number', 'autocharge.mac_address', 'autocharge.status',
                   'membership.first_name', 'membership.last_name', 'autocharge.expiry']
        reorder_columns = ['registration_number', 'autocharge.mac_address', 'autocharge.status',
                           'full_name', 'autocharge.expiry']
        report_name = 'vehicle_autocharge'
    else:
        headers = ['REGISTRATION NUMBER', 'PCID', 'EMAID', 'MAC ADDRESS', 'OWNER', 'EMAIL',
                   'PHONE NUMBER']
        columns = ['registration_number', 'pcid', 'emaid', 'autocharge.mac_address',
                   'membership.first_name', 'membership.last_name',
                   'membership.user.email', 'membership.user.phone_number']
        reorder_columns = ['registration_number', 'pcid', 'emaid', 'autocharge.mac_address',
                           'full_name', 'membership.user.email', 'membership.user.phone_number']
        report_name = 'vehicle'

    filters = {
        "is_pnc_only": is_pnc_only,
        "is_ac_only": is_ac_only
    }
    vehicle_list_query = crud.get_vehicle_list_with_details(dbsession, filters, filters_param)
    report = GenerateReport(report_name, header=headers, columns=columns,
                            reorder_columns=reorder_columns, query=vehicle_list_query)
    await report.generate_vehicle_dataframe_with_query(schema=schema.VehicleResponse,
                                                       multiple_join=['emaid',
                                                                      'membership',
                                                                      'membership.user',
                                                                      'autocharge'])
    await report.operation('full_name', lambda x, y: f'{x} {y}', 'membership.first_name', 'membership.last_name')
    if is_pnc_only:
        await report.extract_emaid_list('emaid')
        await report.extract_emaid_list('emaid_status')
        await report.extract_emaid_list('emaid_contract_end')
        await report.datetime_reformat('emaid_contract_end')
    elif is_ac_only:
        await report.datetime_reformat('autocharge.expiry')
        return await report.generate_report()
    else:
        await report.extract_emaid_list('emaid')
    return await report.generate_report()


@router.get("/membership",
            status_code=status.HTTP_200_OK, response_model=List[schema.VehicleResponse])
async def get_membership_vehicle_list(request: Request,
                                      membership_id: UUID,
                                      dbsession: SessionLocal = Depends(create_session)):
    """
    Get a User Vehicle List
    """
    # auth_token_data = decode_auth_token_from_headers(request.headers)
    # membership_id = auth_token_data.get('membership_id')
    try:
        db_vehicle_list = crud.get_membership_vehicle_list(
            dbsession, membership_id)
        return db_vehicle_list
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/{vehicle_id}",
            status_code=status.HTTP_200_OK)
async def get_vehicle(vehicle_id: UUID,
                      dbsession: SessionLocal = Depends(create_session)):
    """
    Get an Vehicle
    :param str vehicle_id: Target Vehicle ID
    """
    try:
        db_vehicle = crud.get_vehicle_v2(dbsession, vehicle_id)
        return db_vehicle
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.patch("/{vehicle_id}",
              status_code=status.HTTP_200_OK, response_model=schema.VehicleResponse)
async def update_vehicle(vehicle_data: schema.VehicleUpdate, vehicle_id: UUID,
                         dbsession: SessionLocal = Depends(create_session)):
    """
    Update an Vehicle
    :param str vehicle_id: Target Vehicle ID
    :param VehicleUpdate vehicle_data: Vehicle Code Data
    """
    try:
        # Check if vehicle exists
        db_vehicle = crud.get_vehicle(dbsession, vehicle_id)

        if db_vehicle is None:
            raise HTTPException(400, 'Vehicle does not exist.')

        # Update vehicle history and vehicle itself
        crud.update_vehicle_history(dbsession, vehicle_id)
        crud.update_vehicle(dbsession, vehicle_data, vehicle_id)

        # Update AC status if needed according to the previous status.
        # new_ac_status = vehicle_data.ac_status
        # existing_ac_status = db_vehicle.autocharge.status if db_vehicle.autocharge else None

        # bug here. need to fix
        # if existing_ac_status:
        #     if new_ac_status is None:
        #         # Remove the Autocharge record
        #         await crud.delete_autocharge(dbsession, db_vehicle.autocharge.id)
        #     else:
        #         if existing_ac_status != new_ac_status:
        #             # Update the Autocharge record
        #             autocharge_data = AutochargeUpdate(
        #                 status=new_ac_status,
        #                 vehicle_id=str(db_vehicle.id)
        #             )
        #             await crud.update_autocharge(
        #                 dbsession,
        #                 db_vehicle.autocharge.id,
        #                 autocharge_data
        #             )
        # else:
        #     if new_ac_status:
        #         # If no Autocharge exists, create a new one and associate it with the vehicle
        #         autocharge_data = AutochargeCreate(
        #             status=AutochargeStatus.pending,
        #             vehicle_id=str(db_vehicle.id)
        #         )
        #         await crud.create_autocharge(dbsession, autocharge_data)

        return db_vehicle
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/membership/{vehicle_id}", status_code=status.HTTP_200_OK)
async def remove_membership_vehicle(request: Request, vehicle_id: UUID,
                                    dbsession: SessionLocal = Depends(create_session)):
    """
    Delete an Vehicle
    :param str vehicle_id: Target Vehicle ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    try:
        db_vehicle_list = crud.remove_membership_vehicle(
            dbsession, vehicle_id, membership_id)
        return db_vehicle_list
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{vehicle_id}", status_code=status.HTTP_200_OK)
async def delete_vehicle(vehicle_id: UUID,  # noqa: MC0001
                         dbsession: SessionLocal = Depends(create_session)):
    """
    Delete an Vehicle
    :param str vehicle_id: Target Vehicle ID
    """
    try:
        vehicle_record = crud.get_vehicle_v2(dbsession, vehicle_id)

        if vehicle_record is None:
            raise HTTPException(404, 'Vehicle Not Found')

        emaid_records = vehicle_record.emaid
        autocharge_record = vehicle_record.autocharge
        membership_record = vehicle_record.membership

        for emaid_record in emaid_records:
            # If EMAID status is active or disabled, clear the cetificate in Hubject
            if emaid_record.status in (EmaidStatus.active, EmaidStatus.disabled):
                hubject_delete_status = await delete_contract_certificate(dbsession,
                                                                          emaid_record.emaid)
                if not hubject_delete_status:
                    logger.error('Failed to delete contract %s from Hubject', emaid_record.emaid)
                    raise HTTPException(400, f'Failed to delete contract {emaid_record.emaid} from Hubject')
            id_tag_record = emaid_record.id_tag
            if id_tag_record is not None:
                # Decouple the id tag record from the emaid record
                # before soft deleting the id tag object
                id_tag_update = IDTagUpdate(id_tag=id_tag_record.id_tag, name=id_tag_record.name,
                                            type=id_tag_record.type, is_active=False,
                                            member_id=id_tag_record.member_id,
                                            expiration=datetime.now(timezone.utc))
                id_tag_record = IDTagCRUD.update(
                    dbsession,
                    id_tag_record.id,
                    id_tag_update.dict(
                        exclude_unset=False,
                        exclude_defaults=False
                    ),
                    check_permission=False
                )
                if id_tag_record is not None:
                    logger.info('id_tag_record is not None. id is %s', str(id_tag_record.id))
                    crud.delete_id_tag(dbsession, id_tag_record.id)
            # Decouple the emaid object from the vehicle record before soft deleting it
            status_comment = 'Delete Contract'
            emaid_update = EmaidUpdate(
                status=EmaidStatus.deactivated,
                status_comment=status_comment,
                contract_begin=emaid_record.contract_begin,
                contract_end=emaid_record.contract_begin
            )
            EmaidCRUD.update(dbsession, emaid_record.id,
                             emaid_update.dict(exclude_unset=False, exclude_defaults=False))
            crud.delete_emaid(dbsession, emaid_record.id)

        if autocharge_record is not None:
            # Soft delete the corresponding id tag as well as the autocharge record.
            if autocharge_record.id_tag is not None:
                id_tag_record = autocharge_record.id_tag
                if id_tag_record is not None:
                    # Decouple the id tag record from the autocharge record
                    # before soft deleting the id tag object
                    id_tag_update = IDTagUpdate(id_tag=id_tag_record.id_tag,
                                                name=id_tag_record.name,
                                                type=id_tag_record.type, is_active=False,
                                                member_id=id_tag_record.member_id,
                                                expiration=datetime.now(timezone.utc))
                    # id_tag_update = IDTagUpdate(autocharge_id=None)
                    id_tag_record = IDTagCRUD.update(dbsession, id_tag_record.id,
                                                     id_tag_update.dict(exclude_unset=False,
                                                                        exclude_defaults=False),
                                                     check_permission=False)
                    if id_tag_record is not None:
                        logger.info('id_tag_record is not None. id is %s', str(id_tag_record.id))
                        crud.delete_id_tag(dbsession, id_tag_record.id)

            # Decouple the autocharge object from the vehicle object
            # before soft deleting the autocharge object
            autocharge_update = AutochargeUpdate(status=AutochargeStatus.deactivated,
                                                 mac_address=autocharge_record.mac_address)
            AutochargeCRUD.update(dbsession, autocharge_record.id,
                                  autocharge_update.dict(exclude_unset=False,
                                                         exclude_defaults=False))
            crud.delete_autocharge(dbsession, autocharge_record.id)

        # Delete the Vehicle, Delete the emaid and then delete the ID Tag
        crud.remove_membership_vehicle(dbsession, vehicle_id, str(membership_record.id))
        crud.delete_vehicle(dbsession, vehicle_id)

        delete_response = DeleteVehicleResponse(
            detail='Successfully removed vehicle.'
        )
        return delete_response
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/send_pnc_friendly/{vehicle_id}/{emaid}", status_code=status.HTTP_200_OK)
async def send_vehicle_data(vehicle_id: UUID, emaid: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Send vehicle data
    :param str vehicle_id: Target Vehicle ID
    :param str emaid: EMAID
    """
    try:
        response = await push_emaid(dbsession, vehicle_id, emaid)
        if not response.get("success"):
            raise HTTPException(400, response.get('success'))
        return response.get("ocpi_token")
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
