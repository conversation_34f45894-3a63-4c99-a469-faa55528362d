from contextlib import contextmanager
from datetime import datetime, timedelta, timezone
from unittest.mock import patch, AsyncMock, ANY, call, MagicMock
import uuid
import secrets
import json
import base64

import jwt
import pytest
from fastapi.testclient import TestClient
from starlette.datastructures import QueryParams

from app import schema, settings
from app.main import app, ROOT_PATH
from app.tests.factories import (UserFactory, OrganizationFactory, MembershipFactory,
                                 ResourceServerFactory, ResourceFactory, RoleFactory,
                                 OrganizationAuthenticationServiceFactory, ChargingSessionBillFactory,
                                 OperatorFactory, OperatorChargepointFactory, OCPITokenFactory,
                                 ExternalOrganizationFactory, ExternalTokenFactory, PaymentRequestFactory,
                                 WalletFactory, CreditCardFactory, PreAuthPaymentFactory)
from app.tests.mocks.async_client import MockResponse
from app.database import SessionLocal, create_session, Base, engine
from app.schema import PaymentRequestCreditCard, PaymentRequestReason

client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ChargingSessionBillFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorChargepointFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OCPITokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ExternalOrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ExternalTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PaymentRequestFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        WalletFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PreAuthPaymentFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session

CHARGER_BASE_URL = f'{ROOT_PATH}/api/v1/charger'
CHARGER_URL_PREFIX_V1 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'
CHARGER_URL_PREFIX_V2 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'
OCPI_URL = f'{settings.MAIN_OCPI_DOMAIN}/{settings.OCPI_PREFIX}'

CONNECTOR_ID = str(uuid.uuid4())
CHARGING_SESSION_ID = str(uuid.uuid4())
OPERATOR_ID = str(uuid.uuid4())
LOCATION_ID = str(uuid.uuid4())
CHARGE_POINT_ID = str(uuid.uuid4())
COMMAND_ID = str(uuid.uuid4())
TRANSACTION_ID = '1234'
SUCCESS_RESPONSE_DATA = {'headers': {}, 'status_code': 200, 'status': 'success'}
PC_FLOW_CONNECTOR_DATA = {'billing_currency': schema.Currency.myr, 'billing_unit_fee': '0.10'}


@patch('app.routers.v2.charger.send_request')
@patch('app.routers.v2.charger.OCPIPreChargingFlow')
def test_start_ocpi_charging(ocpi_pc_flow_mock, send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    mock_instance = AsyncMock()
    mock_instance.ocpi_start_flow.return_value = SUCCESS_RESPONSE_DATA
    ocpi_pc_flow_mock.return_value = mock_instance

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/start-ocpi-charging/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/start-ocpi-charging/{CONNECTOR_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.post(url, headers=headers)
        assert ocpi_pc_flow_mock.call_count == 1
        assert mock_instance.ocpi_start_flow.call_count == 1
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'POST',
            f'{CHARGER_URL_PREFIX_V1}/ocpi/emsp/{CONNECTOR_ID}/remote-start',
            data=ANY,
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.utils.send_request')
@patch('app.routers.v2.charger.send_request')
@patch('app.routers.v2.charger.OCPIPreChargingFlow')
def test_start_ocpi_charging_mock_data(ocpi_pc_flow_mock, send_request_mock, sr_utils_mock, test_db):
    sr_utils_mock.return_value = MockResponse({'content': 'mock content'}, 200)
    mock_instance = AsyncMock()
    mock_instance.ocpi_start_flow.return_value = SUCCESS_RESPONSE_DATA
    ocpi_pc_flow_mock.return_value = mock_instance

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        ocpi_token = OCPITokenFactory(
            member=mem,
            type='APP_USER'
        )
        db.commit()
        external_org = ExternalOrganizationFactory()
        db.commit()
        ExternalTokenFactory(
            token=str(uuid.uuid4()),
            is_temporary=False,
            external_organization=external_org,
            external_organization_id=str(external_org.id),
            endpoints='{"endpoints": [{"role": "RECEIVER", "identifier": "tokens", "url": "test"}]}',
            external_service_token=base64.b64encode('test_token'.encode('utf-8')).decode('utf-8')
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/start-ocpi-charging/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        mock_data_1 = {
            'command': {
                'id': COMMAND_ID,
                'command': 'test',
                'response_url': 'test',
                'ocpi_location_id': LOCATION_ID,
                'ocpi_evse_connector_id': CONNECTOR_ID
            },
            'location': {
                'ocpi_partner_location_id': str(uuid.uuid4()),
            },
            'cp': {
                'ocpi_partner_evse_uid': 'test',
                'operator_id': str(external_org.id)
            },
            'connector': {
                'ocpi_partner_connector_id': str(uuid.uuid4())
            },
            'operation_request': {
                'id': COMMAND_ID,
                'created_at': datetime.now(),
                'requester_type': 'User',
                'requester_id': 'test_id_tag',
                'operation': 'RemoteStartTransaction',
                'charge_point_id': CHARGE_POINT_ID,
                'connector_id': CONNECTOR_ID
            }
        }
        result_1 = MockResponse(mock_data_1, 200)
        send_request_mock.side_effect = [result_1]

        url = f'{CHARGER_BASE_URL}/start-ocpi-charging/{CONNECTOR_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }

        with pytest.raises(UnboundLocalError) as exc_info:
            client.post(url, headers=headers)

        assert str(exc_info.value) == "local variable 'receiver_command_url' referenced before assignment"
        assert ocpi_pc_flow_mock.call_count == 1
        assert mock_instance.ocpi_start_flow.call_count == 1
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'POST',
            f'{CHARGER_URL_PREFIX_V1}/ocpi/emsp/{CONNECTOR_ID}/remote-start',
            data=ANY,
            headers=ANY
        )
        assert sr_utils_mock.call_count == 1
        sr_utils_mock.assert_called_with(
            'PUT',
            f'test/MY/test/{str(ocpi_token.id)}',
            data=ANY,
            headers=ANY,
            query_params='APP_USER'
        )


@patch('app.routers.v2.charger.send_request')
def test_stop_ocpi_charging_mock_data(send_request_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        ocpi_token = OCPITokenFactory(
            member=mem,
            type='APP_USER'
        )
        db.commit()
        external_org = ExternalOrganizationFactory()
        db.commit()
        external_token = str(uuid.uuid4())
        ExternalTokenFactory(
            token=external_token,
            is_temporary=False,
            external_organization=external_org,
            external_organization_id=str(external_org.id),
            endpoints='{"endpoints": [{"role": "RECEIVER", "identifier": "commands", "url": "test"}]}',
            external_service_token=base64.b64encode('test_token'.encode('utf-8')).decode('utf-8')
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/start-ocpi-charging/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        operation_request_id = str(uuid.uuid4())
        mock_data_1 = {
            'command': {
                'id': COMMAND_ID,
                'command': 'test',
                'response_url': 'test_response_url',
                'ocpi_location_id': LOCATION_ID,
                'ocpi_evse_connector_id': CONNECTOR_ID
            },
            'location': {
                'ocpi_partner_location_id': str(uuid.uuid4()),
            },
            'cp': {
                'ocpi_partner_evse_uid': 'test',
                'operator_id': str(external_org.id)
            },
            'connector': {
                'ocpi_partner_connector_id': str(uuid.uuid4())
            },
            'operation_request': {
                'id': operation_request_id,
                'created_at': str(datetime.now()),
                'requester_type': 'User',
                'requester_id': 'test_id_tag',
                'operation': 'RemoteStartTransaction',
                'charge_point_id': CHARGE_POINT_ID,
                'connector_id': CONNECTOR_ID,
                'status': 'Pending'
            },
            'session': {
                'partner_session_uid': str(uuid.uuid4())
            }
        }
        result_1 = MockResponse(mock_data_1, 200)
        mock_data_2 = {
            'data': {
                'result': 'ACCEPTED'
            }
        }
        result_2 = MockResponse(mock_data_2, 200)
        result_3 = MockResponse({}, 200)
        send_request_mock.side_effect = [result_1, result_2, result_3]

        url = f'{CHARGER_BASE_URL}/stop-ocpi-charging/{TRANSACTION_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }

        response = client.post(url, headers=headers)
        assert send_request_mock.call_count == 3
        data = {
            'ocpi_token_id': str(ocpi_token.id),
            'command': 'STOP_SESSION',
            'response_url': f'{OCPI_URL}/emsp/2.2.1/commands/',
            'status': 'PENDING'
        }
        result_data = {
            'response_data': mock_data_2,
            'operation_request': mock_data_1['operation_request']
        }
        assert send_request_mock.mock_calls == [
            call(
                'POST',
                f'{CHARGER_URL_PREFIX_V1}/ocpi/emsp/{TRANSACTION_ID}/remote-stop',
                data=json.dumps(data),
                headers=ANY
            ),
            call(
                'POST',
                'test/STOP_SESSION',
                data=ANY,
                headers=ANY
            ),
            call(
                'PUT',
                f'{CHARGER_URL_PREFIX_V1}/ocpi/command/{COMMAND_ID}',
                data=json.dumps(result_data),
                headers=ANY
            )
        ]
        assert response.status_code == 200
        response_data = response.json().get('data')
        assert response_data['id'] == str(operation_request_id)
        assert response_data['charge_point_id'] == CHARGE_POINT_ID


@patch('app.routers.v2.charger.send_request')
def test_stop_ocpi_charging_mock_data_reject(send_request_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        ocpi_token = OCPITokenFactory(
            member=mem,
            type='APP_USER'
        )
        db.commit()
        external_org = ExternalOrganizationFactory()
        db.commit()
        external_token = str(uuid.uuid4())
        ExternalTokenFactory(
            token=external_token,
            is_temporary=False,
            external_organization=external_org,
            external_organization_id=str(external_org.id),
            endpoints='{"endpoints": [{"role": "RECEIVER", "identifier": "commands", "url": "test"}]}',
            external_service_token=base64.b64encode('test_token'.encode('utf-8')).decode('utf-8')
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/start-ocpi-charging/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        mock_data_1 = {
            'command': {
                'id': COMMAND_ID,
                'command': 'test',
                'response_url': 'test_response_url',
                'ocpi_location_id': LOCATION_ID,
                'ocpi_evse_connector_id': CONNECTOR_ID
            },
            'location': {
                'ocpi_partner_location_id': str(uuid.uuid4()),
            },
            'cp': {
                'ocpi_partner_evse_uid': 'test',
                'operator_id': str(external_org.id)
            },
            'connector': {
                'ocpi_partner_connector_id': str(uuid.uuid4())
            },
            'operation_request': {
                'id': str(uuid.uuid4()),
                'created_at': str(datetime.now()),
                'requester_type': 'User',
                'requester_id': 'test_id_tag',
                'operation': 'RemoteStartTransaction',
                'charge_point_id': CHARGE_POINT_ID,
                'connector_id': CONNECTOR_ID,
                'status': 'Pending'
            },
            'session': {
                'partner_session_uid': str(uuid.uuid4())
            }
        }
        result_1 = MockResponse(mock_data_1, 200)
        mock_data_2 = {
            'data': {
                'result': 'REJECTED',
                'message': [{'text': 'Test Reject'}]
            }
        }
        result_2 = MockResponse(mock_data_2, 200)
        result_3 = MockResponse({}, 200)
        send_request_mock.side_effect = [result_1, result_2, result_3]

        url = f'{CHARGER_BASE_URL}/stop-ocpi-charging/{TRANSACTION_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }

        response = client.post(url, headers=headers)
        assert send_request_mock.call_count == 3
        data = {
            'ocpi_token_id': str(ocpi_token.id),
            'command': 'STOP_SESSION',
            'response_url': f'{OCPI_URL}/emsp/2.2.1/commands/',
            'status': 'PENDING'
        }
        result_data = {
            'response_data': mock_data_2,
            'operation_request': mock_data_1['operation_request']
        }
        assert send_request_mock.mock_calls == [
            call(
                'POST',
                f'{CHARGER_URL_PREFIX_V1}/ocpi/emsp/{TRANSACTION_ID}/remote-stop',
                data=json.dumps(data),
                headers=ANY
            ),
            call(
                'POST',
                'test/STOP_SESSION',
                data=ANY,
                headers=ANY
            ),
            call(
                'PUT',
                f'{CHARGER_URL_PREFIX_V1}/ocpi/command/{COMMAND_ID}',
                data=json.dumps(result_data),
                headers=ANY
            )
        ]
        assert response.status_code == 200
        assert response.json().get('success') is False
        expected_message = 'Partner rejected for the following reason: Test Reject'
        assert response.json().get('error').get('message')[0] == expected_message


@patch('app.routers.v2.charger.send_request')
@patch('app.routers.v2.charger.PreChargingFlow')
def test_start_charging_proxy(pc_flow_mock, send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    mock_instance = AsyncMock()
    mock_instance.start_flow.return_value = SUCCESS_RESPONSE_DATA
    pc_flow_mock.return_value = mock_instance

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/start-charging/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/start-charging/{CONNECTOR_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.post(url, headers=headers)
        assert pc_flow_mock.call_count == 1
        assert mock_instance.start_flow.call_count == 1
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'POST',
            f'{CHARGER_URL_PREFIX_V1}/connector/{CONNECTOR_ID}/remote-start',
            data=ANY,
            headers=ANY,
            query_params=QueryParams(''),
            timeout=9
        )
        assert response.status_code == 200


@patch('app.routers.v2.charger.send_request')
@patch('app.routers.v2.charger.PreChargingFlow')
def test_reserve_connector(pc_flow_mock, send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    mock_instance = AsyncMock()
    mock_instance.start_flow.return_value = SUCCESS_RESPONSE_DATA
    mock_instance.check_if_user_do_have_active_reservation.return_value = {
        'headers': {}, 'status_code': 200, 'status': 'success'
    }
    pc_flow_mock.return_value = mock_instance

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/reservation/reserve/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/reservation/reserve/{CONNECTOR_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.post(url, headers=headers)
        assert pc_flow_mock.call_count == 1
        assert mock_instance.start_flow.call_count == 1
        assert mock_instance.check_if_user_do_have_active_reservation.call_count == 1
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'POST',
            f'{CHARGER_URL_PREFIX_V1}/connector/{CONNECTOR_ID}/reserve',
            data=ANY,
            headers=ANY,
            query_params={'is_mobile_api': True}
        )
        assert response.status_code == 200


@patch('app.routers.v2.charger.send_request')
def test_request_cancel_reservation(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/reservation/cancel-reservation/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        reservation_id = uuid.uuid4()
        url = f'{CHARGER_BASE_URL}/reservation/cancel-reservation/{reservation_id}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.post(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'POST',
            f'{CHARGER_URL_PREFIX_V1}/connector/cancel_reservation/{reservation_id}',
            data=ANY,
            headers=ANY,
            query_params=QueryParams(''),
            timeout=9
        )
        assert response.status_code == 200


@patch('app.routers.v2.charger.send_request')
def test_stop_charging_session(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/stop-charging/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/stop-charging/{TRANSACTION_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.post(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'POST',
            f'{CHARGER_URL_PREFIX_V1}/connector/remote-stop/{TRANSACTION_ID}',
            data=ANY,
            headers=ANY,
            query_params=QueryParams(''),
            timeout=9
        )
        assert response.status_code == 200


def test_perform_pre_auth_wallet(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        PaymentRequestFactory(
            amount='0.20',
            type=schema.PaymentRequestType.wallet,
            billing_description='Topup',
            reason=schema.PaymentRequestReason.deposit,
            member_id=mem.id,
            status=schema.PaymentRequestStatus.done,
            currency=schema.Currency.myr
        )
        WalletFactory(
            member_id=membership_id,
            balance='0.50'
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/pre-auth/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/pre-auth/{CONNECTOR_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        data = {
            'use_3ds': False,
            'is_ocpi': False,
            'use_wallet': True
        }

        with patch('app.routers.v2.charger.PreChargingFlow') as pc_flow_mock:
            mock_instance = MagicMock()
            mock_instance.start_flow = AsyncMock(return_value=SUCCESS_RESPONSE_DATA)
            mock_instance.is_charging_free.return_value = False
            mock_instance._check_charging_fee.return_value = False  # pylint: disable=protected-access
            mock_instance.get_connector.return_value = PC_FLOW_CONNECTOR_DATA
            pc_flow_mock.return_value = mock_instance

            response = client.post(url, headers=headers, json=data)
            assert pc_flow_mock.call_count == 1
            assert mock_instance.start_flow.call_count == 1
            assert mock_instance.is_charging_free.call_count == 1
            assert mock_instance.get_connector.call_count == 2
            assert response.status_code == 200
            response_data = response.json().get('data')
            assert response_data['is_3ds'] is False
            assert response_data['is_successful'] is True
            assert response_data['amount'] == '0.20'


def test_perform_pre_auth_ocpi_wallet(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        PaymentRequestFactory(
            amount='0.20',
            type=schema.PaymentRequestType.wallet,
            billing_description='Topup',
            reason=schema.PaymentRequestReason.deposit,
            member_id=mem.id,
            status=schema.PaymentRequestStatus.done,
            currency=schema.Currency.myr
        )
        WalletFactory(
            member_id=membership_id,
            balance='0.50'
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/pre-auth/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/pre-auth/{CONNECTOR_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        data = {
            'use_3ds': False,
            'is_ocpi': True,
            'use_wallet': True
        }

        with patch('app.routers.v2.charger.OCPIPreChargingFlow') as ocpi_pc_flow_mock:
            mock_instance = MagicMock()
            mock_instance.ocpi_start_flow = AsyncMock(return_value=SUCCESS_RESPONSE_DATA)
            mock_instance.get_connector.return_value = PC_FLOW_CONNECTOR_DATA
            ocpi_pc_flow_mock.return_value = mock_instance

            response = client.post(url, headers=headers, json=data)
            assert ocpi_pc_flow_mock.call_count == 1
            assert mock_instance.get_connector.call_count == 2
            assert response.status_code == 200
            response_data = response.json().get('data')
            assert response_data['is_3ds'] is False
            assert response_data['is_successful'] is True
            assert response_data['amount'] == '0.20'


def test_perform_pre_auth_ocpi_cc(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        PaymentRequestFactory(
            amount='1.20',
            type=schema.PaymentRequestType.wallet,
            billing_description='Topup',
            reason=schema.PaymentRequestReason.deposit,
            member_id=mem.id,
            status=schema.PaymentRequestStatus.done,
            currency=schema.Currency.myr
        )
        CreditCardFactory(
            member_id=membership_id,
            primary=True,
            last_four_digit='1111',
            currency='MYR'
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/pre-auth/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/pre-auth/{CONNECTOR_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        data = {
            'use_3ds': True,
            'is_ocpi': True,
            'use_wallet': False
        }

        with patch('app.routers.v2.charger.OCPIPreChargingFlow') as ocpi_pc_flow_mock:
            mock_instance = MagicMock()
            mock_instance.ocpi_start_flow = AsyncMock(return_value=SUCCESS_RESPONSE_DATA)
            mock_instance.get_connector.return_value = PC_FLOW_CONNECTOR_DATA
            ocpi_pc_flow_mock.return_value = mock_instance

            response = client.post(url, headers=headers, json=data)
            assert ocpi_pc_flow_mock.call_count == 1
            assert mock_instance.get_connector.call_count == 2
            assert response.status_code == 200
            response_data = response.json().get('data')
            assert response_data['is_3ds'] is True
            assert response_data['is_successful'] is False


@patch('app.routers.v2.charger.send_request')
def test_start_charging_after_pre_auth(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        pr = PaymentRequestCreditCard(currency='MYR', reason=PaymentRequestReason.pre_auth).dict()
        payment_request = PaymentRequestFactory(
            **pr,
            member_id=membership_id,
        )
        db.commit()
        PreAuthPaymentFactory(
            payment_request_id=payment_request.id,
            is_successful=True,
            payment_type='Credit-Card'
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/pre-auth/start-charging/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/pre-auth/start-charging/{CONNECTOR_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        data = {
            'use_wallet': False
        }

        with patch('app.routers.v2.charger.PreChargingFlow') as pc_flow_mock:
            mock_instance = MagicMock()
            mock_instance.start_flow = AsyncMock(return_value=SUCCESS_RESPONSE_DATA)
            mock_instance.is_charging_free.return_value = False
            mock_instance._check_charging_fee.return_value = False  # pylint: disable=protected-access
            mock_instance.get_connector.return_value = {'billing_currency': 'MYR'}
            pc_flow_mock.return_value = mock_instance

            response = client.post(url, headers=headers, json=data)
            assert pc_flow_mock.call_count == 1
            assert mock_instance.start_flow.call_count == 1
            assert mock_instance.is_charging_free.call_count == 1
            assert mock_instance.get_connector.call_count == 1
            assert send_request_mock.call_count == 1
            send_request_mock.assert_called_with(
                'POST',
                f'{CHARGER_URL_PREFIX_V1}/connector/{CONNECTOR_ID}/remote-start',
                data=ANY,
                headers=ANY,
                query_params={},
                timeout=9
            )
            assert response.status_code == 200
            assert response.json().get('success') is True


@patch('app.routers.v2.charger.send_request')
def test_start_charging_after_pre_auth_not_found(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/pre-auth/start-charging/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/pre-auth/start-charging/{CONNECTOR_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        data = {
            'use_wallet': False
        }

        with patch('app.routers.v2.charger.PreChargingFlow') as pc_flow_mock:
            mock_instance = MagicMock()
            mock_instance.start_flow = AsyncMock(return_value=SUCCESS_RESPONSE_DATA)
            mock_instance.is_charging_free.return_value = False
            mock_instance._check_charging_fee.return_value = False  # pylint: disable=protected-access
            mock_instance.get_connector.return_value = {'billing_currency': 'MYR'}
            pc_flow_mock.return_value = mock_instance

            response = client.post(url, headers=headers, json=data)
            assert pc_flow_mock.call_count == 1
            assert mock_instance.start_flow.call_count == 1
            assert mock_instance.is_charging_free.call_count == 1
            assert mock_instance.get_connector.call_count == 1
            assert send_request_mock.call_count == 0
            assert response.status_code == 200
            assert response.json().get('success') is False
            assert response.json().get('error')['message'][0] == 'No binded pre-auth found.'


@patch('app.routers.v2.charger.send_request')
def test_start_ocpi_charging_after_pre_auth(send_request_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=str(organization.id),
            organization_id=str(organization.id),
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=str(organization.id)
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=str(organization.id),
            user_id=(user.id)
        )
        db.commit()

        ocpi_token = OCPITokenFactory(
            member=mem,
            type='APP_USER'
        )
        db.commit()
        external_org = ExternalOrganizationFactory()
        db.commit()
        external_token = str(uuid.uuid4())
        ExternalTokenFactory(
            token=external_token,
            is_temporary=False,
            external_organization=external_org,
            external_organization_id=str(external_org.id),
            endpoints='{"endpoints": [{"role": "RECEIVER", "identifier": "commands", "url": "test"}]}',
            external_service_token=base64.b64encode('test_token'.encode('utf-8')).decode('utf-8')
        )
        db.commit()

        pr = PaymentRequestCreditCard(currency='MYR', reason=PaymentRequestReason.pre_auth).dict()
        payment_request = PaymentRequestFactory(
            **pr,
            member_id=str(mem.id),
        )
        db.commit()
        PreAuthPaymentFactory(
            payment_request_id=payment_request.id,
            is_successful=True,
            payment_type='Credit-Card'
        )
        db.commit()

        mock_data_1 = {
            'command': {
                'id': COMMAND_ID,
                'command': 'test',
                'response_url': 'test_response_url',
                'ocpi_location_id': LOCATION_ID,
                'ocpi_evse_connector_id': CONNECTOR_ID
            },
            'location': {
                'ocpi_partner_location_id': str(uuid.uuid4()),
            },
            'cp': {
                'ocpi_partner_evse_uid': 'test',
                'operator_id': str(external_org.id)
            },
            'connector': {
                'ocpi_partner_connector_id': str(uuid.uuid4())
            },
            'operation_request': {
                'id': str(uuid.uuid4()),
                'created_at': str(datetime.now()),
                'requester_type': 'User',
                'requester_id': 'test_id_tag',
                'operation': 'RemoteStartTransaction',
                'charge_point_id': CHARGE_POINT_ID,
                'connector_id': CONNECTOR_ID,
                'status': 'Pending'
            },
            'session': {
                'partner_session_uid': str(uuid.uuid4())
            }
        }
        result_1 = MockResponse(mock_data_1, 200)
        mock_data_2 = {
            'data': {
                'result': 'ACCEPTED'
            }
        }
        result_2 = MockResponse(mock_data_2, 200)
        result_3 = MockResponse({}, 200)
        send_request_mock.side_effect = [result_1, result_2, result_3]

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/pre-auth/start-ocpi-charging/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(mem.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/pre-auth/start-ocpi-charging/{CONNECTOR_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': str(organization.id)
        }
        data = {
            'use_wallet': False
        }

        with patch('app.routers.v2.charger.OCPIPreChargingFlow') as ocpi_pc_flow_mock:
            mock_instance = MagicMock()
            mock_instance.ocpi_start_flow = AsyncMock(return_value=SUCCESS_RESPONSE_DATA)
            mock_instance.get_connector.return_value = {'billing_currency': 'MYR'}
            ocpi_pc_flow_mock.return_value = mock_instance

            with pytest.raises(UnboundLocalError) as exc_info:
                client.post(url, headers=headers, json=data)

            assert str(exc_info.value) == "local variable 'receiver_command_url' referenced before assignment"
            assert ocpi_pc_flow_mock.call_count == 1
            assert mock_instance.ocpi_start_flow.call_count == 1
            assert mock_instance.get_connector.call_count == 1
            assert send_request_mock.call_count == 1
            data = {
                'ocpi_token_id': str(ocpi_token.id),
                'command': "START_SESSION",
                'response_url': f'{OCPI_URL}/emsp/2.2.1/commands/',
                'status': "PENDING"
            }
            send_request_mock.assert_called_with(
                'POST',
                f'{CHARGER_URL_PREFIX_V1}/ocpi/emsp/{CONNECTOR_ID}/remote-start',
                data=json.dumps(data),
                headers=ANY
            )


def test_check_pre_auth_status(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        pr = PaymentRequestCreditCard(currency='MYR', reason=PaymentRequestReason.pre_auth).dict()
        payment_request = PaymentRequestFactory(
            **pr,
            member_id=membership_id,
        )
        db.commit()
        pre_auth_payment = PreAuthPaymentFactory(
            payment_request_id=payment_request.id,
            is_successful=True,
            payment_type='Credit-Card',
            response={'success': True},
            reference_id='test'
        )
        db.commit()
        pre_auth_id = str(pre_auth_payment.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/pre-auth/check/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/pre-auth/check/{pre_auth_id}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }

        response = client.get(url, headers=headers)
        assert response.status_code == 200
        assert response.json().get('data')['id'] == pre_auth_id


def test_check_pre_auth_status_not_found(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/pre-auth/check/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/pre-auth/check/{str(uuid.uuid4())}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }

        response = client.get(url, headers=headers)
        assert response.status_code == 200
        print('<<< response', response.json())
        assert response.json().get('error')['message'][0] == 'No Pre-Auth Found.'


@patch('app.ruby_proxy_utils.send_request')
def test_update_ocpp_status_ruby(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/ocpp-status/ruby/serial-number/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        serial_number = 'test'
        url = f'{CHARGER_BASE_URL}/ocpp-status/ruby/serial-number/{serial_number}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        data = {
            'connector_number': 0,
            'ocpp_status': 'Reserved',
            'status': 'Reserved'
        }
        response = client.post(url, headers=headers, json=data)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'PATCH',
            f'{CHARGER_URL_PREFIX_V2}/charging/foreign/ocpp-status/',
            data='{"ocpp_status": "Reserved", "status": "Reserved"}',
            headers=ANY,
            query_params={'serial_number': 'test', 'connector_number': 0}
        )
        assert response.status_code == 200


@patch('app.routers.v2.charger.send_request')
def test_get_charge_usage(send_request_mock, test_db):
    mock_data = {
        'id': CHARGING_SESSION_ID,
        'status': 'Charging',
        'meter_start': 0.0,
        'meter_stop': 0.0,
        'session_start': str(datetime.now()),
        'session_end': str(datetime.now() + timedelta(hours=1)),
        'id_tag': 'test_id_tag',
        'transaction_id': TRANSACTION_ID,
        'charge_point_connector': {
            'id': '651fc9fc-810b-486f-9304-d0ebf7a87f35',
            'billing_type': 'Time'
        },
        'meta': {
            'charger_serial_number': 'test_sn',
            'charge_point_vendor': None,
            'location': {
                'id': LOCATION_ID,
                'ocpi_partner_operator': None
            },
            'billing_info': {
                'billing_type': 'Time',
                'billing_unit_fee': 1.0,
                'billing_cycle': 15,
                'billing_currency': 'MYR'
            },
            'operator': {
                'id': OPERATOR_ID
            }
        }
    }
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid,
            meta={
                'organization_name': str(organization.name),
                'transaction_id': TRANSACTION_ID
            }
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/charge-usage/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/charge-usage/{CHARGING_SESSION_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_PREFIX_V2}/charging/{CHARGING_SESSION_ID}',
            headers=ANY,
            data=b''
        )
        assert response.status_code == 200
        assert response.json().get('data').get('total_charging_duration') == 3600.0
        assert response.json().get('data').get('total_charging_usage') == 0.0


@patch('app.routers.v2.charger.send_request')
def test_get_charge_usage_no_cs(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 500)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/charge-usage/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/charge-usage/{CHARGING_SESSION_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_PREFIX_V2}/charging/{CHARGING_SESSION_ID}',
            headers=ANY,
            data=b''
        )
        assert response.json()['error']['message'][0] == 'No charging session found'


@patch('app.utils.get_connector')
@patch('app.routers.v2.charger.send_request')
def test_get_cp_discounted_price(send_request_mock, get_connector_mock, test_db):
    mock_data = [
        {
            'id': CONNECTOR_ID,
            'billing_type': 'Time',
            'billing_unit_fee': 12.3,
            'billing_currency': 'MYR'
        }
    ]
    send_request_mock.return_value = MockResponse(mock_data, 200)

    mock_connector_data = {
        "id": CONNECTOR_ID,
        "charge_point": {
            "operator_id": OPERATOR_ID,
            "tariff_tag_id": str(uuid.uuid4()),
        },
        "connector_type": {
            "kind": "DC",
        }
    }
    get_connector_mock.return_value = mock_connector_data

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        OperatorFactory(
            id=OPERATOR_ID,
            organization_id=organization_id
        )
        db.commit()
        OperatorChargepointFactory(
            charge_point_id=CHARGE_POINT_ID,
            operator_id=OPERATOR_ID
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/location/discount/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/location/discount/{LOCATION_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_PREFIX_V2}/location/charge_points/raw/{LOCATION_ID}',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.utils.get_connector')
@patch('app.routers.v2.charger.send_request')
def test_get_cps_discounted_price(send_request_mock, get_connector_mock, test_db):
    mock_data = [
        {
            'id': CHARGE_POINT_ID,
            'charge_points': [
                {
                    'serial_number': 'test_sn',
                    'connectors': [
                        {
                            'id': CONNECTOR_ID,
                            'billing_type': 'Time',
                            'billing_unit_fee': 12.3,
                            'billing_currency': 'MYR'
                        }
                    ]
                }
            ]
        }
    ]
    send_request_mock.return_value = MockResponse(mock_data, 200)

    mock_connector_data = {
        "id": CONNECTOR_ID,
        "charge_point": {
            "operator_id": OPERATOR_ID,
            "tariff_tag_id": str(uuid.uuid4()),
        },
        "connector_type": {
            "kind": "DC",
        }
    }
    get_connector_mock.return_value = mock_connector_data

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        OperatorFactory(
            id=OPERATOR_ID,
            organization_id=organization_id
        )
        db.commit()
        OperatorChargepointFactory(
            charge_point_id=CHARGE_POINT_ID,
            operator_id=OPERATOR_ID
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/location/discount.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/location/discount'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        data = [
            LOCATION_ID
        ]
        response = client.post(url, headers=headers, json=data)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'POST',
            f'{CHARGER_URL_PREFIX_V2}/location/charge_points/raw',
            headers=ANY,
            data=json.dumps(data)
        )
        assert response.status_code == 200


@patch('app.utils.get_connector')
@patch('app.routers.v2.charger.send_request')
def test_get_connector_by_serial_number(send_request_mock, get_connector_mock, test_db):
    mock_data = [
        {
            'id': CONNECTOR_ID,
            'billing_type': 'Time',
            'billing_unit_fee': 12.3,
            'billing_currency': 'MYR'
        }
    ]
    send_request_mock.return_value = MockResponse(mock_data, 200)

    mock_connector_data = {
        "id": CONNECTOR_ID,
        "charge_point": {
            "operator_id": OPERATOR_ID,
            "tariff_tag_id": str(uuid.uuid4()),
        },
        "connector_type": {
            "kind": "DC",
        }
    }
    get_connector_mock.return_value = mock_connector_data

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        OperatorFactory(
            id=OPERATOR_ID,
            organization_id=organization_id
        )
        db.commit()
        OperatorChargepointFactory(
            charge_point_id=CHARGE_POINT_ID,
            operator_id=OPERATOR_ID
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/connector/charge_points/discount/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/connector/charge_points/discount/test_sn'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_PREFIX_V2}/connector/charge_points/raw/test_sn',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.charger.send_request')
def test_native_command_mapper(send_request_mock, test_db):
    mock_data = {
        'id': '3fa85f64-5717-4562-b3fc-2c963f66afa6',
        'requester_type': 'User',
        'requester_id': 'test_requester_id',
        'operation': 'RemoteStopTransaction',
        'charge_point_id': CHARGE_POINT_ID,
        'connector_id': CONNECTOR_ID,
        'status': 'Pending',
        'result': 'test'
    }
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        OperatorFactory(
            id=OPERATOR_ID,
            organization_id=organization_id
        )
        db.commit()
        OperatorChargepointFactory(
            charge_point_id=CHARGE_POINT_ID,
            operator_id=OPERATOR_ID
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/command/native/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/command/native/{COMMAND_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_PREFIX_V2}/command/native/{COMMAND_ID}',
            data=b'',
            headers=ANY,
            query_params=QueryParams('')
        )
        assert response.status_code == 200
        assert response.json()['data']['result_message'] is None
        assert response.json()['data']['display_support'] is False


@patch('app.routers.v2.charger.send_request')
def test_ocpi_command_mapper(send_request_mock, test_db):
    mock_data = {
        'id': str(uuid.uuid4()),
        'command': 'test',
        'response_url': 'test',
        'status': 'Pending',
        'ocpi_token_id': 'test_tid',
        'ocpi_location_id': str(uuid.uuid4()),
        'ocpi_evse_id': str(uuid.uuid4()),
        'ocpi_evse_connector_id': str(uuid.uuid4()),
        'response_data': None,
        'result_data': None
    }
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        OperatorFactory(
            id=OPERATOR_ID,
            organization_id=organization_id
        )
        db.commit()
        OperatorChargepointFactory(
            charge_point_id=CHARGE_POINT_ID,
            operator_id=OPERATOR_ID
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/command/ocpi/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/command/ocpi/{COMMAND_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_PREFIX_V2}/command/ocpi/{COMMAND_ID}',
            data=b'',
            headers=ANY,
            query_params=QueryParams('')
        )
        assert response.status_code == 200
        assert response.json()['data']['display_support'] is False


@patch('app.routers.v2.charger.send_request')
def test_proxy_request_current(send_request_mock, test_db):
    id_tag = 'test_id_tag'
    mock_data = {
        'id': str(uuid.uuid4),
        'created_at': '2024-12-20T04:22:10.502Z',
        'updated_at': '2024-12-20T04:22:10.502Z',
        'status': 'Pending',
        'expiry_date': 0,
        'id_tag': id_tag,
        'reason': 'string',
        'connector': {
            'id': CONNECTOR_ID,
            'created_at': '2024-12-20T04:22:10.502Z',
            'updated_at': '2024-12-20T04:22:10.502Z',
            'number': 0,
            'connector_label': 'string',
            'info': {},
            'status': 'Available',
            'ocpp_status': 'Available',
            'billing_type': 'string',
            'billing_unit_fee': 0,
            'billing_unit_fee_after_vat': 0,
            'display_vat': False,
            'billing_cycle': 0,
            'billing_currency': 'MYR',
            'connection_fee': 0,
            'meta': {
                'kilowatt': 0,
                'tariffs_start': 0,
                'tariffs_minute': 0
            },
            'connector_type': {
                'id': '3fa85f64-5717-4562-b3fc-2c963f66afa6',
                'created_at': '2024-12-20T04:22:10.502Z',
                'updated_at': '2024-12-20T04:22:10.502Z',
                'name': 'string',
                'kind': 'string',
                'format': 'string'
            },
            'hogging_tariff': []
        },
        'reservation_id': 0,
        'meta': {},
        'ocpi_evse_connector': {}
    }
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id),
            user_id_tag=id_tag
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/charging/current.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/charging/current'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_PREFIX_V2}/charging/current',
            data=b'',
            headers=ANY,
            query_params=QueryParams('')
        )
        assert response.status_code == 200
        assert response.json().get('success') is True
        assert response.json().get('data')['member_id'] == membership_id


@patch('app.routers.v2.charger.send_request')
def test_proxy_request_reservation(send_request_mock, test_db):
    id_tag = 'test_id_tag'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id),
            user_id_tag=id_tag
        )
        db.commit()
        membership_id = str(mem.id)

        external_org = ExternalOrganizationFactory()
        db.commit()
        ExternalTokenFactory(
            token=str(uuid.uuid4()),
            is_temporary=False,
            external_organization=external_org,
            external_organization_id=str(external_org.id),
            endpoints='{"endpoints": [{"role": "RECEIVER", "identifier": "tokens", "url": "test"}]}',
            external_service_token=base64.b64encode('test_token'.encode('utf-8')).decode('utf-8')
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}/charging/reservation/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        mock_data = {
            'id': CHARGING_SESSION_ID,
            'created_at': '2024-12-20T04:18:22.903Z',
            'updated_at': '2024-12-20T04:18:22.903Z',
            'status': 'Pending',
            'expiry_date': 0,
            'id_tag': id_tag,
            'reason': 'string',
            'connector': {
                'id': CONNECTOR_ID,
                'created_at': '2024-12-20T04:18:22.903Z',
                'updated_at': '2024-12-20T04:18:22.903Z',
                'number': 0,
                'connector_label': 'string',
                'info': {},
                'status': 'Available',
                'ocpp_status': 'Available',
                'billing_type': 'string',
                'billing_unit_fee': 0,
                'billing_unit_fee_after_vat': 0,
                'display_vat': False,
                'billing_cycle': 0,
                'billing_currency': 'MYR',
                'connection_fee': 0,
                'meta': {
                    'kilowatt': 0,
                    'tariffs_start': 0,
                    'tariffs_minute': 0
                },
                'connector_type': {
                    'id': '3fa85f64-5717-4562-b3fc-2c963f66afa6',
                    'created_at': '2024-12-20T04:18:22.903Z',
                    'updated_at': '2024-12-20T04:18:22.903Z',
                    'name': 'string',
                    'kind': 'string',
                    'format': 'string'
                },
                'hogging_tariff': []
            },
            'reservation_id': 0,
            'meta': {
                'operator': {
                    'id': str(external_org.id)
                }
            },
            'ocpi_evse_connector': {}
        }
        send_request_mock.return_value = MockResponse(mock_data, 200)

        reservation_id = str(uuid.uuid4)
        url = f'{CHARGER_BASE_URL}/charging/reservation/{reservation_id}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_PREFIX_V2}/charging/reservation/{reservation_id}',
            data=b'',
            headers=ANY,
            query_params=QueryParams('')
        )
        assert response.status_code == 200
        assert response.json().get('success') is True


@patch('app.routers.v2.charger.send_request')
def test_proxy_request_connector(send_request_mock, test_db):
    id_tag = 'test_id_tag'
    mock_data = [
        {
            'id': CHARGING_SESSION_ID,
            'created_at': '2024-12-20T07:12:19.612482+00:00',
            'updated_at': None,
            'status': 'Charging',
            'meter_start': 0.0,
            'meter_stop': 0.0,
            'session_start': '2024-12-20T15:12:19+08:00',
            'session_end': '2024-12-20T16:12:19+08:00',
            'id_tag': 'test_id_tag',
            'transaction_id': 1234,
            'charge_point_connector': {
                'id': CONNECTOR_ID,
                'created_at': '2024-12-20T07:12:19.509983+00:00',
                'updated_at': '2024-12-20T07:12:19.610000+00:00',
                'number': 1,
                'connector_label': None,
                'info': {},
                'status': 'Available',
                'ocpp_status': 'Available',
                'billing_type': 'kWh',
                'billing_unit_fee': 1.0,
                'billing_unit_fee_after_vat': 0.0,
                'display_vat': False,
                'billing_cycle': 1.0,
                'billing_currency': 'MYR',
                'connection_fee': 1.0,
                'meta': None,
                'connector_type': {
                    'id': 'f9bd9d12-36c6-4817-bbcd-410897f7c776',
                    'created_at': '2024-12-20T07:12:19.507567+00:00',
                    'updated_at': None,
                    'name': 'test',
                    'kind': 'test',
                    'format': None
                },
                'hogging_tariff': None
            },
            'ocpi_evse_connector': None,
            'meta': {
                'charger_serial_number': 'test_sn',
                'charge_point_vendor': None,
                'location': {
                    'name': 'Artemis Car Station',
                    'latitude': 1.0,
                    'longitude': 1.0,
                    'address': 'Esplanade Condo',
                    'id': LOCATION_ID,
                    'ocpi_partner_operator': None
                },
                'billing_info': {
                    'billing_type': 'Time',
                    'billing_unit_fee': 1.0,
                    'billing_unit_fee_after_vat': None,
                    'vat_rate': None,
                    'billing_discounted_type': None,
                    'billing_discounted_amount': None,
                    'billing_cycle': 15,
                    'billing_currency': 'MYR',
                    'connection_fee': 0.0,
                    'hogging_tariff': None,
                    'hogging_fee': None,
                    'preferred_payment_flow': None,
                    'preferred_payment_method': None,
                    'is_low_wallet_balance': None,
                    'subscription_info': None
                },
                'operator': {
                    'id': OPERATOR_ID
                }
            },
            'charging_session_type': 'Local',
            'current_wh': 0.0,
            'current_soc': 0.0,
            'auto_cut_cost': 0.0,
            'estimated_cost': {},
            'stop_reason': 'Machine',
            'stop_message': None,
            'hogging_start': None,
            'hogging_end': None,
            'hogging_status': None,
            'hogging_estimated_cost': {}
        }
    ]
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id),
            user_id_tag=id_tag
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{CHARGER_BASE_URL}charging/charging_sessions/connector/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CHARGER_BASE_URL}/charging/charging_sessions/connector/{CONNECTOR_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_PREFIX_V2}/charging/charging_sessions/connector/{CONNECTOR_ID}',
            data=b'',
            headers=ANY,
            query_params=QueryParams('')
        )
        assert response.status_code == 200
        assert response.json().get('success') is True
        assert len(response.json().get('data')) == 1
        assert response.json().get('data')[0]['member_id'] == membership_id
