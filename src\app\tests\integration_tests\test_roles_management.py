from contextlib import contextmanager
import pytest

from faker import Faker
from fastapi.testclient import TestClient

from app.main import app, ROOT_PATH
from app.models import Membership, Organization, Role
from app.tests.factories import (UserFactory, VerificationTokenFactory, OrganizationFactory,
                                 ResourceServerFactory, ResourceFactory,
                                 RoleFactory, MembershipFactory, )
from app.database import SessionLocal, create_session, Base, engine
from app.schema import MembershipType


fake = Faker()
client = TestClient(app)


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VerificationTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


def create_user_with_auth_token(include_user_id=False, include_membership_id=False, include_organization_id=False,
                                is_superuser=False):
    data = {}

    signin_data = {
        'email': '',
        'password': 'password',
    }
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            is_superuser=is_superuser,
            organization_id=organization.id
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        if include_user_id:
            data['user_id'] = str(user.id)

        if include_membership_id:
            data['membership_id'] = str(membership.id)

        if include_organization_id:
            data['organization_id'] = str(organization.id)

        signin_data['email'] = str(user.email)
        signin_data['organization_id'] = str(organization.id)

    url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
    response = client.post(url, json=signin_data)

    data['auth_token'] = response.json()['auth_token']

    return data


def test_list_organization_roles_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/?',
            scope='get,update,post',
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        db.commit()

        for _item in range(3):
            RoleFactory(
                organization_id=organization_id,
            )
            db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role'
    response = client.get(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert len(response.json()) == 4


def test_list_all_roles_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True,
                                       is_superuser=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+',
            scope='get,update,post',
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        db.commit()

        for _item in range(3):
            RoleFactory(
                organization_id=organization_id,
            )
            db.commit()
        # Create new organization
        new_org = OrganizationFactory()
        db.commit()

        for _item in range(2):
            RoleFactory(organization_id=str(new_org.id))
            db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/organization/roles'
    response = client.get(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert len(response.json()) == 6


def test_list_own_accessible_roles(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+',
            scope='get,update,post',
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        db.commit()

        for _item in range(3):
            RoleFactory(
                organization_id=organization_id,
            )
            db.commit()
        # Create new organization
        new_org = OrganizationFactory()
        db.commit()

        for _item in range(2):
            RoleFactory(organization_id=str(new_org.id))

    url = f'{ROOT_PATH}/api/v1/csms/organization/roles'
    response = client.get(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert len(response.json()) == 4


def test_post_valid_role_data_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    valid_data = {
        'name': fake.pystr(),
    }

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/?',
            scope='get,update,post',
        )
        # Create parent organization and attach its id to organization parent_id
        parent_organization = OrganizationFactory()
        db.commit()

        db_org = db.query(Organization).filter(Organization.id == organization_id)
        db_org.update({'parent_id': parent_organization.id})
        db.commit()

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role'
    response = client.post(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 201
    assert response.json()['organization_id'] == organization_id
    assert len(response.json()['id']) > 0


def test_post_valid_role_data_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    valid_data = {
        'name': fake.pystr(),
    }

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/?',
            scope='get,update,post',
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role'
    response = client.post(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 400


def test_post_invalid_role_data_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    valid_data = {
        'name': '',
    }

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/?',
            scope='get,update,post',
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role'
    response = client.post(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 422
    assert len(response.json()['detail']) == 1


def test_patch_valid_role_data_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')
    valid_data = {
        'name': fake.pystr(),
    }
    role_id = None

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/.+',
            scope='get,patch,post',
        )
        # Create parent organization and attach its id to organization parent_id
        parent_organization = OrganizationFactory()
        db.commit()

        db_org = db.query(Organization).filter(Organization.id == organization_id)
        db_org.update({'parent_id': parent_organization.id})
        db.commit()

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        role = RoleFactory(
            organization_id=organization_id,
        )
        db.commit()

        role_id = str(role.id)

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role/{role_id}'
    response = client.patch(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200


def test_patch_valid_role_data_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')
    valid_data = {
        'name': fake.pystr(),
    }
    role_id = None

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/.+',
            scope='get,patch,post',
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        role = RoleFactory(
            organization_id=organization_id,
        )
        db.commit()

        role_id = str(role.id)

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role/{role_id}'
    response = client.patch(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 400


def test_patch_invalid_role_data_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    invalid_data = {
        'name': '',
    }

    role_id = None

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/.+',
            scope='get,patch,post',
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        role = RoleFactory(
            organization_id=organization_id,
        )
        db.commit()

        role_id = str(role.id)

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role/{role_id}'
    response = client.patch(url, json=invalid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 422
    assert len(response.json()['detail']) == 1


def test_delete_existing_role_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')
    role_id = None

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/.+',
            scope='get,patch,post,delete',
        )
        # Create parent organization and attach its id to organization parent_id
        parent_organization = OrganizationFactory()
        db.commit()

        db_org = db.query(Organization).filter(Organization.id == organization_id)
        db_org.update({'parent_id': parent_organization.id})
        db.commit()

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        role = RoleFactory(
            organization_id=organization_id,
            memberships=[],
        )
        db.commit()

        role_id = str(role.id)

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role/{role_id}'
    response = client.delete(url, headers={'authorization': auth['auth_token']}, json={})

    assert response.status_code == 204

    with contextmanager(override_create_session)() as db:
        assert db.query(Role).filter(Role.id == role_id, Role.is_deleted.is_(False)).first() is None


def test_delete_existing_role_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')
    role_id = None

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/.+',
            scope='get,patch,post,delete',
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        role = RoleFactory(
            organization_id=organization_id,
        )
        db.commit()

        role_id = str(role.id)

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role/{role_id}'
    response = client.delete(url, headers={'authorization': auth['auth_token']}, json={})

    assert response.status_code == 400


def test_delete_non_existing_role_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')
    role_id = None

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/.+',
            scope='get,patch,post,delete',
        )
        # Create parent organization and attach its id to organization parent_id
        parent_organization = OrganizationFactory()
        db.commit()

        db_org = db.query(Organization).filter(Organization.id == organization_id)
        db_org.update({'parent_id': parent_organization.id})
        db.commit()

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        role = RoleFactory(
            organization_id=organization_id,
        )
        db.commit()

        role_id = str(role.id)

        db.delete(role)
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role/{role_id}'
    response = client.delete(url, headers={'authorization': auth['auth_token']}, json={})

    assert response.status_code == 400


def test_add_resource_to_role_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    data = {
        'resources': []
    }
    role_id = None

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/.+/resources/add/?',
            scope='get,patch,post,delete',
        )
        # Create parent organization and attach its id to organization parent_id
        parent_organization = OrganizationFactory()
        db.commit()

        db_org = db.query(Organization).filter(Organization.id == organization_id)
        db_org.update({'parent_id': parent_organization.id})
        db.commit()

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        role = RoleFactory(
            organization_id=organization_id,
        )
        db.commit()

        # Create 3 resources to add to Role
        res_server = ResourceServerFactory()
        db.commit()

        for _item in range(3):
            res = ResourceFactory(resourceserver=res_server)

            db.commit()

            data['resources'].append(str(res.id))

        role_id = str(role.id)

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role/{role_id}/resources/add'
    response = client.post(url, json=data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert len(response.json()['resources']) == 3


def test_add_resource_to_role_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    data = {
        'resources': []
    }
    role_id = None

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/.+/resources/add/?',
            scope='get,patch,post,delete',
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        role = RoleFactory(
            organization_id=organization_id,
        )
        db.commit()

        # Create 3 resources to add to Role
        res_server = ResourceServerFactory()
        db.commit()

        for _item in range(3):
            res = ResourceFactory(resourceserver=res_server)

            db.commit()

            data['resources'].append(str(res.id))

        role_id = str(role.id)

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role/{role_id}/resources/add'
    response = client.post(url, json=data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 400


def test_add_non_existent_resource_to_role_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    data = {
        'resources': []
    }
    role_id = None

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/.+/resources/add/?',
            scope='get,patch,post,delete',
        )
        # Create parent organization and attach its id to organization parent_id
        parent_organization = OrganizationFactory()
        db.commit()

        db_org = db.query(Organization).filter(Organization.id == organization_id)
        db_org.update({'parent_id': parent_organization.id})
        db.commit()

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        role = RoleFactory(
            organization_id=organization_id,
        )
        db.commit()

        # Create 3 resources to add to Role
        res_server = ResourceServerFactory()
        db.commit()

        res = ResourceFactory(resourceserver=res_server)

        db.commit()

        data['resources'].append(str(res.id))

        db.delete(res)
        db.commit()

        role_id = str(role.id)

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role/{role_id}/resources/add'
    response = client.post(url, json=data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert len(response.json()['resources']) == 0


def test_remove_resource_from_endpoint_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    data = {
        'resources': []
    }
    role_id = None

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/.+/resources/remove/?',
            scope='get,patch,post,delete',
        )
        # Create parent organization and attach its id to organization parend_id
        parent_organization = OrganizationFactory()
        db.commit()

        db_org = db.query(Organization).filter(Organization.id == organization_id)
        db_org.update({'parent_id': parent_organization.id})
        db.commit()

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        role = RoleFactory(
            organization_id=organization_id,
        )
        db.commit()

        # Create 3 resources to add to Role
        res_server = ResourceServerFactory()
        db.commit()

        for _item in range(3):
            res = ResourceFactory(resourceserver=res_server)

            db.commit()

            role.resources.append(res)
        db.commit()

        data['resources'].append(str(role.resources[0].id))
        data['resources'].append(str(role.resources[1].id))

        role_id = str(role.id)

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role/{role_id}/resources/remove'
    response = client.post(url, json=data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert len(response.json()['resources']) == 1


def test_remove_non_existent_resource_from_role_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True, include_organization_id=True)
    organization_id = auth.pop('organization_id')
    auth.pop('user_id')
    membership_id = auth.pop('membership_id')

    data = {
        'resources': []
    }
    role_id = None

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory(root_path='apollo-main')

        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/role/.+/resources/remove/?',
            scope='get,patch,post,delete',
        )
        # Create parent organization and attach its id to organization parend_id
        parent_organization = OrganizationFactory()
        db.commit()

        db_org = db.query(Organization).filter(Organization.id == organization_id)
        db_org.update({'parent_id': parent_organization.id})
        db.commit()

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=organization_id)

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(membership_id)
        membership.roles.append(role)

        role = RoleFactory(
            organization_id=organization_id,
        )
        db.commit()

        # Create 3 resources to add to Role
        res_server = ResourceServerFactory()
        db.commit()

        for _item in range(3):
            res = ResourceFactory(resourceserver=res_server)

            db.commit()

            role.resources.append(res)
        db.commit()

        res = ResourceFactory(resourceserver=res_server)
        db.commit()
        role.resources.append(res)
        data['resources'].append(str(res.id))
        db.commit()

        db.delete(res)
        db.commit()

        role_id = str(role.id)

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/role/{role_id}/resources/remove'
    response = client.post(url, json=data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert len(response.json()['resources']) == 3
