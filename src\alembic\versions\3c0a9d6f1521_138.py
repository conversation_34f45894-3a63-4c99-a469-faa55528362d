"""138

Revision ID: 3c0a9d6f1521
Revises: dac228556e44
Create Date: 2024-12-12 14:36:25.196740

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3c0a9d6f1521'
down_revision = 'dac228556e44'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_auth_external_token', sa.Column('pnc_friendly', sa.Bo<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_auth_external_token', 'pnc_friendly')
    # ### end Alembic commands ###
