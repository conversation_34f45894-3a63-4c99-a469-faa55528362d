from datetime import datetime, timed<PERSON><PERSON>
from contextlib import contextmanager
from unittest.mock import patch
import jwt
import pytest
from faker import Faker
from fastapi.testclient import TestClient
import secrets
from uuid import UUID, uuid4

from app import settings, schema
from app.main import app, ROOT_PATH
from app.tests.factories import (UserFactory, OrganizationFactory, MembershipFactory,
                                 IDTagFactory, VehicleFactory, AutochargeFactory,
                                 OrganizationAuthenticationServiceFactory, ResourceFactory, RoleFactory,
                                 ResourceServerFactory,)
from app.database import SessionLocal, create_session, Base, engine
from app.schema import AutochargeStatus

from app.tests.utils import faker_phone_number, is_valid_uuid

client = TestClient(app)
fake = Faker()


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        IDTagFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VehicleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        AutochargeFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session

VEHICLE_BASE_URL = f'{ROOT_PATH}/api/v1/vehicle'


@patch('app.utils.httpx.AsyncClient')
def test_add_vehicle(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            phone_number=faker_phone_number(fake),
            verification_method='phone_number',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.regular_user,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        vehicle_data = {
            'name': 'test_vehicle',
            'model': 'test',
            'brand': 'test',
            'registration_number': 'test'
        }
        url = f'{VEHICLE_BASE_URL}'
        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.post(url, headers=headers, json=vehicle_data)

        assert response.json()['success'] is True
        assert response.json()['error'] is None
        assert is_valid_uuid(response.json()['data'])


@pytest.mark.skip
@patch('app.utils.httpx.AsyncClient')
def test_add_vehicle_invalid_member(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.regular_user,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}',
                                    'get,patch,post,delete,put', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{uuid4()}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        vehicle_data = {
            'name': 'test_vehicle',
            'model': 'test',
            'brand': 'test',
            'registration_number': 'test'
        }
        url = f'{VEHICLE_BASE_URL}'
        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.post(url, headers=headers, json=vehicle_data)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Unable to add vehicle'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_delete_vehicle(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()
        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/{vehicle.id}'
        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.delete(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        assert response.json()['message'] == 'Successfully removed vehicle.'


@patch('app.utils.httpx.AsyncClient')
def test_delete_vehicle_wrong_owner(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()
        user1 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user1.id}',
            vehicles=[vehicle]
        )
        db.commit()

        create_res_server_and_roles(db, mem1, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        user2 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user2.id}'
        )
        db.commit()

        create_res_server_and_roles(db, mem1, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user2.id),
                'membership_id': f'{mem2.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/{vehicle.id}'

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.delete(url, headers=headers)
        assert response.json()['error']['code'] == 4003
        assert response.json()['error']['message'][0] == 'Forbidden, wrong owner.'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_delete_vehicle_not_found(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}'
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/{vehicle.id}'
        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.delete(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'No vehicle or PCID found.'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_get_vehicle_by_member_id(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory(
            name='test_vehicle'
        )
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/member/{mem.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.get(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        response_data = response.json()['data'][0]
        assert is_valid_uuid(response_data['id'])
        assert response_data['name'] == 'test_vehicle'


@patch('app.utils.httpx.AsyncClient')
def test_get_vehicle_by_member_id_v2(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicles = [
            VehicleFactory(name=f'test_vehicle_{i}', pcid=f'HUBOpenProvCert00{i}')
            for i in range(3)
        ]
        db.flush()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=vehicles
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/member_v2/{mem.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.get(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        response_data = response.json()['data']['items']
        assert len(response_data) == 3
        assert response_data[0]['name'] == 'test_vehicle_0'
        assert response_data[1]['name'] == 'test_vehicle_1'
        assert response_data[2]['name'] == 'test_vehicle_2'


@patch('app.utils.httpx.AsyncClient')
def test_get_vehicle_by_member_id_v2_pagigation(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicles = [
            VehicleFactory(name=f'test_vehicle_{i}', pcid=f'HUBOpenProvCert00{i}')
            for i in range(3)
        ]
        db.flush()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=vehicles
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/member_v2/{mem.id}?pagination=true&page=1&size=2'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.get(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        response_data = response.json()['data']['items']
        assert len(response_data) == 2
        assert response_data[0]['name'] == 'test_vehicle_0'
        assert response_data[1]['name'] == 'test_vehicle_1'


@patch('app.utils.httpx.AsyncClient')
def test_get_vehicle_by_vehicle_id(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory(
            name='test_vehicle'
        )
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.get(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        response_data = response.json()['data']
        assert is_valid_uuid(response_data['id'])
        assert response_data['name'] == 'test_vehicle'


@patch('app.utils.httpx.AsyncClient')
def test_get_vehicle_by_vehicle_id_wrong_owner(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory(
            name='test_vehicle'
        )
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user1 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user1.id}',
            vehicles=[vehicle]
        )
        db.commit()

        user2 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user2.id}'
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user2.id),
                'membership_id': f'{mem2.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem1, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        create_res_server_and_roles(db, mem2, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.get(url, headers=headers)
        assert response.json()['error']['code'] == 4003
        assert response.json()['error']['message'][0] == 'Forbidden, wrong owner.'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_get_vehicle_by_vehicle_id_not_found(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}'
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.get(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'No vehicle or PCID found.'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_update_vehicle(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory(
            name='test_vehicle'
        )
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        vehicle_data = {
            'name': 'test_vehicle_new_name',
            'model': 'test',
            'brand': 'test',
            'connector_type_id': str(uuid4()),
            'acceptance_rate': 0,
            'battery_capacity': 0,
            'suspend_timer': 0,
            'pcid': 'ABC123DEF456GHI78',
            'registration_number': 'test',
            'mac_address': 'test',
            'ac_status': 'test'
        }
        url = f'{VEHICLE_BASE_URL}/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.patch(url, headers=headers, json=vehicle_data)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        response_data = response.json()['data']
        assert response_data['name'] == 'test_vehicle_new_name'
        assert response_data['vehicle_histories'] is not None


@patch('app.utils.httpx.AsyncClient')
def test_update_vehicle_wrong_owner(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user1 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user1.id}',
            vehicles=[vehicle]
        )
        db.commit()

        user2 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user2.id}'
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user2.id),
                'membership_id': f'{mem2.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        vehicle_data = {
            'name': 'test_vehicle_new_name'
        }
        url = f'{VEHICLE_BASE_URL}/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem1, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        create_res_server_and_roles(db, mem2, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.patch(url, headers=headers, json=vehicle_data)
        assert response.json()['error']['code'] == 4003
        assert response.json()['error']['message'][0] == 'Forbidden, wrong owner.'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_register_vehicle_for_autocharge(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/register/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.post(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        response_data = response.json()['data']
        assert UUID(response_data['vehicle_id']) == UUID(str(vehicle.id))
        assert response_data['detail_message'] == 'Successfully registered vehicle for Autocharge.'

        response = client.post(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        response_data = response.json()['data']
        assert UUID(response_data['vehicle_id']) == UUID(str(vehicle.id))
        assert response_data['detail_message'] == 'Vehicle already registered for Autocharge.'


@patch('app.utils.httpx.AsyncClient')
def test_get_autocharge_status(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id),
            status=AutochargeStatus.active
        )
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.get(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        assert response.json()['data']['autocharge_status'] == 'Active'


@patch('app.utils.httpx.AsyncClient')
def test_get_autocharge_status_wrong_owner(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id)
        )
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user1 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user1.id}',
            vehicles=[vehicle]
        )
        db.commit()

        user2 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user2.id}'
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user2.id),
                'membership_id': f'{mem2.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem1, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        create_res_server_and_roles(db, mem2, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.get(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Unable to get autocharge status for vehicle'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_get_autocharge_status_no_autocharge(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.get(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        assert response.json()['data']['autocharge_status'] is None


@patch('app.utils.httpx.AsyncClient')
def test_activate_autocharge_for_vehicle(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id)
        )
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/activate/{vehicle.id}?mac_address=00:1A:2B:3C:4D:5E'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        response_data = response.json()['data']
        assert UUID(response_data['vehicle_id']) == UUID(str(vehicle.id))
        assert response_data['detail_message'] == 'Successfully activated vehicle for Autocharge.'


@patch('app.utils.httpx.AsyncClient')
def test_activate_autocharge_for_vehicle_invalid(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id)
        )
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/activate/{vehicle.id}?mac_address=invalid'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Invalid MAC Address'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_activate_autocharge_for_vehicle_wrong_owner(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user1 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user1.id}',
            vehicles=[vehicle]
        )
        db.commit()

        user2 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user2.id}'
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user2.id),
                'membership_id': f'{mem2.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/activate/{vehicle.id}?mac_address=00:1A:2B:3C:4D:5E'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem1, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        create_res_server_and_roles(db, mem2, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Unable to activate vehicle for autocharge'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_activate_autocharge_for_vehicle_no_autocharge(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/activate/{vehicle.id}?mac_address=00:1A:2B:3C:4D:5E'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['success'] is False
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Unable to activate vehicle for autocharge'
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_deactivate_autocharge_for_vehicle(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id)
        )
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/deactivate/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        response_data = response.json()['data']
        assert UUID(response_data['vehicle_id']) == UUID(str(vehicle.id))
        assert response_data['detail_message'] == 'Successfully deactivated Autocharge for vehicle.'


@patch('app.utils.httpx.AsyncClient')
def test_deactivate_autocharge_for_vehicle_wrong_owner(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user1 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user1.id}',
            vehicles=[vehicle]
        )
        db.commit()

        user2 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user2.id}'
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user2.id),
                'membership_id': f'{mem2.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/deactivate/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem1, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        create_res_server_and_roles(db, mem2, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Unable to deactivate autocharge for vehicle'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_deactivate_autocharge_for_vehicle_no_autocharge(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/deactivate/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['success'] is False
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Unable to deactivate autocharge for vehicle'
        response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_disable_autocharge_for_vehicle(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        id_tag = IDTagFactory(id_tag='test_id_tag', member_id=mem.id)
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id),
            status=AutochargeStatus.active,
            id_tag=id_tag
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/disable/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        response_data = response.json()['data']
        assert UUID(response_data['vehicle_id']) == UUID(str(vehicle.id))
        assert response_data['detail_message'] == 'Successfully disabled autocharge for vehicle.'


@patch('app.utils.httpx.AsyncClient')
def test_disable_autocharge_for_vehicle_wrong_status(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        id_tag = IDTagFactory(id_tag='test_id_tag', member_id=mem.id)
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id),
            id_tag=id_tag
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/disable/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['success'] is False
        assert response.json()['error']['code'] == 500
        assert response.json()['data'] is None
        assert response.json()['error']['message'][0] == 'Unable to disable autocharge for vehicle'


@patch('app.utils.httpx.AsyncClient')
def test_disable_autocharge_for_vehicle_wrong_owner(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user1 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user1.id}',
            vehicles=[vehicle]
        )
        db.commit()

        user2 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user2.id}'
        )
        db.commit()

        id_tag = IDTagFactory(id_tag='test_id_tag', member_id=mem1.id)
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id),
            status=AutochargeStatus.active,
            id_tag=id_tag
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user2.id),
                'membership_id': f'{mem2.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/disable/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem1, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        create_res_server_and_roles(db, mem2, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Unable to disable autocharge for vehicle'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_disable_autocharge_for_vehicle_no_autocharge(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/disable/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Unable to disable autocharge for vehicle'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_enable_autocharge_for_vehicle(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        id_tag = IDTagFactory(id_tag='test_id_tag', member_id=mem.id)
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id),
            status=AutochargeStatus.disabled,
            id_tag=id_tag
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/enable/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['success'] is True
        assert response.json()['error'] is None
        response_data = response.json()['data']
        assert UUID(response_data['vehicle_id']) == UUID(str(vehicle.id))
        assert response_data['detail_message'] == 'Successfully enabled autocharge for vehicle.'


@patch('app.utils.httpx.AsyncClient')
def test_enable_autocharge_for_vehicle_wrong_status(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        id_tag = IDTagFactory(id_tag='test_id_tag', member_id=mem.id)
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id),
            id_tag=id_tag
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/enable/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Unable to enable autocharge for vehicle'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_enable_autocharge_for_vehicle_wrong_owner(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user1 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user1.id}',
            vehicles=[vehicle]
        )
        db.commit()

        user2 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user2.id}'
        )
        db.commit()

        id_tag = IDTagFactory(id_tag='test_id_tag', member_id=mem1.id)
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id),
            status=AutochargeStatus.active,
            id_tag=id_tag
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user2.id),
                'membership_id': f'{mem2.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/enable/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem1, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        create_res_server_and_roles(db, mem2, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Unable to enable autocharge for vehicle'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_enable_autocharge_for_vehicle_no_autocharge(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/autocharge/enable/{vehicle.id}'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.put(url, headers=headers)
        assert response.json()['success'] is False
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Unable to enable autocharge for vehicle'
        assert response.json()['data'] is None


# @patch('app.utils.httpx.AsyncClient')
# def test_add_vehicle_pcid(async_client_mock, test_db):
#     with contextmanager(override_create_session)() as db,\
#         patch('app.routers.v2.vehicle.lookup_pcid', AsyncMock(return_value=(True, "OK"))) as mock_lookup:
#             vehicle = VehicleFactory()
#             db.commit()

#             organization = OrganizationFactory()
#             db.commit()
#             organization_id = str(organization.id)
#             user = UserFactory(
#                 phone_number=faker_phone_number(fake),
#                 organization_id=organization_id
#             )
#             db.commit()
#             mem = MembershipFactory(
#                 organization_id=organization_id,
#                 user_id=f'{user.id}',
#                 vehicles=[vehicle]
#             )
#             db.commit()

#             id_tag = IDTagFactory(id_tag='test_id_tag', member_id=mem.id)
#             db.commit()

#             AutochargeFactory(
#                 vehicle_id=str(vehicle.id),
#                 status=AutochargeStatus.active,
#                 id_tag=id_tag
#             )
#             db.commit()

#             token = jwt.encode(
#                 {
#                     'exp': datetime.now() + timedelta(days=1),
#                     'user_id': str(user.id),
#                     'membership_id': f'{mem.id}',
#                 },
#                 settings.JWT_SECRET,
#                 algorithm=schema.JWT_ALGORITHM,
#             )

#             url = f'{VEHICLE_BASE_URL}/plug_and_charge/{vehicle.id}?pcid=1HGCM82633A123456'
#             secret_key = secrets.token_hex(32)
#             headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
#             response = client.post(url, headers=headers)
#             mock_lookup.assert_called_once()
#             mock_lookup.assert_called_with(db, '1HGCM82633A123456')
#             print('<<< response', response.json())
#             assert response.json()['success'] is True
#             assert response.json()['error'] is None
#             response_data = response.json()['data']
#             assert UUID(response_data['vehicle_id']) == UUID(str(vehicle.id))
#             assert response_data['detail_message'] == 'Successfully registered vehicle.'


@patch('app.utils.httpx.AsyncClient')
def test_add_vehicle_pcid_invalid(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        AutochargeFactory(
            vehicle_id=str(vehicle.id)
        )
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/plug_and_charge/{vehicle.id}?pcid=invalid'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.post(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Invalid PCID format'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_add_vehicle_pcid_wrong_owner(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user1 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user1.id}',
            vehicles=[vehicle]
        )
        db.commit()

        user2 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user2.id}'
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user2.id),
                'membership_id': f'{mem2.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/plug_and_charge/{vehicle.id}?pcid=1HGCM82633A123456'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem1, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        create_res_server_and_roles(db, mem2, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.post(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'Forbidden, wrong owner of vehicle.'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@pytest.mark.skip
@patch('app.utils.httpx.AsyncClient')
def test_add_vehicle_pcid_registered(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle1 = VehicleFactory(
            pcid='1HGCM82633A123456'
        )
        db.commit()

        vehicle2 = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user1 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user1.id}',
            vehicles=[vehicle1]
        )
        db.commit()

        user2 = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user2.id}',
            vehicles=[vehicle2]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user2.id),
                'membership_id': f'{mem2.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/plug_and_charge/{vehicle2.id}?pcid=1HGCM82633A123456'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem1, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        create_res_server_and_roles(db, mem2, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.post(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'PCID is already registered by someone else.'
        assert response.json()['success'] is False
        assert response.json()['data'] is None


@patch('app.utils.httpx.AsyncClient')
def test_add_vehicle_pcid_exists(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        vehicle1 = VehicleFactory(
            pcid='1HGCM82633A123456'
        )
        db.commit()

        vehicle2 = VehicleFactory()
        db.commit()

        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            vehicles=[vehicle1, vehicle2]
        )
        db.commit()

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{VEHICLE_BASE_URL}/plug_and_charge/{vehicle2.id}?pcid=1HGCM82633A123456'
        secret_key = secrets.token_hex(32)
        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{VEHICLE_BASE_URL}/.*',
                                    'get,patch,post,delete,put', 'apollo-main')

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        response = client.post(url, headers=headers)
        assert response.json()['error']['code'] == 500
        assert response.json()['error']['message'][0] == 'User already has a vehicle with this PCID'
        assert response.json()['success'] is False
        assert response.json()['data'] is None
