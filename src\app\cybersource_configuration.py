from app import settings


class Configuration:
    def __init__(self):
        # SGD MYR
        # DEFAULT CONFIGURATION
        self.authentication_type = settings.CYBERSOURCE_AUTHENTICATION_TYPE
        self.merchantid = settings.CYBERSOURCE_MERCHANT_ID
        self.run_environment = settings.CYBERSOURCE_RUN_ENVIRONMENT
        self.merchant_keyid = settings.CYBERSOURCE_KEY_ID
        self.merchant_secretkey = settings.CYBERSOURCE_SECRET_KEY
        # 3DS CONFIGURATION
        self.authentication_type_3ds = settings.CYBERSOURCE_AUTHENTICATION_TYPE_3DS
        self.merchantid_3ds = settings.CYBERSOURCE_MERCHANT_ID_3DS
        self.run_environment_3ds = settings.CYBERSOURCE_RUN_ENVIRONMENT_3DS
        self.merchant_keyid_3ds = settings.CYBERSOURCE_KEY_ID_3DS
        self.merchant_secretkey_3ds = settings.CYBERSOURCE_SECRET_KEY_3DS
        # N3DS CONFIGURATION
        self.authentication_type_n3ds = settings.CYBERSOURCE_AUTHENTICATION_TYPE_N3DS
        self.merchantid_n3ds = settings.CYBERSOURCE_MERCHANT_ID_N3DS
        self.run_environment_n3ds = settings.CYBERSOURCE_RUN_ENVIRONMENT_N3DS
        self.merchant_keyid_n3ds = settings.CYBERSOURCE_KEY_ID_N3DS
        self.merchant_secretkey_n3ds = settings.CYBERSOURCE_SECRET_KEY_N3DS

        # MYR MID
        # DEFAULT CONFIGURATION
        self.authentication_type_myr = settings.CYBERSOURCE_AUTHENTICATION_TYPE_MYR
        self.merchantid_myr = settings.CYBERSOURCE_MERCHANT_ID_MYR
        self.run_environment_myr = settings.CYBERSOURCE_RUN_ENVIRONMENT_MYR
        self.merchant_keyid_myr = settings.CYBERSOURCE_KEY_ID_MYR
        self.merchant_secretkey_myr = settings.CYBERSOURCE_SECRET_KEY_MYR
        # 3DS CONFIGURATION
        self.authentication_type_3ds_myr = settings.CYBERSOURCE_AUTHENTICATION_TYPE_3DS_MYR
        self.merchantid_3ds_myr = settings.CYBERSOURCE_MERCHANT_ID_3DS_MYR
        self.run_environment_3ds_myr = settings.CYBERSOURCE_RUN_ENVIRONMENT_3DS_MYR
        self.merchant_keyid_3ds_myr = settings.CYBERSOURCE_KEY_ID_3DS_MYR
        self.merchant_secretkey_3ds_myr = settings.CYBERSOURCE_SECRET_KEY_3DS_MYR
        # N3DS CONFIGURATION
        self.authentication_type_n3ds_myr = settings.CYBERSOURCE_AUTHENTICATION_TYPE_N3DS_MYR
        self.merchantid_n3ds_myr = settings.CYBERSOURCE_MERCHANT_ID_N3DS_MYR
        self.run_environment_n3ds_myr = settings.CYBERSOURCE_RUN_ENVIRONMENT_N3DS_MYR
        self.merchant_keyid_n3ds_myr = settings.CYBERSOURCE_KEY_ID_N3DS_MYR
        self.merchant_secretkey_n3ds_myr = settings.CYBERSOURCE_SECRET_KEY_N3DS_MYR

        # META KEY PARAMETERS
        self.use_metakey = False
        self.portfolio_id = ''
        # CONNECTION TIMEOUT PARAMETER
        self.timeout = 1000

    # Assigning the configuration properties in the configuration dictionary
    def get_configuration(self, type="default", currency='SGD'):
        configuration_dictionary = ({})
        if type == '3ds':
            if currency == 'SGD':
                configuration_dictionary["authentication_type"] = self.authentication_type_3ds
                configuration_dictionary["merchantid"] = self.merchantid_3ds
                configuration_dictionary["run_environment"] = self.run_environment_3ds
                configuration_dictionary["merchant_keyid"] = self.merchant_keyid_3ds
                configuration_dictionary["merchant_secretkey"] = self.merchant_secretkey_3ds
            else:
                configuration_dictionary["authentication_type"] = self.authentication_type_3ds_myr
                configuration_dictionary["merchantid"] = self.merchantid_3ds_myr
                configuration_dictionary["run_environment"] = self.run_environment_3ds_myr
                configuration_dictionary["merchant_keyid"] = self.merchant_keyid_3ds_myr
                configuration_dictionary["merchant_secretkey"] = self.merchant_secretkey_3ds_myr
        elif type == 'n3ds':
            if currency == 'SGD':
                configuration_dictionary["authentication_type"] = self.authentication_type_n3ds
                configuration_dictionary["merchantid"] = self.merchantid_n3ds
                configuration_dictionary["run_environment"] = self.run_environment_n3ds
                configuration_dictionary["merchant_keyid"] = self.merchant_keyid_n3ds
                configuration_dictionary["merchant_secretkey"] = self.merchant_secretkey_n3ds
            else:
                configuration_dictionary["authentication_type"] = self.authentication_type_n3ds_myr
                configuration_dictionary["merchantid"] = self.merchantid_n3ds_myr
                configuration_dictionary["run_environment"] = self.run_environment_n3ds_myr
                configuration_dictionary["merchant_keyid"] = self.merchant_keyid_n3ds_myr
                configuration_dictionary["merchant_secretkey"] = self.merchant_secretkey_n3ds_myr
        else:
            if currency == 'SGD':
                configuration_dictionary["authentication_type"] = self.authentication_type
                configuration_dictionary["merchantid"] = self.merchantid
                configuration_dictionary["run_environment"] = self.run_environment
                configuration_dictionary["merchant_keyid"] = self.merchant_keyid
                configuration_dictionary["merchant_secretkey"] = self.merchant_secretkey
            else:
                configuration_dictionary["authentication_type"] = self.authentication_type_myr
                configuration_dictionary["merchantid"] = self.merchantid_myr
                configuration_dictionary["run_environment"] = self.run_environment_myr
                configuration_dictionary["merchant_keyid"] = self.merchant_keyid_myr
                configuration_dictionary["merchant_secretkey"] = self.merchant_secretkey_myr
        return configuration_dictionary
