"""89

Revision ID: 954a8dd8b70b
Revises: 94044eef29c8
Create Date: 2024-04-25 15:19:26.647230

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '954a8dd8b70b'
down_revision = '94044eef29c8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_payment_gateway_reconcilation', 'transaction_cost', type_=sa.String())
    op.alter_column('main_payment_gateway_reconcilation', 'gst', type_=sa.String())
    op.alter_column('main_payment_gateway_reconcilation', 'net_amount', type_=sa.String())
    op.alter_column('main_payment_gateway_reconcilation', 'amount', type_=sa.String())
    op.alter_column('main_payment_gateway_reconcilation', 'bin_number', type_=sa.String())

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_payment_gateway_reconcilation', 'transaction_cost', type_=sa.Float())
    op.alter_column('main_payment_gateway_reconcilation', 'gst', type_=sa.Float())
    op.alter_column('main_payment_gateway_reconcilation', 'net_amount', type_=sa.Float())
    op.alter_column('main_payment_gateway_reconcilation', 'amount', type_=sa.Float())
    op.alter_column('main_payment_gateway_reconcilation', 'bin_number', type_=sa.Float())
    # ### end Alembic commands ###
