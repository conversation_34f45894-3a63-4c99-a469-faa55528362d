"""93

Revision ID: 8cc0248f2df0
Revises: 47da89523b05
Create Date: 2024-05-09 12:03:47.278343

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8cc0248f2df0'
down_revision = '47da89523b05'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_pre_auth_payment_refund', sa.Column('stat_code', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_pre_auth_payment_refund', 'stat_code')
    # ### end Alembic commands ###
