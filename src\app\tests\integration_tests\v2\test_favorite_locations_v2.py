import uuid
from datetime import datetime, timedelta, timezone
from contextlib import contextmanager
from unittest.mock import patch, ANY
import jwt
import pytest
import secrets
from fastapi.testclient import TestClient

from app import settings, schema, models
from app.main import app, ROOT_PATH
from app.tests.factories import (UserFactory, OrganizationFactory, MembershipFactory, ResourceServerFactory,
                                 ResourceFactory, RoleFactory, OrganizationAuthenticationServiceFactory)
from app.database import SessionLocal, create_session, Base, engine
from app.tests.mocks.async_client import MockResponse

client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session

FAVORITE_LOCATION_BASE_URL = f'{ROOT_PATH}/api/v1/favorite_locations'
CHARGER_URL_V2_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'

LOCATION_ID_1 = str(uuid.uuid4())
LOCATION_ID_2 = str(uuid.uuid4())
LOCATION_ID = str(uuid.uuid4())
OPERATOR_ID = str(uuid.uuid4())
CHARGE_POINT_ID = str(uuid.uuid4())


@patch('app.utils.httpx.AsyncClient')
def test_get_favorite_invalid_enum(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        staff = UserFactory(
            is_superuser=True,
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        create_res_server_and_roles(db, mem, str(organization.id),
                                    fr'{FAVORITE_LOCATION_BASE_URL}',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
    url = f'{FAVORITE_LOCATION_BASE_URL}'
    response = client.put(url, headers={'authorization': token}, params={"location_id": LOCATION_ID_1,
                                                                         "type": "favotires"})
    assert response.json()['error']['code'] == 4222


@patch('app.utils.httpx.AsyncClient')
def test_get_favorite_value_error_misssing(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        staff = UserFactory(
            is_superuser=True,
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        create_res_server_and_roles(db, mem, str(organization.id),
                                    fr'{FAVORITE_LOCATION_BASE_URL}',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
    url = f'{FAVORITE_LOCATION_BASE_URL}'
    response = client.put(url, headers={'authorization': token}, params={"type": "Favorite"})
    assert response.json()['error']['code'] == 4222


@patch('app.routers.v2.favorite_locations.send_request')
def test_get_favorite_location_list(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id),
            favorite_locations=[LOCATION_ID_1]
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{FAVORITE_LOCATION_BASE_URL}.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = 'sort_by=distance&longitude=1.0&latitude=2.0'
        url = f'{FAVORITE_LOCATION_BASE_URL}?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/?{params}&location_id={LOCATION_ID_1}&',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.favorite_locations.send_request')
def test_get_favorite_location_list_empty(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id),
            favorite_locations=[]
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{FAVORITE_LOCATION_BASE_URL}.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = 'sort_by=distance&longitude=1.0&latitude=2.0'
        url = f'{FAVORITE_LOCATION_BASE_URL}?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        send_request_mock.assert_not_called()
        assert response.status_code == 200


@patch('app.routers.v2.favorite_locations.send_request')
def test_set_favorite_location_favorite(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id),
            favorite_locations=[LOCATION_ID_1]
        )
        db.commit()
        membership_id = str(mem.id)
        query = db.query(models.Membership).filter(models.Membership.id == mem.id)
        assert len(query[0].favorite_locations) == 1
        assert query[0].favorite_locations[0] == LOCATION_ID_1

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{FAVORITE_LOCATION_BASE_URL}.*',
            'put', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        new_location_id = str(uuid.uuid4())
        params = f'location_id={new_location_id}&type=Favorite'
        url = f'{FAVORITE_LOCATION_BASE_URL}?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.put(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/{new_location_id}',
            headers=ANY
        )
        assert response.status_code == 200
        query = db.query(models.Membership).filter(models.Membership.id == mem.id)
        assert len(query[0].favorite_locations) == 2
        assert query[0].favorite_locations[1] == new_location_id


@patch('app.routers.v2.favorite_locations.send_request')
def test_set_favorite_location_unfavorite(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id),
            favorite_locations=[LOCATION_ID_1]
        )
        db.commit()
        membership_id = str(mem.id)
        query = db.query(models.Membership).filter(models.Membership.id == mem.id)
        assert len(query[0].favorite_locations) == 1
        assert query[0].favorite_locations[0] == LOCATION_ID_1

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{FAVORITE_LOCATION_BASE_URL}.*',
            'put', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = f'location_id={LOCATION_ID_1}&type=Unfavorite'
        url = f'{FAVORITE_LOCATION_BASE_URL}?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.put(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/{LOCATION_ID_1}',
            headers=ANY
        )
        assert response.status_code == 200
        query = db.query(models.Membership).filter(models.Membership.id == mem.id)
        assert len(query[0].favorite_locations) == 0


@patch('app.routers.v2.favorite_locations.send_request')
def test_set_favorite_location_favorite_mock_400(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 400)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id),
            favorite_locations=[LOCATION_ID_1]
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{FAVORITE_LOCATION_BASE_URL}.*',
            'put', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        new_location_id = str(uuid.uuid4())
        params = f'location_id={new_location_id}&type=Favorite'
        url = f'{FAVORITE_LOCATION_BASE_URL}?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.put(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/{new_location_id}',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.favorite_locations.send_request')
def test_get_ocpi_favorite_location_list_mock_data(send_request_mock, test_db):
    mock_data = [{
        'charge_points': [
            {
                'id': CHARGE_POINT_ID,
                'serial_number': 'test_sn',
                'is_private': True,
                'location_id': LOCATION_ID_1,
                'operator_id': OPERATOR_ID
            }
        ]
    }]
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id),
            favorite_locations=[LOCATION_ID_1]
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{FAVORITE_LOCATION_BASE_URL}/ocpi.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = 'sort_by=distance&longitude=1.0&latitude=2.0'
        url = f'{FAVORITE_LOCATION_BASE_URL}/ocpi?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/ocpi/favourite/?{params}&location_id={LOCATION_ID_1}&',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.favorite_locations.send_request')
def test_set_ocpi_favorite_location_favorite(send_request_mock, test_db):
    mock_data = {
        'charge_points': [
            {
                'id': CHARGE_POINT_ID,
                'serial_number': 'test_sn',
                'is_private': True,
                'location_id': LOCATION_ID_1,
                'operator_id': OPERATOR_ID
            }
        ]
    }
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id),
            favorite_locations=[LOCATION_ID_1]
        )
        db.commit()
        membership_id = str(mem.id)
        query = db.query(models.Membership).filter(models.Membership.id == mem.id)
        assert len(query[0].favorite_locations) == 1
        assert query[0].favorite_locations[0] == LOCATION_ID_1

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{FAVORITE_LOCATION_BASE_URL}/ocpi.*',
            'put', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        new_location_id = str(uuid.uuid4())
        params = f'location_id={new_location_id}&type=Favorite'
        url = f'{FAVORITE_LOCATION_BASE_URL}/ocpi?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.put(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/ocpi/{new_location_id}',
            headers=ANY
        )
        assert response.status_code == 200
        query = db.query(models.Membership).filter(models.Membership.id == mem.id)
        assert len(query[0].favorite_locations) == 2
        assert query[0].favorite_locations[1] == new_location_id


@patch('app.routers.v2.favorite_locations.send_request')
def test_set_ocpi_favorite_location_unfavorite(send_request_mock, test_db):
    mock_data = {
        'charge_points': [
            {
                'id': CHARGE_POINT_ID,
                'serial_number': 'test_sn',
                'is_private': True,
                'location_id': LOCATION_ID_1,
                'operator_id': OPERATOR_ID
            }
        ]
    }
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id),
            favorite_locations=[LOCATION_ID_1]
        )
        db.commit()
        membership_id = str(mem.id)
        query = db.query(models.Membership).filter(models.Membership.id == mem.id)
        assert len(query[0].favorite_locations) == 1
        assert query[0].favorite_locations[0] == LOCATION_ID_1

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{FAVORITE_LOCATION_BASE_URL}/ocpi.*',
            'put', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = f'location_id={LOCATION_ID_1}&type=Unfavorite'
        url = f'{FAVORITE_LOCATION_BASE_URL}/ocpi?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.put(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/ocpi/{LOCATION_ID_1}',
            headers=ANY
        )
        assert response.status_code == 200
        query = db.query(models.Membership).filter(models.Membership.id == mem.id)
        assert len(query[0].favorite_locations) == 0
