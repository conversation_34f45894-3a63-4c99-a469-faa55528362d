"""20

Revision ID: 5cbc7914d244
Revises: b2526a3243bc
Create Date: 2022-11-25 14:47:57.749264

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5cbc7914d244'
down_revision = 'b2526a3243bc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_subscription_plan', sa.Column('renewal_fee', sa.Float(), nullable=True))
    op.add_column('main_subscription_plan', sa.Column('delivery_fee', sa.Float(), nullable=True))
    op.add_column('main_subscription_plan', sa.Column('delivery_fee_my', sa.Float(), nullable=True))
    op.add_column('main_subscription_plan', sa.Column('allow_invitation_code', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_subscription_plan', 'allow_invitation_code')
    op.drop_column('main_subscription_plan', 'delivery_fee_my')
    op.drop_column('main_subscription_plan', 'delivery_fee')
    op.drop_column('main_subscription_plan', 'renewal_fee')
    # ### end Alembic commands ###
