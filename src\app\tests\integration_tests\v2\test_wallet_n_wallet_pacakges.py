import secrets
from contextlib import contextmanager
from datetime import datetime, timedelta

import jwt
import pytest
from fastapi.testclient import TestClient

from app import settings, schema
from app.database import SessionLocal, create_session, Base, engine
from app.main import app, ROOT_PATH
from app.models import WalletPackageInvitation, Wallet
from app.schema import MembershipType
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory, CreditCardFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory,
                                 OrganizationAuthenticationServiceFactory, WalletPackageInvitationFactory,
                                 WalletFactory, WalletPackageFactory, PaymentRequestFactory)

client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        WalletFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        WalletPackageFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        WalletPackageInvitationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PaymentRequestFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session

WALLET_BASE_URL = f'{ROOT_PATH}/api/v1/wallet'


def test_redeem_package_with_invalid_code(test_db):
    url = f'{ROOT_PATH}/api/v1/packages/redeem'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        WalletFactory(member_id=str(mem.id))
        db.commit()
        package = WalletPackageFactory(name='Package 1', feature_description=['ABC'])
        db.commit()
        package_invitation = WalletPackageInvitationFactory(code='CODE1', wallet_package_id=package.id)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{ROOT_PATH}/api/v1/packages/redeem',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": staff_id,
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        redeem_data = {
            'redeem_code': f'{package_invitation.code}_ABC'
        }

        response = client.post(url,
                               headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id},
                               json=redeem_data)
        assert response.status_code == 200
        assert not response.json()['success']
        assert response.json()['error']['message'] == ['This code is not valid or expired. Please check and try again.']


def test_redeem_package_with_used_code_redeemed_by_other_user(test_db):
    url = f'{ROOT_PATH}/api/v1/packages/redeem'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(
            parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        staff2 = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)
        staff2_id = str(staff2.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.regular_user,
        )
        mem2 = MembershipFactory(
            organization_id=organization_id,
            user_id=staff2_id,
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        WalletFactory(member_id=str(mem.id))
        wallet2 = WalletFactory(member_id=str(mem2.id))
        db.commit()
        package = WalletPackageFactory(name='Package 1', feature_description=['ABC'])
        db.commit()
        package_invitation = WalletPackageInvitationFactory(code='CODE1', wallet_package_id=package.id,
                                                            linked_at=datetime.utcnow(), wallet_id=wallet2.id)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{ROOT_PATH}/api/v1/packages/redeem',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": staff_id,
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        redeem_data = {
            'redeem_code': f'{package_invitation.code}'
        }

        response = client.post(url,
                               headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id},
                               json=redeem_data)
        assert response.status_code == 200
        assert not response.json()['success']
        assert response.json()['error']['message'] == ['This code is not valid or expired. Please check and try again.']


def test_redeem_package_with_used_code_redeemed_by_same_user(test_db):
    url = f'{ROOT_PATH}/api/v1/packages/redeem'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        wallet = WalletFactory(member_id=str(mem.id))
        db.commit()
        package = WalletPackageFactory(name='Package 1', feature_description=['ABC'])
        db.commit()
        package_invitation = WalletPackageInvitationFactory(code='CODE1', wallet_package_id=package.id,
                                                            linked_at=datetime.utcnow(), wallet_id=wallet.id)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{ROOT_PATH}/api/v1/packages/redeem',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": staff_id,
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        redeem_data = {
            'redeem_code': f'{package_invitation.code}'
        }

        response = client.post(url,
                               headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id},
                               json=redeem_data)
        assert response.status_code == 200
        assert not response.json()['success']
        assert response.json()['error']['message'] == ['You have already redeemed this code.']


def test_redeem_package_with_unredeemeable_package(test_db):
    url = f'{ROOT_PATH}/api/v1/packages/redeem'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        WalletFactory(member_id=str(mem.id))
        db.commit()
        package = WalletPackageFactory(name='Package 1', feature_description=['ABC'], is_redeemable=False)
        db.commit()
        package_invitation = WalletPackageInvitationFactory(code='CODE1', wallet_package_id=package.id)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{ROOT_PATH}/api/v1/packages/redeem',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": staff_id,
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        redeem_data = {
            'redeem_code': f'{package_invitation.code}'
        }

        response = client.post(url,
                               headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id},
                               json=redeem_data)
        assert response.status_code == 200
        assert not response.json()['success']
        assert response.json()['error']['message'] == ['Package not redeemable.']


def test_redeem_package_with_unavailable_package(test_db):
    url = f'{ROOT_PATH}/api/v1/packages/redeem'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        WalletFactory(member_id=str(mem.id))
        db.commit()
        package = WalletPackageFactory(name='Package 1', feature_description=['ABC'], is_active=False)
        db.commit()
        package_invitation = WalletPackageInvitationFactory(code='CODE1', wallet_package_id=package.id)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{ROOT_PATH}/api/v1/packages/redeem',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": staff_id,
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        redeem_data = {
            'redeem_code': f'{package_invitation.code}'
        }

        response = client.post(url,
                               headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id},
                               json=redeem_data)
        assert response.status_code == 200
        assert not response.json()['success']
        assert response.json()['error']['message'] == ['Package not available.']


def test_redeem_package_with_valid_code_topup_success(test_db):
    url = f'{ROOT_PATH}/api/v1/packages/redeem'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        wallet = WalletFactory(member_id=str(mem.id))
        db.commit()
        wallet_id = wallet.id
        package = WalletPackageFactory(name='Package 1', feature_description=['ABC'])
        db.commit()
        package_credit = package.credit_amount
        package_invitation = WalletPackageInvitationFactory(code='CODE1', wallet_package_id=package.id)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{ROOT_PATH}/api/v1/packages/redeem',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": staff_id,
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        redeem_data = {
            'redeem_code': f'{package_invitation.code}'
        }

        response = client.post(url,
                               headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id},
                               json=redeem_data)
        assert response.status_code == 200
        assert response.json()['success']
        assert response.json()['data']

    with contextmanager(override_create_session)() as db:
        package_invitation = db.query(WalletPackageInvitation).get(f'{package_invitation.id}')
        assert package_invitation.linked_at
        assert package_invitation.wallet_id == wallet_id

        wallet = db.query(Wallet).get(f'{wallet_id}')
        assert float(wallet.balance) == float(package_credit)


def test_redeem_package_with_valid_code_topup_success_pre_deposit(test_db):
    url = f'{ROOT_PATH}/api/v1/packages/redeem'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        PaymentRequestFactory(
            amount='14.20',
            type=schema.PaymentRequestType.wallet,
            billing_description='Topup',
            reason=schema.PaymentRequestReason.deposit,
            member_id=mem.id,
            status=schema.PaymentRequestStatus.done,
            currency=schema.Currency.myr
        )
        wallet = WalletFactory(member_id=str(mem.id), balance='14.20')
        db.commit()
        wallet_id = wallet.id
        package = WalletPackageFactory(name='Package 1', feature_description=['ABC'])
        db.commit()
        package_credit = package.credit_amount
        package_invitation = WalletPackageInvitationFactory(code='CODE1', wallet_package_id=package.id)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{ROOT_PATH}/api/v1/packages/redeem',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": staff_id,
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        redeem_data = {
            'redeem_code': f'{package_invitation.code}'
        }

        response = client.post(url,
                               headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id},
                               json=redeem_data)
        assert response.status_code == 200
        assert response.json()['success']
        assert response.json()['data']

    with contextmanager(override_create_session)() as db:
        package_invitation = db.query(WalletPackageInvitation).get(f'{package_invitation.id}')
        assert package_invitation.linked_at
        assert package_invitation.wallet_id == wallet_id

        wallet = db.query(Wallet).get(f'{wallet_id}')
        assert float(wallet.balance) == float(package_credit) + float(14.2)


def test_get_wallet_balance_but_wrong_balance(test_db):
    url = f'{ROOT_PATH}/api/v1/wallet/balance'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        PaymentRequestFactory(
            amount='14.20',
            type=schema.PaymentRequestType.wallet,
            billing_description='Topup',
            reason=schema.PaymentRequestReason.deposit,
            member_id=mem.id,
            status=schema.PaymentRequestStatus.done,
            currency=schema.Currency.myr
        )
        WalletFactory(member_id=str(mem.id), balance='0.20')
        db.commit()
        # wallet_id = wallet.id
        create_res_server_and_roles(db, mem, str(organization.id), fr'{ROOT_PATH}/api/v1/wallet/balance',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": staff_id,
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        response = client.get(url,
                              headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id})
        assert response.status_code == 200
        assert response.json()['success']
        assert response.json()['data']
        assert float(response.json()['data']['balance']) == 14.20


def test_get_wallet_balance_but_no_balance(test_db):
    url = f'{ROOT_PATH}/api/v1/wallet/balance'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.regular_user,
        )
        db.commit()
        create_res_server_and_roles(db, mem, str(organization.id), fr'{ROOT_PATH}/api/v1/wallet/balance',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": staff_id,
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        response = client.get(url,
                              headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id})
        assert response.status_code == 200
        assert response.json()['success']
        assert response.json()['data']
        assert float(response.json()['data']['balance']) == 0.00
