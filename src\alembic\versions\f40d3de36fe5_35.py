"""35
Revision ID: f40d3de36fe5
Revises: 58e4451c87b4
Create Date: 2023-04-28 13:46:00.029694
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f40d3de36fe5'
down_revision = '58e4451c87b4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_charging_session_bill',
                  sa.Column('meta', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_charging_session_bill', 'meta')
    # ### end Alembic commands ###
