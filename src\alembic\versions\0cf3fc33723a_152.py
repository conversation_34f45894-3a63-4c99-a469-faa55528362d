"""152

Revision ID: 0cf3fc33723a
Revises: 0f25b5932237
Create Date: 2025-02-13 03:09:28.417756

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0cf3fc33723a'
down_revision = '0f25b5932237'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_campaign', sa.Column('per_user_usage_limit', sa.Integer(), nullable=True))
    op.add_column('main_campaign', sa.Column('valid_membership_segments_only', postgresql.ARRAY(sa.String()), nullable=True))
    op.add_column('main_campaign', sa.Column('is_stackable', sa.<PERSON>(), nullable=False, server_default=sa.false()))
    op.add_column('main_campaign', sa.Column('valid_period', postgresql.JSONB(astext_type=sa.Text()), nullable=True))

    op.add_column('main_membership', sa.Column('membership_segment', sa.String(), nullable=True))
    # ### end Alembic commands ###

def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_membership', 'membership_segment')

    op.drop_column('main_campaign', 'valid_period')
    op.drop_column('main_campaign', 'is_stackable')
    op.drop_column('main_campaign', 'valid_membership_segments_only')
    op.drop_column('main_campaign', 'per_user_usage_limit')
    # ### end Alembic commands ###
