"""74

Revision ID: 4b1307359838
Revises: 3e750bf97772
Create Date: 2024-02-21 18:11:54.771027

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4b1307359838'
down_revision = '3e750bf97772'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_pre_auth_payment', sa.Column('charging_session_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_pre_auth_payment', 'charging_session_id')
    # ### end Alembic commands ###
