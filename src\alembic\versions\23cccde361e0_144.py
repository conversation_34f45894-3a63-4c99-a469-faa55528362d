"""144

Revision ID: 23cccde361e0
Revises: 7cbc8b030660
Create Date: 2025-01-20 19:01:10.732913

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '23cccde361e0'
down_revision = '7cbc8b030660'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_dunning_repayment_log',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('bill_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('charging_session_id', sa.String(), nullable=True),
    sa.Column('payment_amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('payment_payload', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('payment_response', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['bill_id'], ['main_charging_session_bill.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('main_membership_dunning', sa.Column('dunnings', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_membership_dunning', sa.Column('all_outstanding_bill_ids', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_membership_dunning', sa.Column('total_outstanding_amount', sa.Numeric(scale=2), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_membership_dunning', 'total_outstanding_amount')
    op.drop_column('main_membership_dunning', 'all_outstanding_bill_ids')
    op.drop_column('main_membership_dunning', 'dunnings')
    op.drop_table('main_dunning_repayment_log')
    # ### end Alembic commands ###
