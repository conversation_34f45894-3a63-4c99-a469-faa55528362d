# Docker Related

## Postgres
POSTGRES_APOLLO_MAIN_DATABASE=apollo_main
POSTGRES_DB=apollo_main
POSTGRES_USER=apollo_main
POSTGRES_PASSWORD=apollo_main
POSTGRES_HOST=main-postgres
POSTGRES_PORT=5432
DATABASE_SSL=False

## Rabbit MQ
RABBITMQ_HOST=main-rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=apollo
RABBITMQ_PASS=apollo
RABBITMQ_DEFAULT_USER=apollo
RABBITMQ_DEFAULT_PASS=apollo

# Apps Related

ALLOWED_HOSTS=localhost,127.0.0.1,apollo-main,testserver
CORS_ORIGINS=localhost,127.0.0.1,testserver
JWT_SECRET=1234abcd!
LOG_LEVEL=DEBUG
APOLLO_MAIN_DOMAIN=https://apollo.itstartechs.com
CHARGER_DOMAIN=http://apollo-charger:8000


MERCHANT_ID=MERCHANT_ID
VERIFY_KEY=VERIFY_KEY
SECRET_KEY=SECRET_KEY

## Sendgrid
SENDGRID_API_KEY=sendgrid_api_key
SENDER_EMAIL=<EMAIL>
SENDER_NAME=YGT

## Twillio
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=8f145daa1112c2280a23aede622fea71
TWILIO_VERIFY_SERVICE_SID=VAff143ee96ddaaabd5882e659290f9862

## Sentry
SENTRY_DSN=https://<EMAIL>/6135016

## S3
S3_BUCKET = apollo-python-develop
AWS_ACCESS_KEY = ********************
AWS_SECRET_ACCESS_KEY = hcAh3n0BtqYPJf+FgFM7q2EQ5UvpUSINQm7NAOOh

MIGRATION_RAILS_DOMAIN=https://staging.jomcharge.com/api/migration
MIGRATION_API_KEY=313f5053-5561-4dac-986d-3b918d92dbb3
