# pylint:disable=too-many-lines
import datetime
import json
import math
from typing import ClassVar
from urllib.parse import urlparse

import pydantic
from dateutil import parser

import httpx
import pytz
import starlette
from fastapi import HTTPException
from fastapi import Request
# from fastapi import BackgroundTasks
from fastapi.exceptions import ValidationError
from fastapi.openapi.utils import get_openapi
from fastapi import Response as FastAPIResponse
from pydantic import dataclasses
from httpx import Response
from py_ocpi import get_application, enums
from py_ocpi.core.config import settings
from py_ocpi.core.data_types import URL, DisplayText
from py_ocpi.core.enums import Action
from py_ocpi.core.exceptions import AuthorizationOCPIError
from py_ocpi.modules.cdrs.v_2_2_1.enums import AuthMethod
from py_ocpi.modules.cdrs.v_2_2_1.schemas import (CdrToken, Cdr, CdrLocation, ChargingPeriod, CdrDimension,
                                                  CdrDimensionType)
from py_ocpi.modules.commands.v_2_2_1.enums import CommandType, CommandResponseType
from py_ocpi.modules.commands.v_2_2_1.schemas import CommandResponse, CommandResult
from py_ocpi.modules.credentials.v_2_2_1.schemas import Credentials, CredentialsRole
from py_ocpi.modules.locations.v_2_2_1.enums import ConnectorFormat, PowerType, ConnectorType, Capability
from py_ocpi.modules.locations.v_2_2_1.schemas import GeoLocation, Location, Connector, EVSE, BusinessDetails, Image, \
    ImageCategory
from py_ocpi.modules.sessions.v_2_2_1.enums import SessionStatus
from py_ocpi.modules.sessions.v_2_2_1.schemas import Session
from py_ocpi.modules.tariffs.v_2_2_1.enums import TariffDimensionType
from py_ocpi.modules.tariffs.v_2_2_1.schemas import Tariff, PriceComponent, TariffElement, TariffRestrictions
from py_ocpi.modules.tokens.v_2_2_1.enums import TokenType
from py_ocpi.modules.tokens.v_2_2_1.schemas import Token, EnergyContract
from py_ocpi.modules.versions.enums import VersionNumber
from starlette.middleware.base import BaseHTTPMiddleware

from app import schema, token
from app.settings import (MAIN_OCPI_DOMAIN, MAIN_ROOT_PATH, CHARGER_ROOT_PATH,
                          CHARGER_OCPI_DOMAIN, OCPI_COUNTRY_CODE, OCPI_PARTY_ID, OCPI_HOST, IS_DEVELOP_ENV,
                          SEND_OCPI_ROAMING_OPERATING_HOURS)

main_path = f"{MAIN_OCPI_DOMAIN}/{MAIN_ROOT_PATH}"
charger_path = f'{CHARGER_OCPI_DOMAIN}/{CHARGER_ROOT_PATH}'
MAIN_CHARGER_URL_PREFIX = f'{CHARGER_OCPI_DOMAIN}/{CHARGER_ROOT_PATH}'


def remove_none(obj):
    if isinstance(obj, (list, tuple, set)):
        return type(obj)(remove_none(x) for x in obj if x is not None)
    if isinstance(obj, dict):
        return type(obj)((remove_none(k), remove_none(v))
                         for k, v in obj.items() if k is not None and v is not None)
    return obj


def decode_auth_token_from_headers(headers: dict) -> dict:
    auth_token = headers.get('authorization')
    if auth_token:
        auth_token = auth_token.removeprefix(f'{schema.TokenTypeEnum.bearer} ')
        decoded = token.decode_token(auth_token)
        return decoded
    return {}


class Crud:
    @classmethod
    async def authorization(cls, external_authorization_token: str):
        if not external_authorization_token:
            raise HTTPException(status_code=401, detail="No authorization token provided")

        path = f"{main_path}/api/v1/csms/external_auth/ocpi-auth/{external_authorization_token}"
        async with httpx.AsyncClient() as client:
            response = await client.post(path, timeout=30)
        try:
            if response.status_code == 200 and response.json()['success']:
                return {'authorization': f"Token {response.json()['token']}", 'party_id': response.json()['party_id'],
                        'country_code': response.json()['country_code'],
                        'standardized_tariff': response.json()['standardized_tariff'],
                        'is_publish_all': response.json()['is_publish_all'], 'is_lta': response.json()['is_lta']}
        except Exception as e:  # pylint: disable=broad-except
            print(e)
            raise HTTPException(status_code=401, detail="Your authorization token is invalid")

    @classmethod
    async def charger_authorization(cls, token: str):
        if not token:
            raise HTTPException(status_code=401, detail="No authorization token provided")
        path = f"{main_path}/api/v1/csms/external_auth/ocpi-charger-auth/{token}"
        async with httpx.AsyncClient() as client:
            response = await client.post(path, timeout=30)
        try:
            if response.status_code == 200 and response.json()['success']:
                # return response.json()['success']
                return {
                    'ocpi-authorization': f"Token {response.json()['token']}",
                    'external_organization_id': response.json()['token_object']['external_organization_id'],
                    'standardized_tariff': response.json()['standardized_tariff'],
                    'is_publish_all': response.json()['is_publish_all'],
                    'is_lta': response.json()['is_lta']
                }
        except Exception as e:  # pylint: disable=broad-except
            print(e)
            raise HTTPException(status_code=401, detail="Your authorization token is invalid")

    @classmethod
    async def temp_authorization(cls, token: str):
        if not token:
            raise HTTPException(status_code=401, detail="No authorization token provided")
        path = f"{main_path}/api/v1/csms/external_auth/temp_token_auth/{token}"
        async with httpx.AsyncClient() as client:
            response = await client.post(path, timeout=30)
        try:
            if response.status_code == 200 and response.json()['success']:
                # return response.json()['success']
                return {'ocpi-authorization': f"Token {response.json()['token']}"}
        except Exception as e:  # pylint: disable=broad-except
            print(e)
            raise HTTPException(status_code=401, detail="Your authorization token is invalid")
        # command_response = await crud.do(ModuleID.commands, RoleEnum.cpo, Action.send_command, command_data.dict(),
        #                                  command=command, auth_token=auth_token, version=VersionNumber.v_2_2_1)

    @classmethod
    async def do(cls,  # pylint:disable=too-many-statements, #pylint: disable=broad-except, # noqa: MC0011
                 module: enums.ModuleID, role: enums.RoleEnum, *args, **kwargs):
        # Versions - Should allow user to authenticate in both token A and token C
        if module == enums.ModuleID.versions:
            path = f"{main_path}/api/v1/csms/external_auth/auth-token-a-or-c/{kwargs['auth_token']}"
            async with httpx.AsyncClient() as client:
                response = await client.post(path, timeout=30)
            if response.status_code == 200 and response.json()['success']:
                # return response.json()['success']
                return {'ocpi-authorization': f"Token {response.json()['token']}"}
            return None

        # Credentials
        if module == enums.ModuleID.credentials_and_registration:
            path = f"{main_path}/api/v1/csms/external_auth/check-existing-temp-token/{kwargs['auth_token']}"
            async with httpx.AsyncClient() as client:
                response = await client.get(path, timeout=30)
            if response.status_code == 200:
                # Disable registration if already reigstered
                return response.json()
            # Allow registration since user hold a credentials_token_a
            return None

        if module == enums.ModuleID.commands:
            action = args[0]

            if action == Action.get_client_token:
                path = f"{main_path}/api/v1/csms/external_auth/get-client-token/{kwargs['auth_token']}"
                async with httpx.AsyncClient() as client:
                    response = await client.get(path, timeout=30)
                if response.status_code == 200:
                    return response.json()['external_service_token']
                # Allow registration since user hold a credentials_token_a
                raise HTTPException(status_code=401, detail="Your authorization token is invalid")

            if action == Action.send_command:
                data = args[1]
                main_auth_token = await cls.authorization(kwargs['auth_token'])
                if kwargs['command'] == CommandType.start_session:
                    # command_response_url = data['response_url']
                    token = data['token']
                    try:
                        id_tag = 'EMSP-' + token['country_code'] + '-' + token['party_id']
                    except Exception as e:  # pylint: disable=broad-except
                        print(e)
                    # id_tag = token['contract_id']
                    main_auth_token['ID_TAG'] = id_tag
                    command_response = CommandResponseType.not_supported
                    message = []

                    token_path = f"{main_path}/api/v1/csms/auth/emsp-token-token/" \
                                 f"{str(args[1]['token']['country_code'])}/" \
                                 f"{str(args[1]['token']['party_id'])}/" \
                                 f"{str(args[1]['token']['uid'])}"

                    # If error occur that is not caught change assume that command result is rejected
                    # Get CPO Token, check if Token exist, else cannot bill!
                    async with httpx.AsyncClient() as client:
                        try:
                            response = await client.get(token_path, timeout=30)
                            if response.status_code == 404:
                                command_response = CommandResponseType.rejected
                                message += ["CPO Token not found!"]
                                return {'result': command_response, 'message': message}
                        except Exception:  # pylint: disable=broad-except
                            command_response = CommandResponseType.rejected
                            message += ["CPO Token not found!"]
                            return {'result': command_response, 'message': message}
                    # Try to remote-start-charging
                    path = f"ocpi/cpo/{data['connector_id']}/remote-start"
                    async with httpx.AsyncClient() as client:

                        try:

                            # path = f"{main_path}/api/v1/csms/external_auth/get-client-token/{kwargs['auth_token']}"
                            data = {
                                'cpo_charging_session': {
                                    'auth_method': 'COMMAND',
                                    'country_code': str(args[1]['token']['country_code']),
                                    'party_id': str(args[1]['token']['party_id']),
                                    'id_tag': str(args[1]['token']['uid']),
                                    'ocpi_partner_location_id': str(args[1]['location_id']),
                                    'ocpi_partner_evse_uid': str(args[1]['evse_uid']),
                                    'ocpi_partner_connector_id': str(args[1]['connector_id']),
                                    'cdr_token': args[1]['token'],
                                    'status': SessionStatus.pending,
                                },
                            }
                            if args[1]['authorization_reference']:
                                data['cpo_charging_session']['authorization_reference'] = args[1][
                                    'authorization_reference']
                            response = await client.post(f'{MAIN_CHARGER_URL_PREFIX}/{path}', timeout=30,
                                                         headers=main_auth_token, json=data)
                            # Catch timeout exception for command result
                        except httpx.TimeoutException:
                            message += ["Request timeout"]
                    if response.status_code == 200:
                        command_response = CommandResponseType.accepted
                        message += ["Charging session successfully initiated"]

                    # Execution of the command failed at the CP
                    elif response.status_code == 203:
                        response_json = response.json()
                        print(response_json)
                        data = {
                            'cpo_charging_session': {
                                'auth_method': 'COMMAND',
                                'country_code': str(args[1]['token']['country_code']),
                                'party_id': str(args[1]['token']['party_id']),
                                'id_tag': str(args[1]['token']['uid']),
                                'ocpi_partner_location_id': str(args[1]['location_id']),
                                'ocpi_partner_evse_uid': str(args[1]['evse_uid']),
                                'ocpi_partner_connector_id': str(args[1]['connector_id']),
                                'cdr_token': args[1]['token'],
                                'status': SessionStatus.pending,
                            }
                        }
                        if args[1]['authorization_reference']:
                            data['cpo_charging_session']['authorization_reference'] = args[1][
                                'authorization_reference']
                        query_params = {
                            'connector_id': response_json['id'],
                            'charge_point_id': response_json['charge_point']['id'],
                            'serial_number': response_json['charge_point']['serial_number'],
                            'connector_number': response_json['number'],
                            'id_tag': id_tag,
                        }
                        path = 'ocpi/start-charging-ruby-ocpi'
                        url = f'{main_path}/api/v1/csms/{path}'
                        async with httpx.AsyncClient() as client:
                            response = await client.post(url, json=data, timeout=30, params=query_params)
                            if response.status_code == 200:
                                command_response = CommandResponseType.accepted
                                message += ["Charging session successfully initiated"]
                                return {'result': command_response, 'message': message}

                            command_response = CommandResponseType.rejected
                            message += ["Initiation of charging session failed"]
                            return {'result': command_response, 'message': message}

                        # command_response = CommandResponseType.rejected
                        # message += ["Initiation of charging session failed"]
                        # return {'result': command_response, 'message': message}

                    elif response.status_code in [400, 404]:
                        print("Error message: ")
                        print(response.content)
                        print(response.content.decode())
                        command_response = CommandResponseType.rejected
                        message += ["Initiation of charging session failed"]

                    # Command was rejected by CP
                    else:
                        command_response = CommandResponseType.rejected
                        message += ["Command has been rejected"]
                    return {'result': command_response, 'message': message}
                if kwargs['command'] == CommandType.stop_session:
                    command_response = CommandResponseType.not_supported
                    message = []

                    path = f"ocpi/charging/{data['session_id']}"
                    async with httpx.AsyncClient() as client:
                        response = await client.get(f'{MAIN_CHARGER_URL_PREFIX}/{path}',
                                                    headers=main_auth_token, timeout=30)
                    if response.status_code == 200:
                        response_json = response.json()
                        print(response_json)
                        id_tag = 'EMSP-' + response_json['country_code'] + '-' + response_json['party_id'] + '-USER'
                        main_auth_token['ID_TAG'] = id_tag

                        # Stop charging session
                        path = f"ocpi/cpo/remote-stop/{response.json()['transaction_id']}"
                        async with httpx.AsyncClient() as client:
                            try:
                                response = await client.post(f'{MAIN_CHARGER_URL_PREFIX}/{path}', timeout=30,
                                                             headers=main_auth_token)
                            # Catch timeout exception for command result
                            except httpx.TimeoutException:
                                message += ["Request timeout"]

                        if response.status_code == 200:
                            command_response = CommandResponseType.accepted
                            message += ["Charging session successfully initiated"]
                        elif response.status_code == 204:
                            command_response = CommandResponseType.rejected
                            message += ["Session is ended, but idling detected, please unplug the cable"]

                        elif response.status_code == 203:
                            response_json = response.json()
                            print(response_json)
                            query_params = {
                                'id_tag': id_tag,
                                'serial_number': response_json['cpc']['charge_point']['serial_number'],
                                'connector_id': response_json['cpc']['id'],
                                'charge_point_id': response_json['cpc']['charge_point']['id'],
                                'connector_number': response_json['cpc']['number'],

                            }
                            path = 'ocpi/stop-charging-ruby-ocpi'
                            url = f'{main_path}/api/v1/csms/{path}'
                            async with httpx.AsyncClient() as client:
                                response = await client.post(url, json=data, timeout=30, params=query_params)
                                if response.status_code == 200:
                                    command_response = CommandResponseType.accepted
                                    message += ["Charging session successfully initiated"]
                                    return {'result': command_response, 'message': message}

                                command_response = CommandResponseType.rejected
                                message += ["Initiation of charging session failed"]
                                return {'result': command_response, 'message': message}

                            # command_response = CommandResponseType.rejected
                            # message += ["Initiation of charging session failed"]
                            # return {'result': command_response, 'message': message}

                        # Execution of the command failed at the CP
                        elif response.status_code in [400, 404]:
                            command_response = CommandResponseType.rejected
                            message += ["Initiation of charging session failed"]

                        # Command was rejected by CP
                        else:
                            command_response = CommandResponseType.rejected
                            message += ["Command has been rejected"]

                    return {'result': command_response, 'message': message}
                return {'result': 'Failed', 'message': 'Failed'}

    # @classmethod
    # async def get(cls, module: enums.ModuleID, role: enums.RoleEnum, id, *args,
    #               **kwargs):  # pylint: disable=too-many-return-statements
    @classmethod
    async def get(cls,  # pylint:disable=too-many-statements, #pylint: disable=broad-except, # noqa: MC0011
                  module: enums.ModuleID, role: enums.RoleEnum, *args, **kwargs):
        if module == enums.ModuleID.tariffs:
            main_auth_token = await cls.authorization(kwargs['auth_token'])
            id = args[0]
            path = f'ocpi/tariff/{id}'
            async with httpx.AsyncClient() as client:
                response = await client.get(f'{MAIN_CHARGER_URL_PREFIX}/{path}',
                                            headers=main_auth_token, timeout=30)
            if response.status_code == 200:
                return response.json()
            return False
        # Locations
        if module == enums.ModuleID.locations:
            if role == enums.RoleEnum.emsp:
                main_auth_token = await cls.charger_authorization(kwargs['auth_token'])
                id = args[0]
                path = f'ocpi/location/{id}'
                async with httpx.AsyncClient() as client:
                    response = await client.get(f'{MAIN_CHARGER_URL_PREFIX}/{path}',
                                                headers=main_auth_token, timeout=30)
                if response.status_code == 200:
                    modified_json = response.json()
                    modified_json['is_publish_all'] = bool(int(main_auth_token['is_publish_all']))
                    return modified_json
                return False
            if role == enums.RoleEnum.cpo:
                main_auth_token = await cls.authorization(kwargs['auth_token'])
                id = args[0]
                path = f'ocpi/location/{id}'
                async with httpx.AsyncClient() as client:
                    response = await client.get(f'{MAIN_CHARGER_URL_PREFIX}/{path}',
                                                headers=main_auth_token, timeout=30)
                if response.status_code == 200:
                    modified_json = response.json()
                    modified_json['is_publish_all'] = bool(int(main_auth_token['is_publish_all']))
                    modified_json['receiver_party_id'] = main_auth_token['party_id']
                    modified_json['receiver_country_code'] = main_auth_token['country_code']
                    return modified_json
                return False

        # Tokens
        if module == enums.ModuleID.tokens:
            if role == enums.RoleEnum.emsp:
                main_auth_token = await cls.authorization(kwargs['auth_token'])
                path = f'{main_path}/api/v1/csms/id_tag/id-tag/{id}'
                async with httpx.AsyncClient() as client:
                    response = await client.get(path, headers=main_auth_token, timeout=30)
                if response.status_code == 200:
                    return response.json()
            else:
                id = args[0]
                main_auth_token = await cls.authorization(kwargs['auth_token'])
                # path = f'{main_path}/api/v1/csms/external_auth/cpo/token/{str(id)}'
                path = f"{main_path}/api/v1/csms/auth/emsp-token-token/" \
                       f"{str(kwargs['country_code'])}/" \
                       f"{str(kwargs['party_id'])}/" \
                       f"{str(id)}"
                async with httpx.AsyncClient() as client:
                    response = await client.get(path, timeout=30)

                if response.status_code == 200:
                    return response.json()

                return False

        # Credentials
        if module == enums.ModuleID.credentials_and_registration:
            path = f"{main_path}/api/v1/csms/external_auth/external-token/{id}"
            async with httpx.AsyncClient() as client:
                response = await client.get(path, timeout=30)
            if response.status_code == 200:
                return response.json()
            if response.status_code == 400:
                return None

        if module == enums.ModuleID.commands:
            main_auth_token = await cls.authorization(kwargs['auth_token'])

            command = kwargs['command']
            if command == CommandType.start_session:
                operation_request = 'RemoteStartTransaction'
                # connector_id = kwargs['command_data'].connector_id
                connector_id = kwargs['command_data'].evse_uid
                path = f'ocpi/command/{connector_id}'

                async with httpx.AsyncClient() as client:
                    response = await client.get(f'{MAIN_CHARGER_URL_PREFIX}/{path}/{operation_request}',
                                                headers=main_auth_token, timeout=30)
                if response.status_code == 200:
                    return response.json()

            elif command == CommandType.stop_session:
                operation_request = 'RemoteStopTransaction'
                session_id = kwargs['command_data'].session_id

                path = f'ocpi/command/stop/{session_id}'

                async with httpx.AsyncClient() as client:
                    response = await client.get(f'{MAIN_CHARGER_URL_PREFIX}/{path}/{operation_request}',
                                                headers=main_auth_token, timeout=30)
                if response.status_code == 200:
                    return response.json()
            else:
                print(response.content)

            return False

        if module == enums.ModuleID.sessions:
            main_auth_token = await cls.authorization(kwargs['auth_token'])
            if role == enums.RoleEnum.emsp:
                args_element = args
                session_id = str(args_element[0])
                country_code = kwargs.get('country_code')
                party_id = kwargs.get('party_id')
                path = f'ocpi/emsp/session/{country_code}/{party_id}/{session_id}'

                async with httpx.AsyncClient() as client:
                    response = await client.get(f'{MAIN_CHARGER_URL_PREFIX}/{path}', headers=main_auth_token,
                                                timeout=30)
                if response.status_code == 200:
                    return response.json()
                return False
            return False
        raise ValidationError(module, id)

    @classmethod
    async def list(cls, module: enums.ModuleID, role: enums.RoleEnum, filters, *args, **kwargs):  # noqa: MC0001
        main_auth_token = await cls.authorization(kwargs['auth_token'])
        # Locations
        if module == enums.ModuleID.locations:
            path = 'ocpi/locations'
            size = filters['limit']
            page = math.floor(filters['offset'] / size) + 1
            params = {
                'size': size,
                'page': page,
            }
            if filters['date_to']:
                params['date_to'] = filters['date_to']
            if filters['date_from']:
                params['date_from'] = filters['date_from']
            async with httpx.AsyncClient() as client:
                response = await client.get(f'{MAIN_CHARGER_URL_PREFIX}/{path}', params=params,
                                            headers=main_auth_token, timeout=30)
            if response.status_code == 200:
                is_last_page = False
                if response.json()['total'] <= filters['offset'] + size:
                    is_last_page = True

                modified_items = []
                for item in response.json()['items']:
                    item['is_publish_all'] = bool(int(main_auth_token['is_publish_all']))
                    item['receiver_party_id'] = main_auth_token['party_id']
                    item['receiver_country_code'] = main_auth_token['country_code']
                    modified_items.append(item)  # Store the modified item

                return modified_items, response.json()['total'], is_last_page

        # Tariffs
        if module == enums.ModuleID.tariffs:
            path = 'ocpi/tariffs'
            size = filters['limit']
            page = math.floor(filters['offset'] / size) + 1
            params = {
                'size': size,
                'page': page,
            }
            if filters['date_to']:
                params['date_to'] = filters['date_to']
            if filters['date_from']:
                params['date_from'] = filters['date_from']
            async with httpx.AsyncClient() as client:
                tariff_standard = main_auth_token['standardized_tariff']
                response = await client.get(f'{MAIN_CHARGER_URL_PREFIX}/{path}', params=params,
                                            headers=main_auth_token, timeout=30)
            if response.status_code == 200:
                modified_items = [{'tariff_standard': tariff_standard, **item} for item in response.json()['items']]
                is_last_page = False
                if response.json()['total'] <= filters['offset'] + size:
                    is_last_page = True
                return modified_items, response.json()['total'], is_last_page

        # Sessions
        if module == enums.ModuleID.sessions:
            path = 'ocpi/cpo/sessions'
            size = filters['limit']
            page = math.floor(filters['offset'] / size) + 1
            params = {
                'size': size,
                'page': page,
            }
            if filters['date_to']:
                params['date_to'] = filters['date_to']
            if filters['date_from']:
                params['date_from'] = filters['date_from']
            async with httpx.AsyncClient() as client:
                response = await client.get(f'{MAIN_CHARGER_URL_PREFIX}/{path}', params=params,
                                            headers=main_auth_token, timeout=30)
            if response.status_code == 200:
                is_last_page = False
                if response.json()['total'] <= filters['offset'] + size:
                    is_last_page = True
                return response.json()['items'], response.json()['total'], is_last_page

        # CDRs
        if module == enums.ModuleID.cdrs:
            headers = {
                'party_id': main_auth_token['party_id'],
                'country_code': main_auth_token['country_code'],
            }
            path = 'ocpi/cdr'
            size = filters['limit']
            page = math.floor(filters['offset'] / size) + 1
            # date_to = filters['date_to']
            params = {
                'size': size,
                'page': page,
            }
            if filters['date_to']:
                params['date_to'] = filters['date_to']
            if filters['date_from']:
                params['date_from'] = filters['date_from']
            async with httpx.AsyncClient() as client:
                tariff_standard = main_auth_token['standardized_tariff']
                response = await client.get(f'{main_path}/api/v1/csms/{path}', params=params, timeout=30,
                                            headers=headers)
                print(response.content)
            if response.status_code == 200:
                modified_items = [{'tariff_standard': tariff_standard, **item} for item in response.json()['items']]
                is_last_page = False
                if response.json()['total'] <= filters['offset'] + size:
                    is_last_page = True
                return modified_items, response.json()['total'], is_last_page

        raise ValidationError(module, filters)

    @classmethod
    async def create(cls, module: enums.ModuleID, role: enums.RoleEnum, data: dict, auth_token, **kwargs):  # noqa
        if module == enums.ModuleID.tariffs:
            main_auth_token = await cls.authorization(auth_token)
            async with httpx.AsyncClient() as client:
                path = 'ocpi/tariff'
                response = await client.post(f'{MAIN_CHARGER_URL_PREFIX}/{path}', timeout=30, json=data,
                                             headers=main_auth_token)
                if response.status_code == 200:
                    return ocpi_sender_receiver_adapter(response.json(), role, module)

                return None
        # Locations
        if module == enums.ModuleID.locations:
            if role == enums.RoleEnum.emsp:
                charger_header = await cls.charger_authorization(auth_token)
                async with httpx.AsyncClient() as client:
                    data = remove_none(data)
                    path = 'ocpi/location'
                    response = await client.put(f'{MAIN_CHARGER_URL_PREFIX}/{path}', timeout=30,
                                                params={'ocpi_location_id': str(kwargs['location_id'])},
                                                json=data, headers=charger_header)
                    if response.status_code == 200:
                        return ocpi_sender_receiver_adapter(response.json(), role, module)
                    return None
        # Tokens
        if module == enums.ModuleID.tokens:
            if role == enums.RoleEnum.emsp:
                main_auth_token = await cls.authorization(kwargs['auth_token'])
                auth_token_data = decode_auth_token_from_headers(main_auth_token)
                membership_id = auth_token_data.get('membership_id')
                path = f"{main_path}/api/v1/csms/id_tag/"
                async with httpx.AsyncClient() as client:
                    data = dict(data)
                    # Make energy contract instance encodable by json
                    for key, value in data.items():
                        if isinstance(value, EnergyContract):
                            data[key] = dict(value)
                    token_data = {
                        "id_tag": kwargs['token_uid'],
                        "name": 'Token OCPI',
                        "type": schema.IDTagType.rfid,
                        "expiration": str(datetime.datetime.now() + datetime.timedelta(days=365)),
                        "is_active": True,
                        "description": 'OCPI token module',
                        "member_id": membership_id,
                        "is_external": True,
                        "meta": data
                    }
                    response = await client.post(path, json=token_data, headers=main_auth_token, timeout=30)
                    if response.status_code == 201:
                        return response.json()
            else:
                # main_auth_token = await cls.authorization(auth_token)
                path = f"{main_path}/api/v1/csms/external_auth/cpo/token"
                # data = data
                data['partner_ocpi_cpo_token_id'] = data['uid']

                async with httpx.AsyncClient() as client:
                    response = await client.post(path, json=data, timeout=30)

                return response.json()
        # Commands
        if module == enums.ModuleID.commands:
            main_auth_token = await cls.authorization(kwargs['auth_token'])
            command_response = CommandResponseType.not_supported
            message = []

            # Start charging session remotely
            if kwargs['command'] == CommandType.start_session:
                # If error occur that is not caught change assume that command result is rejected
                path = f"connector/cpo/{data['connector_id']}remote-start"
                async with httpx.AsyncClient() as client:
                    try:
                        response = await client.post(f'{MAIN_CHARGER_URL_PREFIX}/{path}', timeout=30,
                                                     headers=main_auth_token)
                    # Catch timeout exception for command result
                    except httpx.TimeoutException:
                        message += ["Request timeout"]

                if response.status_code == 200:
                    command_response = CommandResponseType.accepted
                    message += ["Charging session successfully initiated"]

                # Execution of the command failed at the CP
                elif response.status_code in [400, 404]:

                    try:
                        response_json = response.json()
                        if response_json.get('error') == 'Charge Point is not on Python':
                            path = 'ocpi/start-charging-ruby-ocpi'
                            response = await client.post(f'{main_path}/api/v1/csms/{path}', timeout=30)

                            print('cp not on python')
                            call_back = data['response_url']
                            client_auth_token = 'abc'  # nosec

                            command_result = Adapter.command_result_adapter(True, VersionNumber.v_2_2_1)

                            async with httpx.AsyncClient() as client:
                                authorization_token = f'Token {client_auth_token}'
                                await client.post(call_back, json=command_result.dict(),
                                                  headers={'authorization': authorization_token})
                            command_response = CommandResponseType.accepted
                            message += ["Charging session successfully initiated"]
                        else:
                            command_response = CommandResponseType.rejected
                            message += ["Initiation of charging session failed"]
                    except Exception as e:  # pylint: disable=broad-except
                        print(e)
                        command_response = CommandResponseType.rejected
                        message += ["Initiation of charging session failed"]

                # Command was rejected by CP
                else:
                    command_response = CommandResponseType.rejected
                    message += ["Command has been rejected"]

            # Stop charging session remotely
            if kwargs['command'] == CommandType.stop_session:
                # Get charging session transaction id based on session id
                path = f"charging/{data['session_id']}"
                async with httpx.AsyncClient() as client:
                    response = await client.post(f'{MAIN_CHARGER_URL_PREFIX}/{path}',
                                                 headers=main_auth_token, timeout=30)
                if response.status_code == 200:
                    response_json = response.json()
                    id_tag = 'EMSP-' + response_json['country_code'] + '-' + response_json['party_id'] + '-USER'
                    # Stop charging session
                    path = f"/ocpi/cpo/remote-stop/{response.json()['transaction_id']}"
                    async with httpx.AsyncClient() as client:
                        try:
                            response = await client.post(f'{MAIN_CHARGER_URL_PREFIX}/{path}', timeout=30,
                                                         headers=main_auth_token)
                        # Catch timeout exception for command result
                        except httpx.TimeoutException:
                            message += ["Request timeout"]

                    if response.status_code == 200:
                        command_response = CommandResponseType.accepted
                        message += ["Charging session successfully initiated"]

                    # Execution of the command failed at the CP
                    elif response.status_code in [400, 404]:
                        command_response = CommandResponseType.rejected
                        message += ["Initiation of charging session failed"]

                    # Command was rejected by CP
                    else:
                        command_response = CommandResponseType.rejected
                        message += ["Command has been rejected"]

            # Reserve connector
            if kwargs['command'] == CommandType.reserve_now:
                # Assume connector ID as EVSE ID
                if 'evse_uid' in data:
                    connector_id = data['evse_uid']
                else:
                    # Get location if EVSE is not provided
                    location_crud = await cls.get(module.locations, enums.RoleEnum.cop, data['location_id'])
                    location = Adapter.location_adapter(location_crud, VersionNumber.v_2_2_1)
                    connector_id = location.evses[0].connectors[0]['id']

                path = f"connector/{connector_id}/reserve"
                async with httpx.AsyncClient() as client:
                    try:
                        body = {'expiry_date': data['expiry_date'], 'reservation_id': data['reservation_id']}
                        response = await client.post(f'{MAIN_CHARGER_URL_PREFIX}/{path}', json=body,
                                                     headers=main_auth_token, timeout=30)
                    # Catch timeout exception for command result
                    except httpx.TimeoutException:
                        message += ["Request timeout"]

                if response.status_code == 200:
                    command_response = CommandResponseType.accepted
                    message += ["Connector has been successfully reserved"]

                # Execution of the command failed at the CP
                elif response.status_code in [400, 404]:
                    command_response = CommandResponseType.rejected
                    message += ["Reservation of connector failed"]

                # Command was rejected by CP
                else:
                    command_response = CommandResponseType.rejected
                    message += ["Command has been rejected"]

            # Cancel reservation
            if kwargs['command'] == CommandType.cancel_reservation:
                # If error occur that is not caught change assume that command result is rejected
                async with httpx.AsyncClient() as client:
                    try:
                        path = f'connector/get_reservation/{data["reservation_id"]}'
                        response = await client.post(f'{MAIN_CHARGER_URL_PREFIX}/{path}',
                                                     headers=main_auth_token, timeout=30)
                        path = f"connector/cancel_reservation/{response.json()['id']}"
                        response = await client.post(f'{MAIN_CHARGER_URL_PREFIX}/{path}',
                                                     headers=main_auth_token, timeout=30)
                    # Catch timeout exception for command result
                    except httpx.TimeoutException:
                        message += ["Request timeout"]

                if response.status_code == 200:
                    command_response = CommandResponseType.accepted
                    message += ["Reservation has been successfully cancelled"]

                # Execution of the command failed at the CP
                elif response.status_code in [400, 404]:
                    command_response = CommandResponseType.rejected
                    message += ["Cancellation of reservation failed"]

                # Command was rejected by CP
                else:
                    command_response = CommandResponseType.rejected
                    message += ["Command has been rejected"]

            # Return command response
            return {'result': command_response, 'message': message}

        if module == enums.ModuleID.tokens:
            print('yes')

        # Credentials
        if module == enums.ModuleID.credentials_and_registration:
            # here
            main_auth_token = await cls.temp_authorization(auth_token)

            token_data = schema.ExternalTokenUpdate(
                version=VersionNumber.v_2_2_1,
                endpoints=data['endpoints'],
                external_service_token=data['credentials']['token'],
                url=data['credentials']['url'],
            ).dict()

            # todo: change it to take in url
            path = f"{main_path}/api/v1/csms/external_auth/generate-new-auth-token/{auth_token}"
            async with httpx.AsyncClient() as client:
                response = await client.post(path, json=token_data, headers=main_auth_token, timeout=30)
                if response.status_code == 200:
                    # Update CPO's info
                    path = f"{main_path}/api/v1/csms/external_auth/cpo/{data['credentials']['token']}"

                    operator_data = emsp_operator_adapter(data['credentials']['roles'][0])
                    async with httpx.AsyncClient() as client:
                        external_org_response = await client.post(path, json=operator_data, headers=main_auth_token,
                                                                  timeout=30)
                        if external_org_response.status_code == 200:
                            return response.json()
                raise AuthorizationOCPIError()

        if module == enums.ModuleID.sessions:
            data_json = json.loads(json.dumps(data))

            token = data_json['cdr_token']
            path = f"{main_path}/api/v1/csms/external_auth/id-tag-token/{token['uid']}"
            async with httpx.AsyncClient() as client:
                id_tag = await client.get(path, timeout=30)

            id_tag = id_tag.json()

            user_id_tag = id_tag['user_id_tag']
            data_json['partner_session_uid'] = data_json.pop('id')
            data_json['ocpi_partner_connector_id'] = data_json.pop('connector_id')
            data_json['ocpi_partner_evse_uid'] = data_json.pop('evse_uid')
            data_json['ocpi_partner_location_id'] = data_json.pop('location_id')
            data_json['id_tag'] = user_id_tag
            # data_json['cdr_token'] = {}

            path = f"ocpi/emsp/session"
            async with httpx.AsyncClient() as client:
                charging_response = await client.post(f'{MAIN_CHARGER_URL_PREFIX}/{path}', timeout=30,
                                                      json=data_json)
                return charging_response

        if module == enums.ModuleID.cdrs:
            if role == enums.RoleEnum.emsp:
                path = f"{main_path}/api/v1/csms/external_auth/cpo/cdr"
                # data = data
                data['partner_ocpi_cpo_cdr_id'] = data['id']

                async with httpx.AsyncClient() as client:
                    response = await client.post(path, json=data, timeout=30)
                return response.json()

    @classmethod
    async def update(cls, module: enums.ModuleID, role: enums.RoleEnum, data, id, *args, **kwargs):  # noqa: MC0001
        main_auth_token = await cls.authorization(kwargs['auth_token'])
        # Tokens
        if module == enums.ModuleID.tokens:
            if role == enums.RoleEnum.cpo:
                main_auth_token = await cls.authorization(kwargs['auth_token'])
                # path = f'{main_path}/api/v1/csms/external_auth/cpo/token/{id}'
                path = f"{main_path}/api/v1/csms/auth/emsp-token-token/" \
                       f"{str(kwargs['country_code'])}/" \
                       f"{str(kwargs['party_id'])}/" \
                       f"{str(id)}"

                async with httpx.AsyncClient() as client:
                    response = await client.patch(path, timeout=30, json=data)
                if response.status_code == 200:
                    return response.json()
            else:
                main_auth_token = await cls.authorization(kwargs['auth_token'])
                path = f'{main_path}/api/v1/csms/id_tag/id-tag/{id}'

                # Get previous id tag in order to update only detailed fields
                async with httpx.AsyncClient() as client:
                    response = await client.get(path, timeout=30, headers=main_auth_token)
                if response.status_code == 200:
                    data = data.dict(exclude_unset=True, exclude_defaults=True)

                    # Make energy contract instance encodable by json
                    for key, value in data.items():
                        if isinstance(value, EnergyContract):
                            data[key] = dict(value)
                    id_tag_token_data = response.json()['meta']

                    # Update only fields that are in request
                    for key in data.keys():
                        if key in id_tag_token_data:
                            del id_tag_token_data[key]
                    meta_data_update = {'meta': dict(id_tag_token_data, **data)}

                    # Update certain id tag
                    path = f'{main_path}/api/v1/csms/id_tag/{response.json()["id"]}'
                    async with httpx.AsyncClient() as client:
                        response = await client.patch(path, json=meta_data_update,
                                                      headers=main_auth_token, timeout=30)
                    if response.status_code == 200:
                        return response.json()

        # Sessions
        if module == enums.ModuleID.sessions:
            country_code = kwargs.get('country_code')
            party_id = kwargs.get('party_id')
            if role == enums.RoleEnum.emsp:
                session_id = data.get('id')
                path = f'ocpi/emsp/session/{country_code}/{party_id}/{session_id}'

                async with httpx.AsyncClient() as client:
                    response = await client.put(f'{MAIN_CHARGER_URL_PREFIX}/{path}', headers=main_auth_token,
                                                timeout=30, json=data)

                    return response.json()
            else:
                main_auth_token = await cls.authorization(kwargs['auth_token'])
                # Get charging session in order to retrieve associated connector ID
                path = f"charging/{id}"
                async with httpx.AsyncClient() as client:
                    charging_response = await client.get(f'{MAIN_CHARGER_URL_PREFIX}/{path}',
                                                         headers=main_auth_token, timeout=30)
                if charging_response.status_code == 200:
                    connector_id = charging_response.json()['charge_point_connector']['id']
                    path = f"connector/{connector_id}/ocpp"
                    ocpp_message_data = {
                        'reason': 'Start OCPP Operation',
                        'body': {
                            'message_type': 'Set charging preferences',
                            'parameters': data
                        }
                    }
                    async with httpx.AsyncClient() as client:
                        response = await client.post(f'{MAIN_CHARGER_URL_PREFIX}/{path}', json=ocpp_message_data,
                                                     headers=main_auth_token, timeout=30)
                        if response.status_code == 200:
                            return charging_response.json()

        # Credentials
        if module == enums.ModuleID.credentials_and_registration:
            main_auth_token = await cls.authorization(kwargs['auth_token'])
            path = f"{main_path}/api/v1/csms/external_auth/external-token/{data['credentials']['token']}"
            token_data = schema.ExternalTokenUpdate(
                version=VersionNumber.v_2_2_1,
                endpoints=data['endpoints'],
                external_service_token=data['credentials']['token'],
                url=data['credentials']['url']
            ).dict()

            async with httpx.AsyncClient() as client:
                response = await client.post(path, json=token_data, headers=main_auth_token, timeout=30)

        # Locations
        if module == enums.ModuleID.locations:
            if role == enums.RoleEnum.emsp:
                main_auth_token = await cls.charger_authorization(kwargs['auth_token'])
                data = remove_none(data)
                path = f'{MAIN_CHARGER_URL_PREFIX}/ocpi/location'
                async with httpx.AsyncClient() as client:
                    response = await client.put(path, json=data, timeout=30, params={'ocpi_location_id': str(id)},
                                                headers=main_auth_token)

                    if response.status_code == 200:
                        return ocpi_sender_receiver_adapter(response.json(), role, module)
                    return False

        # Tariffs
        if module == enums.ModuleID.tariffs:
            main_auth_token = await cls.authorization(kwargs['auth_token'])
            data = remove_none(data)
            path = f'{MAIN_CHARGER_URL_PREFIX}/ocpi/tariff'

            # path = f"{main_path}/api/v1/csms/location/{data['']}"
            async with httpx.AsyncClient() as client:
                response = await client.put(path, json=data, timeout=30, params={'ocpi_tariff_id': str(id)},
                                            headers=main_auth_token)

                if response.status_code == 200:
                    return ocpi_sender_receiver_adapter(response.json(), role, module)
                return False

        if module == enums.ModuleID.commands and role == enums.RoleEnum.emsp:
            path = f"ocpi/command/{id}"

            async with httpx.AsyncClient() as client:
                result_data = {
                    'result_data': data
                }
                response = await client.put(f'{MAIN_CHARGER_URL_PREFIX}/{path}', timeout=30,
                                            json=result_data)
                return response


def ocpi_sender_receiver_adapter(data, role, module):
    adapted_data = {
        'data': data,
        'role': role,
        'module': module
    }
    return adapted_data


def emsp_operator_adapter(data):
    adapted_data = {
        'party_id': data['party_id'],
        'country_code': data['country_code'],
        'description': str(data['business_details']['name'] + ' ' + data['role'])

    }
    if 'logo' in data['business_details']:
        adapted_data['logo'] = data['business_details']['logo']
    if 'website' in data['business_details']:
        adapted_data['website'] = data['business_details']['website']
    return adapted_data


def format_latitude(value):
    try:
        val = float(value)
        s = str(val)
        if "e" in s or "E" in s:
            s = f"{val:.15f}".rstrip("0").rstrip(".")

        int_part, dec_part = s.split(".")
        if len(dec_part) < 5:
            dec_part = dec_part.ljust(5, "0")
        elif len(dec_part) > 7:
            dec_part = dec_part[:7]

        result = f"{int_part}.{dec_part}"
        return result[:10]
    except Exception:  # pylint: disable=broad-except
        return str(value)[:10]


def format_longitude(value):
    try:
        val = float(value)
        s = str(val)
        if "e" in s or "E" in s:
            s = f"{val:.15f}".rstrip("0").rstrip(".")

        int_part, dec_part = s.split(".")
        if len(dec_part) < 5:
            dec_part = dec_part.ljust(5, "0")
        elif len(dec_part) > 7:
            dec_part = dec_part[:7]

        result = f"{int_part}.{dec_part}"
        return result[:11]
    except Exception:  # pylint: disable=broad-except
        return str(value)[:11]


def emsp_single_location_adapter(data):  # noqa: MC0001
    try:
        data['evses'] = data['ocpi_evses']
    except KeyError:
        data['evses'] = []
    evses = []
    for evse in data['evses']:  # pylint: disable=too-many-nested-blocks
        connectors = []
        for connector in evse['connectors']:
            connector_type = connector['connector_type']
            if connector_type['kind'] == 'AC':
                connector_type['kind'] = PowerType.ac_1_phase
            else:
                connector_type['kind'] = PowerType.dc
            connector_obj = Connector(
                id=connector['ocpi_partner_connector_id'],
                standard=connector_type['name'],
                format=connector_type['format'],
                power_type=connector_type['kind'],
                max_voltage=connector['max_voltage'],
                max_amperage=connector['max_amperage'],
                last_updated=connector['updated_at'] if connector['updated_at'] else connector[
                    'created_at'],
            )
            connector_dict = connector_obj.dict()
            for x in connector_dict.keys():
                if connector_dict[x] == {} or connector_dict[x] is None or connector_dict[x] == []:
                    if connector[x] != {} and connector[x] is not None and connector[x] != []:
                        try:
                            setattr(connector_obj, x, connector[x])
                        except (KeyError, AttributeError):
                            pass
            connectors.append(connector_obj)

        coordinates = None
        if 'latitude' in evse and 'longitude' in evse:
            if evse['latitude'] is not None and evse['longitude'] is not None:
                latitude = format_latitude(str(evse['latitude']))
                longitude = format_longitude(str(evse['longitude']))
                coordinates = GeoLocation(latitude=latitude, longitude=longitude)

        last_updated = evse.get('last_updated') or evse.get('updated_at') or evse.get('created_at')
        evse_obj = EVSE(
            uid=evse['ocpi_partner_evse_uid'],
            evse_id=evse['ocpi_partner_evse_id'],
            status=evse['status'].upper(),
            last_updated=last_updated,
            coordinates=coordinates,
            connectors=connectors
        )
        evse_dict = evse_obj.dict()
        for x in evse_dict.keys():
            if evse_dict[x] == {} or evse_dict[x] is None or evse_dict[x] == []:
                try:
                    setattr(evse_obj, x, evse[x])
                    # evse_obj = evse[x]
                except (KeyError, AttributeError):
                    pass
        evses.append(evse_obj)

    operator_obj = None
    sub_operator_obj = None
    owner_obj = None
    try:
        if data['ocpi_partner_operator']:
            operator_obj = BusinessDetails(**data['ocpi_partner_operator'])
    except Exception as e:  # pylint: disable=broad-except
        print("Exception occurred parsing partner operator, %s", str(e))

    try:
        if data['ocpi_partner_sub_operator']:
            sub_operator_obj = BusinessDetails(**data['ocpi_partner_sub_operator'])
    except Exception as e:  # pylint: disable=broad-except
        print("Exception occurred parsing sub-partner operator, %s", str(e))

    try:
        if data['ocpi_partner_owner']:
            owner_obj = BusinessDetails(**data['ocpi_partner_owner'])
    except Exception as e:  # pylint: disable=broad-except
        print("Exception occurred parsing owner operator, %s", str(e))

    location = Location(
        country_code=data['country_code'],
        party_id=data['party_id'],
        id=data['ocpi_partner_location_id'],
        publish=data['publish'],
        name=data['name'],
        address=data['address'][:45],
        city=data['city'],
        state=data['state'],
        country=data['country'],
        coordinates=GeoLocation(
            latitude=format_latitude(str(data['latitude'])),
            longitude=format_longitude(str(data['longitude']))
        ),
        evses=evses,
        images=data['images'] if data['images'] else [],
        time_zone=data['time_zone'],
        last_updated=data['updated_at'] if data['updated_at'] else data['created_at'],
    )
    if operator_obj is not None:
        location.operator = operator_obj
    if sub_operator_obj is not None:
        location.suboperator = sub_operator_obj
    if owner_obj is not None:
        location.owner = owner_obj
    return location


def emsp_single_tariff_adapter(data):
    tariff_obj = Tariff(
        id=data['ocpi_partner_tariff_id'],
        party_id=data['party_id'],
        country_code=data['country_code'],
        currency=data['currency'],
        tariff_alt_text=data['tariff_alt_text'],
        elements=data['elements'],
        last_updated=data['last_updated'],
    )
    tariff_dict = tariff_obj.dict()
    for x in tariff_dict.keys():
        if tariff_dict[x] == {} or tariff_dict[x] is None or tariff_dict[x] == []:
            if data[x] != {} and data[x] is not None and data[x] != []:
                try:
                    setattr(tariff_obj, x, data[x])
                except (KeyError, AttributeError):
                    pass

    return tariff_obj


def native_session_to_ocpi_adapter(data):
    if data.get('status'):  # pylint:disable=no-else-return
        status = data.get('status')
        hogging_status = data.get('hogging_status', None)

        if hogging_status and hogging_status == 'Started':
            status = 'ACTIVE'
        return status
    else:
        status = data['cpo_cs']['status']
        hogging_status = data['cpo_cs'].get('hogging_status', None)

        if hogging_status and hogging_status == 'Started':
            status = 'ACTIVE'

        return status


def native_connector_hogging_fee_to_tariff(hogging_tariff: list[dict], data) -> list[TariffElement]:
    elements = []
    if hogging_tariff is not None:
        for tariff in hogging_tariff:
            hogging_price = tariff['price']

            hogging_vat_percentage = 0.00
            if 'price_after_vat' in tariff:
                hogging_vat_percentage = round((tariff['price_after_vat'] - hogging_price) / hogging_price * 100, 0)

            if int(data['tariff_standard']):
                hogging_price = round((3600 / tariff['step_size']) * tariff['price_after_vat'], 4)

            hogging_step_size = tariff['step_size']
            start_time = tariff['start_time']
            end_time = tariff['end_time']

            tariff_restrictions = None
            grace_period_seconds = tariff.get('grace_period_seconds')

            if grace_period_seconds:
                tariff_restrictions = TariffRestrictions(min_duration=grace_period_seconds)

            elements.append(
                TariffElement(
                    price_components=[
                        PriceComponent(
                            type=TariffDimensionType.parking_time,  # Assuming hogging tariff is based on time
                            price=hogging_price,
                            vat=hogging_vat_percentage,
                            step_size=hogging_step_size,
                            restrictions={"start_time": start_time, "end_time": end_time},
                        )
                    ],
                    restrictions=tariff_restrictions  # Only added if grace period exists
                )
            )
    return elements


def native_connector_tariff_to_ocpi_adapter(data):  # noqa: MC0001
    type_adapter = {
        'kWh': TariffDimensionType.energy,
        'Time': TariffDimensionType.time,
    }

    if data["updated_at"] and data["updated_at"] is not None:
        last_updated = data["updated_at"]
    else:
        last_updated = data["created_at"]

    try:
        connector_last_updated = str(datetime.datetime.strptime(last_updated, '%Y-%m-%dT%H:%M:%S.%f%z'))
    except ValueError:
        try:
            connector_last_updated = datetime.datetime.strptime(last_updated, '%Y-%m-%dT%H:%M:%S.%f').astimezone(
                pytz.UTC).strftime('%Y-%m-%dT%H:%M:%SZ')
        except ValueError:
            connector_last_updated = str(datetime.datetime.strptime(last_updated, "%Y-%m-%dT%H:%M:%S%z"))
    try:
        formatted_connector_last_updated = datetime.datetime. \
            strptime(connector_last_updated, '%Y-%m-%d %H:%M:%S.%f%z').strftime('%Y-%m-%dT%H:%M:%SZ')

    except ValueError:
        formatted_connector_last_updated = datetime.datetime. \
            strptime(connector_last_updated, '%Y-%m-%d %H:%M:%S%z').strftime('%Y-%m-%dT%H:%M:%SZ')
    vat_percentage = 0.00
    # IF tariff_standard = True, follow OCPI standard. else follow standard with old partners
    if int(data['tariff_standard']) and data['billing_type'] == 'Time':
        price = data['billing_unit_fee']
        if data.get('billing_unit_fee_after_vat'):
            vat_percentage = round((data.get('billing_unit_fee_after_vat') - price) / price * 100, 0)

        # to convert into price per hour
        price = round(((60 / data['billing_cycle']) * data['billing_unit_fee']), 4)

    else:
        price = data['billing_unit_fee']
        if data.get('billing_unit_fee_after_vat'):
            vat_percentage = round((data.get('billing_unit_fee_after_vat') - price) / price * 100, 0)

    if data['billing_type'] == 'kWh':
        step_size = data['billing_cycle']
    if data['billing_type'] == 'Time':
        # map from minute to seconds
        step_size = data['billing_cycle'] * 60
    elements_list = [
        TariffElement(
            price_components=[
                PriceComponent(
                    type=type_adapter[data['billing_type']],
                    price=price,
                    vat=vat_percentage,
                    step_size=step_size,
                )
            ]
        )
    ]

    charge_point_data = data.get('charge_point') or data.get('charge_points')

    if (charge_point_data and 'capabilities' in charge_point_data and
            'HOGGING_CAPABLE' in charge_point_data['capabilities']):
        if 'hogging_tariff' in data:
            hogging_elements = native_connector_hogging_fee_to_tariff(data['hogging_tariff'], data)
            elements_list.extend(hogging_elements)

    tariff_obj = Tariff(
        country_code=OCPI_COUNTRY_CODE,
        party_id=OCPI_PARTY_ID,
        id=data['id'],
        currency=data['billing_currency'],
        elements=elements_list,
        last_updated=formatted_connector_last_updated
    )
    return tariff_obj


def native_operator_is_private_to_ocpi_adaptor(publish_location, data):
    try:
        if data.get('operator'):
            if data.get('operator').get('is_private') is not None:
                is_private_operator = data.get('operator').get('is_private')
                if is_private_operator:
                    publish_location = False
                # display_as_private = data.get('operator').get('display_as_private')
    except Exception as e:  # pylint: disable=broad-except
        # print(f"An error occurred: {e}")
        print(
            type(e).__name__,  # TypeError
            __file__,  # /tmp/example.py
            e.__traceback__.tb_lineno  # 2
        )
    return publish_location


def native_location_is_private_to_ocpi_adaptor(data):
    # if is_deleted is True, set to_publish to Not True (False)
    # if is_deleted is False, set to_publish to Not False (True)
    to_publish = not data['is_deleted']

    # if to_publish is True, still publish (not deleted, then check ocpi flag)
    if to_publish:
        # if ocpi_enabled is True, set to_publish to True
        # if ocpi_enabled is False, set to_publish to False
        to_publish = data['ocpi_enabled']

        # if ocpi_enabled is False, but is pushing to global party
        if not to_publish:
            to_publish = data['is_publish_all']

    # if to_publish is still True, but ocpi is actually not enabled is not enabled, but we are to hide all
    # we set to False
    if to_publish:
        if data['ocpi_hide_to_all_party'] is True:
            to_publish = False

    # hotfix/hack for acotech to be the only receiver to receive smart setia alam / penang
    if to_publish:
        location_name = data.get('name', '').lower()
        receiver_party_id = data.get('receiver_party_id', '').lower()
        receiver_country_code = data.get('receiver_country_code', '').lower()
        if location_name in ['smart setia alam', 'smart penang'] and (
                receiver_party_id != 'aco' or receiver_country_code != 'my'
        ):
            to_publish = False

    return to_publish


def native_charge_point_operator_is_private_to_ocpi_evse_adaptor(data):
    mark_evse_as_removed = False
    try:
        if data.get('operator', {}).get('is_private'):
            mark_evse_as_removed = True

        return mark_evse_as_removed
    except Exception as e:  # pylint: disable=broad-except
        # print(f"An error occurred: {e}")
        print(
            type(e).__name__,  # TypeError
            __file__,  # /tmp/example.py
            e.__traceback__.tb_lineno  # 2
        )
        return mark_evse_as_removed


def native_location_images_to_ocpi_adaptor(data):
    location_images = []
    try:
        if data['images']:
            for image in data['images']:
                image_obj = Image(url=image['url'], category='LOCATION', type=image['type'][:4])
                location_images.append(image_obj)
    except Exception as e:  # pylint: disable=broad-except
        # print(f"An error occurred: {e}")
        print(
            type(e).__name__,  # TypeError
            __file__,  # /tmp/example.py
            e.__traceback__.tb_lineno  # 2
        )

    return location_images


def native_location_country_to_ocpi_adaptor(country):
    country_adaptor = {
        'Singapore': 'SGP',
        'Malaysia': 'MYS',
        'Indonesia': 'IDN',
        'Brunei': 'BRN',
        'Cambodia': 'KHM',
        'Thailand': 'THA',
    }

    return country_adaptor.get(country)


def native_location_operator_to_operator_details(data):
    try:
        if data.get('display_as_operator'):
            logo_data = data.get('images', {})
            logo = Image(**logo_data) if logo_data else None
            logo.thumbnail = logo.url if logo else None
            logo.type = 'png'
            business_details = BusinessDetails(
                name=data.get('friendly_name', '')[:100],
                website=data.get('website', ''),
                logo=logo
            )
            return business_details
        return None
    except Exception as e:  # pylint: disable=broad-except
        print('Error in converting operator details: ', e)
        return None


def native_connector_label_to_ocpi_adaptor(connector):
    connector_label = None
    try:
        connector_label = connector['connector_label'][:16]
    except Exception:  # pylint: disable=broad-except
        _ = ''
    return connector_label


def native_connector_serial_number_to_ocpi_adaptor(cp, connector):
    return f"{OCPI_COUNTRY_CODE}*{OCPI_PARTY_ID}*{cp['serial_number']}*{connector['number']}"


def native_connector_capabilities_to_ocpi_adaptor(cp):
    capabilities = cp.get('capabilities', [])
    if capabilities is None:
        capabilities = []
    filtered_capabilities = list(set(capabilities) & set(capability.value for capability in Capability))
    if Capability.start_session_connector_required.value not in filtered_capabilities:
        filtered_capabilities.append(Capability.start_session_connector_required.value)
    if Capability.remote_start_stop_capable.value not in filtered_capabilities:
        filtered_capabilities.append(Capability.remote_start_stop_capable.value)
    return filtered_capabilities


def native_connector_power_to_ocpi_adaptor(connector):
    try:
        if connector['meta']['kilowatt'] > 0:
            max_electric_power = connector['meta']['kilowatt'] * 1000
        else:
            max_electric_power = 22000
    except (TypeError, KeyError):
        max_electric_power = 22000

    return max_electric_power


def native_connector_format_to_ocpi_adaptor(connector_format):
    connector_format_adapter = {
        'CABLE': ConnectorFormat.cable,
        'SOCKET': ConnectorFormat.socket,
    }

    ocpi_parsed_connector_format = connector_format_adapter.get(connector_format)
    return ocpi_parsed_connector_format


def native_connector_type_to_ocpi_adaptor(power_type):
    power_type_adaptor = {
        'AC': PowerType.ac_1_phase,
        'DC': PowerType.dc,
        'AC_3_PHASE': PowerType.ac_3_phase,
        'AC_1_PHASE': PowerType.ac_3_phase,
    }

    ocpi_parsed_power_type = power_type_adaptor.get(power_type)
    return ocpi_parsed_power_type


def native_connector_name_to_ocpi_adaptor(name):
    connector_name_adaptor = {
        'CHAdeMO': ConnectorType.chadmeo,
        'CHADEMO': ConnectorType.chadmeo,
        'Type 1 - CHAdeMO': ConnectorType.chadmeo,
        'IEC_62196_T1': ConnectorType.iec_62196_t1,
        'Type 1 - Yazaki': ConnectorType.iec_62196_t1,
        'Type 1 - Combo': ConnectorType.iec_62196_t1_combo,
        'IEC_62196_T2': ConnectorType.iec_62196_t2,
        'IEC_62196_T2_COMBO': ConnectorType.iec_62196_t2_combo,
        'Type 2 - Mennekes': ConnectorType.iec_62196_t2,
        'Type 1 - Schuko': ConnectorType.domestic_f,
        'IEC_62196_T1_COMBO': ConnectorType.iec_62196_t1_combo,
    }

    ocpi_parsed_connector_name = connector_name_adaptor.get(name)
    return ocpi_parsed_connector_name


def native_connector_status_to_ocpi_adaptor(connector):
    # Define the mapping inside the function or ensure it is accessible globally if defined outside
    connector_status_adaptor = {
        'Available': 'AVAILABLE',
        'Preparing': 'AVAILABLE',
        'Finishing': 'CHARGING',
        'Charging': 'CHARGING',
        'SuspendedEV': 'CHARGING',
        'SuspendedEVSE': 'CHARGING',
        'Unavailable': 'UNKNOWN',
        'Reserved': 'RESERVED',
        'Faulted': 'OUTOFORDER',
    }
    # Access the status using the OCPP status from the connector object
    ocpi_status = connector_status_adaptor.get(connector['ocpp_status'],
                                               'UNKNOWN')  # Default to 'UNKNOWN' if status is not found
    return ocpi_status


def native_location_operating_hours_to_ocpi_adaptor(data: dict):
    day_to_weekday = {
        "monday": 1,
        "tuesday": 2,
        "wednesday": 3,
        "thursday": 4,
        "friday": 5,
        "saturday": 6,
        "sunday": 7
    }

    if data.get("is_24"):
        return {
            "twentyfourseven": True,
            "exceptional_closings": [],
            "exceptional_openings": []
        }

    regular_hours = []

    if data.get("operating_hours"):
        for day, times in data['operating_hours'].items():
            if day in day_to_weekday and isinstance(times, dict) and "period_begin" in times and "period_end" in times:
                try:
                    period_begin = times["period_begin"][:5]  # Trimming seconds if present
                    period_end = times["period_end"][:5]  # Using the original end time
                    weekday = day_to_weekday[day]
                    regular_hours.append({"weekday": weekday, "period_begin": period_begin, "period_end": period_end})
                except Exception as e:  # pylint: disable=broad-except
                    print(f"Error processing {day}: {e}")

    if regular_hours:
        return {
            "twentyfourseven": False,
            "regular_hours": regular_hours,
            "exceptional_closings": [],
            "exceptional_openings": []
        }
    return None


def native_charge_point_status_to_ocpi_adaptor(cp: dict, connector: dict, display_as_private=False):  # noqa: MC0001
    if cp['status'] in ['Unavailable', 'Faulted']:
        status = 'OUTOFORDER'
    elif cp['status'] in ['Unknown']:
        status = 'UNKNOWN'
    else:
        status = native_connector_status_to_ocpi_adaptor(connector)

    if cp['is_connected'] is False:
        status = 'UNKNOWN'

    try:
        if cp['maintenance_status'] and cp['maintenance_status'] != 'Available':
            if cp['maintenance_status'] == 'Blocked':
                status = 'BLOCKED'
            elif cp['maintenance_status'] == 'Inoperative':
                status = 'INOPERATIVE'
            elif cp['maintenance_status'] == 'OutOfOrder':
                status = 'OUTOFORDER'
            elif cp['maintenance_status'] == 'Planned':
                status = 'PLANNED'
            elif cp['maintenance_status'] == 'Rejected':
                status = 'OUTOFORDER'
    except KeyError:
        print('KeyError, maintenance_status not found')
    if cp['is_deleted']:
        status = 'REMOVED'

    if status != 'REMOVED':
        # is private and where it should display as private (default)
        if cp['is_private'] and display_as_private:
            status = 'REMOVED'

    return status


def native_connector_last_updated_to_ocpi_adaptor(connector):
    if connector['updated_at']:
        last_updated = connector['updated_at']
    else:
        last_updated = connector['created_at']
    try:
        # Attempt to parse the datetime with dateutil.parser which is more flexible
        parsed_date = parser.parse(last_updated)
    except ValueError:
        # Fallback if parsing fails
        try:
            parsed_date = datetime.datetime.strptime(last_updated, '%Y-%m-%dT%H:%M:%S.%f').astimezone(
                pytz.UTC)
        except ValueError as e:
            raise ValueError(f"Error parsing date: {last_updated}") from e

        # Convert the datetime to the desired format
    connector_last_updated = parsed_date.strftime('%Y-%m-%dT%H:%M:%SZ')

    return connector_last_updated


def native_location_last_updated_to_ocpi_adaptor(data):
    if data['updated_at']:
        last_updated = data['updated_at']
    else:
        last_updated = data['created_at']

    try:
        # Attempt to parse the datetime with dateutil.parser which is more flexible
        parsed_date = parser.parse(last_updated)
    except ValueError:
        # Fallback if parsing fails
        try:
            parsed_date = datetime.datetime.strptime(last_updated, '%Y-%m-%dT%H:%M:%S.%f').astimezone(pytz.UTC)
        except ValueError as e:
            raise ValueError(f"Error parsing date: {last_updated}") from e

    # Convert the datetime to the desired format
    last_updated = parsed_date.strftime('%Y-%m-%dT%H:%M:%SZ')

    return last_updated


def native_location_timezone_to_ocpi_adaptor(country):
    timezone_adapter = {
        'Singapore': 'Asia/Singapore',
        'Malaysia': 'Asia/Kuala_Lumpur',
        'Indonesia': 'Asia/Jakarta',
        'Brunei': 'Asia/Brunei',
        'Cambodia': 'Asia/Phnom_Penh',
        'Thailand': 'Asia/Bangkok',
        'Philippines': 'Asia/Manila',
    }

    return timezone_adapter.get(country)


def native_display_as_private_checker(data):
    # to cater where we do not want to send as REMOVED
    # For example, if the location is private, but there is
    # agreement with roaming partner to allow them use, then this is place

    display_as_private = True
    try:
        if data.get('operator'):
            if data.get('operator').get('display_as_private') is not None:
                display_as_private = data.get('operator').get('display_as_private')
    except Exception as e:  # pylint: disable=broad-except
        # print(f"An error occurred: {e}")
        print(
            type(e).__name__,  # TypeError
            __file__,  # /tmp/example.py
            e.__traceback__.tb_lineno  # 2
        )
    return display_as_private


def format_erc_to_ocpi_standard(connector_id):
    if '-' in connector_id:
        prefix, suffix = connector_id.rsplit('-', 1)
        suffix = suffix.zfill(3)  # Ensure the last part is zero-padded to 3 digits
        return f"{prefix}-{suffix}"
    return connector_id  # Return as is if no hyphen found


class Adapter:
    @classmethod
    def location_adapter(cls, data,  # noqa: MC0001
                         version: VersionNumber = VersionNumber.v_2_2_1) -> Location:
        if not data:
            raise HTTPException(status_code=404, detail="Location not found")

        # ocpi location to ocpi location
        if data.get('data'):
            locations_data = data['data']
            if isinstance(locations_data, list):
                locations = []
                for x in range(len(locations)):
                    locations.append(emsp_single_location_adapter(locations_data[x]))
                return locations
            emsp_single_location = emsp_single_location_adapter(locations_data)
            return emsp_single_location

        # native to ocpi location adapter
        if data.get('is_24') is not None:  # pylint: disable=too-many-nested-blocks
            for_lta = data.get('for_lta', False)
            evse_list = []

            # is_private = False

            display_as_private = native_display_as_private_checker(data)
            # NOTE: IS_PRIVATE IS INVERSE OF PUBLISH WHICH IS USED IN OCPI
            location_to_publish = native_location_is_private_to_ocpi_adaptor(data)

            if for_lta:
                display_as_private = False
                location_to_publish = True
            # if location_to_publish is True:
            #     location_to_publish = native_operator_is_private_to_ocpi_adaptor(location_to_publish, data)

            if len(data['charge_points']) > 0:
                for cp in data['charge_points']:
                    if for_lta:
                        if not cp.get('erc_number'):  # Checks for None, empty string, or missing key
                            continue
                    filtered_capabilities = native_connector_capabilities_to_ocpi_adaptor(cp)

                    # is_private = not cp['is_private']
                    for connector in cp['connectors']:
                        try:

                            connector_uid = connector['id']
                            evse_id = native_connector_serial_number_to_ocpi_adaptor(cp, connector)
                            connector_max_electric_power = native_connector_power_to_ocpi_adaptor(connector)
                            connector_status = native_charge_point_status_to_ocpi_adaptor(cp, connector,
                                                                                          display_as_private)
                            connector_label = native_connector_label_to_ocpi_adaptor(connector)
                            connector_last_updated = native_connector_last_updated_to_ocpi_adaptor(connector)
                            connector_standard = native_connector_name_to_ocpi_adaptor(
                                connector['connector_type']['name'])

                            connector_format = native_connector_format_to_ocpi_adaptor(
                                connector['connector_type']['format'])
                            connector_power_type = native_connector_type_to_ocpi_adaptor(
                                connector['connector_type']['kind'])

                            connector_id = str(connector['number'])
                            if for_lta:
                                connector_id = f"{cp['erc_number']}-{str(connector['number'])}"
                                connector_id = format_erc_to_ocpi_standard(connector_id)

                            if location_to_publish is False:
                                connector_status = 'REMOVED'
                            if connector_status != 'REMOVED':
                                mark_evse_as_removed = native_charge_point_operator_is_private_to_ocpi_evse_adaptor(cp)
                                if mark_evse_as_removed is True:
                                    connector_status = 'REMOVED'
                            # print(connector)
                            evse = EVSE(
                                uid=connector_uid,
                                evse_id=evse_id,
                                status=connector_status,
                                connectors=[
                                    Connector(
                                        id=connector_id,
                                        standard=connector_standard,
                                        format=connector_format,
                                        power_type=connector_power_type,
                                        max_voltage=100,
                                        max_amperage=20,
                                        max_electric_power=connector_max_electric_power,
                                        tariff_ids=[connector['id']],
                                        last_updated=connector_last_updated
                                    )
                                ],
                                last_updated=connector_last_updated,
                                physical_reference=connector_label,
                                capabilities=filtered_capabilities
                            )
                            evse_list.append(evse)
                        except Exception as e:  # pylint: disable=broad-except
                            # print(f"An error occurred: {e}")
                            print(e)
                            print(
                                type(e).__name__,  # TypeError
                                __file__,  # /tmp/example.py
                                e.__traceback__.tb_lineno  # 2
                            )

            try:
                city = data['city']
                if city in [' ', '']:
                    city = '#NA'

                location_images = native_location_images_to_ocpi_adaptor(data)
                location_country = native_location_country_to_ocpi_adaptor(data['country'])
                location_timezone = native_location_timezone_to_ocpi_adaptor(data['country'])
                location_last_updated = native_location_last_updated_to_ocpi_adaptor(data)
                location_opening_times = native_location_operating_hours_to_ocpi_adaptor(data)
                location_data = Location(
                    country_code=OCPI_COUNTRY_CODE,
                    party_id=OCPI_PARTY_ID,
                    id=data['id'],
                    publish=location_to_publish,
                    name=data['name'],
                    address=data['address'][:45],
                    city=city,
                    state=data['state'][:20],
                    country=location_country,
                    coordinates=GeoLocation(
                        latitude=format_latitude(str(data['latitude'])),
                        longitude=format_longitude(str(data['longitude']))
                    ),
                    evses=evse_list,
                    time_zone=location_timezone,
                    last_updated=location_last_updated,
                    images=location_images,
                )
                if SEND_OCPI_ROAMING_OPERATING_HOURS:
                    location_opening_times.opening_times = location_opening_times

                try:
                    if data.get('operator'):
                        operator = native_location_operator_to_operator_details(data.get('operator'))
                        location_data.operator = operator
                except Exception as e:  # pylint: disable=broad-except
                    # print(f"An error occurred: {e}")
                    print(
                        type(e).__name__,  # TypeError
                        __file__,  # /tmp/example.py
                        e.__traceback__.tb_lineno  # 2
                    )
                if for_lta:
                    location_data.owner = None
                    location_data.suboperator = None
                return location_data
            except Exception as e:  # pylint: disable=broad-except
                # print(f"An error occurred: {e}")
                print(
                    type(e).__name__,  # TypeError
                    __file__,  # /tmp/example.py
                    e.__traceback__.tb_lineno  # 2
                )
        locations_data = data

        emsp_single_location = emsp_single_location_adapter(locations_data)
        return emsp_single_location

    @classmethod
    def tariff_adapter(cls, data, version: VersionNumber = VersionNumber.v_2_2_1) -> Tariff:
        if data.get('data'):
            emsp_tariff = emsp_single_tariff_adapter(data['data'])
            return emsp_tariff
        if data.get('billing_cycle'):
            connector_tariff = native_connector_tariff_to_ocpi_adapter(data)
            return connector_tariff
        emsp_tariff = emsp_single_tariff_adapter(data)
        return emsp_tariff

    @classmethod
    def token_adapter(cls, data, version: VersionNumber = VersionNumber.v_2_2_1) -> Token:
        try:
            if data.get('partner_ocpi_cpo_token_id'):
                data['uid'] = data['partner_ocpi_cpo_token_id']
                data['last_updated'] = str(datetime.datetime.strptime(data['updated_at'],
                                                                      '%Y-%m-%dT%H:%M:%S.%f%z')) if \
                    data['updated_at'] else str(datetime.datetime.strptime(data['created_at'],
                                                                           '%Y-%m-%dT%H:%M:%S.%f%z'))
            if data.get('member_id'):
                try:
                    data['last_updated'] = data['updated_at'].strftime('%Y-%m-%dT%H:%M:%SZ')
                except (KeyError, AttributeError):
                    data['last_updated'] = data['created_at'].strftime('%Y-%m-%dT%H:%M:%SZ')
                data.pop('description')
                data.pop('meta')
                data.pop('member_id')
                data.pop('updated_at')
                data.pop('created_at')

            token = Token(**data)
            return token
        except Exception as e:  # pylint: disable=broad-except
            print(e)

    @classmethod
    def commands_adapter(cls, data, version: VersionNumber) -> CommandResponse:
        return CommandResponse(
            **data,
            timeout=30
        )

    @classmethod
    def command_response_adapter(cls, data) -> CommandResponse:
        try:
            if data['message']:
                display_texts = []
                for message in data['message']:
                    display_texts.append(DisplayText(language='en', text=message))
                command_response = CommandResponse(result=data['result'], timeout=270, message=display_texts)
                try:
                    print("----- Command Response Adapter Start ----")
                    print(json.loads(command_response.json()))
                    print("----- Command Response Adapter End ----")
                except Exception as e:  # pylint: disable=broad-except
                    print("Error on command response adapter with error: %s", str(e))

                return command_response
            return CommandResponse(result=data['result'], timeout=270)
        except Exception as e:  # pylint: disable=broad-except
            print(e)

        command_response = CommandResponse(result=data['result'], timeout=270)
        try:
            print("----- Command Response Adapter Start ----")
            print(json.loads(command_response.json()))
            print("----- Command Response Adapter End ----")
        except Exception as e:  # pylint: disable=broad-except
            print("Error on command adapter with error: %s", str(e))

        return command_response

    @classmethod
    def command_result_adapter(cls, data, version: VersionNumber) -> CommandResult:
        if data is True:
            return CommandResult(result='ACCEPTED')
        return CommandResult(result='REJECTED')
        # return {'result': data['result'], 'message': data['message']}

    @classmethod
    def session_adapter(cls, data, version: VersionNumber = VersionNumber.v_2_2_1) -> Session:  # noqa: MC0001
        if isinstance(data, Response):
            data = data.json()
        # Meant for chargEV as CPO
        if data.get('cpo_transaction_id'):
            try:
                session = Session(
                    country_code=OCPI_COUNTRY_CODE,
                    party_id=OCPI_PARTY_ID,
                    id=data.get('id'),
                    start_date_time=data.get('start_date_time'),
                    end_date_time=data.get('end_date_time'),
                    kwh=data.get('kwh'),
                    auth_method=data.get('auth_method'),
                    location_id=data.get('ocpi_partner_location_id'),
                    evse_uid=data.get('ocpi_partner_evse_uid'),
                    connector_id=data.get('ocpi_partner_connector_id'),
                    status=data.get('status'),
                    last_updated=data['updated_at'] if data['updated_at'] else data['created_at'],
                    currency=data.get('currency'),
                    # TODO: SOLVE CURRENCY MISSING ISSUE IN CPO CHARGING SESSION
                    # currency=data.get('currency'),
                    cdr_token=data.get('cdr_token'),
                    authorization_reference=data['authorization_reference'] if data[
                        'authorization_reference'] else None,
                )
                try:
                    if data.get('soc') is not None:
                        session.soc = float(data.get('soc'))
                except Exception:  # pylint: disable=broad-except
                    _ = ''
                try:
                    print("----- Session Adapter Start ----")
                    print(json.loads(session.json()))
                    print("----- Session Adapter End ----")
                except Exception as e:  # pylint: disable=broad-except
                    print("Error on session adapter with error: %s", str(e))

                return session
            except Exception as e:  # pylint: disable=broad-except
                print(e)

        # Meant as for chargEV as eMSP (Direct Map)
        if data.get('partner_session_uid'):
            try:
                if data.get('updated_at'):
                    session_updated_at = data['updated_at']
                else:
                    session_updated_at = data['created_at']
                try:
                    session_updated_at = str(datetime.datetime.strptime(session_updated_at, '%Y-%m-%dT%H:%M:%S.%f%z'))
                except ValueError:
                    session_updated_at = datetime.datetime.strptime(session_updated_at,
                                                                    '%Y-%m-%dT%H:%M:%S.%f').astimezone(
                        pytz.UTC).strftime('%Y-%m-%dT%H:%M:%SZ')

                session = Session(
                    country_code=data.get('country_code'),
                    party_id=data.get('party_id'),
                    id=data.get('partner_session_uid'),
                    start_date_time=data.get('start_date_time'),
                    end_date_time=data.get('end_date_time'),
                    kwh=data.get('kwh'),
                    auth_method=data.get('auth_method'),
                    location_id=data.get('ocpi_partner_location_id'),
                    evse_uid=data.get('ocpi_partner_evse_uid'),
                    connector_id=data.get('ocpi_partner_connector_id'),
                    status=data.get('status'),
                    last_updated=session_updated_at,
                    currency=data.get('currency'),
                    cdr_token=data.get('cdr_token'),
                )
                try:
                    if data.get('soc') is not None:
                        session.soc = float(data.get('soc'))
                except Exception:  # pylint: disable=broad-except
                    _ = ''
                try:
                    print("----- Session Adapter Start ----")
                    print(json.loads(session.json()))
                    print("----- Session Adapter End ----")
                except Exception as e:  # pylint: disable=broad-except
                    print("Error on session adapter with error: %s", str(e))

                return session
            except Exception as e:  # pylint: disable=broad-except
                print(e)
            cdr_token = CdrToken(
                country_code=OCPI_COUNTRY_CODE,
                party_id=OCPI_PARTY_ID,
                uid=data['id_tag'],
                type=TokenType.rfid,
                contract_id=data['id_tag']
            )
            if data['status'] == 'Charging':
                status = SessionStatus.active
            if data['status'] == 'Completed':
                status = SessionStatus.completed
            session = Session(
                country_code=OCPI_COUNTRY_CODE,
                party_id=OCPI_PARTY_ID,
                id=data['id'],
                start_date_time=data['session_start'],
                end_date_time=data['session_end'] if data['session_end'] else None,
                kwh=data['meter_stop'] - data['meter_start'],
                cdr_token=cdr_token,
                auth_method='COMMAND',
                location_id=data['meta']['location']['id'],
                evse_uid=data['charge_point_connector']['id'],
                connector_id=data['charge_point_connector']['id'],
                currency=data['charge_point_connector']['billing_currency'],
                status=status,
                last_updated=data['updated_at'] if data['updated_at'] else data['created_at'],
            )
            try:
                if data.get('soc') is not None:
                    session.soc = float(data.get('soc'))
            except Exception:  # pylint: disable=broad-except
                _ = ''
            try:
                print("----- Session Adapter Start ----")
                print(json.loads(session.json()))
                print("----- Session Adapter End ----")
            except Exception as e:  # pylint: disable=broad-except
                print("Error on session adapter with error: %s", str(e))

            return session

        # Meant for chargEV as CPO [But with cpo_cs and cs (Two layer wrapping)]
        try:
            try:
                session_last_updated = str(
                    datetime.datetime.strptime(data['cpo_cs']['updated_at'], '%Y-%m-%dT%H:%M:%S.%f%z'))
            except ValueError:
                session_last_updated = datetime.datetime.strptime(data['cpo_cs']['updated_at'],
                                                                  '%Y-%m-%dT%H:%M:%S.%f').astimezone(
                    pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S.%fZ')
            id = data['cpo_cs']['id']
            start_date_time = data['cpo_cs']['start_date_time']
            end_date_time = data['cpo_cs']['end_date_time'] if data['cpo_cs']['end_date_time'] else None
            kwh = data['cpo_cs']['kwh'] if data['cpo_cs']['kwh'] else 0
            # cdr_token = data['cpo_cs']['cdr_token']
            auth_method = data['cpo_cs']['auth_method']
            authorization_reference = data['cpo_cs']['authorization_reference'] if data['cpo_cs'][
                'authorization_reference'] else None
            meter_id = data['cpo_cs']['meter_id'] if data['cpo_cs']['meter_id'] else None

            location_id = data['cpo_cs']['ocpi_partner_location_id']
            evse_uid = data['cpo_cs']['ocpi_partner_evse_uid']
            connector_id = data['cpo_cs']['ocpi_partner_connector_id']
            last_updated = session_last_updated
            charging_periods = []
            currency = data['db_cs']['meta']['billing_info']['billing_currency']
            status = native_session_to_ocpi_adapter(data)
            total_cost = data['cpo_cs']['total_cost'] if data['cpo_cs']['total_cost'] else {}
            country_code = OCPI_COUNTRY_CODE
            party_id = OCPI_PARTY_ID
            cdr_token = CdrToken(**data['cpo_cs']['cdr_token'])

            if status == 'ACTIVE' and end_date_time:
                end_date_time = None

            session = Session(location_id=location_id, evse_uid=evse_uid, connector_id=connector_id,
                              last_updated=last_updated, charging_periods=charging_periods, currency=currency,
                              id=id, start_date_time=start_date_time, end_date_time=end_date_time, kwh=kwh,
                              cdr_token=cdr_token, auth_method=auth_method,
                              authorization_reference=authorization_reference, meter_id=meter_id, status=status,
                              total_cost=total_cost, country_code=country_code, party_id=party_id)

            try:
                if data['cpo_cs']['soc'] is not None:
                    session.soc = float(data['cpo_cs']['soc'])
            except Exception:  # pylint: disable=broad-except
                _ = ''
            try:
                print("----- Session Adapter Start ----")
                print(json.loads(session.json()))
                print("----- Session Adapter End ----")
            except Exception as e:  # pylint: disable=broad-except
                print("Error on session adapter with error: %s", str(e))

            return session
        except Exception as e:  # pylint: disable=broad-except
            print(e)

    @classmethod
    def cdr_adapter(cls, data,  # pylint: disable=too-many-locals  # noqa
                    version: VersionNumber = VersionNumber.v_2_2_1) -> Cdr:
        if data.get('partner_ocpi_cpo_cdr_id'):
            data['id'] = data['partner_ocpi_cpo_cdr_id']
            data['last_updated'] = str(datetime.datetime.strptime(data['updated_at'], '%Y-%m-%dT%H:%M:%S.%f%z')) if \
                data['updated_at'] else str(datetime.datetime.strptime(data['created_at'], '%Y-%m-%dT%H:%M:%S.%f%z'))
            # data['last_updated'] = last_updated
            try:
                return Cdr(**data)
            except Exception as e:  # pylint: disable=broad-except
                print(e)
        else:
            charging_session = data.get('charging_session')
            cpo_cs = data.get('cpo_cs')
            charging_session_bill = data.get('charging_session_bill')

            # logging.error(charging_session['connector']['connector_type'])
            connector = charging_session['charge_point_connector']
            cp = charging_session['charge_point_connector']['charge_point']
            location = charging_session['charge_point_connector']['charge_point']['location']

            connector_uid = connector['id']
            connector_id = native_connector_serial_number_to_ocpi_adaptor(cp, connector)
            connector_standard = native_connector_name_to_ocpi_adaptor(connector['connector_type']['name'])

            connector_format = native_connector_format_to_ocpi_adaptor(connector['connector_type']['format'])
            connector_power_type = native_connector_type_to_ocpi_adaptor(connector['connector_type']['kind'])

            location_country = native_location_country_to_ocpi_adaptor(location['country'])
            city = location['city']
            if city in [' ', '']:
                city = '#NA'
            if city is None:
                city = '#NA'

            cdr_token = CdrToken(**cpo_cs.get('cdr_token'))
            cdr_location = CdrLocation(
                id=location['id'],
                name=location['name'],
                address=location['address'][:45],
                city=city,
                country=location_country,
                coordinates=GeoLocation(
                    latitude=format_latitude(str(location['latitude'])),
                    longitude=format_longitude(str(location['longitude'])),
                ),
                evse_id=connector_id,
                evse_uid=connector_uid,
                # evse_id=charging_session['charge_point_connector']['charge_point']['serial_number'],
                connector_id=str(charging_session['charge_point_connector']['number']),
                connector_standard=connector_standard,
                connector_format=connector_format,
                connector_power_type=connector_power_type,
            )
            cdr_dimensions = [CdrDimension(
                type=CdrDimensionType.energy,
                volume=float(charging_session['charging_usage']) / 1000.0,
            )]

            charging_periods = [ChargingPeriod(
                start_date_time=charging_session['session_start'],
                dimensions=cdr_dimensions,
                tariff_ids=[charging_session['charge_point_connector']['id']],
            )]
            charging_session['charge_point_connector']['tariff_standard'] = data['tariff_standard']
            tariff = native_connector_tariff_to_ocpi_adapter(charging_session['charge_point_connector'])

            try:
                cdr_last_updated = str(
                    datetime.datetime.strptime(charging_session['updated_at'], '%Y-%m-%dT%H:%M:%S.%f%z')) if \
                    charging_session['updated_at'] else str(
                    datetime.datetime.strptime(charging_session['created_at'], '%Y-%m-%dT%H:%M:%S.%f%z'))
            except ValueError:
                cdr_last_updated = datetime.datetime.strptime(charging_session['updated_at'],
                                                              '%Y-%m-%dT%H:%M:%S.%f').astimezone(
                    pytz.UTC).strftime('%Y-%m-%dT%H:%M:%SZ')

            # TODO: ADD PARKING DURATION
            _ = 0
            parking_cost_incl_vat = float(charging_session_bill.get('hogging_fee', 0))
            parking_cost_excl_vat = float(charging_session_bill.get('hogging_fee', 0))
            tax_rate = round(float(charging_session_bill.get('tax_rate', 0)), 1)  # e.g., 8% VAT'
            total_cost_excl_vat = float(charging_session['total_charge_amount']) / (1 + tax_rate / 100)
            total_cost_incl_vat = float(charging_session['total_charge_amount'])

            if parking_cost_incl_vat != 0.00:
                parking_cost_incl_vat = round(parking_cost_excl_vat * (1 + tax_rate / 100), 2)

            authorization_reference = cpo_cs["authorization_reference"] if cpo_cs["authorization_reference"] else None
            cdr = Cdr(
                country_code=OCPI_COUNTRY_CODE,
                party_id=OCPI_PARTY_ID,
                tariffs=[tariff],
                id=str(charging_session_bill['id']),
                session_id=cpo_cs['id'],
                start_date_time=charging_session['session_start'],
                end_date_time=charging_session['session_end'],
                cdr_token=cdr_token,
                auth_method=AuthMethod.command,
                authorization_reference=authorization_reference,
                cdr_location=cdr_location,
                currency=charging_session['charge_point_connector']['billing_currency'],
                charging_periods=charging_periods,
                total_cost={
                    'excl_vat': total_cost_excl_vat,
                    'incl_vat': total_cost_incl_vat
                },
                total_parking_cost={
                    'incl_vat': parking_cost_incl_vat,
                    'excl_vat': parking_cost_excl_vat,
                },
                total_energy=charging_session['charging_usage'] / 1000.0,
                total_time=round((charging_session['duration'] / 3600.), 4),
                credit=False,
                home_charging_compensation=False,
                last_updated=cdr_last_updated
            )
            try:
                print("----- CDR Adapter Start ----")
                print(json.loads(cdr.json()))
                print("----- CDR Adapter End ----")
            except Exception as e:  # pylint: disable=broad-except
                print("Error on CDR adapter with error: %s", str(e))

            return cdr

    @classmethod
    def credentials_adapter(cls, data, version: VersionNumber = VersionNumber.v_2_2_1):
        standard_url = URL(f'https://{OCPI_HOST}/{settings.OCPI_PREFIX}/versions')
        return Credentials(
            token=data['token'],
            url=data.get('url', standard_url),
            roles=[CredentialsRole(
                role=enums.RoleEnum.cpo,
                business_details=BusinessDetails(
                    name='ChargEV',
                    website='https://chargev-ygt.com/',
                    logo=Image(
                        url='https://chargev-python-logo.s3.ap-southeast-1.amazonaws.com/chargEV.png',
                        thumbnail='https://chargev-python-logo.s3.ap-southeast-1.amazonaws.com/chargEV.png',
                        category=ImageCategory.owner,
                        type='png',
                        width=0,
                        height=0
                    )
                ),
                party_id=OCPI_PARTY_ID,
                country_code=OCPI_COUNTRY_CODE
            ), CredentialsRole(
                role=enums.RoleEnum.emsp,
                business_details=BusinessDetails(
                    name='ChargEV',
                    website='https://chargev-ygt.com/',
                    logo=Image(
                        url='https://chargev-python-logo.s3.ap-southeast-1.amazonaws.com/chargEV.png',
                        thumbnail='https://chargev-python-logo.s3.ap-southeast-1.amazonaws.com/chargEV.png',
                        category=ImageCategory.owner,
                        type='png',
                        width=0,
                        height=0
                    )
                ),
                party_id=OCPI_PARTY_ID,
                country_code=OCPI_COUNTRY_CODE
            )],
        )


app = get_application([VersionNumber.v_2_2_1], [enums.RoleEnum.cpo, enums.RoleEnum.emsp], Crud, Adapter)


async def log_ocpi_histories(ocpi_histories):
    try:
        path = f"{main_path}/api/v1/csms/ocpi/incoming-log"

        async with httpx.AsyncClient() as client:
            _ = await client.post(path, data=json.dumps(ocpi_histories), timeout=30)
    except Exception:  # pylint: disable=broad-except
        # Handle any exceptions that might occur while logging
        _ = ''


async def get_request_body(request: Request) -> bytes:
    body = await request.body()

    request._receive = ReceiveProxy(receive=request.receive, cached_body=body)  # pylint: disable=protected-access
    return body


@dataclasses.dataclass
class ReceiveProxy:
    """Proxy to starlette.types.Receive.__call__ with caching first receive call."""

    receive: starlette.types.Receive
    cached_body: bytes
    _is_first_call: ClassVar[bool] = True  # pylint: disable=unsubscriptable-object

    async def __call__(self):
        # First call will be for getting request body => returns cached result
        if self._is_first_call:
            self._is_first_call = False
            return {"type": "http.request", "body": self.cached_body, "more_body": False}

        return await self.receive()


class LogRequestResponseMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)

    async def set_body(self, request: Request):
        body = await request.body()

        request._receive = ReceiveProxy(receive=request.receive, cached_body=body)  # pylint: disable=protected-access
        return body

    async def dispatch(self, request: Request, call_next):  # noqa : MC001
        # Log the incoming request method and URL
        await self.set_body(request)
        request_content = await request.body()
        try:
            request_body = await request.json()
        except Exception:  # pylint: disable=broad-except
            request_body = {}
        # Call the next middleware or route handler
        response = await call_next(request)
        if 'ping' in str(request.url).lower() or 'docs' in str(request.url).lower():
            return response
        if 'commands' not in str(request.url).lower():
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk
            try:
                ocpi_histories = {
                    "type": "Incoming",
                    "request_url": str(request.url),
                    "request_header": str(request.headers),
                    "response_code": str(response.status_code),
                    "response_content": str(response_body),
                    "request_method": str(request.method),
                }
                try:
                    parsed_url = urlparse(str(request.url))
                    path_elements = parsed_url.path.split('/')

                    # The module part should be at index 4, given the structure of the URL
                    path_module = path_elements[4] if len(path_elements) > 4 else None
                    ocpi_histories['ocpi_module'] = str(path_module).lower()
                except Exception:  # pylint: disable=broad-except
                    _ = ''
                try:
                    ocpi_histories['response_body'] = json.loads(response_body)
                except Exception:  # pylint: disable=broad-except
                    _ = ''

                try:
                    ocpi_histories['request_content'] = str(request_content)
                except Exception:  # pylint: disable=broad-except
                    _ = ''
                try:
                    ocpi_histories['request_body'] = request_body
                except Exception:  # pylint: disable=broad-except
                    _ = ''

                await log_ocpi_histories(ocpi_histories)
            except Exception:  # pylint: disable=broad-except
                _ = ''

            return FastAPIResponse(content=response_body, status_code=response.status_code,
                                   headers=dict(response.headers),
                                   media_type=response.media_type)
        try:
            ocpi_histories = {
                "type": "Incoming",
                "request_url": str(request.url),
                "request_header": str(request.headers),
                "request_method": str(request.method),
            }
            try:
                ocpi_histories['request_content'] = str(request_content)
            except Exception:  # pylint: disable=broad-except
                _ = ''
            try:
                ocpi_histories['request_body'] = request_body
            except Exception:  # pylint: disable=broad-except
                _ = ''
            await log_ocpi_histories(ocpi_histories)
        except Exception:  # pylint: disable=broad-except
            _ = ''
        return response


# Attach the custom middleware to the FastAPI app
app.add_middleware(LogRequestResponseMiddleware)


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title="Apollo OCPI API",
        version="1.0",
        description="Apollo OCPI API Reference",
        routes=app.routes,
    )
    # add Authorization to SwaggerUi
    openapi_schema["components"]["securitySchemes"] = {
        "ApiKeyAuth": {
            "type": "apiKey",
            "in": "header",
            "name": "Authorization",
        }
    }
    openapi_schema["security"] = [
        {
            "ApiKeyAuth": []
        }
    ]
    app.openapi_schema = openapi_schema
    return app.openapi_schema


if pydantic.parse_obj_as(bool, IS_DEVELOP_ENV):
    app.openapi = custom_openapi
else:
    app.docs_url = None
    app.openapi = None
    app.redoc_url = None
    app.openapi_url = ''


def ping_response():
    return {"message": "pong"}


@app.get("/ping")
async def ping():
    return ping_response()


@app.get("/ocpi/ping")
async def ping_with_ocpi():
    return ping_response()
