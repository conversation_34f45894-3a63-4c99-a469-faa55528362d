"""14

Revision ID: 191ec2a304ee
Revises: 0ca322a66acf
Create Date: 2022-10-19 13:52:48.539880

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '191ec2a304ee'
down_revision = '0ca322a66acf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'main_subscription_card', ['number'])
    op.create_unique_constraint(None, 'main_subscription_card', ['id_tag'])
    op.drop_constraint('main_subscription_invitation_subscription_plan_id_key', 'main_subscription_invitation', type_='unique')
    op.alter_column('main_subscription_card', 'linked_at',
                    existing_type=sa.DateTime(),
                    type_=sa.DateTime(timezone=True),
                    existing_nullable=True)
    op.alter_column('main_subscription_invitation', 'linked_at',
                    existing_type=sa.DateTime(),
                    type_=sa.DateTime(timezone=True),
                    existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('main_subscription_invitation_subscription_plan_id_key', 'main_subscription_invitation', ['subscription_plan_id'])
    op.drop_constraint(None, 'main_subscription_card', type_='unique')
    op.drop_constraint(None, 'main_subscription_card', type_='unique')
    op.alter_column('main_subscription_card', 'linked_at',
                    existing_type=sa.DateTime(timezone=True),
                    type_=sa.DateTime(),
                    existing_nullable=True)
    op.alter_column('main_subscription_invitation', 'linked_at',
                    existing_type=sa.DateTime(timezone=True),
                    type_=sa.DateTime(),
                    existing_nullable=True)
    # ### end Alembic commands ###
