"""40

Revision ID: ff97c29d313f
Revises: cd6aaaf6d179
Create Date: 2023-05-30 07:24:40.396852

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ff97c29d313f'
down_revision = 'cd6aaaf6d179'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_id_tag', sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key(None, 'main_id_tag', 'main_organization', ['organization_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_id_tag', 'organization_id')
    # ### end Alembic commands ###
