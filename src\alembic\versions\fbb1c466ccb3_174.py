"""174

Revision ID: fbb1c466ccb3
Revises: 81fa77a000d3
Create Date: 2025-05-27 15:54:57.591595

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fbb1c466ccb3'
down_revision = '81fa77a000d3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('unique_vehicle_pcid', table_name='main_vehicle')
    op.create_index('unique_vehicle_pcid', 'main_vehicle', ['pcid', 'is_deleted'], unique=False, postgresql_where=sa.text('NOT is_deleted'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('unique_vehicle_pcid', table_name='main_vehicle', postgresql_where=sa.text('NOT is_deleted'))
    op.create_index('unique_vehicle_pcid', 'main_vehicle', ['pcid', 'is_deleted'], unique=False)
    # ### end Alembic commands ###
