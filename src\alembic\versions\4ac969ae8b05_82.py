"""82

Revision ID: 4ac969ae8b05
Revises: 886d9ac38048
Create Date: 2024-04-03 12:26:43.993994

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4ac969ae8b05'
down_revision = '886d9ac38048'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_charging_session_bill', sa.Column('id_tag', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_charging_session_bill', 'id_tag')
    # ### end Alembic commands ###
