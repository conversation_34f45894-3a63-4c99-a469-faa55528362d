import logging
from typing import List
from uuid import UUID
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi_pagination import Params, Page
from fastapi_pagination.ext.sqlalchemy import paginate

from app import settings, schema, crud, exceptions
from app.database import create_session, SessionLocal
from app.permissions import permission
# from app.ruby_proxy_utils import sync_ruby_id_tag
from app.utils import GenerateReport

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/id_tag",
    tags=['id_tag', ],
    dependencies=[Depends(permission)]
)


async def id_tag_filter_parameters(name: str = None, type: str = None, enabled: bool = None,
                                   from_date: datetime = None, to_date: datetime = None,
                                   username: str = None):
    return {'name': name, 'type': type, 'is_active': enabled, 'from_date': from_date,
            'to_date': to_date, 'username': username}


def id_tag_additional_filter(id_tag: str = None, phone_number: str = None, card_serial_number: str = None):
    return {'id_tag': id_tag, 'phone_number': phone_number, 'name': card_serial_number}


@router.post("/", response_model=schema.IDTagResponse,
             status_code=status.HTTP_201_CREATED)
async def add_id_tag(id_tag_data: schema.IDTag,
                     dbsession: SessionLocal = Depends(create_session)):
    """
    Add an IDTag

    :param IDTag id_tag_data: IDTag Data
    """
    try:
        db_id_tag = crud.create_id_tag(dbsession, id_tag_data)
        # id_tag_dict = id_tag_data.dict()
        # await sync_ruby_id_tag(id_tag_dict['id_tag'], id_tag_dict['member_id'], id_tag_dict['expiration'])
        return db_id_tag
    except exceptions.ApolloIDTagError as e:
        raise HTTPException(400, e.__str__())


@router.get("/", response_model=Page[schema.IDTagResponse],
            status_code=status.HTTP_200_OK)
async def get_all_id_tags(filter_id_tags: dict = Depends(id_tag_filter_parameters),
                          filters: dict = Depends(id_tag_additional_filter),
                          params: Params = Depends(),
                          dbsession: SessionLocal = Depends(create_session)):
    query = crud.get_all_id_tags_with_filters(db=dbsession, tag_filters=filter_id_tags, id_fitlers=filters)
    return paginate(query, params)


@router.get("/download", status_code=status.HTTP_200_OK)
async def download_id_tags(filter_id_tags: dict = Depends(id_tag_filter_parameters),
                           filters: dict = Depends(id_tag_additional_filter),
                           dbsession: SessionLocal = Depends(create_session)):
    """
    Download ID Tags
    """
    query = crud.get_all_id_tags_with_filters(db=dbsession, tag_filters=filter_id_tags, id_fitlers=filters)
    headers = ['ID TAG', 'CARD SERIAL NUMBER', 'PHONE NUMBER', 'DESCRIPTION', 'ENABLED', 'EXPIRY DATE']
    columns = ['id_tag', 'name', 'member.user.phone_number', 'description', 'is_active', 'expiration']
    report = GenerateReport("ID_TAG", header=headers, columns=columns, query=query)
    await report.generate_dataframe_with_query(schema=schema.IDTagResponse, join='member')
    await report.nan_handling('is_active', 'Yes', 'True')
    await report.nan_handling('is_active', 'No', 'False')
    await report.datetime_reformat('expiration')
    return await report.generate_report()


@router.get("/member/id-tag/{id_tag}", status_code=200)
async def get_member_by_id_tag(id_tag: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Get member by its ID tag
    """
    db_member = crud.get_member_by_id_tag(dbsession, id_tag)
    return db_member


@router.get("/member/{member_id}",
            status_code=status.HTTP_200_OK, response_model=List[schema.IDTagResponse])
async def get_id_tag_list(member_id: UUID,
                          dbsession: SessionLocal = Depends(create_session)):
    """
    Get a Member IDTag List

    :param str member_id: Target Membership ID
    """
    try:
        db_id_tag_list = crud.get_member_id_tag_list(dbsession, member_id)
        return db_id_tag_list
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/id-tag/{id_tag}", status_code=status.HTTP_200_OK, include_in_schema=False)
async def get_id_tag_by_id_tag(id_tag: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Get a IDTag by its id tag
    """
    try:
        db_id_tag = crud.get_id_tag_by_id_tag(dbsession, id_tag)
        return db_id_tag
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/{id_tag_id}",
            status_code=status.HTTP_200_OK, response_model=schema.IDTagResponse)
async def get_id_tag(id_tag_id: UUID,
                     dbsession: SessionLocal = Depends(create_session)):
    """
    Get an IDTag

    :param str id_tag_id: Target IDTag ID
    """
    try:
        db_id_tag = crud.get_id_tag(dbsession, id_tag_id)
        return db_id_tag
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.patch("/{id_tag_id}",
              status_code=status.HTTP_200_OK, response_model=schema.IDTagResponse)
async def update_id_tag(id_tag_data: schema.IDTagUpdate, id_tag_id: UUID,
                        dbsession: SessionLocal = Depends(create_session)):
    """
    Update an IDTag

    :param str id_tag_id: Target IDTag ID
    :param IDTagUpdate id_tag_data: IDTag Code Data
    """
    try:
        db_id_tag = crud.update_id_tag(dbsession, id_tag_data, id_tag_id)

        # await sync_ruby_id_tag(db_id_tag.id, db_id_tag.member_id, db_id_tag.expiration)
        return db_id_tag
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{id_tag_id}", status_code=status.HTTP_200_OK)
async def delete_id_tag(id_tag_id: UUID,
                        dbsession: SessionLocal = Depends(create_session)):
    """
    Delete an IDTag

    :param str id_tag_id: Target IDTag ID
    """
    try:
        crud.delete_id_tag(dbsession, id_tag_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
