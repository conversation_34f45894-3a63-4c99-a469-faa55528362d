"""64

Revision ID: 540a5fbafb27
Revises: 58a9bcc44c2e
Create Date: 2023-11-14 17:37:41.350452

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '540a5fbafb27'
down_revision = '58a9bcc44c2e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_page_resource', sa.Column('manage', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_page_resource', 'manage')
    # ### end Alembic commands ###
