from typing import Optional
from pydantic import BaseModel


class CreateNotificationService(BaseModel):
    # sender_id: str
    send_now: bool
    scheduled_time: Optional[int]  # pylint: disable=unsubscriptable-object
    to_ios: bool
    to_android: bool
    member_ids: list
    title: str
    description: str
    remarks: str


class UpdateNotificationService(BaseModel):
    # sender_id: str
    send_now: bool
    scheduled_time: Optional[int]  # pylint: disable=unsubscriptable-object
    to_ios: bool
    to_android: bool
    member_ids: list
    title: str
    description: str
