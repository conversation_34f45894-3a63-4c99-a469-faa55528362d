"""12

Revision ID: 51d0a8d7e263
Revises: 59473f8e17f6
Create Date: 2022-09-29 13:23:48.042457

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '51d0a8d7e263'
down_revision = '59473f8e17f6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_auth_external_token',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('token', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('is_temporary', sa.Boolean(), nullable=True),
    sa.Column('versions', sa.String(), nullable=True),
    sa.Column('endpoints', sa.String(), nullable=True),
    sa.Column('url', sa.String(), nullable=True),
    sa.Column('external_service_token', sa.String(), nullable=True),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['main_organization.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['main_auth_user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('external_service_token')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_auth_external_token')
    # ### end Alembic commands ###
