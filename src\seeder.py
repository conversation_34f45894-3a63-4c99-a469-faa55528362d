from contextlib import contextmanager
import secrets
import bcrypt
import typer

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app import models, crud, schema
from app.defaults import default_seeder_ids
from app.tests.factories import (OrganizationFactory, OperatorFactory, CreditCardFactory, IDTagFactory,
                                 SubscriptionFactory, SubscriptionPlanFactory, OperatorChargepointFactory,
                                 UserFactory, MembershipFactory, MembershipExtendedFactory,
                                 OrganizationAuthenticationServiceFactory)

from app.database import SessionLocal, create_session

app = typer.Typer()


def add(model, dbsession: Session, data: dict, *args, **kwargs):
    """create an object
    """
    db_object = model(**data)
    dbsession.add(db_object)
    dbsession.commit()
    dbsession.refresh(db_object)
    return db_object


def update(model, dbsession: Session, object_id, data: dict, *args, **kwargs):
    """update an object
    """
    db_object = dbsession.query(model).get(object_id)
    for key, value in data.items():
        setattr(db_object, key, value)
    dbsession.commit()
    dbsession.refresh(db_object)
    return db_object


@app.callback()
def callback():
    """
    CLI application is used in order to initialise database or update records within.
    """


def override_create_session():
    try:
        db = SessionLocal()
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        IDTagFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionPlanFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorChargepointFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipExtendedFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


PHONE_NUMBER = '+60138781000'


# pylint:disable=logging-not-lazy
@app.command()
def init():  # noqa
    """
    Init command initializes database.

    If database is empty, it will create default objects.
    """
    with contextmanager(create_session)() as dbsession:
        db_organization = dbsession.query(models.Organization).filter(models.Organization.name == 'Apollo').first()
        if db_organization:
            organization_response = typer.prompt(
                f"""\nDefault organizations found. (Name={db_organization.name}, ID={db_organization.id})
Enter value to continue:
1) Seed with root organization.
2) Seed with new organizations.
3) Quit\n""")
            if organization_response == "1":
                seed_with_root_organization(str(db_organization.id))
            elif organization_response == "2":
                seed_with_random_organizations(str(db_organization.id))
            elif organization_response == "3":
                raise typer.Exit()
            else:
                typer.echo("Only enter either 1, 2, or 3!")
                init()
        else:
            organization_response = typer.prompt(f"""\nOrganization table is empty.
Enter value to continue:
1) Seed with new random organizations.
2) Quit\n""")
            if organization_response == "1":
                seed_with_random_organizations(str(db_organization.id))
            elif organization_response == "2":
                raise typer.Exit()
            else:
                typer.echo("Only enter either 1, or 2!")
                init()


@app.command()
def seed_with_root_organization(organization_id: str):
    """
    Seeder that seeds db with root organization.
    """
    with contextmanager(override_create_session)() as dbsession:
        default_admin_user = dbsession.query(models.User).filter(models.User.email == '<EMAIL>').first()
        if default_admin_user:
            update_admin(str(default_admin_user.id), str(organization_id))
            seed_operator_for_root_organization(str(organization_id))
        else:
            raise typer.Exit()


@app.command()
def seed_with_random_organizations(parent_id: str):
    """
    Seeder that seeds db with random organization.
    """
    user_count = 0
    continue_response = typer.confirm(f"""\nSub-organizations will be seeded. Do you want to continue?\n""")
    if continue_response:
        minimum_user = typer.prompt(
            f"""\nEnter minimum number of user to be created for each organization""")
        maximum_user = typer.prompt(
            f"""Enter maximum number of user to be created for each organization""")

        try:
            minimum_user = int(minimum_user)
            maximum_user = int(maximum_user) + 1
        except ValueError:
            seed_with_random_organizations(parent_id)
            typer.echo("Number of user must be in integer format")

        for loop_index in range(5):
            with contextmanager(override_create_session)() as dbsession:
                try:
                    organization = OrganizationFactory(
                        id=default_seeder_ids.default_seeder_organizations[loop_index]['organization_id'],
                        parent_id=f'{parent_id}')
                    dbsession.commit()

                    secret_key = default_seeder_ids.default_seeder_organizations[loop_index]['secret_key']
                    OrganizationAuthenticationServiceFactory(id=organization.id, organization_id=organization.id,
                                                             secret_key=schema.argon_ph.hash(secret_key))
                    dbsession.commit()

                    operator = OperatorFactory(
                        id=default_seeder_ids.default_ids[loop_index * 5]['operator_id'],
                        organization_id=organization.id)
                    dbsession.commit()

                    for x in range(len(default_seeder_ids.default_ids)):
                        OperatorChargepointFactory(
                            charge_point_id=default_seeder_ids.default_ids[loop_index * 5 + x][
                                'charge_point_id'], operator_id=operator.id)
                        dbsession.commit()

                    for _ in range(secrets.choice(range(minimum_user, maximum_user))):
                        phone_number = '+' + str((int(PHONE_NUMBER.split("+")[1]) + user_count))
                        membership = seed_user_with_organization_id(organization.id, dbsession, phone_number)
                        user_count = user_count + 1
                        subscription = SubscriptionPlanFactory(organization_id=organization.id,
                                                               category=schema.SubscriptionPlanCategory.percentage,
                                                               amount=0.00)
                        dbsession.commit()
                        SubscriptionFactory(member_id=str(membership.id), subscription_plan_id=subscription.id,
                                            is_default=False)
                        dbsession.commit()
                    typer.echo(f'Sub organizations created.')
                except IntegrityError:
                    dbsession.rollback()
                    typer.echo('Default organization already exists')
    else:
        raise typer.Exit()


@app.command()
def update_admin(admin_user_id: str, organization_id: str):
    with contextmanager(override_create_session)() as dbsession:
        mem = dbsession.query(models.Membership).filter(
            models.Membership.user_id == admin_user_id).first()
        if mem:
            try:
                membership_id = str(mem.id)
                mem.user_id_tag = mem.user_id_tag if mem.user_id_tag else crud.create_user_id_tag(dbsession)
                dbsession.commit()
                typer.echo('Updated ID Tag for admin in membership table.')

                MembershipExtendedFactory(membership_id=membership_id)
                dbsession.commit()

                CreditCardFactory(member_id=membership_id)
                dbsession.commit()
                typer.echo('Generated Credit Card for admin.')

                IDTagFactory(member_id=membership_id, type=schema.IDTagType.rfid)
                dbsession.commit()
                typer.echo('Generated ID Tag for admin.')

                subscription = SubscriptionPlanFactory(organization_id=organization_id)
                dbsession.commit()
                typer.echo('Generated Subscription Plan for organization.')

                SubscriptionFactory(member_id=membership_id, subscription_plan_id=subscription.id, is_default=False)
                dbsession.commit()
                typer.echo('Generated Subscription for admin.')
            except IntegrityError:
                dbsession.rollback()
        else:
            typer.echo('No default admin membership')


@app.command()
def seed_operator_for_root_organization(organization_id: str):
    with contextmanager(override_create_session)() as dbsession:
        try:
            operator = OperatorFactory(organization_id=organization_id)
            dbsession.commit()
            typer.echo('Generated Operator for root organization.')
            OperatorChargepointFactory(
                charge_point_id=default_seeder_ids.default_ids[0]['charge_point_id'],
                operator_id=operator.id)
            dbsession.commit()
            typer.echo('Generated Operator Charge Point for root organization.')
        except IntegrityError:
            dbsession.rollback()


@app.command()
def seed_operator_for_sub_organization(organization_id: str, loop_index: int):
    with contextmanager(override_create_session)() as dbsession:
        try:
            typer.echo(f'SEED OPERATOR LOOP STARTED no.{loop_index}')
            for x in range(5):
                typer.echo(f'SEED OPERATOR INNER LOOP STARTED no.{loop_index * 5 + x}')
                operator = OperatorFactory(
                    id=default_seeder_ids.default_ids[loop_index * 5 + x]['operator_id'],
                    organization_id=organization_id)
                dbsession.commit()
                OperatorChargepointFactory(
                    charge_point_id=default_seeder_ids.default_ids[0]['charge_point_id'],
                    operator_id=operator.id)
                dbsession.commit()
            typer.echo(f'SEED OPERATOR LOOP ENDED no.{loop_index}')
        except IntegrityError:
            dbsession.rollback()


def seed_user_with_organization_id(organization_id: str, dbsession: Session, phone_number: str):
    try:
        user = UserFactory(is_verified=True, verification_method='phone', phone_number=phone_number)
        dbsession.commit()

        membership = MembershipFactory(organization_id=f'{organization_id}', user_id=f'{user.id}',
                                       password=bcrypt.hashpw(b'Password@123', bcrypt.gensalt(11, b'2a')).decode(
                                           'utf-8'),
                                       membership_type=schema.MembershipType.regular_user)
        dbsession.commit()

        MembershipExtendedFactory(membership_id=str(membership.id))
        dbsession.commit()

        CreditCardFactory(member_id=str(membership.id))
        dbsession.commit()

        IDTagFactory(member_id=str(membership.id), type=schema.IDTagType.rfid)
        dbsession.commit()

        return membership
    except IntegrityError:
        dbsession.rollback()


if __name__ == '__main__':
    app()
