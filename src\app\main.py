# import json
import logging
import os
import tracemalloc
import time
import random
from contextlib import contextmanager
from threading import Thread
from dotenv import load_dotenv
import pydantic
import sentry_sdk
from fastapi import FastAPI

from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.middleware.authentication import AuthenticationMiddleware
from uvicorn.middleware.proxy_headers import ProxyHeadersMiddleware
# from uvicorn.workers import UvicornWorker

from app import settings, routers

from app.routers import v2 as v2_routers
from app.database import create_session
from app.models import TraceMalloc
from app.permissions import BasicAuthBackend
from app.middlewares import RequestContextMiddleware, set_admin_as_context_user

load_dotenv()
env = os.environ

logger = logging.getLogger(__name__)

if settings.ENABLE_SENTRY:
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production.
        traces_sample_rate=1.0,
    )

ROOT_PATH = settings.MAIN_ROOT_PATH

random_number = int(random.random() * 10_000)  # nosec

if not pydantic.parse_obj_as(bool, settings.IS_DEVELOP_ENV):
    app = FastAPI(docs_url=None,
                  openapi=None,
                  redoc_url=None,
                  openapi_url=None)
else:
    app = FastAPI(docs_url=f'/{ROOT_PATH}/docs',
                  openapi_url=f"/{ROOT_PATH}/openapi.json",
                  openapi_tags=settings.API_TAGS_METADATA)

app.add_middleware(ProxyHeadersMiddleware, trusted_hosts=["*"])

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    AuthenticationMiddleware,
    backend=BasicAuthBackend()
)

app.add_middleware(RequestContextMiddleware)


@app.get(f'/{ROOT_PATH}/ping')
def read_root():
    return {'ping': True}


app.include_router(
    routers.auth_router,
)
app.include_router(
    routers.organizations_router,
)
app.include_router(
    routers.resource_router,
)
app.include_router(
    routers.organization_memberships_router,
)
app.include_router(
    routers.roles_router,
)
app.include_router(
    routers.invites_router,
)
app.include_router(
    routers.operators_router,
)
app.include_router(
    routers.charger_router,
)
app.include_router(
    routers.payments_router,
)
app.include_router(
    routers.outlier_router,
)
app.include_router(
    routers.cc_router,
)
app.include_router(
    routers.wallet_router,
)
app.include_router(
    routers.promo_code_router,
)
app.include_router(
    routers.campaign_router,
)
app.include_router(
    routers.id_tag_router,
)
app.include_router(
    routers.vehicle_router,
)
app.include_router(
    routers.vehicle_webhook,
)
app.include_router(
    routers.fav_cp_router,
)
app.include_router(
    routers.invoice_router,
)
app.include_router(
    routers.e_invoice_router,
)
app.include_router(
    routers.user_router,
)
app.include_router(
    routers.subscriptions_router,
)
app.include_router(
    routers.external_auth_router,
)
app.include_router(
    routers.ocpi_router,
)
app.include_router(
    routers.organizations_auth_service_router,
)

app.include_router(
    routers.power_cable_router,
)
app.include_router(
    routers.cybersource_router,
)
app.include_router(
    routers.enery_storage_system_router,
)
app.include_router(
    routers.certificates_management_router,
)
app.include_router(
    routers.email_report_router,
)
app.include_router(
    routers.notification_router,
)

app.include_router(
    routers.log_router,
)

app.include_router(
    v2_routers.auth_router
)

app.include_router(
    v2_routers.charging_router
)
app.include_router(
    v2_routers.organizations_router,
)
app.include_router(
    v2_routers.subscription_router
)
app.include_router(
    v2_routers.campaign_router,
)
app.include_router(
    v2_routers.user_router
)
app.include_router(
    v2_routers.invoice_router
)
app.include_router(
    v2_routers.favorite_location_router

)
app.include_router(
    v2_routers.credit_card_router
)
app.include_router(
    v2_routers.location_router
)
app.include_router(
    v2_routers.wallet_router
)
app.include_router(
    v2_routers.package_router
)
app.include_router(
    v2_routers.partner_charging_router
)
app.include_router(
    v2_routers.vehicle_router
)
app.include_router(
    v2_routers.guest_router
)
app.include_router(
    v2_routers.report_router
)
app.include_router(
    v2_routers.e_invoice_router
)


def monitor_memory():
    frame_number = 25
    tracemalloc.start(frame_number)
    while True:
        time.sleep(10)
        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.statistics('lineno')
        total_allocated_memory = sum(stat.size for stat in snapshot.statistics('traceback'))
        stat_string = f"Seed Number: {random_number}\n[ Top {frame_number} Memory Allocation ] \n"
        for stat in top_stats[:25]:
            stat_string += str(stat) + "\n"
        with contextmanager(create_session)() as dbsession:
            set_admin_as_context_user(dbsession)
            trace_malloc = TraceMalloc(content=stat_string,
                                       mem_allocated=total_allocated_memory)
            dbsession.add(trace_malloc)
            dbsession.commit()


@app.on_event("startup")
async def startup_event():
    if settings.TRACEMALLOC_TRACK:
        Thread(target=monitor_memory, daemon=True).start()


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title="Apollo API",
        version="1.0",
        description="Apollo API Reference",
        routes=app.routes,
    )
    # add Bearer Authorization to SwaggerUi
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "in": "header",
            "scheme": "bearer",
            "name": "Authorization",
        }
    }
    openapi_schema["security"] = [
        {
            "BearerAuth": []
        }
    ]

    # with open('config/charger_openapi.json', encoding="utf8") as file:
    #     charger_openapi = json.load(file)
    #
    # paths = charger_openapi['paths']
    # loop = list(paths.items())
    # for key, value in loop:
    #     if key.split('/')[4] == 'csms':
    #         new_key = key.replace('apollo-charger/api/v1/csms', 'apollo-main/api/v1/csms/charger')
    #     else:
    #         new_key = key.replace('apollo-charger/api/v1', 'apollo-main/api/v1/charger')
    #
    #     paths[new_key] = value
    #     del paths[key]
    # openapi_schema['paths'].update(paths)
    # openapi_schema['components']['schemas'].update(charger_openapi['components']['schemas'])
    #
    app.openapi_schema = openapi_schema
    return app.openapi_schema


if settings.IS_DEVELOP_ENV:
    app.openapi = custom_openapi
