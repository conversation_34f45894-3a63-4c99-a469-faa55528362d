"""168

Revision ID: e7ac535b4fff
Revises: 2f145e021709
Create Date: 2025-05-22 23:10:32.061954

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e7ac535b4fff'
down_revision = '2f145e021709'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_e_credit_note', sa.Column('e_credit_note_date_time', sa.DateTime(timezone=True), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('e_credit_note_number', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('e_invoice_date_time', sa.DateTime(timezone=True), nullable=True))
    op.add_column('main_e_invoice', sa.Column('e_invoice_number', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_e_invoice', 'e_invoice_date_time')
    op.drop_column('main_e_invoice', 'e_invoice_number')
    op.drop_column('main_e_credit_note', 'e_credit_note_date_time')
    op.drop_column('main_e_credit_note', 'e_credit_note_number')
    # ### end Alembic commands ###
