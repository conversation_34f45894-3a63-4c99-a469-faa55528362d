from contextlib import contextmanager
from uuid import uuid4

import pytest
from faker import Faker
from fastapi.testclient import TestClient

from app.constants import AUTHORIZATION_METHOD_ERRORS, AUTHORIZATION_METHOD_SUCCESS_MSG
from app.main import app, ROOT_PATH
from app.tests.factories import (UserFactory, VerificationTokenFactory, OrganizationFactory,
                                 ResourceServerFactory, ResourceFactory,
                                 RoleFactory, MembershipFactory, )
from app.database import SessionLocal, create_session, Base, engine
from app.models import Membership
from app.schema import MembershipType


fake = Faker()
client = TestClient(app)

SUCCESS_MSG = AUTHORIZATION_METHOD_SUCCESS_MSG
ERRORS = AUTHORIZATION_METHOD_ERRORS


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VerificationTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


def create_user_with_auth_token(include_user_id=False, include_membership_id=False, include_organization_id=False,
                                root_path: str = '', res_path: str = '', res_scope: str = ''):
    data = {}

    signin_data = {
        'email': '',
        'password': 'password',
    }
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            is_superuser=True,
            organization_id=organization.id
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        res_server = ResourceServerFactory(root_path=root_path)

        res = ResourceFactory(
            path=res_path,
            scope=res_scope,
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=f'{organization.id}')

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(f'{membership.id}')
        membership.roles.append(role)
        db.commit()

        if include_user_id:
            data['user_id'] = str(user.id)

        if include_membership_id:
            data['membership_id'] = str(membership.id)

        if include_organization_id:
            data['organization_id'] = str(organization.id)

        signin_data['email'] = str(user.email)
        signin_data['organization_id'] = str(organization.id)

    url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
    response = client.post(url, json=signin_data)

    data['auth_token'] = response.json()['auth_token']

    return data


# Resource Servers

def test_list_resource_servers_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource_server/?',
                                       res_scope='get,update,post')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    with contextmanager(override_create_session)() as db:
        ResourceServerFactory()
        ResourceServerFactory()
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource_server'
    response = client.get(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert len(response.json()) == 3


def test_post_resource_server_with_valid_data_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource_server/?',
                                       res_scope='get,update,post')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    valid_data = {
        'url': fake.domain_name(),
        'name': fake.pystr(),
    }

    with contextmanager(override_create_session)() as db:
        ResourceServerFactory()
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource_server'
    response = client.post(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 201
    assert response.json()['name'] == valid_data['name']
    assert response.json()['url'] == valid_data['url']


def test_post_resource_server_with_invalid_data_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource_server/?',
                                       res_scope='get,update,post')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource_server'

    valid_data = {
        'url': '',
        'name': fake.pystr(),
    }

    with contextmanager(override_create_session)() as db:
        ResourceServerFactory()
        db.commit()

    response = client.post(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 422


def test_patch_resource_server_with_valid_data_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource_server/.+',
                                       res_scope='get,patch,post')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_server_id = None
    valid_data = {
        'url': fake.domain_name(),
    }

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory()
        db.commit()

        res_server_id = res_server.id

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource_server/{res_server_id}'
    response = client.patch(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert response.json()['url'] == valid_data['url']
    assert response.json()['name'] == res_server.name


def test_patch_resource_server_with_valid_data_does_not_exist_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource_server/.+',
                                       res_scope='get,patch,post')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_server_id = None
    valid_data = {
        'url': fake.domain_name(),
    }

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory()
        db.commit()

        res_server_id = res_server.id

        db.delete(res_server)
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource_server/{res_server_id}'
    response = client.patch(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 400


def test_patch_resource_server_with_invalid_data_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource_server/.+',
                                       res_scope='get,patch,post')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_server_id = uuid4()
    valid_data = {
        'url': '',
        'name': ''
    }

    with contextmanager(override_create_session)() as db:
        ResourceServerFactory()
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource_server/{res_server_id}'
    response = client.patch(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 422
    assert len(response.json()['detail']) == 2


def test_get_resource_server_that_exists_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource_server/.+',
                                       res_scope='get,patch,post')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_server_id = None
    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory()
        db.commit()

        res_server_id = str(res_server.id)

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource_server/{res_server_id}'
    response = client.get(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert response.json()['id'] == str(res_server_id)


def test_get_resource_server_that_does_not_exist_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource_server/.+',
                                       res_scope='get,patch,post')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_server_id = None
    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory()
        db.commit()

        res_server_id = str(res_server.id)

        db.delete(res_server)
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource_server/{res_server_id}'
    response = client.get(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 400


def test_delete_existing_resource_server_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource_server/.+',
                                       res_scope='get,patch,delete')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_server_id = None
    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory()
        db.commit()

        res_server_id = str(res_server.id)

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource_server/{res_server_id}'
    response = client.delete(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 204


def test_delete_resource_server_does_not_exist_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource_server/.+',
                                       res_scope='get,patch,delete')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_server_id = None
    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory()
        db.commit()

        res_server_id = str(res_server.id)

        db.delete(res_server)
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource_server/{res_server_id}'
    response = client.delete(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 400


# # # Resources


def test_list_resources_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource/?',
                                       res_scope='get,patch,post,delete')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource'

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory()
        ResourceFactory(resourceserver=res_server)
        ResourceFactory(resourceserver=res_server)
        db.commit()

    response = client.get(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert len(response.json()) == 3


def test_post_resource_with_valid_data_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource/?',
                                       res_scope='get,patch,post')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource'

    valid_data = {
        'name': fake.pystr(),
        'path': fake.file_path(),
        'scope': 'get,',
        'resourceserver_id': '',
    }

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory()

        ResourceFactory()
        db.commit()

        valid_data['resourceserver_id'] = str(res_server.id)

    response = client.post(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 201
    assert response.json()['name'] == valid_data['name']
    assert response.json()['path'] == valid_data['path']
    assert response.json()['scope'] == valid_data['scope']


def test_post_resource_with_invalid_data_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource/?',
                                       res_scope='get,patch,post')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource'

    valid_data = {
        'name': fake.pystr(),
        'path': fake.file_path(),
    }

    with contextmanager(override_create_session)() as db:
        ResourceFactory()
        db.commit()

    response = client.post(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 422


def test_patch_resource_with_valid_data_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource/.+',
                                       res_scope='get,patch,post,delete')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_id = None
    valid_data = {
        'name': fake.domain_name(),
        'path': fake.file_path(),
        'scope': 'post,patch,put'
    }

    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory()
        res = ResourceFactory(resourceserver=res_server)
        db.commit()

        res_id = res.id

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource/{res_id}'
    response = client.patch(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert response.json()['name'] == valid_data['name']
    assert response.json()['scope'] == valid_data['scope']
    assert response.json()['path'] == valid_data['path']


def test_patch_resource_with_valid_data_does_not_exist_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource/.+',
                                       res_scope='get,patch,post,delete')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_id = None
    valid_data = {
        'name': fake.domain_name(),
    }

    with contextmanager(override_create_session)() as db:
        res = ResourceFactory()
        db.commit()

        res_id = res.id

        db.delete(res)
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource/{res_id}'
    response = client.patch(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 400


def test_patch_resource_with_invalid_data_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource/.+',
                                       res_scope='get,patch,post,delete')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_id = uuid4()
    valid_data = {
        'scope': '',
        'name': ''
    }

    with contextmanager(override_create_session)() as db:
        ResourceFactory()
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource/{res_id}'
    response = client.patch(url, json=valid_data, headers={'authorization': auth['auth_token']})

    assert response.status_code == 422
    assert len(response.json()['detail']) == 1


def test_get_resource_that_exists_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource/.+',
                                       res_scope='get,patch,post,delete')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_id = None
    with contextmanager(override_create_session)() as db:
        res_server = ResourceServerFactory()
        res = ResourceFactory(resourceserver=res_server)
        db.commit()

        res_id = str(res.id)

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource/{res_id}'
    response = client.get(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 200
    assert response.json()['id'] == str(res_id)


def test_get_resource_that_does_not_exist_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource/.+',
                                       res_scope='get,patch,post,delete')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_id = None
    with contextmanager(override_create_session)() as db:
        res = ResourceFactory()
        db.commit()

        res_id = str(res.id)

        db.delete(res)
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource/{res_id}'
    response = client.get(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 400


def test_delete_existing_resource_succeeds(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource/.+',
                                       res_scope='get,patch,post,delete')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_id = None
    with contextmanager(override_create_session)() as db:
        res = ResourceFactory()
        db.commit()

        res_id = str(res.id)

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource/{res_id}'
    response = client.delete(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 204


def test_delete_resource_does_not_exist_fails(test_db):
    auth = create_user_with_auth_token(include_user_id=True, include_membership_id=True,
                                       include_organization_id=True, root_path='apollo-main',
                                       res_path=f'{ROOT_PATH}/api/v1/csms/resources/resource/.+',
                                       res_scope='get,patch,post,delete')
    auth.pop('organization_id')
    auth.pop('user_id')
    auth.pop('membership_id')

    res_id = None
    with contextmanager(override_create_session)() as db:
        res = ResourceFactory()
        db.commit()

        res_id = str(res.id)

        db.delete(res)
        db.commit()

    url = f'{ROOT_PATH}/api/v1/csms/resources/resource/{res_id}'
    response = client.delete(url, headers={'authorization': auth['auth_token']})

    assert response.status_code == 400
