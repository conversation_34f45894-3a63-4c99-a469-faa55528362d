import logging
from typing import List
from uuid import UUID
import urllib
import json
from datetime import datetime, timedelta, timezone
from urllib.parse import urlencode
from fastapi import APIRouter, HTTPException, Depends, Request, status
from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from jose import jwt
from twilio.rest import Client
from app import exceptions, settings, crud, models
from app import schema
from app.crud.auth import MembershipCRUD
from app.crud.payment import CommercialInvoiceCRUD
from app.middlewares import set_admin_as_context_user
from app.permissions import permission, x_api_key
from app.schema import v2 as mobile_schema
from app.utils import calculate_md5_hash, decode_auth_token_from_headers, RouteErrorHandler, \
    send_welcome_email_for_bmw_and_mini_sub_plan, MAIN_URL_PREFIX
from app.database import create_session, SessionLocal
from app.constants import PAYMENT_DIRECT_API_URL

# from app.ruby_proxy_utils import sync_ruby_membership

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

twilio_client = Client(settings.TWILIO['ACCOUNT_SID'], settings.TWILIO['AUTH_TOKEN'])

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/subscriptions",
    tags=['v2 subscriptions', ],
    dependencies=[Depends(permission), Depends(x_api_key)],
    route_class=RouteErrorHandler
)


@router.get("/plan", status_code=status.HTTP_200_OK, response_model=List[mobile_schema.MobileSubscriptionPlanResponse],
            tags=['v2 subscriptions', ])
async def get_subscription_plan(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    get subscription plan based on user's member id
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        member_id = auth_token_data.get('membership_id')
    except Exception:
        raise HTTPException(401, 'Authorization token is missing.')
    try:
        mem_record = MembershipCRUD.query(dbsession).filter(models.Membership.id == member_id).one()
        # possible to remove the membership query, use basecrud membership function
        db_subscription_plans = crud.get_mobile_subscription_plan_list_v2(dbsession, mem_record.organization_id)

        for x in db_subscription_plans:
            db_subscription_plan_fees = crud.get_subscription_fee_by_plan(dbsession, str(x.id))
            x.subscription_fees = [mobile_schema.MobileSubscriptionFeeResponse.from_orm(db_subscription_plan_fee)
                                   for db_subscription_plan_fee in db_subscription_plan_fees]

        return db_subscription_plans

    except Exception:
        raise HTTPException(404, 'Membership Not Found.')


@router.get("/member", status_code=status.HTTP_200_OK, response_model=List[mobile_schema.MobileSubscriptionResponse],
            tags=['v2 subscriptions', ])
async def get_subscription(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    get user's subscribed plan
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')

    except Exception:
        raise HTTPException(401, 'Authorization token is missing.')

    try:
        subscriptions = crud.get_mobile_subscription_list(dbsession, membership_id)
        if all([x.is_default is False for x in subscriptions]):
            crud.change_member_default_plan_mobile(dbsession, str(subscriptions[0].id),
                                                   str(membership_id))

        # Query and assign has_invoice flag for mobile to display button
        subscription_plan_ids = [x.subscription_plan.id for x in subscriptions]
        db_commercial_invoices = CommercialInvoiceCRUD.query(dbsession) \
            .join(models.SubscriptionOrder, models.CommercialInvoice.subscription_order) \
            .join(models.Subscription, models.SubscriptionOrder.subscription) \
            .filter(
            models.SubscriptionOrder.member_id == membership_id,
            models.Subscription.subscription_plan_id.in_(subscription_plan_ids),
            models.SubscriptionOrder.status == schema.SubscriptionStatus.success
        ) \
            .with_entities(models.Subscription.subscription_plan_id) \
            .distinct() \
            .all()

        subscription_plans_with_invoices = {row.subscription_plan_id for row in db_commercial_invoices}

        for x in subscriptions:
            db_subscription_plan_fees = crud.get_subscription_fee_by_plan(dbsession, str(x.subscription_plan.id))
            x.subscription_plan.subscription_fees = [
                mobile_schema.MobileSubscriptionFeeResponse.from_orm(db_subscription_plan_fee)
                for db_subscription_plan_fee in db_subscription_plan_fees]
            x.has_invoice = x.subscription_plan.id in subscription_plans_with_invoices

        return subscriptions

    except Exception:
        raise HTTPException(404, 'Membership Not Found.')


@router.patch('/member/{subscription_id}', status_code=status.HTTP_200_OK,
              response_model=mobile_schema.MobileSubscriptionResponse)
async def change_member_default_plan_mobile(request: Request, subscription_id: UUID,
                                            dbsession: SessionLocal = Depends(create_session)):
    """
    Allows member to change its default plan
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
        db_subscription = crud.change_member_default_plan_mobile(dbsession, str(subscription_id), str(membership_id))
        return db_subscription
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get('/invitation/{subscription_invitation_id}', status_code=status.HTTP_200_OK,
            response_model=mobile_schema.SubscriptionInvitationResponse)
async def get_subscription_invitation_mobile(subscription_invitation_id: UUID,
                                             dbsession: SessionLocal = Depends(create_session)):
    """
    Get a subscription invitation
    """
    try:
        db_subscription_invitation = crud.get_subscription_invitation(dbsession, subscription_invitation_id)
        return db_subscription_invitation
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get('/check_invitation_code/', response_model=mobile_schema.SubscriptionInvitationResponse,
            status_code=status.HTTP_200_OK)
async def check_invitation_code_mobile(request: Request, subscription_plan_id: UUID, code: str,
                                       dbsession: SessionLocal = Depends(create_session)):
    """
    Check subscription invitation
    """
    try:
        # db_subscription_card = crud.get_non_linked_subscription_card(dbsession, subscription_plan_id)
        # if not db_subscription_card:
        #     raise HTTPException(400, 'Membership plan not available')
        db_subscription_plan = crud.get_subscription_plan(dbsession, subscription_plan_id)

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
    if not db_subscription_plan.allow_invitation_code:
        raise HTTPException(400, 'Invitation code cannot be used')

    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
        subscription_invitation = crud.check_invitation_code(dbsession, subscription_plan_id, code, membership_id)
        return subscription_invitation
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Invitation code is invalid')


@router.get('/member/details', status_code=status.HTTP_200_OK,
            response_model=mobile_schema.MobileSubscriptionPlanFullResponse)
async def get_member_subscription_details(request: Request, subscription_id: UUID,
                                          dbsession: SessionLocal = Depends(create_session)):
    """
    Get member subscription details
    """
    try:
        db_subscription = crud.get_subscription_details(dbsession, subscription_id)
        db_subscription_plan_fees = crud.get_subscription_fee_by_plan(dbsession,
                                                                      str(db_subscription.subscription_plan.id))
        db_subscription.subscription_plan.subscription_fees = [
            mobile_schema.MobileSubscriptionFeeResponse.from_orm(db_subscription_plan_fee)
            for db_subscription_plan_fee in db_subscription_plan_fees]

        db_custom_plan = crud.get_custom_plan_by_subscription_plan_id(dbsession,
                                                                      str(db_subscription.subscription_plan.id))
        if len(db_custom_plan) > 0:
            db_subscription.custom_plan = db_custom_plan

        return db_subscription
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post('/register-subscription', status_code=status.HTTP_200_OK)  # noqa: MC0001
async def register_subscription(request: Request,  # noqa: MC0001
                                register_data: mobile_schema.MobileSubscriptionRegistration,  # noqa: MC0001
                                dbsession: SessionLocal = Depends(create_session)):  # noqa: MC0001
    """
    Allows member to register to subscription plan

    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    db_member = crud.get_membership_by_id(dbsession, membership_id)
    discount_amount = 0
    # Check if subscription plan require invitation code
    data = register_data.dict()
    subscription_invitation = None

    try:
        db_subscription_card = crud.get_non_linked_subscription_card(dbsession, data['subscription_plan_id'])
        if not db_subscription_card:
            raise HTTPException(400, 'Membership plan not available')
        db_subscription_plan = crud.get_subscription_plan(dbsession, data['subscription_plan_id'])
        db_subscription_plan_fees = crud.get_subscription_fee_by_plan(dbsession, str(db_subscription_plan.id))

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
    if db_subscription_plan.is_private and data['invitation_code'] is None:
        raise HTTPException(400, 'No invitation code provided')
    if not db_subscription_plan.allow_invitation_code and data['invitation_code']:
        raise HTTPException(400, 'Invitation code cannot be used')
    # If invitation code is provided check if is exists and if is valid
    if data['invitation_code']:
        try:
            subscription_invitation = crud.get_subscription_invitation_code_mobile(dbsession, data['invitation_code'],
                                                                                   membership_id)
            discount_amount = subscription_invitation.discount_amount
        except exceptions.ApolloObjectDoesNotExist:
            subscription_invitation = None

    # if subscription plan allows for invitation
    if db_subscription_plan.allow_invitation_code:

        # if subscription plan is private but invitation is invalid
        if (db_subscription_plan.is_private and
                not (subscription_invitation and
                     subscription_invitation.subscription_plan_id == data['subscription_plan_id'])):
            raise HTTPException(400, 'Invitation code is invalid')

        # if invitation is valid
        if (subscription_invitation
                and subscription_invitation.subscription_plan_id == data['subscription_plan_id']):
            discount_amount = subscription_invitation.discount_amount
            crud.use_subscription_invitation(dbsession, membership_id, subscription_invitation.id)

    # Remove keys that are not needed for subscription model
    data = register_data.dict()
    _ = data.pop('currency')
    currency = db_subscription_plan.payment_currency or schema.Currency.myr
    payable_fee_list = data.pop('payable_fees')
    payable_fees = []

    # Map payable fees to subscription payable fees model, raise error if not valid.
    # This prevent FE Hacking Issues
    if payable_fee_list:
        for payable_fee in payable_fee_list:
            fee = next((x for x in db_subscription_plan_fees if x.name == payable_fee), None)
            if fee is None:
                raise HTTPException(400, 'Invalid payable fee')
            payable_fees.append({'amount': fee.amount,
                                 'name': fee.name.replace('_', ' ').title()})

    try:
        subscription = crud.create_subscription(dbsession, membership_id, data)
        subscription_order = crud.create_subscription_order(dbsession, subscription, currency,
                                                            membership_id, payable_fees, discount_amount)

    except (exceptions.ApolloSubscriptionError, exceptions.ApolloSubscriptionOrderError,
            exceptions.ApolloObjectDoesNotExist) as e:
        raise HTTPException(400, e.__str__())

    # Except non-paying member (for eg: BMW customer where they are given promo to no-pay)
    # except ValueError:
    if subscription_order.amount == 0:
        set_admin_as_context_user(dbsession)
        crud.activate_subscription(dbsession, str(subscription.id))
        # await sync_ruby_membership(dbsession, str(subscription.id))
        await send_welcome_email_for_bmw_and_mini_sub_plan(dbsession, subscription_order)
        return crud.get_subscription(dbsession, str(subscription.id))

    try:
        meta = json.loads(schema.MembershipResponse.from_orm(db_member).json())
        subscription_payment_request = crud.create_subscription_payment_request(dbsession, membership_id,
                                                                                subscription_order.amount,
                                                                                subscription_order.currency,
                                                                                str(subscription_order.id),
                                                                                meta)
    except exceptions.ApolloPaymentRequestError as e:
        raise HTTPException(400, e.__str__())

    if settings.PAYMENT_GATEWAY_TYPE == schema.CreditCardPaymentGateway.cybersource:
        base_url = str(request.base_url)
        if not base_url.endswith('/'):
            base_url += '/'

        domain = settings.APOLLO_MAIN_DOMAIN.rstrip('/')

        url_path = request.url_for('serve_cybersource_cc_html')
        url_path = url_path.replace(base_url, f'{domain}/')
        query_params = urllib.parse.urlencode({
            "membership_id": membership_id,
            "payment_request_id": subscription_payment_request.id
        })

        return f'{url_path}?{query_params}'

    vkey = calculate_md5_hash(f'{subscription_payment_request.amount}{settings.MERCHANT_ID}'
                              f'{subscription_payment_request.invoice_number}{settings.VERIFY_KEY}')
    params = {
        'amount': subscription_payment_request.amount,
        'orderid': subscription_payment_request.invoice_number,
        'bill_name': f'{subscription_payment_request.member.first_name} '
                     f'{subscription_payment_request.member.last_name}',
        'bill_email': subscription_payment_request.member.user.email,
        'bill_mobile': subscription_payment_request.member.user.phone_number,
        'bill_desc': subscription_payment_request.billing_description,
        'vcode': vkey,
        'channel': 'creditAN',
        'username': settings.MERCHANT_ID,
        'app_name': 'Apollo',
    }
    if currency in [schema.Currency.sgd]:
        params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
        params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
    elif currency in [schema.Currency.bnd]:
        params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
        params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
    elif currency in [schema.Currency.khr]:
        params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
        params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
    else:
        params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-return-url'
        params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-callback'

    if currency in [schema.Currency.sgd, schema.Currency.bnd, schema.Currency.khr]:
        params['channel'] = 'creditAI'  # SGD and BND use credtiAI

    url = f'{PAYMENT_DIRECT_API_URL}?' + urllib.parse.urlencode(params)
    return url


@router.post('/member/renewal/', status_code=status.HTTP_200_OK)
async def member_subscription_renewal(request: Request, subscription_id: UUID,  # noqa: MC0001
                                      dbsession: SessionLocal = Depends(create_session)):
    """
    Enable member to renew subscription
    :param subscription_id: UUID:
    """
    try:
        _ = schema.Currency = schema.Currency.myr
        discount_amount = 0

        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
        set_admin_as_context_user(dbsession)

        try:

            db_subscription = crud.get_subscription(dbsession, subscription_id)
            db_subscription_plan = crud.get_subscription_plan(dbsession, str(db_subscription.subscription_plan_id))
            # db_active_plan = crud.get_subscription_by_member_and_plan_id(dbsession, membership_id, subscription_id)
            db_subscription_plan_fees = crud.get_subscription_fee_by_plan(dbsession,
                                                                          str(db_subscription.subscription_plan_id))
            # if db_active_plan.is_active is False:
            #     raise HTTPException(400, 'Subscription Plan is no longer valid')
        except (exceptions.ApolloSubscriptionRenewalError,
                exceptions.ApolloObjectDoesNotExist) as e:
            raise HTTPException(400, e.__str__())

        fee = next((x for x in db_subscription_plan_fees if x.name == 'renewal_fee'), None)
        if fee is None:
            raise HTTPException(400, 'SubscriptionPlanFee does not exists')

        currency = db_subscription_plan.payment_currency or schema.Currency.myr

        payable_fees = [({'amount': fee.amount,
                          'name': fee.name.replace('_', ' ').title()})]

        try:
            subscription_order = crud.create_subscription_order(dbsession, db_subscription, currency,
                                                                membership_id, payable_fees, discount_amount)

        except (exceptions.ApolloSubscriptionError, exceptions.ApolloSubscriptionOrderError,
                exceptions.ApolloObjectDoesNotExist) as e:
            raise HTTPException(400, e.__str__())

        try:

            subscription_payment_request = crud.create_renewal_payment_request(dbsession, membership_id,
                                                                               fee.amount,
                                                                               currency,
                                                                               str(subscription_order.id))
        except exceptions.ApolloPaymentRequestError as e:
            raise HTTPException(400, e.__str__())

        if settings.PAYMENT_GATEWAY_TYPE == schema.CreditCardPaymentGateway.cybersource:
            base_url = str(request.base_url)
            if not base_url.endswith('/'):
                base_url += '/'

            domain = settings.APOLLO_MAIN_DOMAIN.rstrip('/')

            url_path = request.url_for('serve_cybersource_cc_html')
            url_path = url_path.replace(base_url, f'{domain}/')

            query_params = urllib.parse.urlencode({
                "membership_id": membership_id,
                "payment_request_id": subscription_payment_request.id
            })

            return f'{url_path}?{query_params}'

        vkey = calculate_md5_hash(f'{subscription_payment_request.amount}{settings.MERCHANT_ID}'
                                  f'{subscription_payment_request.invoice_number}{settings.VERIFY_KEY}')
        params = {
            'amount': subscription_payment_request.amount,
            'orderid': subscription_payment_request.invoice_number,
            'bill_name': f'{subscription_payment_request.member.first_name} '
                         f'{subscription_payment_request.member.last_name}',
            'bill_email': subscription_payment_request.member.user.email,
            'bill_mobile': subscription_payment_request.member.user.phone_number,
            'bill_desc': subscription_payment_request.billing_description,
            'vcode': vkey,
            'channel': 'creditAN',
            'username': settings.MERCHANT_ID,
            'app_name': 'Apollo',
        }
        if currency in [schema.Currency.sgd]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        elif currency in [schema.Currency.bnd]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        elif currency in [schema.Currency.khr]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        else:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-callback'

        if currency in [schema.Currency.sgd, schema.Currency.bnd, schema.Currency.khr]:
            params['channel'] = 'creditAI'  # SGD and BND use credtiAI

        url = f'{PAYMENT_DIRECT_API_URL}?' + urllib.parse.urlencode(params)
        return url

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/order/{subscription_plan_id}", status_code=status.HTTP_200_OK, tags=['v2 subscriptions', ])
async def get_subscription_invoice(request: Request, subscription_plan_id: UUID,
                                   dbsession: SessionLocal = Depends(create_session),
                                   pagination: bool = True,
                                   params: Params = Depends()):
    """
    get subscription order (invoice) based on user and plan
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
    except Exception:
        raise HTTPException(401, 'Authorization token is missing.')

    db_commercial_invoices = CommercialInvoiceCRUD.query(dbsession) \
        .join(models.SubscriptionOrder, models.CommercialInvoice.subscription_order) \
        .join(models.Subscription, models.SubscriptionOrder.subscription) \
        .filter(
        models.SubscriptionOrder.member_id == membership_id,
        models.Subscription.subscription_plan_id == subscription_plan_id,
        models.SubscriptionOrder.status == schema.SubscriptionStatus.success
    ) \
        .order_by(models.CommercialInvoice.created_at.desc())
    if pagination:
        return paginate(db_commercial_invoices, params)
    # else:
    return db_commercial_invoices.all()


@router.get("/order/pdf/url/{subscription_invoice_id}", status_code=status.HTTP_200_OK)
async def get_commercial_invoice(request: Request, subscription_invoice_id: UUID,
                                 dbsession: SessionLocal = Depends(create_session)):
    """
    return Subscription Invoice PDF URL
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    if not membership_id:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized")

    print(settings.JWT_REPORT_SECRET)

    token = jwt.encode(
        {
            "exp": datetime.now(tz=timezone.utc) + timedelta(days=1),
            "subscription_invoice_id": str(subscription_invoice_id),
            "membership_id": f'{membership_id}',
        },
        settings.JWT_REPORT_SECRET,
        algorithm=schema.JWT_ALGORITHM,
    )

    # Encode the token as a query parameter
    query_string = urlencode({'key': token, 'generate_pdf': True})
    base_path = f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}"
    url = f"{base_path}/api/v1/invoice/order/pdf/download/?{query_string}"

    return url
