"""3

Revision ID: e8b5e1fcf69e
Revises: f26c2975ec43
Create Date: 2022-07-18 13:04:06.038654

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e8b5e1fcf69e'
down_revision = 'f26c2975ec43'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('membership_vehicle_association',
    sa.Column('main_membership_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('main_vehicle_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['main_membership_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['main_vehicle_id'], ['main_vehicle.id'], ondelete='CASCADE')
    )
    op.drop_table('main_user_vehicle')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_user_vehicle',
    sa.Column('main_auth_user_id', postgresql.UUID(), autoincrement=False, nullable=True),
    sa.Column('main_vehicle_id', postgresql.UUID(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['main_auth_user_id'], ['main_auth_user.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['main_vehicle_id'], ['main_vehicle.id'], ondelete='CASCADE')
    )
    op.drop_table('membership_vehicle_association')
    # ### end Alembic commands ###
