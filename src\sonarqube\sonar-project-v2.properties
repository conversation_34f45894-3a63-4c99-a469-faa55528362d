sonar.projectKey=apollo-main-v2
sonar.projectName=apollo-main-v2
sonar.projectVersion=1.0
sonar.sources=app/routers/v2
sonar.python.coverage.reportPaths=coverage.xml
sonar.host.url=${env.SONAR_HOST_URL}
sonar.token=${env.SONAR_TOKEN_V2}
sonar.scm.exclusions.disabled=true
sonar.coverage.exclusions=**/tests/**,*/__init__.py,app/routers/v2/charger.py,app/routers/v2/location.py,\
app/routers/v2/favorite_locations.py,app/routers/v2/partner_charging_flow.py
