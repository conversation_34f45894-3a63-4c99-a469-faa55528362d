"""85

Revision ID: 2865002780e8
Revises: 30b8ef480ac4
Create Date: 2024-04-04 23:21:47.623128

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2865002780e8'
down_revision = '30b8ef480ac4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_payment_gateway_reconcilation',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('billing_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('order_id', sa.String(), nullable=True),
    sa.Column('tran_id', sa.String(), nullable=True),
    sa.Column('channel', sa.String(), nullable=True),
    sa.Column('amount', sa.Float(), nullable=True),
    sa.Column('stat_code', sa.String(), nullable=True),
    sa.Column('stat_name', sa.String(), nullable=True),
    sa.Column('billing_name', sa.String(), nullable=True),
    sa.Column('service_item', sa.String(), nullable=True),
    sa.Column('bin_number', sa.Integer(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('billing_email', sa.String(), nullable=True),
    sa.Column('transaction_rate', sa.Float(), nullable=True),
    sa.Column('gst', sa.Integer(), nullable=True),
    sa.Column('net_amount', sa.Float(), nullable=True),
    sa.Column('bank_name', sa.String(), nullable=True),
    sa.Column('settlement_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('payment_request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ),
    sa.ForeignKeyConstraint(['payment_request_id'], ['main_payment_request.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_payment_gateway_reconcilation')
    # ### end Alembic commands ###
