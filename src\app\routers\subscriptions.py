# pylint:disable=too-many-lines
from contextlib import contextmanager
import json
import logging
from datetime import datetime
from uuid import UUID
import urllib
import io
from zoneinfo import ZoneInfo

import pytz
import pandas as pd
from fastapi import APIRouter, Depends, Request, HTTPException, status, BackgroundTasks, UploadFile, File
from fastapi.responses import RedirectResponse
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate

from psycopg2.errors import UniqueViolation, ForeignKeyViolation
from sqlalchemy.orm import joinedload, Session
from sqlalchemy.exc import IntegrityError

from app import settings, schema, crud, exceptions, models
from app.celery import app as celery_app
from app.crud.reporting_task import create_reporting_task_record
from app.database import create_session, SessionLocal
from app.permissions import permission
from app.constants import PAYMENT_DIRECT_API_URL
from app.schema import ReportingTaskCreate
from app.utils import (decode_auth_token_from_headers, generate_charger_header, CHARGER_URL_PREFIX,
                       send_request, calculate_md5_hash, check_update_or_create_custom_plan, date_filters,
                       user_filters, GenerateReport, convert_meta_to_string,
                       create_or_update_tariff_plan, create_or_update_operator_plan,
                       check_update_or_create_ocpi_custom_plan,
                       topup_prepaid_wallet_plan, deduct_prepaid_wallet_batch, deduct_unused_prepaid_wallet_topup)
from app.lta_tasks import send_subscriber_list_via_email_sync

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/subscriptions",
    tags=['subscriptions', ],
    dependencies=[Depends(permission)],
)


# Subscription plan
def subscription_filters(name: str = None, default_plan: bool = None, plan: str = None):
    return {'number': name, 'is_default': default_plan, 'plan': plan}


def plan_filters(name: str = None, organization: str = None, status: bool = None):
    return {'name': name, 'organization': organization, 'status': status}


def order_filters(username: str = None, currency: str = None, status: str = None,
                  transaction_type: str = None, description: str = None):
    return {'username': username, 'currency': currency, 'status': status, 'type': transaction_type,
            'description': description}


@router.post('/plan', status_code=status.HTTP_201_CREATED, response_model=schema.SubscriptionPlanResponse)
async def add_subscription_plan(subscription_data: schema.SubscriptionPlan,
                                dbsession: SessionLocal = Depends(create_session)):
    """
    Add new subscription plan
    """
    try:
        db_subscription_plan = crud.create_subscription_plan(dbsession, subscription_data)
        return db_subscription_plan
    except exceptions.ApolloSubscriptionPlanError as e:
        raise HTTPException(400, e.__str__())


@router.patch('/plan/{subscription_plan_id}', status_code=status.HTTP_200_OK,
              response_model=schema.SubscriptionPlanResponse)
async def update_subscription_plan(subscription_plan_id: UUID, subscription_data: schema.SubscriptionPlanUpdate,
                                   dbsession: SessionLocal = Depends(create_session)):
    """
    Update a subscription plan
    """
    try:
        db_subscription_plan = crud.update_subscription_plan(dbsession, subscription_data, subscription_plan_id)
        return db_subscription_plan
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get('/plan', status_code=status.HTTP_200_OK, response_model=Page[schema.SubscriptionPlanResponse])
async def get_subscription_plan_list(dbsession: SessionLocal = Depends(create_session),
                                     params: Params = Depends(), plan_filters: dict = Depends(plan_filters)):
    """
    Get subscription plans list
    """
    db_subscription_plan = crud.get_subscription_plan_list(dbsession, plan_filters)
    return paginate(db_subscription_plan, params)


@router.get('/plan/download', status_code=status.HTTP_200_OK)
async def download_subscription_plan(dbsession: SessionLocal = Depends(create_session),
                                     plan_filters: dict = Depends(plan_filters)):
    """
    Download subscription plans
    """
    query = crud.get_subscription_plan_list(dbsession, plan_filters)
    headers = ['PLAN NAME', 'DESCRIPTION', 'ORGANIZATION', 'STATUS', 'INVITATION CODE', 'PRIVATE']
    columns = ['name', 'description', 'organization.name', 'is_active', 'allow_invitation_code', 'is_private']

    report = GenerateReport("subscription_plan", header=headers, columns=columns, query=query)
    await report.generate_dataframe_with_query(schema=schema.SubscriptionPlanDownload, join='organization')
    await report.nan_handling('is_active', 'Active', 'True')
    await report.nan_handling('is_active', 'In-active', 'False')
    return await report.generate_report()


@router.get('/plan/{subscription_plan_id}', status_code=status.HTTP_200_OK,
            response_model=schema.SubscriptionPlanResponse)
async def get_subscription_plan(subscription_plan_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Get a subscription plan
    """
    try:
        db_subscription_plan = crud.get_subscription_plan(dbsession, subscription_plan_id)
        return db_subscription_plan
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete('/plan/{subscription_plan_id}', status_code=status.HTTP_204_NO_CONTENT)
async def delete_subscription_plan(subscription_plan_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Delete a subscription plan
    """
    try:
        crud.delete_subscription_plan(dbsession, subscription_plan_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Subscription Fee

@router.post('/fee', status_code=status.HTTP_201_CREATED, response_model=schema.SubscriptionFeeResponse)
async def add_subscription_fee(subscription_fee_data: schema.SubscriptionFee,
                               dbsession: SessionLocal = Depends(create_session)):
    """
    Add new subscription fee
    """
    try:
        db_subscription_fee = crud.create_subscription_fee(dbsession, subscription_fee_data)
        return db_subscription_fee
    except exceptions.ApolloSubscriptionFeeError as e:
        raise HTTPException(400, e.__str__())


@router.patch('/fee/{subscription_fee_id}', status_code=status.HTTP_200_OK,
              response_model=schema.SubscriptionFeeResponse)
async def update_subscription_fee(subscription_fee_id: UUID,
                                  subscription_fee_data: schema.SubscriptionFeeUpdate,
                                  dbsession: SessionLocal = Depends(create_session)):
    """
    Update a subscription fee
    """
    try:
        db_subscription_fee = crud.update_subscription_fee(dbsession, subscription_fee_data, subscription_fee_id)
        return db_subscription_fee
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get('/fee', status_code=status.HTTP_200_OK, response_model=Page[schema.SubscriptionFeeResponse])
async def get_subscription_fee_list(dbsession: SessionLocal = Depends(create_session),
                                    subscription_plan_id: str = None,
                                    params: Params = Depends()):
    """
    Get subscription fees list
    """
    db_subscription_fee_list = crud.get_subscription_fee_list(dbsession, subscription_plan_id)
    return paginate(db_subscription_fee_list, params)


@router.get('/fee/{subscription_fee_id}', status_code=status.HTTP_200_OK,
            response_model=schema.SubscriptionFeeResponse)
async def get_subscription_fee(subscription_fee_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Get a subscription fee
    """
    try:
        db_subscription_fee = crud.get_subscription_fee(dbsession, subscription_fee_id)
        return db_subscription_fee
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete('/fee/{subscription_fee_id}', status_code=status.HTTP_204_NO_CONTENT)
async def delete_subscription_fee(subscription_fee_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Delete a subscription fee
    """
    try:
        crud.delete_subscription_fee(dbsession, subscription_fee_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Subscription custom plan


@router.post('/custom-plan', status_code=status.HTTP_201_CREATED, response_model=schema.SubscriptionCustomPlanResponse)
async def add_subscription_custom_plan(request: Request, subscription_data: schema.SubscriptionCustomPlan,
                                       dbsession: SessionLocal = Depends(create_session)):
    """
    Add new subscription custom plan
    """
    data = dict(subscription_data)
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    query_params = request.query_params
    url = f"{CHARGER_URL_PREFIX}/connector/{data['connector_id']}"
    response = await send_request('GET', url=url, headers=headers, query_params=query_params)
    if response.status_code == 200:
        connector = response.json()
        data['connector_number'] = connector['number']
        data['charger_point_id'] = connector['charge_point']['charge_box_serial_number']
        billing_unit_fee = connector['billing_unit_fee']

        return check_update_or_create_custom_plan(dbsession, data, billing_unit_fee, data['amount'],
                                                  'CREATE', subscription_custom_plan_id=None)

    raise HTTPException(400, "Cant get connector data")


@router.patch('/custom-plan/{subscription_custom_plan_id}', status_code=status.HTTP_200_OK,
              response_model=schema.SubscriptionCustomPlanResponse)
async def update_subscription_custom_plan(request: Request, subscription_custom_plan_id: UUID,
                                          subscription_data: schema.SubscriptionCustomPlanUpdate,
                                          dbsession: SessionLocal = Depends(create_session)):
    """
    Update a subscription custom plan
    """
    data = subscription_data.dict(exclude_unset=True, exclude_defaults=True)
    try:
        db_custom_plan = crud.get_subscription_custom_plan(dbsession, subscription_custom_plan_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Invalid Custom Plan ID')

    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    query_params = request.query_params

    if 'connector_id' in data:
        url = f"{CHARGER_URL_PREFIX}/connector/{data['connector_id']}"
    else:
        url = f"{CHARGER_URL_PREFIX}/connector/{db_custom_plan.connector_id}"

    response = await send_request('GET', url=url, headers=headers, query_params=query_params)

    if response.status_code == 200:
        connector = response.json()
        data['connector_number'] = connector['number']
        data['charger_point_id'] = connector['charge_point']['charge_box_serial_number']
        billing_unit_fee = connector['billing_unit_fee']
    else:
        raise HTTPException(400, "Cant get connector data")

    if 'amount' in data:
        new_custom_plan_amount = data['amount']
    else:
        new_custom_plan_amount = db_custom_plan.amount

    return check_update_or_create_custom_plan(dbsession, data, billing_unit_fee, new_custom_plan_amount,
                                              'UPDATE', subscription_custom_plan_id)


@router.get('/custom-plan', status_code=status.HTTP_200_OK, response_model=Page[schema.SubscriptionCustomPlanResponse])
async def get_subscription_custom_plan_list(dbsession: SessionLocal = Depends(create_session),
                                            subscription_plan_id: str = None, charger_point_id: str = None,
                                            params: Params = Depends()):
    """
    Get subscription custom plans list
    """
    db_subscription_custom_plan = crud.get_subscription_custom_plan_list(dbsession,
                                                                         subscription_plan_id,
                                                                         charger_point_id)
    return paginate(db_subscription_custom_plan, params)


@router.get('/custom-plan/download', status_code=status.HTTP_200_OK)
async def download_subscription_custom_plan(dbsession: SessionLocal = Depends(create_session),
                                            subscription_plan_id: str = None, charger_point_id: str = None):
    """
    Download subscription plans
    """
    query = crud.get_subscription_custom_plan_list(dbsession, subscription_plan_id, charger_point_id)
    headers = ['CHARGE POINT', 'CONNECTOR', 'SUBSCRIPTION PLAN', 'DISCOUNT RATE', 'CREATED DATE']
    columns = ['charger_point_id', 'connector_number', 'subscription_plan.name', 'amount', 'created_at']

    report = GenerateReport("subscription_custom_plan", header=headers, columns=columns, query=query)
    await report.generate_dataframe_with_query(schema=schema.SubscriptionCustomPlanResponse, join='subscription_plan')
    await report.datetime_reformat('created_at')
    return await report.generate_report()


@router.get('/custom-plan/{subscription_custom_plan_id}', status_code=status.HTTP_200_OK,
            response_model=schema.SubscriptionCustomPlanResponse)
async def get_subscription_custom_plan(subscription_custom_plan_id: UUID,
                                       dbsession: SessionLocal = Depends(create_session)):
    """
    Get a subscription custom plan
    """
    try:
        db_subscription_custom_plan = crud.get_subscription_custom_plan(dbsession, subscription_custom_plan_id)
        return db_subscription_custom_plan
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete('/custom-plan/{subscription_custom_plan_id}', status_code=status.HTTP_204_NO_CONTENT)
async def delete_subscription_custom_plan(subscription_custom_plan_id: UUID,
                                          dbsession: SessionLocal = Depends(create_session)):
    """
    Delete a subscription custom plan
    """
    try:
        crud.delete_subscription_custom_plan(dbsession, subscription_custom_plan_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Tariff Tag


@router.post(
    '/tariff-tag',
    status_code=status.HTTP_201_CREATED,
    response_model=schema.TariffTagResponse
)
async def add_tariff_tag(
        payload: schema.TariffTagCreate,
        db: Session = Depends(create_session),
):
    try:
        return crud.create_tariff_tag(db, payload)
    except exceptions.ApolloTariffTagError as e:
        raise HTTPException(409, f"{e} Tag name: {payload.name} already exists.")


@router.patch(
    "/tariff-tag/{tariff_tag_id}",
    status_code=status.HTTP_200_OK,
    response_model=schema.TariffTagResponse,
)
async def update_tariff_tag(
        tariff_tag_id: UUID,
        payload: schema.TariffTagUpdate,
        db: Session = Depends(create_session),
):
    try:
        return crud.update_tariff_tag(db, tariff_tag_id, payload)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(404, str(e))
    except IntegrityError:
        raise HTTPException(409, f"Tag name: {payload.name} already exists.")


@router.get(
    "/tariff-tag",
    status_code=status.HTTP_200_OK,
    response_model=Page[schema.TariffTagResponse],
)
async def get_tariff_tag_list(
        name: str = None,
        params: Params = Depends(),
        db: Session = Depends(create_session),
):
    query = crud.get_tariff_tag(db, name=name)
    return paginate(query, params)


@router.get(
    "/tariff-tag/{tariff_tag_id}",
    status_code=status.HTTP_200_OK,
    response_model=schema.TariffTagResponse,
)
async def get_tariff_tag(
        tariff_tag_id: UUID,
        db: Session = Depends(create_session),
):
    try:
        tariff_tag = crud.get_tariff_tag(db, tariff_tag_id=tariff_tag_id)
        return tariff_tag
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, str(e))


@router.delete(
    "/tariff-tag/{tariff_tag_id}",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_tariff_tag(
        tariff_tag_id: UUID,
        db: Session = Depends(create_session),
):
    try:
        crud.delete_tariff_tag(db, tariff_tag_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, str(e))


# Subscription Tariff plan


@router.post(
    "/tariff-plan",
    status_code=status.HTTP_201_CREATED,
    response_model=schema.SubscriptionTariffPlanResponse,
)
async def add_subscription_tariff_plan(
        payload: schema.SubscriptionTariffPlanCreate,
        db: Session = Depends(create_session),
):
    return create_or_update_tariff_plan(db, payload)


@router.patch(
    "/tariff-plan/{subscription_tariff_plan_id}",
    status_code=status.HTTP_200_OK,
    response_model=schema.SubscriptionTariffPlanResponse,
)
async def update_subscription_tariff_plan(
        subscription_tariff_plan_id: UUID,
        payload: schema.SubscriptionTariffPlanUpdate,
        db: Session = Depends(create_session),
):
    return create_or_update_tariff_plan(db, payload, subscription_tariff_plan_id)


@router.get(
    "/tariff-plan",
    status_code=status.HTTP_200_OK,
    response_model=Page[schema.SubscriptionTariffPlanResponse],
)
async def get_subscription_tariff_plan_list(
        tariff_tag: str = None,
        subscription_plan_id: UUID = None,
        remark: str = None,
        params: Params = Depends(),
        db: Session = Depends(create_session),
):
    query = crud.get_subscription_tariff_plan(
        db=db,
        subscription_plan_id=subscription_plan_id,
        tariff_tag=tariff_tag,
        remark=remark,
    )
    return paginate(query, params)


@router.get(
    "/tariff-plan/{subscription_tariff_plan_id}",
    status_code=status.HTTP_200_OK,
    response_model=schema.SubscriptionTariffPlanResponse,
)
async def get_subscription_tariff_plan(
        subscription_tariff_plan_id: UUID,
        db: Session = Depends(create_session),
):
    try:
        tariff_plan = crud.get_subscription_tariff_plan(
            db=db,
            subscription_tariff_plan_id=subscription_tariff_plan_id,
        )
        return tariff_plan
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete(
    "/tariff-plan/{subscription_tariff_plan_id}",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_subscription_tariff_plan(
        subscription_tariff_plan_id: UUID,
        db: Session = Depends(create_session),
):
    try:
        crud.delete_subscription_tariff_plan(db, subscription_tariff_plan_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Subscription Operator Plan


@router.post(
    "/operator-plan",
    status_code=status.HTTP_201_CREATED,
    response_model=schema.SubscriptionOperatorPlanResponse,
)
async def add_subscription_operator_plan(
        payload: schema.SubscriptionOperatorPlanCreate,
        db: Session = Depends(create_session),
):
    return create_or_update_operator_plan(db, payload)


@router.patch(
    "/operator-plan/{subscription_operator_plan_id}",
    status_code=status.HTTP_200_OK,
    response_model=schema.SubscriptionOperatorPlanResponse,
)
async def update_subscription_operator_plan(
        subscription_operator_plan_id: UUID,
        payload: schema.SubscriptionOperatorPlanUpdate,
        db: Session = Depends(create_session),
):
    return create_or_update_operator_plan(db, payload, subscription_operator_plan_id)


@router.get(
    "/operator-plan",
    status_code=status.HTTP_200_OK,
    response_model=Page[schema.SubscriptionOperatorPlanResponse],
)
async def get_subscription_operator_plan_list(
        operator_name: str = None,
        subscription_plan_id: UUID = None,
        remark: str = None,
        params: Params = Depends(),
        db: Session = Depends(create_session),
):
    query = crud.get_subscription_operator_plan(
        db=db,
        subscription_plan_id=subscription_plan_id,
        operator_name=operator_name,
        remark=remark,
    )
    return paginate(query, params)


@router.get(
    "/operator-plan/{subscription_operator_plan_id}",
    status_code=status.HTTP_200_OK,
    response_model=schema.SubscriptionOperatorPlanResponse,
)
async def get_subscription_operator_plan(
        subscription_operator_plan_id: UUID,
        db: Session = Depends(create_session),
):
    try:
        operator_plan = crud.get_subscription_operator_plan(
            db=db,
            subscription_operator_plan_id=subscription_operator_plan_id,
        )
        return operator_plan
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete(
    "/operator-plan/{subscription_operator_plan_id}",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_subscription_operator_plan(
        subscription_operator_plan_id: UUID,
        db: Session = Depends(create_session),
):
    try:
        crud.delete_subscription_operator_plan(db, subscription_operator_plan_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Subscription Card


@router.post('/card', status_code=status.HTTP_201_CREATED, response_model=schema.SubscriptionCardResponse)
async def add_subscription_card(subscription_card_data: schema.SubscriptionCard,
                                dbsession: SessionLocal = Depends(create_session)):
    """
    Add new subscription card
    """
    try:
        db_subscription_card = crud.create_subscription_card(dbsession, subscription_card_data)
        return db_subscription_card
    except exceptions.ApolloSubscriptionCardError as e:
        raise HTTPException(400, e.__str__())


@router.patch('/card/{subscription_card_id}', status_code=status.HTTP_200_OK,
              response_model=schema.SubscriptionCardResponse)
async def update_subscription_card(subscription_card_id: UUID,
                                   subscription_card_data: schema.SubscriptionCardUpdate,
                                   dbsession: SessionLocal = Depends(create_session)):
    """
    Update a subscription card
    """
    try:
        db_subscription_card = crud.update_subscription_card(dbsession, subscription_card_data, subscription_card_id)
        return db_subscription_card
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get('/card', status_code=status.HTTP_200_OK, response_model=Page[schema.SubscriptionCardResponse])
async def get_subscription_card_list(dbsession: SessionLocal = Depends(create_session),
                                     subscriber_id: str = None, id_tag: str = None, plan: str = None,
                                     params: Params = Depends(), date_filters: dict = Depends(date_filters)):
    """
    Get subscription cards list
    """
    query = crud.get_subscription_card_list(dbsession, date_filters, subscriber_id, id_tag, plan)
    return paginate(query, params)


@router.get('/card/download', status_code=status.HTTP_200_OK)
async def download_subscription_card(dbsession: SessionLocal = Depends(create_session),
                                     subscriber_id: str = None, id_tag: str = None, plan: str = None,
                                     date_filters: dict = Depends(date_filters)):
    """
    Download subscription cards
    """
    query = crud.get_subscription_card_list(dbsession, date_filters, subscriber_id, id_tag, plan)
    headers = ['SUBSCRIBER ID', 'ID TAG', 'PLAN', 'SUBSCRIBED DATE', 'CREATED DATE']
    columns = ['number', 'id_tag', 'subscription_plan.name', 'linked_at', 'created_at']

    report = GenerateReport("subscription_card", header=headers, columns=columns, query=query)
    await report.generate_dataframe_with_query(schema=schema.SubscriptionCardResponse, join='subscription_plan')
    await report.datetime_reformat('linked_at')
    await report.datetime_reformat('created_at')
    return await report.generate_report()


@router.get('/card/{subscription_card_id}', status_code=status.HTTP_200_OK,
            response_model=schema.SubscriptionCardResponse)
async def get_subscription_card(subscription_card_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Get a subscription card
    """
    try:
        db_subscription_card = crud.get_subscription_card(dbsession, subscription_card_id)
        return db_subscription_card
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete('/card/{subscription_card_id}', status_code=status.HTTP_204_NO_CONTENT)
async def delete_subscription_card(subscription_card_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Delete a subscription card
    """
    try:
        crud.delete_subscription_card(dbsession, subscription_card_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Subscription Invitation


@router.post('/invitation', status_code=status.HTTP_201_CREATED, response_model=schema.SubscriptionInvitationResponse)
async def add_subscription_invitation(subscription_invitation_data: schema.SubscriptionInvitation,
                                      dbsession: SessionLocal = Depends(create_session)):
    """
    Add new subscription invitation
    """
    try:
        db_subscription_invitation = crud.create_subscription_invitation(dbsession, subscription_invitation_data)
        return db_subscription_invitation
    except exceptions.ApolloSubscriptionInvitationError as e:
        raise HTTPException(400, e.__str__())


@router.patch('/invitation/{subscription_invitation_id}', status_code=status.HTTP_200_OK,
              response_model=schema.SubscriptionInvitationResponse)
async def update_subscription_invitation(subscription_invitation_id: UUID,
                                         subscription_invitation_data: schema.SubscriptionInvitationUpdate,
                                         dbsession: SessionLocal = Depends(create_session)):
    """
    Update a subscription invitation
    """
    try:
        db_subscription_invitation = crud.update_subscription_invitation(dbsession, subscription_invitation_data,
                                                                         subscription_invitation_id)
        return db_subscription_invitation
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get('/invitation', status_code=status.HTTP_200_OK, response_model=Page[schema.SubscriptionInvitationResponse])
async def get_subscription_invitation_list(dbsession: SessionLocal = Depends(create_session),
                                           subscription_plan_id: str = None, code: str = None, username: str = None,
                                           params: Params = Depends()):
    """
    Get subscription invitations list
    """
    query = crud.get_subscription_invitation_list(dbsession, subscription_plan_id, code, username)
    return paginate(query, params)


@router.get('/invitation/download', status_code=status.HTTP_200_OK)
async def download_subscription_invitation(dbsession: SessionLocal = Depends(create_session),
                                           subscription_plan_id: str = None, code: str = None, username: str = None):
    """
    Download subscription invitations
    """
    query = crud.get_subscription_invitation_list(dbsession, subscription_plan_id, code, username)
    headers = ['CODE', 'DISCOUNT', 'USERNAME', 'LINKED DATE']
    columns = ['code', 'discount_amount', 'member.first_name', 'member.last_name', 'linked_at']
    reorder_columns = ['code', 'discount_amount', 'full_name', 'linked_at']

    report = GenerateReport("subscription_invitation_code", header=headers, columns=columns,
                            reorder_columns=reorder_columns, query=query)
    await report.generate_dataframe_with_query(schema=schema.SubscriptionInvitationResponse, join='member')
    await report.operation('full_name', lambda x, y: f'{x} {y}', 'member.first_name', 'member.last_name')
    await report.nan_handling('full_name', 'Not Available', 'nan nan')
    await report.datetime_reformat('linked_at')
    return await report.generate_report()


@router.get('/invitation/{subscription_invitation_id}', status_code=status.HTTP_200_OK,
            response_model=schema.SubscriptionInvitationResponse)
async def get_subscription_invitation(subscription_invitation_id: UUID,
                                      dbsession: SessionLocal = Depends(create_session)):
    """
    Get a subscription invitation
    """
    try:
        db_subscription_invitation = crud.get_subscription_invitation(dbsession, subscription_invitation_id)
        return db_subscription_invitation
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete('/invitation/{subscription_invitation_id}', status_code=status.HTTP_204_NO_CONTENT)
async def delete_subscription_invitation(subscription_invitation_id: UUID,
                                         dbsession: SessionLocal = Depends(create_session)):
    """
    Delete a subscription invitation
    """
    try:
        crud.delete_subscription_invitation(dbsession, subscription_invitation_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Subscription - admin


@router.post('/admin/{membership_id}', status_code=status.HTTP_201_CREATED, response_model=schema.SubscriptionResponse)
async def register_member_to_subscription_plan_by_admin(membership_id: UUID,
                                                        subscription_data: schema.SubscriptionAdmin,
                                                        dbsession: SessionLocal = Depends(create_session)):
    """
    Allows admin to register member to particular subscription plan
    """

    data = dict(subscription_data)
    try:
        db_subscription_card = crud.get_non_linked_subscription_card_by_number(dbsession, subscription_data.number,
                                                                               subscription_data.subscription_plan_id)
        if not db_subscription_card:
            card_data = schema.SubscriptionCard(subscription_plan_id=subscription_data.subscription_plan_id,
                                                number=subscription_data.number)
            db_subscription_card = crud.create_subscription_card(dbsession, card_data)

        data['subscription_card_id'] = db_subscription_card.id
        db_subscription = crud.create_subscription(dbsession, membership_id, data)
        crud.activate_member_subscription_by_admin(dbsession, db_subscription, db_subscription_card)
        return db_subscription
    except exceptions.ApolloSubscriptionError as e:
        raise HTTPException(400, e.__str__())
    except exceptions.ApolloSubscriptionCardError as e:
        raise HTTPException(400, e.__str__())


@router.patch('/admin/{subscription_id}', status_code=status.HTTP_200_OK, response_model=schema.SubscriptionResponse)
async def update_subscription(subscription_id: UUID, subscription_data: schema.SubscriptionUpdate,
                              dbsession: SessionLocal = Depends(create_session)):
    """
    Allows admin to update particular subscription
    """
    try:
        db_subscription = crud.update_subscription(dbsession, subscription_id,
                                                   subscription_data.dict(exclude_unset=True, exclude_defaults=True))
        return db_subscription
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete('/admin/{subscription_id}', status_code=status.HTTP_204_NO_CONTENT)
async def delete_subscription(subscription_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Allows admin to delete particular subscription
    """
    try:
        crud.delete_subscription(dbsession, subscription_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get('/order', status_code=status.HTTP_200_OK, response_model=Page[schema.SubscriptionOrderResponse])
async def get_subscription_order_list(dbsession: SessionLocal = Depends(create_session),
                                      params: Params = Depends(), date_filters: dict = Depends(date_filters),
                                      order_filters: dict = Depends(order_filters)):
    """
    Allows retrieving subscription order list based on user privileges
    """
    query = crud.get_subscription_order_list(dbsession, date_filters, order_filters)
    return paginate(query, params)


@router.get('/order/download', status_code=status.HTTP_200_OK)
async def download_subscription_order(dbsession: SessionLocal = Depends(create_session),
                                      date_filters: dict = Depends(date_filters),
                                      order_filters: dict = Depends(order_filters)):
    """
    Download subscription order
    """
    query = crud.get_subscription_order_list(dbsession, date_filters, order_filters)
    query = query.options(joinedload(models.SubscriptionOrder.subscription))
    headers = ['USER NAME', 'DESCRIPTION', 'TOTAL AMOUNT', 'CURRENCY',
               'PAYMENT STATUS', 'TRANSACTION TYPE', 'ORDER DATE', 'ADDRESS']
    columns = ['member.first_name', 'member.last_name', 'payable_fees',
               'amount', 'currency', 'status', 'created_at', 'subscription.meta.city',
               'subscription.meta.state', 'subscription.meta.country', 'subscription.meta.address_1',
               'subscription.meta.address_2', 'subscription.meta.postal_code']
    reorder_columns = ['full_name', 'payable_fees', 'amount', 'currency', 'status', 'type', 'created_at',
                       'address']

    report = GenerateReport("order", header=headers, columns=columns,
                            reorder_columns=reorder_columns, query=query)
    await report.generate_dataframe_with_query(schema=schema.SubscriptionOrderResponse, join='member')
    await report.operation('full_name', lambda x, y: f'{x} {y}', 'member.first_name', 'member.last_name')
    await report.combine_multiple_col('address', ['subscription.meta.address_1', 'subscription.meta.address_2',
                                                  'subscription.meta.postal_code', 'subscription.meta.city',
                                                  'subscription.meta.state', 'subscription.meta.country'])
    await report.nan_handling('full_name', 'Not Available', 'nan nan')
    await report.create_static_col('type', 'Subscription')
    await report.datetime_reformat('created_at')
    await report.operation('payable_fees', convert_meta_to_string, 'payable_fees', 'payable_fees')
    return await report.generate_report()


# Subscription - member


@router.get('/member', status_code=status.HTTP_200_OK, response_model=Page[schema.SubscriptionResponse])
async def get_member_subscription_plans_list(request: Request, dbsession: SessionLocal = Depends(create_session),
                                             subscription_plan_id: str = None,
                                             subscriber_id: str = None,
                                             member_id: str = None,
                                             user_filters: dict = Depends(user_filters),
                                             date_filters: dict = Depends(date_filters),
                                             subscription_filters: dict = Depends(subscription_filters),
                                             params: Params = Depends()):
    """
    Allows member to get its subscription plans list
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    query = crud.get_subscription_list(dbsession, subscription_plan_id, date_filters,
                                       subscriber_id, member_id, user_filters, subscription_filters, membership_id)
    return paginate(query, params)


@router.get('/member/download', status_code=status.HTTP_200_OK)
async def download_member_subscription_plans(request: Request,  # pylint: disable=too-many-locals
                                             dbsession: SessionLocal = Depends(create_session),
                                             subscription_plan_id: str = None,
                                             subscriber_id: str = None,
                                             member_id: str = None,
                                             user_filters: dict = Depends(user_filters),
                                             date_filters: dict = Depends(date_filters),
                                             subscription_filters: dict = Depends(subscription_filters),
                                             filename: str = None, send_to: str = None, ):
    """
    Download member subscription plans
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    headers = ['SUBSCRIBER ID', 'EMAIL', 'PHONE NUMBER', 'STATUS', 'FULL NAME', 'TNC OPTION', 'PLAN',
               'INVITATION CODE', 'DEFAULT PLAN', 'CREATED DATE', 'START DATE', 'EXPIRY DATE', 'VEHICLE MODEL', 'CITY',
               'STATE', 'COUNTRY', 'ADDRESS1', 'ADDRESS2', 'POSTAL CODE', 'ALLOW MARKETING', 'DELIVERY OPTION',
               'BUSINESS ARRANGEMENT', 'VEHICLE MANUFACTURER', 'IDENTIFICATION NUMBER', 'AMOUNT', 'PAYABLE FEES']
    columns = ['id', 'number', 'member.user.email', 'member.user.phone_number', 'subscription_plan.name',
               'member.first_name', 'member.last_name', 'invitation_code', 'is_default', 'start_date', 'end_date',
               'meta.vehicle_model', 'meta.city', 'meta.state', 'meta.country', 'meta.address_1', 'meta.address_2',
               'meta.postal_code', 'meta.business_arrangement', 'meta.allow_marketing', 'meta.delivery_option',
               'meta.vehicle_manufacturer', 'meta.identification_number', 'status', 'meta.tnc_option', 'created_at']

    reorder_columns = ['number', 'member.user.email', 'member.user.phone_number', 'status', 'full_name',
                       'meta.tnc_option', 'subscription_plan.name', 'invitation_code', 'is_default', 'created_at',
                       'start_date', 'end_date', 'meta.vehicle_model', 'meta.city', 'meta.state', 'meta.country',
                       'meta.address_1', 'meta.address_2', 'meta.postal_code', 'meta.allow_marketing',
                       'meta.delivery_option', 'meta.business_arrangement', 'meta.vehicle_manufacturer',
                       'meta.identification_number', 'amount', 'payable_fees']

    request_data = {
        "headers": dict(request.headers)
    }

    if not filename:
        tz = pytz.timezone('Asia/Kuala_Lumpur')
        current_datetime = datetime.now(tz)
        format_datetime = current_datetime.strftime("%d.%m.%Y_(%H:%M)")
        filename = 'subscriber_' + format_datetime

    if send_to:
        if 'from_date' in date_filters and isinstance(date_filters['from_date'], datetime):
            date_filters['from_date'] = date_filters['from_date'].isoformat()  # Convert to string
        if 'to_date' in date_filters and isinstance(date_filters['to_date'], datetime):
            date_filters['to_date'] = date_filters['to_date'].isoformat()  # Convert to string
        message = {
            'subscription_plan_id': subscription_plan_id,
            'user_filters': user_filters,
            'date_filters': date_filters,
            'subscription_filters': subscription_filters,
            'subscriber_id': subscriber_id,
            'member_id': member_id,
            'request_data': request_data,
            'headers': headers,
            'columns': columns,
            'reorder_columns': reorder_columns,
            'filename': filename,
            'email': send_to,
        }

        task_data = ReportingTaskCreate(
            task_name="subscriber_list",
            json_payload=message,
            retry_count=0
        )

        reporting_task = create_reporting_task_record(dbsession, task_data)
        task_uid = reporting_task.id
        message['task_uid'] = task_uid
        send_subscriber_list_via_email_sync.delay(message=message)
        return {'message': 'The report will be ready in 15 minutes.'}

    query = crud.get_subscription_list(dbsession, subscription_plan_id, date_filters,
                                       subscriber_id, member_id, user_filters, subscription_filters, membership_id)
    extra_query = crud.get_subscription_order_list(dbsession)

    report = GenerateReport("subscribers", header=headers, columns=columns,
                            reorder_columns=reorder_columns, query=query)
    await report.generate_dataframe_with_query(schema=schema.SubscriptionResponse,
                                               multiple_join=['member', 'subscription_plan'])
    await report.extra_data_with_query(query=extra_query, schema=schema.SubscriptionOrderDownload,
                                       left_on='id', right_on='subscription_id', ignore_key_error=True)
    await report.drop_duplicate('id')
    await report.nan_handling('is_default', 'Active', 'True')
    await report.nan_handling('is_default', 'In-active', 'False')
    await report.operation('full_name', lambda x, y: f'{x} {y}', 'member.first_name', 'member.last_name')
    await report.operation('payable_fees', convert_meta_to_string, 'payable_fees', 'payable_fees',
                           ignore_key_error=True)
    await report.datetime_reformat('created_at')
    await report.datetime_reformat_default('start_date')
    await report.datetime_reformat_default('end_date')
    return await report.generate_report()


@router.post('/member', status_code=status.HTTP_201_CREATED, response_model=schema.SubscriptionResponse)
async def register_member_to_subscription_plan(request: Request,  # noqa: MC0001
                                               subscription_data: schema.SubscriptionRegistration,
                                               dbsession: SessionLocal = Depends(create_session)):
    """
    Allows member to register to subscription plan
    """

    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    db_member = crud.get_membership_by_id(dbsession, membership_id)
    # Check if subscription plan require invitation code
    data = subscription_data.dict()
    discount_amount = 0
    try:
        db_subscription_plan = crud.get_subscription_plan(dbsession, subscription_data.subscription_plan_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())

    # If invitation code is provided check if is exists and if is valid
    if subscription_data.invitation_code:
        try:
            subscription_invitation = crud.get_subscription_invitation_by_code(dbsession,
                                                                               subscription_data.invitation_code)
            discount_amount = subscription_invitation.discount_amount
        except exceptions.ApolloObjectDoesNotExist:
            subscription_invitation = None

    # if subscription plan allows for invitation
    if db_subscription_plan.allow_invitation_code:

        # if subscription plan is private but invitation is invalid
        if (db_subscription_plan.is_private and
                not (subscription_invitation and
                     subscription_invitation.subscription_plan_id == subscription_data.subscription_plan_id)):
            raise HTTPException(400, 'Invitation code is invalid')

        # if invitation is valid
        if (subscription_invitation
                and subscription_invitation.subscription_plan_id == subscription_data.subscription_plan_id):
            discount_amount = subscription_invitation.discount_amount
            crud.use_subscription_invitation(dbsession, membership_id, subscription_invitation.id)

    # Remove keys that are not needed for subscription model
    currency = data.pop('currency')
    payable_fee_list = data.pop('payable_fees')

    try:
        subscription = crud.create_subscription(dbsession, membership_id, data)
        subscription_order = crud.create_subscription_order(dbsession, subscription, currency,
                                                            membership_id, payable_fee_list, discount_amount)
    except (exceptions.ApolloSubscriptionError, exceptions.ApolloSubscriptionOrderError,
            exceptions.ApolloObjectDoesNotExist) as e:
        raise HTTPException(400, e.__str__())

    try:
        meta = json.loads(schema.MembershipResponse.from_orm(db_member).json())
        subscription_payment_request = crud.create_subscription_payment_request(
            dbsession, membership_id,
            max(subscription_order.amount - discount_amount, 0),
            subscription_order.currency, str(subscription_order.id), meta)
    except exceptions.ApolloPaymentRequestError as e:
        raise HTTPException(400, e.__str__())

    vkey = calculate_md5_hash(f'{subscription_payment_request.amount}{settings.MERCHANT_ID}'
                              f'{subscription_payment_request.id}{settings.VERIFY_KEY}')
    params = {
        'amount': subscription_payment_request.amount,
        'orderid': subscription_payment_request.id,
        'bill_name': f'{subscription_payment_request.member.first_name} '
                     f'{subscription_payment_request.member.last_name}',
        'bill_email': subscription_payment_request.member.user.email,
        'bill_mobile': subscription_payment_request.member.user.phone_number,
        'bill_desc': subscription_payment_request.billing_description,
        'vcode': vkey,
        'channel': 'creditAN',
        'username': settings.MERCHANT_ID,
        'app_name': 'Apollo',
    }
    if currency in [schema.Currency.sgd, schema.Currency.bnd]:
        params['channel'] = 'creditAI'  # SGD and BND use credtiAI

    # subscription is always paid in MYR
    url = f'{PAYMENT_DIRECT_API_URL}?' + urllib.parse.urlencode(params)
    return RedirectResponse(url)


@router.patch('/member/{subscription_id}', status_code=status.HTTP_200_OK, response_model=schema.SubscriptionResponse)
async def change_member_default_plan(subscription_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Allows member to change its default plan
    """
    try:
        db_subscription = crud.get_subscription(dbsession, subscription_id)
        db_subscription = crud.change_member_default_plan(dbsession, subscription_id, db_subscription.member_id)
        return db_subscription
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/connector-pricing/{connector_id}", status_code=200, response_model=schema.Connector)
async def get_connector_pricing(request: Request, connector_id: str,
                                dbsession: SessionLocal = Depends(create_session)) -> dict:
    """
    Get connector pricing based on subscription plans
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    url = f'{CHARGER_URL_PREFIX}/connector/{connector_id}'
    response = await send_request('GET', url=url, headers=headers)
    connector = response.json()
    return crud.get_connector_pricing(dbsession, membership_id, connector)


@router.post('/ocpi/custom-plan', status_code=status.HTTP_201_CREATED,
             response_model=schema.SubscriptionOCPICustomPlanResponse)
async def add_subscription_ocpi_custom_plan(request: Request, subscription_data: schema.SubscriptionOCPICustomPlan,
                                            dbsession: SessionLocal = Depends(create_session)):
    """
    Add new subscription custom plan
    """
    data = dict(subscription_data)
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    query_params = request.query_params
    url = f"{CHARGER_URL_PREFIX}/connector/ocpi/{data['connector_id']}"
    response = await send_request('GET', url=url, headers=headers, query_params=query_params)
    if response.status_code == 200:
        connector = response.json()
        data['evse_number'] = connector['number']
        data['evse_id'] = connector['ocpi_evse']['ocpi_partner_evse_id']
        billing_unit_fee = connector['billing_unit_fee']

        return check_update_or_create_ocpi_custom_plan(dbsession, data, billing_unit_fee, data['amount'],
                                                       'CREATE', subscription_ocpi_custom_plan_id=None)

    raise HTTPException(400, "Cant get connector data")


@router.patch('/ocpi/custom-plan/{subscription_ocpi_custom_plan_id}', status_code=status.HTTP_200_OK,
              response_model=schema.SubscriptionOCPICustomPlanResponse)
async def update_subscription_ocpi_custom_plan(request: Request, subscription_ocpi_custom_plan_id: UUID,
                                               subscription_data: schema.SubscriptionOCPICustomPlanUpdate,
                                               dbsession: SessionLocal = Depends(create_session)):
    """
    Update a subscription custom plan
    """
    data = subscription_data.dict(exclude_unset=True, exclude_defaults=True)
    try:
        db_custom_plan = crud.get_subscription_ocpi_custom_plan(dbsession, subscription_ocpi_custom_plan_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Invalid Custom Plan ID')

    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    query_params = request.query_params

    if 'connector_id' in data:
        url = f"{CHARGER_URL_PREFIX}/connector/ocpi/{data['connector_id']}"
    else:
        url = f"{CHARGER_URL_PREFIX}/connector/ocpi/{db_custom_plan.connector_id}"

    response = await send_request('GET', url=url, headers=headers, query_params=query_params)

    if response.status_code == 200:
        connector = response.json()
        data['evse_number'] = connector['number']
        data['evse_id'] = connector['ocpi_evse']['ocpi_partner_evse_id']
        billing_unit_fee = connector['billing_unit_fee']
    else:
        raise HTTPException(400, "Cant get connector data")

    if 'amount' in data:
        new_custom_plan_amount = data['amount']
    else:
        new_custom_plan_amount = db_custom_plan.amount

    return check_update_or_create_ocpi_custom_plan(dbsession, data, billing_unit_fee, new_custom_plan_amount,
                                                   'UPDATE', subscription_ocpi_custom_plan_id)


@router.get('/ocpi/custom-plan', status_code=status.HTTP_200_OK,
            response_model=Page[schema.SubscriptionOCPICustomPlanResponse])
async def get_subscription_ocpi_custom_plan_list(dbsession: SessionLocal = Depends(create_session),
                                                 subscription_plan_id: str = None, evse_id: str = None,
                                                 params: Params = Depends()):
    """
    Get subscription custom plans list
    """
    db_subscription_ocpi_custom_plan = crud.get_subscription_ocpi_custom_plan_list(dbsession,
                                                                                   subscription_plan_id,
                                                                                   evse_id)
    return paginate(db_subscription_ocpi_custom_plan, params)


@router.get('/ocpi/custom-plan/download', status_code=status.HTTP_200_OK)
async def download_subscription_ocpi_custom_plan(dbsession: SessionLocal = Depends(create_session),
                                                 subscription_plan_id: str = None, evse_id: str = None):
    """
    Download subscription plans
    """
    query = crud.get_subscription_ocpi_custom_plan_list(dbsession, subscription_plan_id, evse_id)
    headers = ['EVSE ID', 'CONNECTOR', 'SUBSCRIPTION PLAN', 'DISCOUNT RATE', 'CREATED DATE']
    columns = ['evse_id', 'evse_number', 'subscription_plan.name', 'amount', 'created_at']

    report = GenerateReport("subscription_ocpi_custom_plan", header=headers, columns=columns, query=query)
    await report.generate_dataframe_with_query(schema=schema.SubscriptionCustomPlanResponse, join='subscription_plan')
    await report.datetime_reformat('created_at')
    return await report.generate_report()


@router.get('/ocpi/custom-plan/{subscription_ocpi_custom_plan_id}', status_code=status.HTTP_200_OK,
            response_model=schema.SubscriptionOCPICustomPlanResponse)
async def get_subscription_ocpi_custom_plan(subscription_ocpi_custom_plan_id: UUID,
                                            dbsession: SessionLocal = Depends(create_session)):
    """
    Get a subscription custom plan
    """
    try:
        db_subscription_ocpi_custom_plan = crud.get_subscription_ocpi_custom_plan(dbsession,
                                                                                  subscription_ocpi_custom_plan_id)
        return db_subscription_ocpi_custom_plan
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete('/ocpi/custom-plan/{subscription_ocpi_custom_plan_id}', status_code=status.HTTP_204_NO_CONTENT)
async def delete_subscription_ocpi_custom_plan(subscription_ocpi_custom_plan_id: UUID,
                                               dbsession: SessionLocal = Depends(create_session)):
    """
    Delete a subscription custom plan
    """
    try:
        crud.delete_subscription_ocpi_custom_plan(dbsession, subscription_ocpi_custom_plan_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Prepaid Wallet Subscription Plan

def topup_prepaid_wallet_plan_bg(prepaid_wallet_plan_id: UUID):
    with contextmanager(create_session)() as db:
        prepaid_wallet_plan = db.query(models.PrepaidWalletPlan).get(prepaid_wallet_plan_id)
        topup_prepaid_wallet_plan(db, prepaid_wallet_plan)


def deduct_prepaid_wallet_batch_bg(prepaid_wallet_plan_batch_id: UUID):
    with contextmanager(create_session)() as db:
        prepaid_wallet_plan_batch = db.query(models.PrepaidWalletPlanBatch).get(prepaid_wallet_plan_batch_id)
        deduct_prepaid_wallet_batch(db, prepaid_wallet_plan_batch, auto_commit=True)


@router.post(
    '/prepaid-wallet-plan',
    status_code=status.HTTP_201_CREATED,
    response_model=schema.PrepaidWalletPlanResponse
)
async def add_prepaid_wallet_plan(
    payload: schema.PrepaidWalletPlanCreate,
    db: Session = Depends(create_session),
):
    try:
        return crud.create_prepaid_wallet_plan(db, payload)
    except exceptions.ApolloPrepaidWalletPlanError as e:
        name = payload.name
        raise HTTPException(409, f"{e} Prepaid Wallet Plan with {name=} already exists.")


@router.post(
    "/prepaid-wallet-plan/topup",
    status_code=status.HTTP_202_ACCEPTED,
)
def prepaid_wallet_plan_topup(
    prepaid_wallet_plan_id: UUID,
    background_tasks: BackgroundTasks,
    db: Session = Depends(create_session),
):
    try:
        prepaid_wallet_plan = crud.get_prepaid_wallet_plan(db, prepaid_wallet_plan_id)
        if not prepaid_wallet_plan.is_active:
            raise HTTPException(400, "Prepaid Wallet Plan is not active.")

        background_tasks.add_task(topup_prepaid_wallet_plan_bg, prepaid_wallet_plan_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(404, str(e))


@router.patch(
    '/prepaid-wallet-plan/{prepaid_wallet_plan_id}',
    status_code=status.HTTP_200_OK,
    response_model=schema.PrepaidWalletPlanResponse,
)
async def update_prepaid_wallet_plan(
    prepaid_wallet_plan_id: UUID,
    payload: schema.PrepaidWalletPlanUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(create_session),
):
    try:
        existing_plan = crud.get_prepaid_wallet_plan(db, prepaid_wallet_plan_id)
        if existing_plan.is_active == payload.is_active:
            raise HTTPException(400, "Cannot change the status of the plan to the same status.")

        updated_plan = crud.update_prepaid_wallet_plan(db, prepaid_wallet_plan_id, payload)

        if payload.is_active is True:
            background_tasks.add_task(topup_prepaid_wallet_plan_bg, prepaid_wallet_plan_id)

        elif payload.is_active is False:
            latest_batch = crud.get_prepaid_wallet_plan_batch(db, updated_plan.latest_batch_id)
            latest_batch.end_time = datetime.now(ZoneInfo(existing_plan.timezone or "Asia/Kuala_Lumpur"))
            db.commit()
            db.refresh(latest_batch)
            background_tasks.add_task(deduct_prepaid_wallet_batch_bg, latest_batch.id)

        return updated_plan
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(404, str(e))
    except IntegrityError:
        name = payload.name
        raise HTTPException(409, f"Prepaid Wallet Plan with {name=} already exists.")


@router.get(
    '/prepaid-wallet-plan',
    status_code=status.HTTP_200_OK,
    response_model=Page[schema.PrepaidWalletPlanResponse],
)
async def get_prepaid_wallet_plan_list(
    name: str = None,
    description: str = None,
    currency: schema.PrepaidWalletCurrency = None,
    is_active: bool = None,
    plan_type: schema.PrepaidWalletPlanType = None,
    interval: schema.PrepaidWalletPlanInterval = None,
    params: Params = Depends(),
    db: Session = Depends(create_session),
):
    query = crud.get_prepaid_wallet_plan(
        db=db,
        name=name,
        description=description,
        currency=currency,
        is_active=is_active,
        plan_type=plan_type,
        interval=interval,
    )
    return paginate(query, params)


@router.get(
    '/prepaid-wallet-plan/{prepaid_wallet_plan_id}',
    status_code=status.HTTP_200_OK,
    response_model=schema.PrepaidWalletPlanResponse,
)
async def get_prepaid_wallet_plan(
    prepaid_wallet_plan_id: UUID,
    db: Session = Depends(create_session),
):
    try:
        return crud.get_prepaid_wallet_plan(db, prepaid_wallet_plan_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(404, str(e))


@router.delete('/prepaid-wallet-plan/{prepaid_wallet_plan_id}', status_code=status.HTTP_204_NO_CONTENT)
async def delete_prepaid_wallet_plan(
    prepaid_wallet_plan_id: UUID,
    db: Session = Depends(create_session),
):
    try:
        crud.delete_prepaid_wallet_plan(db, prepaid_wallet_plan_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(404, str(e))


# Prepaid Wallet Plan Batch (NO Create/Update/Delete because it's transactional data)


@router.get(
    '/prepaid-wallet-plan-batch/{prepaid_wallet_plan_batch_id}',
    status_code=status.HTTP_200_OK,
    response_model=schema.PrepaidWalletPlanBatchResponse,
)
async def get_prepaid_wallet_plan_batch(
    prepaid_wallet_plan_batch_id: UUID,
    db: Session = Depends(create_session),
):
    try:
        return crud.get_prepaid_wallet_plan_batch(db, prepaid_wallet_plan_batch_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(404, str(e))


@router.get(
    '/prepaid-wallet-plan-batch',
    status_code=status.HTTP_200_OK,
    response_model=Page[schema.PrepaidWalletPlanBatchResponse],
)
async def get_prepaid_wallet_plan_batch_list(
    prepaid_wallet_plan_id: UUID = None,
    currency: schema.PrepaidWalletCurrency = None,
    plan_type: schema.PrepaidWalletPlanType = None,
    interval: schema.PrepaidWalletPlanInterval = None,
    start_time: datetime = None,
    end_time: datetime = None,
    status: schema.PrepaidWalletPlanBatchStatus = None,
    params: Params = Depends(),
    db: Session = Depends(create_session),
):
    query = crud.get_prepaid_wallet_plan_batch(
        db=db,
        prepaid_wallet_plan_id=prepaid_wallet_plan_id,
        currency=currency,
        plan_type=plan_type,
        interval=interval,
        start_time=start_time,
        end_time=end_time,
        status=status,
    )
    return paginate(query, params)


# Prepaid Wallet Subscription


@router.post(
    '/prepaid-wallet-subscription',
    status_code=status.HTTP_201_CREATED,
    response_model=schema.PrepaidWalletSubscriptionResponse
)
async def add_prepaid_wallet_subscription(
    payload: schema.PrepaidWalletSubscriptionCreate,
    db: Session = Depends(create_session),
):
    try:
        return crud.create_prepaid_wallet_subscription(db, payload)
    except IntegrityError as e:
        member_id = payload.member_id
        prepaid_wallet_plan_id = payload.prepaid_wallet_plan_id

        if isinstance(e.orig, UniqueViolation):
            detail_msg = f"""
                Prepaid Wallet Subscription for {member_id=} and {prepaid_wallet_plan_id=} and already exists.
            """.strip()
        elif isinstance(e.orig, ForeignKeyViolation):
            detail_msg = f"{member_id=} or {prepaid_wallet_plan_id=} does not exist in database."
        else:
            logger.error(e)
            detail_msg = "Unknown error occurred."

        raise HTTPException(409, detail_msg)
    except exceptions.ApolloPrepaidWalletSubscriptionMoreThanOneActiveError as e:
        raise HTTPException(409, str(e))


@router.post(
    "/prepaid-wallet-subscription/upload",
    status_code=status.HTTP_202_ACCEPTED,
)
async def prepaid_wallet_subscription_upload(
    prepaid_wallet_plan_id: UUID,
    is_active: bool,
    file: UploadFile = File(...),
    db: Session = Depends(create_session),
):
    try:
        file_extension = file.filename.split(".")[-1]
        content = await file.read()
        if "csv" in file_extension:
            # df = pd.read_csv(file.file, encoding='utf-8')
            df = pd.read_csv(io.BytesIO(content))
        elif "xl" in file_extension:
            # Python error where seekable is not implemented at SpooledTemporaryFile
            # AttributeError: 'SpooledTemporaryFile' object has no attribute 'seekable'
            # df = pd.read_excel(file.file, engine='openpyxl')
            df = pd.read_excel(io.BytesIO(content))
        else:
            raise HTTPException(400, "Invalid file type. Only CSV and Excel files are supported.")

        emails = df["email"].tolist()
        db_memberships = crud.get_membership_by_email_list(db, emails)

        if len(db_memberships) != len(emails):
            found_emails = {membership.user.email for membership in db_memberships}
            not_found_emails = set(emails) - found_emails
            err_msg = f"Some members are not found: {', '.join(not_found_emails)}"
            raise HTTPException(404, err_msg)

        crud.bulk_create_prepaid_wallet_subscription(
            db=db,
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            member_ids=[membership.id for membership in db_memberships],
            is_active=is_active,
        )
    except exceptions.ApolloPrepaidWalletSubscriptionError as e:
        raise HTTPException(
            409,
            f"{e} Prepaid Wallet Subscription for {prepaid_wallet_plan_id=} already exists for some members."
        )
    except exceptions.ApolloPrepaidWalletSubscriptionMoreThanOneActiveError as e:
        raise HTTPException(409, str(e))


@router.patch(
    '/prepaid-wallet-subscription/{prepaid_wallet_subscription_id}',
    status_code=status.HTTP_200_OK,
    response_model=schema.PrepaidWalletSubscriptionResponse,
)
async def update_prepaid_wallet_subscription(
    prepaid_wallet_subscription_id: UUID,
    payload: schema.PrepaidWalletSubscriptionUpdate,
    db: Session = Depends(create_session),
):
    try:
        updated_subscription = crud.update_prepaid_wallet_subscription(db, prepaid_wallet_subscription_id, payload)
        if payload.is_active is False and updated_subscription.prepaid_wallet_plan.latest_batch_id:
            latest_batch = crud.get_prepaid_wallet_plan_batch(
                db=db,
                prepaid_wallet_plan_batch_id=updated_subscription.prepaid_wallet_plan.latest_batch_id,
            )
            topup_transaction = db.query(models.PrepaidWalletTransaction).filter(
                models.PrepaidWalletTransaction.prepaid_wallet_plan_batch_id == latest_batch.id,
                models.PrepaidWalletTransaction.prepaid_wallet_subscription_id == prepaid_wallet_subscription_id,
                models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
            ).first()

            if topup_transaction:
                deduct_unused_prepaid_wallet_topup(
                    db=db,
                    topup_transaction=topup_transaction,
                    prepaid_wallet_plan_batch=latest_batch,
                    auto_commit=True,
                )

        return updated_subscription
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(404, str(e))
    except exceptions.ApolloPrepaidWalletSubscriptionMoreThanOneActiveError as e:
        raise HTTPException(409, str(e))
    except IntegrityError as e:
        member_id = payload.member_id
        prepaid_wallet_plan_id = payload.prepaid_wallet_plan_id

        if isinstance(e.orig, UniqueViolation):
            detail_msg = f"""
                Prepaid Wallet Subscription for {member_id=} and {prepaid_wallet_plan_id=} and already exists.
            """.strip()
        elif isinstance(e.orig, ForeignKeyViolation):
            detail_msg = f"{member_id=} or {prepaid_wallet_plan_id=} does not exist in database."
        else:
            logger.error(e)
            detail_msg = "Unknown error occurred."

        raise HTTPException(409, detail_msg)


@router.get(
    '/prepaid-wallet-subscription',
    status_code=status.HTTP_200_OK,
    response_model=Page[schema.PrepaidWalletSubscriptionResponse],
)
async def get_prepaid_wallet_subscription_list(
    member_id: UUID = None,
    member_name: str = None,
    member_email: str = None,
    member_phone: str = None,
    prepaid_wallet_plan_id: UUID = None,
    prepaid_wallet_plan_name: str = None,
    prepaid_wallet_plan_type: schema.PrepaidWalletPlanType = None,
    prepaid_wallet_plan_interval: schema.PrepaidWalletPlanInterval = None,
    prepaid_wallet_plan_is_active: bool = None,
    params: Params = Depends(),
    db: Session = Depends(create_session),
):
    query = crud.get_prepaid_wallet_subscription(
        db=db,
        member_id=member_id,
        member_name=member_name,
        member_email=member_email,
        member_phone=member_phone,
        prepaid_wallet_plan_id=prepaid_wallet_plan_id,
        prepaid_wallet_plan_name=prepaid_wallet_plan_name,
        prepaid_wallet_plan_type=prepaid_wallet_plan_type,
        prepaid_wallet_plan_interval=prepaid_wallet_plan_interval,
        prepaid_wallet_plan_is_active=prepaid_wallet_plan_is_active,
    )
    return paginate(query, params)


@router.get(
    '/prepaid-wallet-subscription/{prepaid_wallet_subscription_id}',
    status_code=status.HTTP_200_OK,
    response_model=schema.PrepaidWalletSubscriptionResponse,
)
async def get_prepaid_wallet_subscription(
    prepaid_wallet_subscription_id: UUID,
    db: Session = Depends(create_session),
):
    try:
        return crud.get_prepaid_wallet_subscription(db, prepaid_wallet_subscription_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(404, str(e))


@router.delete('/prepaid-wallet-subscription/{prepaid_wallet_subscription_id}', status_code=status.HTTP_204_NO_CONTENT)
async def delete_prepaid_wallet_subscription(
    prepaid_wallet_subscription_id: UUID,
    db: Session = Depends(create_session),
):
    try:
        prepaid_wallet_subscription = crud.get_prepaid_wallet_subscription(db, prepaid_wallet_subscription_id)
        prepaid_wallet_plan_batch = prepaid_wallet_subscription.prepaid_wallet_plan.latest_batch
        if prepaid_wallet_plan_batch:
            topup_transaction = db.query(models.PrepaidWalletTransaction).filter(
                models.PrepaidWalletTransaction.prepaid_wallet_plan_batch_id == prepaid_wallet_plan_batch.id,
                models.PrepaidWalletTransaction.prepaid_wallet_subscription_id == prepaid_wallet_subscription_id,
                models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
            ).one_or_none()
            if topup_transaction:
                deduct_unused_prepaid_wallet_topup(
                    db=db,
                    topup_transaction=topup_transaction,
                    prepaid_wallet_plan_batch=prepaid_wallet_plan_batch,
                    auto_commit=True,
                )
        crud.delete_prepaid_wallet_subscription(db, prepaid_wallet_subscription_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(404, str(e))


# Prepaid Wallet Transaction (NO Create/Update/Delete because it's transactional data)


@router.get(
    '/prepaid-wallet-transaction/{prepaid_wallet_transaction_id}',
    status_code=status.HTTP_200_OK,
    response_model=schema.PrepaidWalletTransactionResponse,
)
async def get_prepaid_wallet_transaction(
    prepaid_wallet_transaction_id: UUID,
    db: Session = Depends(create_session),
):
    try:
        return crud.get_prepaid_wallet_transaction(db, prepaid_wallet_transaction_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(404, str(e))


@router.get(
    '/prepaid-wallet-transaction',
    status_code=status.HTTP_200_OK,
    response_model=Page[schema.PrepaidWalletTransactionResponse],
)
async def get_prepaid_wallet_transaction_list(
    prepaid_wallet_subscription_id: UUID = None,
    prepaid_wallet_plan_batch_id: UUID = None,
    currency: schema.PrepaidWalletCurrency = None,
    transaction_type: schema.PrepaidWalletTransactionType = None,
    remarks: str = None,
    start_time: datetime = None,
    end_time: datetime = None,
    params: Params = Depends(),
    db: Session = Depends(create_session),
):
    query = crud.get_prepaid_wallet_transaction(
        db=db,
        prepaid_wallet_subscription_id=prepaid_wallet_subscription_id,
        prepaid_wallet_plan_batch_id=prepaid_wallet_plan_batch_id,
        currency=currency,
        transaction_type=transaction_type,
        remarks=remarks,
        start_time=start_time,
        end_time=end_time,
    )
    return paginate(query, params)


@router.put("/prepaid-wallet-plan/set-batch-end-time")
def force_update_latest_batch_end_time(
    prepaid_wallet_plan_id: UUID,
    end_time: datetime = None,
    db: Session = Depends(create_session),
):
    """
    Force update latest batch end time. NOW() if end_time is None
    """
    try:
        prepaid_wallet_plan = db.query(models.PrepaidWalletPlan).get(prepaid_wallet_plan_id)
        if not prepaid_wallet_plan.latest_batch_id:
            raise HTTPException(404, "No latest batch ID for this Prepaid Wallet Plan.")
        latest_batch = db.query(models.PrepaidWalletPlanBatch).get(prepaid_wallet_plan.latest_batch_id)
        if not latest_batch:
            raise HTTPException(404, "The latest batch is not found.")
        latest_batch.end_time = end_time or datetime.now(ZoneInfo(prepaid_wallet_plan.timezone or "Asia/Kuala_Lumpur"))
        db.commit()
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(404, str(e))


@router.post("/prepaid-wallet-plan/cron-job")
def cron_handle_prepaid_wallet_subscription():
    """
    Test endpoint to trigger handle_prepaid_wallet_subscription cron job
    """
    try:
        celery_app.send_task(
            f'{settings.MAIN_SERVICE_TASKS_PREFIX}.handle_prepaid_wallet_subscription',
            queue=settings.MAIN_SERVICE_WORKER_QUEUE,
            routing_key=settings.MAIN_SERVICE_WORKER_ROUTE
        )
    except Exception as e:
        raise HTTPException(400, f"Unknown Error: {e}")
