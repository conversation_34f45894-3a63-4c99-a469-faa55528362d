import random
from contextlib import contextmanager
from unittest.mock import patch
from datetime import datetime, timedelta
import secrets
from uuid import UUID

from faker import Faker
from fastapi.testclient import TestClient

import pytest
import jwt

from app import settings, schema, models
from app.main import app, ROOT_PATH

from app.schema import MembershipType, argon_ph
from app.tests.factories import UserFactory, VerificationTokenFactory, OrganizationFactory, MembershipFactory, \
    MembershipExtendedFactory, OrganizationAuthenticationServiceFactory, ResourceServerFactory, ResourceFactory, \
    RoleFactory
from app.database import SessionLocal, create_session, Base, engine

fake = Faker()
client = TestClient(app)


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def faker_phone_number(fake: Faker) -> str:
    random_number = ''.join(random.choice('123456789') for _ in range(8))
    random_number = '+601' + random_number
    return random_number


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VerificationTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipExtendedFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


def test_post_signin_password_with_valid_data_succeeds(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signin/user/password'

    valid_data = {
        'phone_number': '',
        'password': 'password',
        'organization_id': ''
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='phone',
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}'
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()

        valid_data['phone_number'] = f'{user.phone_number}'
        valid_data['organization_id'] = f'{organization.id}'

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)

    assert response.status_code == 200
    assert len(response.json()['data']['auth_token']) > 0


def test_post_signin_password_with_invalid_phone_number_fails(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signin/user/password'

    valid_data = {
        'phone_number': '',
        'password': 'IamAPassword123',
        'organization_id': ''
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='phone',
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}'
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()

        valid_data['phone_number'] = f'{user.phone_number}0000'
        valid_data['organization_id'] = f'{organization.id}'

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)

    assert response.json()['error']['message'] == ['User not found.']


def test_post_signin_password_with_invalid_password_fails(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signin/user/password'

    valid_data = {
        'phone_number': '',
        'password': 'IamAPassword123_incorrect',
        'organization_id': ''
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='phone',
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}'
        )
        db.commit()

        valid_data['phone_number'] = f'{user.phone_number}'
        valid_data['organization_id'] = f'{organization.id}'

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
    assert response.json()['error']['message'] == ['Phone number with password does not matches.']


def test_post_signin_password_with_valid_data_not_verified_fails(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signin/user/password'

    valid_data = {
        'phone_number': '',
        'password': 'password',
        'organization_id': ''
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='phone',
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}'
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id))
        db.commit()

        valid_data['phone_number'] = f'{user.phone_number}'
        valid_data['organization_id'] = f'{organization.id}'

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
    assert response.json()['error']['message'] == ['User not found.']


def test_post_valid_profile_succeeds(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/get-profile'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )

        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": f'{user.id}',
                "membership_id": f'{membership.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    response = client.get(url, headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id})

    assert response.status_code == 200


def test_post_valid_profile_fails_wrong_jwt(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/get-profile'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='phone',
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        member = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}'
        )

        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(user.id),
                "membership_id": f'{member.id}',
            },
            'wrong_jwt',
            algorithm=schema.JWT_ALGORITHM,
        )

    response = client.post(url, headers={'authorization': token})

    assert response.status_code == 400
    assert response.json()['detail'] == 'Token validation error'


def test_get_profile_guest(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/get-profile'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            organization_id=organization.id,
            is_guest=True,
            user_type=schema.UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)
        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}'
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": f'{user.id}',
                "membership_id": f'{membership.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    response = client.get(url, headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id})
    assert response.status_code == 200


def test_patch_update_profile_first_name_last_name(test_db):
    url = f'{ROOT_PATH}/api/v1/organization/update-profile'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )

        db.commit()
        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()

        create_res_server_and_roles(db, membership, str(organization.id),
                                    f'{ROOT_PATH}/api/v1/organization/update-profile',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": f'{user.id}',
                "membership_id": f'{membership.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        update_body = {
            'first_name': 'FIRST_NAME',
            'last_name': 'LAST_NAME'
        }

    response = client.patch(url,
                            headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id},
                            json=update_body)

    assert response.status_code == 200
    assert response.json()['data']['first_name'] == 'FIRST_NAME'
    assert response.json()['data']['last_name'] == 'LAST_NAME'


def test_patch_update_profile_email_taken_within_same_org_is_verified(test_db):
    url = f'{ROOT_PATH}/api/v1/organization/update-profile'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        user_2 = UserFactory(
            phone_number=faker_phone_number(fake),
            email='<EMAIL>',
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()

        membership_2 = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user_2.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership_2.id), phone_verified=True)
        db.commit()

        create_res_server_and_roles(db, membership, str(organization.id),
                                    f'{ROOT_PATH}/api/v1/organization/update-profile',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": f'{user.id}',
                "membership_id": f'{membership.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        update_body = {
            'email': '<EMAIL>',
        }

    response = client.patch(url,
                            headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id},
                            json=update_body)

    assert response.status_code == 200
    assert not response.json()['success']
    assert response.json()['error']['message'] == ['Email already registered']


def test_patch_update_profile_email_taken_different_org(test_db):
    url = f'{ROOT_PATH}/api/v1/organization/update-profile'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_2 = OrganizationFactory()
        db.commit()

        email = '<EMAIL>'
        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            email=email,
            user_type=schema.UserType.regular
        )
        db.commit()

        user_2 = UserFactory(
            phone_number=faker_phone_number(fake),
            email='<EMAIL>',
            organization_id=organization_2.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()

        membership_2 = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user_2.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership_2.id), phone_verified=True)
        db.commit()

        create_res_server_and_roles(db, membership, str(organization.id),
                                    f'{ROOT_PATH}/api/v1/organization/update-profile',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": f'{user.id}',
                "membership_id": f'{membership.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        update_body = {
            'email': '<EMAIL>',
        }

        response = client.patch(url,
                                headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id},
                                json=update_body)

        assert response.status_code == 200
        assert response.json()['success']
        assert response.json()['data']['user']['email'] == '<EMAIL>'

        db.commit()
        users = db.query(models.User).all()
        db.commit()

        for i in users:
            assert i.email == '<EMAIL>'
            if i.id == user.id:
                assert i.organization_id == organization.id
            else:
                assert i.organization_id == organization_2.id


def test_logout(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/logout'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='phone',
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)
        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}'
        )
        db.commit()

        MembershipExtendedFactory(
            membership_id=str(membership.id),
            phone_verified=True
        )
        db.commit()

        login_url = f'{ROOT_PATH}/api/v1/auth/signin/user/password'
        login_data = {
            'phone_number': str(user.phone_number),
            'password': 'password',
            'organization_id': organization_id
        }
        headers = {'x-api-key': secret_key, 'x-api-sid': organization_id}
        login_response = client.post(login_url, headers=headers, json=login_data)
        assert login_response.status_code == 200
        auth_token = login_response.json()['data']['auth_token']
        headers = {'authorization': auth_token, 'x-api-key': secret_key, 'x-api-sid': organization_id}

        response = client.post(url, headers=headers)
        assert response.status_code == 200
        assert response.json()['message'] == 'Logout succeed.'
