"""15

Revision ID: 4267b210e978
Revises: 191ec2a304ee
Create Date: 2022-10-27 08:04:36.233729

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4267b210e978'
down_revision = '191ec2a304ee'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_user_vehicle',
    sa.Column('main_auth_user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('main_vehicle_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['main_auth_user_id'], ['main_auth_user.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['main_vehicle_id'], ['main_vehicle.id'], ondelete='CASCADE')
    )
    op.add_column('main_operator', sa.Column('email', sa.String(), nullable=True))
    op.add_column('main_operator', sa.Column('description', sa.String(), nullable=True))
    op.add_column('main_operator', sa.Column('phone_number', sa.String(), nullable=True))
    op.add_column('main_operator', sa.Column('website', sa.String(), nullable=True))
    op.alter_column('main_promo_code', 'limit',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_promo_code', 'limit',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_column('main_operator', 'website')
    op.drop_column('main_operator', 'phone_number')
    op.drop_column('main_operator', 'description')
    op.drop_column('main_operator', 'email')
    op.drop_table('main_user_vehicle')
    # ### end Alembic commands ###
