"""145

Revision ID: 0d0f5fddbacf
Revises: 23cccde361e0
Create Date: 2025-01-24 13:29:09.252769

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0d0f5fddbacf'
down_revision = '23cccde361e0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_campaign_promo_code_usage', sa.Column('transaction_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_campaign_promo_code_usage', 'transaction_id')
    # ### end Alembic commands ###
