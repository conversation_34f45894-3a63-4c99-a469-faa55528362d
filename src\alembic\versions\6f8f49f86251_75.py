"""75

Revision ID: 6f8f49f86251
Revises: 4b1307359838
Create Date: 2024-02-22 00:16:36.593556

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6f8f49f86251'
down_revision = '4b1307359838'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_pre_auth_payment_capture',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('domain', sa.String(), nullable=True),
    sa.Column('transaction_id', sa.String(), nullable=True),
    sa.Column('amount', sa.String(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('reference_id', sa.String(), nullable=True),
    sa.Column('response', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('pre_auth_payment_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('charging_session_id', sa.String(), nullable=True),
    sa.Column('payment_request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['payment_request_id'], ['main_payment_request.id'], ),
    sa.ForeignKeyConstraint(['pre_auth_payment_id'], ['main_pre_auth_payment.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('main_pre_auth_payment', sa.Column('transaction_id', sa.String(), nullable=True))
    op.add_column('main_pre_auth_payment', sa.Column('is_used', sa.Boolean(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_pre_auth_payment', 'is_used')
    op.drop_column('main_pre_auth_payment', 'transaction_id')
    op.drop_table('main_pre_auth_payment_capture')
    # ### end Alembic commands ###
