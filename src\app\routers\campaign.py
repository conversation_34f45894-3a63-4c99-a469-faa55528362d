from math import ceil
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import ValidationError
from sqlalchemy.orm import Session
from sqlalchemy.exc import NoResultFound, IntegrityError

from app import settings, schema, crud, exceptions
from app.database import create_session
from app.permissions import permission
from app.utils import (
    CHARGER_URL_PREFIX,
    GenerateReport,
    decode_auth_token_from_headers,
    generate_charger_header,
    send_request,
)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/campaign",
    tags=["campaign"],
    dependencies=[Depends(permission)],
)


# Campaign

@router.post("/", status_code=status.HTTP_201_CREATED, response_model=schema.CampaignResponse)
async def create_campaign(
    campaign_data: schema.CampaignCreate,
    dbsession: Session = Depends(create_session),
):
    try:
        db_campaign = crud.CampaignCRUD.add(dbsession, campaign_data.dict())
        return db_campaign
    except exceptions.ApolloCampaignError as e:
        raise HTTPException(400, e.__str__())


@router.patch("/{campaign_id}", status_code=status.HTTP_200_OK, response_model=schema.CampaignResponse)
async def update_campaign(
    campaign_id: UUID,
    campaign_data: schema.CampaignUpdate,
    dbsession: Session = Depends(create_session),
):
    try:
        db_campaign = crud.CampaignCRUD.update(
            dbsession, campaign_id, campaign_data.dict(exclude_unset=True)
        )
        return db_campaign
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/", status_code=status.HTTP_200_OK, response_model=Page[schema.CampaignResponse])
async def get_campaign_list(
    dbsession: Session = Depends(create_session),
    filters: schema.CampaignFiltersParam = Depends(),
    params: Params = Depends(),
):
    db_campaign_list = crud.get_all_campaign_promo(dbsession, filters)
    return paginate(db_campaign_list, params)


@router.get("/{campaign_id}", status_code=status.HTTP_200_OK, response_model=schema.CampaignResponse)
async def get_campaign(
    campaign_id: UUID,
    dbsession: Session = Depends(create_session),
):
    try:
        db_campaign = crud.CampaignCRUD.get(dbsession, campaign_id)
        return db_campaign
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{campaign_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_campaign(
    campaign_id: UUID,
    dbsession: Session = Depends(create_session),
):
    try:
        crud.CampaignCRUD.delete(dbsession, campaign_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/report/", status_code=status.HTTP_200_OK,)
async def get_campaign_report(
    dbsession: Session = Depends(create_session),
    filters: schema.CampaignFiltersParam = Depends(),
):
    headers = ['CAMPAIGN NAME', 'DESCRIPTION', 'PUBLIC DESCRIPTION', 'ORGANIZATION NAME',
               'CURRENCY', 'DISCOUNT CATEGORY', 'DISCOUNT AMOUNT', 'DISCOUNT AMOUNT CAP', 'MINIMUM AMOUNT',
               'IS REUSABLE', 'OCPI USABLE', 'ONLY EXCLUSIVE LOCATIONS', 'PROMOTION CODE TYPE',
               'START DATE', 'EXPIRY DATE', 'IS ACTIVE']
    columns = ['name', 'description', 'public_description', 'organization.name', 'currency',
               'discount_category', 'discount_amount', 'discount_amount_cap', 'min_amount', 'is_reusable',
               'ocpi_usable', 'only_exclusive_locations', 'promo_code_type', 'start_at', 'expire_at', 'is_active']

    db_campaign_list = crud.get_all_campaign_promo(dbsession, filters)

    report = GenerateReport("campaign", header=headers,
                            columns=columns, query=db_campaign_list)
    await report.generate_dataframe_with_query(schema=schema.CampaignResponse,
                                               join='organization')
    await report.datetime_reformat('start_at')
    await report.datetime_reformat('expire_at')

    return await report.generate_report()


# Campaign Promo Code

@router.post(
    "/promo_code",
    status_code=status.HTTP_201_CREATED,
    response_model=list[schema.CampaignPromoCodeResponse],
)
async def create_campaign_promo_code(
    campaign_promo_code_data: schema.CampaignPromoCodeCreateRequest,
    dbsession: Session = Depends(create_session),
):
    try:
        promo_code_dict = campaign_promo_code_data.dict()
        number_of_code = int(promo_code_dict.get('number_of_code'))
        created_promo_codes = []
        for _ in range(number_of_code):
            retries = 2  # Allow 2 retries on failure
            while retries >= 0:
                try:
                    promo_code_create_schema = schema.CampaignPromoCodeCreate(
                        code=crud.generate_campaign_promo_code(15, promo_code_dict.get('prefix')),
                        campaign_id=promo_code_dict.get('campaign_id')
                    )
                    db_campaign_promo_code = crud.CampaignPromoCodeCRUD.add(dbsession, promo_code_create_schema.dict())
                    created_promo_codes.append(db_campaign_promo_code)
                    break  # Success, exit retry loop
                except IntegrityError:
                    print("Integrity error occur when generating promocode, retrying soon...")
                    dbsession.rollback()
                    retries -= 1
                    if retries < 0:
                        raise  # Raise exception if all retries fail

        return created_promo_codes
    except ValidationError as e:
        raise HTTPException(400, str(e))
    except exceptions.ApolloCampaignPromoCodeError as e:
        raise HTTPException(400, e.__str__())


@router.post(
    "/promo_code/non_unique",
    status_code=status.HTTP_201_CREATED,
    response_model=schema.CampaignPromoCodeResponse,
)
async def create_campaign_promo_code_non_unique(
    campaign_promo_code_data: schema.CampaignPromoCodeCreate,
    dbsession: Session = Depends(create_session),
):
    try:
        promo_code_dict = campaign_promo_code_data.dict()
        promo_code_dict['code'] = (promo_code_dict.get('code')).upper()
        db_campaign_promo_code = crud.CampaignPromoCodeCRUD.add(dbsession, promo_code_dict)
        return db_campaign_promo_code
    except ValidationError as e:
        raise HTTPException(400, str(e))
    except exceptions.ApolloCampaignPromoCodeError as e:
        raise HTTPException(400, e.__str__())


@router.patch(
    "/promo_code/{campaign_promo_code_id}",
    status_code=status.HTTP_200_OK,
    response_model=schema.CampaignPromoCodeResponse,
)
async def update_campaign_promo_code(
    campaign_promo_code_id: UUID,
    campaign_promo_code_data: schema.CampaignPromoCodeUpdate,
    dbsession: Session = Depends(create_session),
):
    try:
        db_campaign_promo_code = crud.CampaignPromoCodeCRUD.update(
            dbsession, campaign_promo_code_id, campaign_promo_code_data.dict(exclude_unset=True)
        )
        return db_campaign_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get(
    "/{campaign_id}/promo_code/unique",
    status_code=status.HTTP_200_OK,
    response_model=Page[schema.CampaignPromoCodeWithUsageResponse],
)
async def get_campaign_promo_code_unique(
    campaign_id: UUID,
    dbsession: Session = Depends(create_session),
    filters: schema.CampaignPromoCodeUsageFiltersParam = Depends(),
    params: Params = Depends(),
):
    db_campaign_promo_code_list = crud.get_all_campaign_promo_code(dbsession,
                                                                   campaign_id,
                                                                   True,
                                                                   filters)
    return paginate(db_campaign_promo_code_list, params)


@router.get(
    "/{campaign_id}/promo_code/non_unique",
    status_code=status.HTTP_200_OK,
    response_model=Page[schema.CampaignPromoCodeResponse],
)
async def get_campaign_promo_code_non_unique(
    campaign_id: UUID,
    dbsession: Session = Depends(create_session),
    filters: schema.CampaignPromoCodeUsageFiltersParam = Depends(),
    params: Params = Depends(),
):
    db_campaign_promo_code_list = crud.get_all_campaign_promo_code(dbsession,
                                                                   campaign_id,
                                                                   False,
                                                                   filters)
    result = db_campaign_promo_code_list.all()
    response_data = [
        schema.CampaignPromoCodeResponse.from_orm(row[0]).dict() | {"claimed": row[1]}
        for row in result
    ]
    total = len(response_data)
    page = params.page
    size = params.size
    start = (page - 1) * size
    end = start + size
    paginated_data = response_data[start:end]
    total_pages = ceil(total / size)

    return {
        "items": paginated_data,
        "total": total,
        "page": page,
        "size": size,
        "pages": total_pages,
    }


@router.get(
    "/{campaign_id}/promo_code/report",
    status_code=status.HTTP_200_OK,
)
async def get_campaign_promo_code_report(
    campaign_id: UUID,
    dbsession: Session = Depends(create_session),
    is_unique: bool = False,
    filters: schema.CampaignPromoCodeUsageFiltersParam = Depends()
):
    headers_unique = ['CAMPAIGN NAME', 'CODE', 'LIMIT', 'BOOKED', 'USED', 'USED DATE',
                      'USER NAME', 'USER PHONE NUMBER', 'USER EMAIL', 'TRANSACTION ID']
    columns_unique = ['campaign.name', 'code', 'limit', 'booked', 'used', 'used_at',
                      'membership_full_name', 'user_phone_number',
                      'user_email', 'transaction_id']
    headers_non_unique = ['CAMPAIGN NAME', 'CODE', 'LIMIT', 'CREATED DATE']
    columns_non_unique = ['campaign.name', 'code', 'limit', 'created_at']

    db_campaign_promo_code = crud.get_all_campaign_promo_code(dbsession,
                                                              campaign_id,
                                                              is_unique,
                                                              filters)
    if is_unique:
        report = GenerateReport("campaign_promo_code_unique", header=headers_unique,
                                columns=columns_unique,
                                query=db_campaign_promo_code)
        await report.generate_dataframe_with_query_and_extraction(
            schema=schema.CampaignPromoCodeWithUsageResponse,
            multiple_join=['promo_code_usage',
                           'promo_code_usage.membership',
                           'campaign'],
            columns_extraction=['booked', 'used', 'used_at', 'transaction_id',
                                'membership_full_name',
                                'user_phone_number',
                                'user_email'],
            object_extraction=['promo_code_usage'])
        await report.nan_handling('booked', 'False', None)
        await report.nan_handling('used', 'False', None)
        await report.datetime_reformat('used_at')
        return await report.generate_report()
    report = GenerateReport("campaign_promo_code_non_unique", header=headers_non_unique,
                            columns=columns_non_unique,
                            query=db_campaign_promo_code)
    await report.generate_dataframe_with_query(schema=schema.CampaignPromoCodeResponse,
                                               join='campaign')
    await report.datetime_reformat('created_at')
    return await report.generate_report()


@router.get(
    "/promo_code/{campaign_promo_code_id}",
    status_code=status.HTTP_200_OK,
    response_model=schema.CampaignPromoCodeResponse,
)
async def get_campaign_promo_code(
    campaign_promo_code_id: UUID,
    dbsession: Session = Depends(create_session),
):
    try:
        db_campaign_promo_code = crud.CampaignPromoCodeCRUD.get(dbsession, campaign_promo_code_id)
        return db_campaign_promo_code
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/promo_code/{campaign_promo_code_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_campaign_promo_code(
    campaign_promo_code_id: UUID,
    dbsession: Session = Depends(create_session),
):
    try:
        crud.CampaignPromoCodeCRUD.delete(dbsession, campaign_promo_code_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# Promo Code Usage
@router.get(
    "/{campaign_id}/promo_code_usage",
    status_code=status.HTTP_200_OK,
    response_model=Page[schema.CampaignPromoCodeUsageResponseLarge],
)
async def get_campaign_promo_code_usage_list(
    campaign_id: UUID,
    dbsession: Session = Depends(create_session),
    filters: schema.CampaignPromoCodeUsageFiltersParam = Depends(),
    params: Params = Depends(),
):
    db_campaign_usage = crud.get_all_campaign_promo_code_usage(dbsession, campaign_id, filters)
    return paginate(db_campaign_usage, params)


@router.get(
    "/{campaign_id}/promo_code_usage/report",
    status_code=status.HTTP_200_OK,
)
async def get_campaign_promo_code_usage_report(
    campaign_id: UUID,
    dbsession: Session = Depends(create_session),
    filters: schema.CampaignPromoCodeUsageFiltersParam = Depends(),
):
    headers = ['CAMPAIGN NAME', 'CODE', 'LIMIT', 'PROMO CODE USAGE ID', 'BOOKED', 'USED',
               'USED DATE', 'USER NAME', 'USER PHONE NUMBER', 'USER EMAIL']
    columns = ['campaign_promo_code.campaign.name', 'campaign_promo_code.code',
               'campaign_promo_code.limit', 'id', 'booked', 'used', 'used_at',
               'membership.first_name', 'membership.last_name', 'membership.user.phone_number',
               'membership.user.email']
    reorder_columns = ['campaign_promo_code.campaign.name', 'campaign_promo_code.code',
                       'campaign_promo_code.limit', 'id', 'booked',
                       'used', 'used_at', 'full_name', 'membership.user.phone_number',
                       'membership.user.email']

    db_campaign_usage = crud.get_all_campaign_promo_code_usage(dbsession, campaign_id,
                                                               filters)
    report = GenerateReport("campaign_promo_code_usage", header=headers,
                            columns=columns, reorder_columns=reorder_columns,
                            query=db_campaign_usage)
    await report.generate_dataframe_with_query(schema=schema.CampaignPromoCodeUsageResponseLarge,
                                               multiple_join=['campaign_promo_code.campaign',
                                                              'campaign_promo_code', 'membership',
                                                              'membership.user'])
    await report.operation('full_name', lambda x, y: f'{x} {y}', 'membership.first_name', 'membership.last_name')
    await report.nan_handling('booked', 'False', None)
    await report.nan_handling('used', 'False', None)
    await report.datetime_reformat('used_at')
    return await report.generate_report()


# Promo Location
@router.get("/{campaign_id}/locations/", status_code=status.HTTP_200_OK)
async def get_campaign_location(
    request: Request,
    campaign_id: UUID,
    dbsession: Session = Depends(create_session),
    params: Params = Depends(),
):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    dict_query_params = dict(request.query_params)

    if dict_query_params['pagination'] == 'True':
        dict_params = dict(params)
        dict_query_params['page'] = dict_params['page']
        dict_query_params['size'] = dict_params['size']
    db_campaign = crud.CampaignCRUD.get(dbsession, campaign_id)
    dict_query_params['location_ids'] = db_campaign.exclusive_location_ids
    url = f'{CHARGER_URL_PREFIX}/location/campaign_promo_list_location/'
    response = await send_request('GET', url=url, headers=headers, query_params=dict_query_params)
    return response.json()


@router.patch("/{campaign_id}/locations/{location_id}", status_code=status.HTTP_200_OK,
              response_model=schema.CampaignResponse)
async def update_campaign_location(
    campaign_id: UUID,
    location_id: UUID,
    dbsession: Session = Depends(create_session),
):
    try:
        db_campaign = crud.CampaignCRUD.get(dbsession, campaign_id)
        location_ids = list(db_campaign.exclusive_location_ids)
        location_ids.append(location_id)
        campaign_data = schema.CampaignUpdate(exclusive_location_ids=location_ids)
        db_campaign = crud.CampaignCRUD.update(
            dbsession, campaign_id, campaign_data.dict(exclude_unset=True)
        )
        return db_campaign
    except NoResultFound:
        raise HTTPException(400, f'Campaign with id {campaign_id} does not exist')


@router.delete("/{campaign_id}/locations/{location_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_campaign_location(
    campaign_id: UUID,
    location_id: UUID,
    dbsession: Session = Depends(create_session),
):
    try:
        db_campaign = crud.CampaignCRUD.get(dbsession, campaign_id)
        db_campaign.exclusive_location_ids = [loc for loc in db_campaign.exclusive_location_ids if loc != location_id]
        campaign_data = schema.CampaignUpdate(exclusive_location_ids=db_campaign.exclusive_location_ids)
        crud.CampaignCRUD.update(
            dbsession, campaign_id, campaign_data.dict(exclude_unset=True)
        )
    except NoResultFound:
        raise HTTPException(400, f'Campaign with id {campaign_id} does not exist')
