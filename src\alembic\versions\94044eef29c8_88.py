"""88

Revision ID: 94044eef29c8
Revises: 2c6cf9b90045
Create Date: 2024-04-24 11:21:02.848667

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '94044eef29c8'
down_revision = '2c6cf9b90045'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_payment_gateway_reconcilation', sa.Column('charging_session_bill_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key(None, 'main_payment_gateway_reconcilation', 'main_charging_session_bill', ['charging_session_bill_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'main_payment_gateway_reconcilation', type_='foreignkey')
    op.drop_column('main_payment_gateway_reconcilation', 'charging_session_bill_id')
    # ### end Alembic commands ###
