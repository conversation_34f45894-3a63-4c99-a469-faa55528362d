import pytest
import uuid
import json
from contextlib import contextmanager
from datetime import datetime, timedelta

from app import models, schema
from app.database import Base, engine, SessionLocal
from app.routers.charger import (
    audit_log_charger, log_filters, energy_consumed_filters, evse_filters
)
from app.tests.mocks.async_client import MockResponse, MockRequest
from app.tests.factories import OrganizationFactory, UserFactory, MembershipFactory


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


@pytest.mark.asyncio
async def test_audit_log_charger(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        mock_headers = {
            'X-Audit': json.dumps({
                'audit': True,
                'table_name': 'test-table-name',
                'log_type': 'create',
                'api': 'test'
            })
        }
        response = MockResponse({}, 200, headers=mock_headers)
        request = MockRequest({
            'X-group-id': str(uuid.uuid4())
        })

        db_log = db.query(models.UserAuditLog).filter(models.UserAuditLog.user_id == user.id).all()
        assert len(db_log) == 0

        await audit_log_charger(db, response, str(user.id), str(mem.id), request)
        db_log = db.query(models.UserAuditLog).filter(models.UserAuditLog.user_id == user.id).all()
        assert len(db_log) == 1
        assert db_log[0].table_name == 'test-table-name'


@pytest.mark.asyncio
async def test_log_filters():
    date = datetime.now()
    params = {
        'serial_number': 'test_sn',
        'from_': date,
        'to': date,
        'message_type': 'test',
    }
    result = await log_filters(**params)
    assert result == params


@pytest.mark.asyncio
async def test_audit_log_charger_unexpected_argument():
    date = datetime.now()
    params = {
        'send_to': 'test',
        'serial_number': 'test_sn',
        'from_': date,
        'to': date,
        'message_type': 'test',
    }

    with pytest.raises(TypeError) as exc_info:
        await log_filters(**params)
        assert exc_info.value == "log_filters() got an unexpected keyword argument 'send_to'"


@pytest.mark.asyncio
async def test_energy_consumed_filters():
    date = datetime.now()
    params = {
        'date_start': date,
        'date_end': date,
        'connector_id': 'test',
        'is_download': True
    }
    result = await energy_consumed_filters(**params)
    assert result == params


@pytest.mark.asyncio
async def test_energy_consumed_filters_unexpected_argument():
    date = datetime.now()
    params = {
        'date_start': date,
        'date_end': date,
        'connector_id': 'test',
        'is_download': True,
        'dummy': 'dummy'
    }

    with pytest.raises(TypeError) as exc_info:
        await energy_consumed_filters(**params)
        assert exc_info.value == "energy_consumed_filters() got an unexpected keyword argument 'dummy'"


@pytest.mark.asyncio
async def test_evse_filters():
    id = str(uuid.uuid4())
    params = {
        'id': id,
        'ocpi_partner_evse_id': id,
        'status': 'test',
        'location_country_code': 'test',
        'location_name': 'test',
        'location_address': 'test'
    }
    result = evse_filters(**params)
    assert result == params


@pytest.mark.asyncio
async def test_evse_filters_extra_argument():
    id = str(uuid.uuid4())
    params = {
        'id': id,
        'ocpi_partner_evse_id': id,
        'status': 'test',
        'location_country_code': 'test',
        'location_name': 'test',
        'location_address': 'test',
        'party_id': 'test'
    }
    result = evse_filters(**params)
    expected = {
        'id': id,
        'ocpi_partner_evse_id': id,
        'status': 'test',
        'location_country_code': 'test',
        'location_name': 'test',
        'location_address': 'test',
        'vendor': 'test'
    }
    assert result == expected
