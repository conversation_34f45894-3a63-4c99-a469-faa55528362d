import logging
from uuid import UUI<PERSON>
from typing import Union

from fastapi.responses import JSONResponse
from fastapi import APIRouter, Depends, HTTPException, Request, status
from app import schema, settings, crud, exceptions
from app.database import create_session, SessionLocal
from app.permissions import permission, x_api_key
from app.utils import decode_auth_token_from_headers, \
    generate_charger_header, location_merge_operator_details, send_request, RouteErrorHandler, safe_get

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

CHARGER_URL_V2_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/favorite_locations",
    tags=['v2 favorite_location', ],
    dependencies=[Depends(permission), Depends(x_api_key)],
    route_class=RouteErrorHandler
)


@router.get("", status_code=status.HTTP_200_OK, tags=['v2 favorite_location', ])
async def get_favorite_location_list(request: Request, longitude: float,
                                     latitude: float, sort_by: Union[str, None] = 'distance',  # noqa
                                     dbsession: SessionLocal = Depends(create_session)):
    """
    Get Member's Favorite Location List
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    try:
        favorite_loc_list = crud.get_member_favorite_location_list(dbsession, membership_id)

        if favorite_loc_list:
            path = f'location/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}'
            loc_list_str = "".join([str("location_id=" + item + "&") for item in favorite_loc_list])
            path = f'{path}&{loc_list_str}'

            url = f'{CHARGER_URL_V2_PREFIX}/{path}'
            response = await send_request('GET', url, headers=headers)
            return JSONResponse(response.json(), status_code=response.status_code)
        return favorite_loc_list
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.put("", status_code=status.HTTP_200_OK, tags=['v2 favorite_location', ])
async def set_favorite_location(request: Request, location_id: UUID, type: crud.FavoriteType,
                                dbsession: SessionLocal = Depends(create_session), ):
    """
    Set Member's Favorite Location
    :param str location_id: Location ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    try:
        crud.set_favorite_location(dbsession, membership_id, type, str(location_id))

        path = 'location/'
        path = f'{path}{location_id}'

        url = f'{CHARGER_URL_V2_PREFIX}/{path}'
        response = await send_request('GET', url, headers=headers)
        if response.status_code == 404 or response.status_code == 400:
            new_json = {}
            return JSONResponse(new_json, status_code=200)
        return JSONResponse(response.json(), status_code=response.status_code)

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/ocpi", status_code=status.HTTP_200_OK, tags=['v2 favorite_location', ])
async def get_ocpi_favorite_location_list(request: Request, longitude: float,  # noqa: MC0001
                                          latitude: float, sort_by: Union[str, None] = 'distance',  # noqa
                                          dbsession: SessionLocal = Depends(create_session)):
    """
    Get Member's Favorite Location List
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    try:  # pylint: disable=too-many-nested-blocks
        favorite_loc_list = crud.get_member_favorite_location_list(dbsession, membership_id)

        if favorite_loc_list:
            path = f'location/ocpi/favourite/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}'
            loc_list_str = "".join([str("location_id=" + item + "&") for item in favorite_loc_list])
            path = f'{path}&{loc_list_str}'

            url = f'{CHARGER_URL_V2_PREFIX}/{path}'
            response = await send_request('GET', url, headers=headers)
            response_json = response.json()
            if len(response.json()) > 0:
                # Mapping for external parties
                operators = []
                operators_by_name = []
                for item in response_json:
                    operator_id = safe_get(item, 'charge_points', 0, 'operator_id')
                    operator_name = safe_get(item, 'ocpi_partner_operator', 'name')
                    if operator_name is not None:
                        operators_by_name.append(operator_name)
                    if operator_id is not None:
                        operators.append(operator_id)

                operators = crud.get_external_organization_by_operator_list(dbsession, operators)
                operators = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in operators]

                operators_dict = {}
                for l2 in operators:
                    operator_details = l2.dict()
                    operators_dict[str(operator_details['id'])] = {k: v for k, v in operator_details.items() if
                                                                   k != 'id'}

                if len(operators_by_name) > 0:
                    operators_by_name = crud.get_external_organization_by_operator_name(dbsession,
                                                                                        operators_by_name)
                    operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                                         operators_by_name]
                    for l2 in operators_by_name:
                        operator_details = l2.dict()
                        operators_dict[str(operator_details['original_name'])] = {k: v for k, v in
                                                                                  operator_details.items()
                                                                                  if k != 'original_name'}

                response_json_merged = location_merge_operator_details(response_json, operators_dict)
                return response_json_merged
            return response_json
        return favorite_loc_list
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.put("/ocpi", status_code=status.HTTP_200_OK, tags=['v2 favorite_location', ])
async def set_ocpi_favorite_location(request: Request, location_id: UUID,  # pylint: disable=too-many-branches
                                     type: crud.FavoriteType,
                                     dbsession: SessionLocal = Depends(create_session)):
    """
    Set Member's Favorite Location
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    try:
        crud.set_favorite_location(dbsession, membership_id, type, str(location_id))

        path = 'location/ocpi/'
        path = f'{path}{location_id}'

        url = f'{CHARGER_URL_V2_PREFIX}/{path}'
        response = await send_request('GET', url, headers=headers)
        response_json = response.json()
        if response.status_code == 404 or response.status_code == 400:
            new_json = {}
            return JSONResponse(new_json, status_code=200)

        operators_by_name = []
        operators = []
        operator_id = safe_get(response_json, 'charge_points', 0, 'operator_id')
        operator_name = safe_get(response_json, 'ocpi_partner_operator', 'name')
        if operator_name is not None:
            operators_by_name = [operator_name]
        if operator_id is not None:
            operators = [operator_id]

        operators = crud.get_external_organization_by_operator_list(dbsession, operators)
        operators = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in operators]
        operators_dict = {}
        for l2 in operators:
            operator_details = l2.dict()
            operators_dict[str(operator_details['id'])] = {k: v for k, v in operator_details.items() if k != 'id'}

        if len(operators_by_name) > 0:
            operators_by_name = crud.get_external_organization_by_operator_name(dbsession,
                                                                                operators_by_name)
            operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                                 operators_by_name]
            for l2 in operators_by_name:
                operator_details = l2.dict()
                operators_dict[str(operator_details['original_name'])] = {k: v for k, v in
                                                                          operator_details.items()
                                                                          if k != 'original_name'}

        response_json_merged = location_merge_operator_details([response_json], operators_dict)
        return response_json_merged[0]

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
