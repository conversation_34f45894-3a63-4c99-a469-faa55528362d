# pylint:disable=unused-variable
# pylint:disable=too-many-lines
import base64
import importlib.util
import json
import os
from contextlib import contextmanager
from datetime import datetime, timedelta, timezone

import jwt
from CyberSource import *

from app import schema, crud, exceptions
from app.crud import get_member_primary_cc, check_cc_is_not_blacklisted
from app.database import create_session
from app.schema import CyberSourceTMSStepCreate
from app.settings import APOLLO_MAIN_DOMAIN, CYBERSOURCE_CLIENT_SECRET_KEY, USE_V2_MICROFORM, COMPLY_UOB_COMPLIANCE

ALGORITHM = "HS256"

# Load configuration module
config_file = os.path.join(os.getcwd(), "app", "cybersource_configuration.py")
spec = importlib.util.spec_from_file_location("module.name", config_file)
configuration = importlib.util.module_from_spec(spec)
spec.loader.exec_module(configuration)

config_obj = configuration.Configuration()
config = config_obj.get_configuration()

CDG_DEFAULT_ADDRESS_1 = '205, Braddell Road'
CDG_DEFAULT_LOCALITY = 'Braddell Road'
CDG_DEFAULT_POSTAL = '579701'
CDG_DEFAULT_COUNTRY = 'SG'
# CDG_DEFAULT_ADMIN_AREA = 'SG'

# CDG_DEFAULT_ADDRESS_1 = '1 Market St'
# CDG_DEFAULT_ADMIN_AREA = 'CA'
# CDG_DEFAULT_COUNTRY = 'US'
# CDG_DEFAULT_LOCALITY = 'san francisco'
# CDG_DEFAULT_POSTAL = '94105'

CDG_DEFAULT_FIRST_NAME = 'CDG'
CDG_DEFAULT_LAST_NAME = 'ENGIE'
CDG_DEFAULT_EMAIL = '<EMAIL>'
CDG_DEFAULT_NUMBER = '6568818383'


# Utility function to delete None values in input request JSON body
def del_none(d):
    for key, value in list(d.items()):
        if value is None:
            del d[key]
        elif isinstance(value, dict):
            del_none(value)
    return d


# Function to extract jti value from JWT transient token payload
def get_transient_token_payload_from_jwt_transient_payload(transient_token_jwt, result_type: str = 'jti'):
    payload = jwt.decode(transient_token_jwt[1:-1], options={"verify_signature": False})
    jti_value = payload.get(result_type, None)
    return jti_value


# Function to create JWT token
def create_jwt_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + expires_delta if expires_delta else datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, CYBERSOURCE_CLIENT_SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


# Function to decode JWT token
def decode_session_jwt_token(token: str):
    try:
        decoded_token = jwt.decode(token, key=CYBERSOURCE_CLIENT_SECRET_KEY, algorithms=[ALGORITHM, ],
                                   options={"verify_signature": True})
        exp_timestamp = decoded_token["exp"]
        exp_datetime = datetime.fromtimestamp(exp_timestamp, tz=timezone.utc)
        return decoded_token if exp_datetime >= datetime.now(tz=timezone.utc) else None
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None


def decode_jwt_token(token: str):
    try:
        decoded_token = jwt.decode(token, options={"verify_signature": False})
        return decoded_token
    except jwt.InvalidTokenError as e:
        print(e)
        return None


# def decode_base64_to_dict(token: str) -> dict:
#     print(token)
#     decoded_string = base64.b64decode(token).decode('utf-8')
#     return json.loads(decoded_string)
def decode_base64_to_dict(token: str) -> dict:
    # Fix padding issue
    padding = '=' * (4 - len(token) % 4)
    token += padding
    decoded_string = base64.b64decode(token).decode('utf-8')
    return json.loads(decoded_string)


# Function to create a customer in CyberSource
def create_customer(user_dict, reference_no):
    buyer_information = Tmsv2customersBuyerInformation(
        merchant_customer_id=str(user_dict.id),
        email=user_dict.email
    )

    client_reference_information = Tmsv2customersClientReferenceInformation(
        code=reference_no
    )

    request_obj = PostCustomerRequest(
        buyer_information=buyer_information.__dict__,
        client_reference_information=client_reference_information.__dict__
    )

    request_obj = del_none(request_obj.__dict__)
    request_obj = json.dumps(request_obj)
    print(request_obj)

    try:

        client_config = config_obj.get_configuration('3ds')
        api_instance = CustomerApi(client_config)
        _, status, body = api_instance.post_customer(request_obj)
        print(f"CREATE CUSTOMER API RESPONSE CODE: {status}")
        print(f"CREATE CUSTOMER API RESPONSE BODY: {body}")
        return body
    except Exception as e:  # pylint: disable=broad-except
        print(f"\nException when calling CustomerApi->post_customer: {e}\n")


# Function to delete a customer in CyberSource
def delete_customer(customer_token_id):
    try:

        client_config = config_obj.get_configuration('3ds')
        api_instance = CustomerApi(client_config)
        _, status, body = api_instance.delete_customer(customer_token_id)
        print(f"DELETE CUSTOMER API RESPONSE CODE: {status}")
        print(f"DELETE CUSTOMER API RESPONSE BODY: {body}")
        return body
    except Exception as e:  # pylint: disable=broad-except
        print(f"\nException when calling CustomerApi->delete_customer: {e}\n")


# Function to create an instrument identifier in CyberSource
def create_instrument_identifier(cc_data):
    client_config = config_obj.get_configuration('3ds')
    api_instance = InstrumentIdentifierApi(client_config)
    card_info = TmsEmbeddedInstrumentIdentifierCard(number=cc_data)

    body = PostInstrumentIdentifierRequest(card=card_info.__dict__)
    request_obj = del_none(body.__dict__)
    request_obj = json.dumps(request_obj)
    print(request_obj)

    try:
        _, status, body = api_instance.post_instrument_identifier(request_obj)
        print(f"CREATE Instrument Identifier API RESPONSE CODE: {status}")
        print(f"CREATE Instrument Identifier API RESPONSE BODY: {body}")
        return body
    except Exception as e:  # pylint: disable=broad-except
        print(f"\nException when calling InstrumentIdentifierApi->post_instrument_identifier: {e}\n")


# Function to update the default payment instrument card for a customer
def update_customer_default_payment_instrument_card(customer_token, payment_instrument):
    request_obj = PatchCustomerPaymentInstrumentRequest(default=True)
    request_obj = del_none(request_obj.__dict__)
    request_obj = json.dumps(request_obj)
    print(request_obj)

    try:
        client_config = config_obj.get_configuration('3ds')
        api_instance = CustomerPaymentInstrumentApi(client_config)
        _, status, body = api_instance.patch_customers_payment_instrument(customer_token, payment_instrument,
                                                                          request_obj)
        print(f"UPDATE Customer Default Instrument Identifier API RESPONSE CODE: {status}")
        print(f"UPDATE Customer Default Instrument Identifier API RESPONSE BODY: {body}")
        return body
    except Exception as e:  # pylint: disable=broad-except
        print(f"\nException when calling CustomerPaymentInstrumentApi->patch_customers_payment_instrument: {e}\n")


# Function to create a payment in CyberSource
# noqa: MC0001, #pylint:disable=too-many-statements, too-many-locals
def create_payment(customer_token: str, payment_instrument_token: str, amount: float, currency: schema.Currency,
                   auth_indicator: int, reference_id: str = 'TC50171_3', capture_directly: bool = False,
                   is_3ds: bool = False, transaction_id: str = None, network_transaction_id: str = None,
                   bypass_consumer_auth_check: bool = False):
    client_reference_information = Ptsv2paymentsClientReferenceInformation(code=reference_id,
                                                                           reconciliation_id=reference_id
                                                                           )
    if capture_directly:
        # Not using capture_directly, instead using function create_recurring_payment
        merchant_initiated_transaction = \
            Ptsv2paymentsProcessingInformationAuthorizationOptionsInitiatorMerchantInitiatedTransaction(
                reason='',
                previous_transaction_id=network_transaction_id
            )
        initiator = Ptsv2paymentsProcessingInformationAuthorizationOptionsInitiator(
            stored_credential_used=True,
            merchant_initiated_transaction=merchant_initiated_transaction.__dict__,
            type='MERCHANT'
        )
        processing_information = Ptsv2paymentsProcessingInformation(
            reconciliation_id=reference_id,
            commerce_indicator="Recurring",
            capture=True,
            authorization_options=Ptsv2paymentsProcessingInformationAuthorizationOptions(
                initiator=initiator.__dict__
            ).__dict__
        )
    else:

        merchant_initiated_transaction = \
            Ptsv2paymentsProcessingInformationAuthorizationOptionsInitiatorMerchantInitiatedTransaction(
                reason=''
            )
        initiator = Ptsv2paymentsProcessingInformationAuthorizationOptionsInitiator(
            stored_credential_used=True,
            merchant_initiated_transaction=merchant_initiated_transaction.__dict__,
        )
        processing_information = Ptsv2paymentsProcessingInformation(
            reconciliation_id=reference_id,
            commerce_indicator='Internet',
            authorization_options=Ptsv2paymentsProcessingInformationAuthorizationOptions(
                initiator=initiator.__dict__,
                auth_indicator="0",
            ).__dict__,
        )
    amount_details = Ptsv2paymentsOrderInformationAmountDetails(total_amount=amount, currency=currency)
    order_information = Ptsv2paymentsOrderInformation(amount_details=amount_details.__dict__)
    payment_information = Ptsv2paymentsPaymentInformation(
        customer=Ptsv2paymentsPaymentInformationCustomer(id=customer_token).__dict__,
        # payment_instrument=Ptsv2paymentsPaymentInformationPaymentInstrument(id=payment_instrument_token).__dict__
    )

    request_obj_kwargs = {
        'client_reference_information': client_reference_information.__dict__,
        'processing_information': processing_information.__dict__,
        'order_information': order_information.__dict__,
        'payment_information': payment_information.__dict__
    }

    if is_3ds:
        consumer_auth_info = Ptsv2paymentsConsumerAuthenticationInformation(
            authentication_transaction_id=transaction_id)

        processing_information_action_list = []
        if bypass_consumer_auth_check:
            processing_information_action_list.append("CONSUMER_AUTHENTICATION")
        else:
            processing_information_action_list.append("VALIDATE_CONSUMER_AUTHENTICATION")
        processing_information = Ptsv2paymentsProcessingInformation(
            reconciliation_id=reference_id,
            commerce_indicator='Internet',
            action_list=processing_information_action_list,
            authorization_options=Ptsv2paymentsProcessingInformationAuthorizationOptions(
                initiator=initiator.__dict__
            ).__dict__
        )
        request_obj_kwargs["processing_information"] = processing_information.__dict__
        request_obj_kwargs["consumer_authentication_information"] = consumer_auth_info.__dict__

    request_obj = CreatePaymentRequest(**request_obj_kwargs)
    request_obj = del_none(request_obj.__dict__)
    request_obj = json.dumps(request_obj)

    try:
        if not isinstance(currency, str):
            currency = str(currency.value.upper())
        else:
            currency = str(currency.upper())
        merchant_type = '3ds' if is_3ds else 'n3ds'
        client_config = config_obj.get_configuration(merchant_type, str(currency).upper())
        api_instance = PaymentsApi(client_config)
        _, status, body = api_instance.create_payment(request_obj)
        print(f"CREATE PAYMENT API RESPONSE CODE: {status}")
        print(f"CREATE PAYMENT API RESPONSE BODY: {body}")
        # if auth_indicator == 0 and capture_directly:
        #     return capture_payment(json.loads(body), order_information, client_reference_information)
        return body
    except Exception as e:  # pylint: disable=broad-except
        print(f"\nException when calling PaymentsApi->create_payment: {e}\n")


# Function to capture a payment in CyberSource
def capture_payment(payment_response, order_information, client_reference_information, payment_type: bool,
                    currency: str, processing_information: Ptsv2paymentsProcessingInformation = None):
    request_obj = CapturePaymentRequest(
        client_reference_information=client_reference_information.__dict__,
        order_information=order_information.__dict__
    )
    if processing_information:
        request_obj = CapturePaymentRequest(
            client_reference_information=client_reference_information.__dict__,
            order_information=order_information.__dict__,
            processing_information=processing_information.__dict__,
        )
    request_obj = del_none(request_obj.__dict__)
    request_obj = json.dumps(request_obj)

    try:
        id = payment_response['id']
        print(f"Payment response id: {id}")
        merchant_type = '3ds' if payment_type else 'n3ds'
        # merchant_type = 'n3ds'
        if not isinstance(currency, str):
            currency = str(currency.value.upper())
        else:
            currency = str(currency.upper())
        client_config = config_obj.get_configuration(merchant_type, str(currency).upper())
        api_instance = CaptureApi(client_config)
        _, status, body = api_instance.capture_payment(request_obj, id)
        print(f"CAPTURE PAYMENT API RESPONSE CODE: {status}")
        print(f"CAPTURE PAYMENT API RESPONSE BODY: {body}")
        return body
    except Exception as e:  # pylint: disable=broad-except
        print(f"\nException when calling CaptureApi->capture_payment: {e}\n")
        raise


# Wrap data into cybersource payment
def capture_payment_wrapper(trans_id, order_id, amount, currency, payment_type):
    client_reference_information = Ptsv2paymentsClientReferenceInformation(code=order_id,
                                                                           reconciliation_id=order_id
                                                                           )
    amount_details = Ptsv2paymentsOrderInformationAmountDetails(total_amount=amount, currency=currency)
    order_information = Ptsv2paymentsOrderInformation(amount_details=amount_details.__dict__)
    processing_information = Ptsv2paymentsProcessingInformation(reconciliation_id=order_id)
    payment_request = {'id': trans_id}
    return capture_payment(payment_request, order_information, client_reference_information, payment_type, currency,
                           processing_information)


# Function to generate capture context in CyberSource
def generate_capture_context(currency: str):
    if not isinstance(currency, str):
        currency = str(currency.value.upper())
    else:
        currency = str(currency.upper())
    client_config = config_obj.get_configuration('3ds', str(currency).upper())
    api_instance = MicroformIntegrationApi(client_config)
    generate_capture_context_request = {
        "targetOrigins": [APOLLO_MAIN_DOMAIN],
        "clientReferenceInformation": {"code": "VISA"}
    }

    if USE_V2_MICROFORM:
        generate_capture_context_request.update({
            "clientVersion": "v2",
            "allowedCardNetworks": ["VISA", "MASTERCARD"]
        })

    request_obj = del_none(generate_capture_context_request)
    request_obj = json.dumps(request_obj)
    print(request_obj)

    try:
        return_data, status, body = api_instance.generate_capture_context(request_obj)
        return body, request_obj
    except Exception as e:  # pylint: disable=broad-except
        print("Error generating capture context with error as: %s", str(e))
        return None, None


# Function to setup completion with Flex transient token in CyberSource
def setup_completion_with_flex_transient_token(token_type: schema.CyberSourceTokenType, transient_token_payload: str,
                                               reference_id: str, db_cc: schema.CyberSourceCreditCard = None,
                                               currency: str = 'SGD'):
    client_reference_information = Riskv1decisionsClientReferenceInformation(code=reference_id)

    if token_type == schema.CyberSourceTokenType.transient_token:
        token_information = Riskv1authenticationsetupsTokenInformation(
            transient_token=get_transient_token_payload_from_jwt_transient_payload(transient_token_payload)
        )
        request_obj_kwargs = {'client_reference_information': client_reference_information.__dict__,
                              'token_information': token_information.__dict__}
    else:
        payment_information = Riskv1authenticationsetupsPaymentInformation(
            customer=Riskv1authenticationsetupsPaymentInformationCustomer(customer_id=db_cc.user_token).__dict__,
        )
        request_obj_kwargs = {'client_reference_information': client_reference_information.__dict__,
                              'payment_information': payment_information.__dict__}

    request_obj = PayerAuthSetupRequest(**request_obj_kwargs)
    request_obj = del_none(request_obj.__dict__)
    request_obj = json.dumps(request_obj)
    print(request_obj)

    try:
        if not isinstance(currency, str):
            currency = str(currency.value.upper())
        else:
            currency = str(currency.upper())
        client_config = config_obj.get_configuration('3ds', str(currency).upper())
        api_instance = PayerAuthenticationApi(client_config)
        return_data, status, body = api_instance.payer_auth_setup(request_obj)
        return body, request_obj
    except Exception as e:  # pylint: disable=broad-except
        print(f"\nException when calling PayerAuthenticationApi->payer_auth_setup: {e}\n")
        return None, None


# Function to check enrollment in CyberSource
def check_enrollment(db_member,  # noqa: MC0001
                     db_user, token_type: str, reference_id: str, return_url: str,
                     transient_token_payload: str, session_id: str,
                     amount: float, currency: schema.Currency, screen_width: str = None, screen_height: str = None,
                     ip_address: str = None):
    client_reference_information = Riskv1decisionsClientReferenceInformation(code=session_id)
    mandate_challenge = False
    try:
        db_first_name = db_member.first_name
        db_last_name = db_member.last_name
        db_email = db_user.email
        db_number = db_user.phone_number
    except AttributeError:
        db_first_name = db_member.get('first_name')
        db_last_name = db_member.get('last_name')
        db_email = db_member.get('email')
        db_number = db_member.get('phone_number')
        mandate_challenge = True

    if db_first_name is None:
        db_first_name = CDG_DEFAULT_FIRST_NAME
    if db_last_name is None:
        db_last_name = CDG_DEFAULT_LAST_NAME
    if db_email is None:
        db_email = CDG_DEFAULT_EMAIL
    if db_number is None:
        db_number = CDG_DEFAULT_NUMBER

    db_first_name = db_first_name.replace(' ', '')
    db_last_name = db_last_name.replace(' ', '')
    db_number = db_number.lstrip('+')

    order_information = Riskv1authenticationsOrderInformation(
        amount_details=Riskv1authenticationsOrderInformationAmountDetails(
            currency=currency,
            total_amount=amount
        ).__dict__,
        bill_to=Riskv1authenticationsOrderInformationBillTo(
            address1=CDG_DEFAULT_ADDRESS_1,
            # administrative_area=CDG_DEFAULT_ADMIN_AREA,
            country=CDG_DEFAULT_COUNTRY,
            locality=CDG_DEFAULT_LOCALITY,
            first_name=db_first_name,
            last_name=db_last_name,
            phone_number=db_number,
            email=db_email,
            postal_code=CDG_DEFAULT_POSTAL,
        ).__dict__
    )

    consumer_authentication_information = Riskv1decisionsConsumerAuthenticationInformation(
        return_url=return_url,
        reference_id=reference_id,
        acs_window_size='05',
    )
    if mandate_challenge:
        consumer_authentication_information.challenge_code = '04'

    request_obj_kwargs = {
        'client_reference_information': client_reference_information.__dict__,
        'order_information': order_information.__dict__,
        'consumer_authentication_information': consumer_authentication_information.__dict__
    }

    if screen_width:
        if ip_address:
            device_information = Riskv1authenticationsDeviceInformation(http_browser_screen_width=screen_width,
                                                                        http_browser_screen_height=screen_height,
                                                                        ip_address=ip_address)
        else:
            device_information = Riskv1authenticationsDeviceInformation(http_browser_screen_width=screen_width,
                                                                        http_browser_screen_height=screen_height)
        request_obj_kwargs['device_information'] = device_information.__dict__

    if token_type == schema.CyberSourceTokenType.transient_token:
        if not transient_token_payload:
            raise ValueError("Transient token not found")
        jti_value = get_transient_token_payload_from_jwt_transient_payload(transient_token_payload)
        token_information = Riskv1authenticationsetupsTokenInformation(transient_token=jti_value)
        request_obj_kwargs['token_information'] = token_information.__dict__
    else:
        customer_information = Riskv1authenticationsetupsPaymentInformationCustomer(customer_id=transient_token_payload)
        payment_information = Riskv1authenticationsetupsPaymentInformation(customer=customer_information.__dict__)
        request_obj_kwargs['payment_information'] = payment_information.__dict__

    request_obj = CheckPayerAuthEnrollmentRequest(**request_obj_kwargs)
    request_obj = del_none(request_obj.__dict__)
    request_obj = json.dumps(request_obj)
    print(request_obj)
    try:
        if not isinstance(currency, str):
            currency = str(currency.value.upper())
        else:
            currency = str(currency.upper())
        client_config = config_obj.get_configuration('3ds', str(currency).upper())
        api_instance = PayerAuthenticationApi(client_config)
        return_data, status, body = api_instance.check_payer_auth_enrollment(request_obj)
        return body, request_obj
    except Exception as e:  # pylint: disable=broad-except
        print(f"\nException when calling PayerAuthenticationApi->check_payer_auth_enrollment: {e}\n")
        return None, None


# Function to validate authentication results in CyberSource
def validate_authentication_results(token_type: schema.CyberSourceTokenType, transaction_id: str, reference_id: str,
                                    transient_token_payload: str, amount: float, currency):
    client_reference_information = Riskv1decisionsClientReferenceInformation(code=reference_id)
    order_information = Riskv1authenticationresultsOrderInformation(
        amount_details=Riskv1authenticationresultsOrderInformationAmountDetails(
            currency=currency,
            total_amount=amount
        ).__dict__
    )
    consumer_authentication_information = Riskv1authenticationresultsConsumerAuthenticationInformation(
        authentication_transaction_id=transaction_id
    )
    request_obj_kwargs = {
        'client_reference_information': client_reference_information.__dict__,
        'order_information': order_information.__dict__,
        'consumer_authentication_information': consumer_authentication_information.__dict__
    }

    if token_type == schema.CyberSourceTokenType.transient_token:
        jti_value = get_transient_token_payload_from_jwt_transient_payload(transient_token_payload)
        token_information = Riskv1authenticationsetupsTokenInformation(transient_token=jti_value)
        request_obj_kwargs['token_information'] = token_information.__dict__
    else:
        customer_information = Riskv1authenticationsetupsPaymentInformationCustomer(customer_id=transient_token_payload)
        payment_information = Riskv1authenticationsetupsPaymentInformation(customer=customer_information.__dict__)
        request_obj_kwargs['payment_information'] = payment_information.__dict__

    request_obj = ValidateRequest(**request_obj_kwargs)
    request_obj = del_none(request_obj.__dict__)
    request_obj = json.dumps(request_obj)
    print(request_obj)

    try:
        if not isinstance(currency, str):
            currency = str(currency.value.upper())
        else:
            currency = str(currency.upper())
        client_config = config_obj.get_configuration('3ds', str(currency).upper())
        api_instance = PayerAuthenticationApi(client_config)
        return_data, status, body = api_instance.validate_authentication_results(request_obj)
        print(f"API RESPONSE CODE: {status}")
        print(f"API RESPONSE BODY: {body}")
        return body, request_obj
    except Exception as e:  # pylint: disable=broad-except
        print(f"\nException when calling PayerAuthenticationApi->validate_authentication_results: {e}\n")
        return None, None


# noqa: MC0001, #pylint:disable=too-many-statements, too-many-locals
def create_tms_token_with_pa(transaction_id, reference_id, transient_token_payload,  # noqa: MC0001
                             db_member, db_user, amount,
                             currency: str = 'USD', screen_width: str = None, screen_height: str = None,
                             save_cc: bool = True, bypass_consumer_auth_check: bool = False,
                             payment_form_id: str = None, ip_address: str = None):
    client_reference_information_code = reference_id
    client_reference_information = Ptsv2paymentsClientReferenceInformation(
        code=client_reference_information_code,
        reconciliation_id=reference_id,
    )
    if not transient_token_payload:
        raise ValueError("Transient token not found")

    token_information = Ptsv2paymentsTokenInformation(
        transient_token_jwt=transient_token_payload,
    )

    processing_information_action_list = ["TOKEN_CREATE", "VALIDATE_CONSUMER_AUTHENTICATION"]
    if bypass_consumer_auth_check:
        processing_information_action_list.remove("VALIDATE_CONSUMER_AUTHENTICATION")
        processing_information_action_list.append('CONSUMER_AUTHENTICATION')

    # processing_information_action_list = ["TOKEN_CREATE"]
    processing_information_action_token_types = ["customer", "paymentInstrument"]
    processing_information_capture = False
    processing_information = Ptsv2paymentsProcessingInformation(
        reconciliation_id=reference_id,
        action_list=processing_information_action_list,
        action_token_types=processing_information_action_token_types,
        capture=processing_information_capture
    )
    if COMPLY_UOB_COMPLIANCE:
        processing_info_store_credentials = True
        processing_info_auth = (
            Ptsv2paymentsProcessingInformationAuthorizationOptionsInitiator(
                credential_stored_on_file=processing_info_store_credentials
            ))
        processing_info_auth_options = Ptsv2paymentsProcessingInformationAuthorizationOptions(
            initiator=processing_info_auth.__dict__
        )
        processing_information.authorization_options = processing_info_auth_options.__dict__

    order_information_amount_details_total_amount = amount
    order_information_amount_details_currency = currency
    order_information_details = Ptsv2paymentsOrderInformationAmountDetails(
        total_amount=order_information_amount_details_total_amount,
        currency=order_information_amount_details_currency
    )

    db_first_name = db_member.first_name
    db_last_name = db_member.last_name
    db_email = db_user.email
    db_number = db_user.phone_number
    if db_first_name is None:
        db_first_name = CDG_DEFAULT_FIRST_NAME
    if db_last_name is None:
        db_last_name = CDG_DEFAULT_LAST_NAME
    if db_email is None:
        db_email = CDG_DEFAULT_EMAIL
    if db_number is None:
        db_number = CDG_DEFAULT_NUMBER

    order_information_bill_to_first_name = db_first_name.replace(' ', '')
    order_information_bill_to_last_name = db_last_name.replace(' ', '')
    order_information_bill_to_email = db_email
    order_information_bill_to_phone_number = db_number.lstrip('+')

    order_information_bill_to_address1 = CDG_DEFAULT_ADDRESS_1
    order_information_bill_to_locality = CDG_DEFAULT_LOCALITY
    # order_information_bill_to_administrative_area = CDG_DEFAULT_ADMIN_AREA
    order_information_bill_to_postal_code = CDG_DEFAULT_POSTAL
    order_information_bill_to_country = CDG_DEFAULT_COUNTRY

    order_information_bill_to = Ptsv2paymentsOrderInformationBillTo(
        first_name=order_information_bill_to_first_name,
        last_name=order_information_bill_to_last_name,
        address1=order_information_bill_to_address1,
        locality=order_information_bill_to_locality,
        # administrative_area=order_information_bill_to_administrative_area,
        postal_code=order_information_bill_to_postal_code,
        country=order_information_bill_to_country,
        email=order_information_bill_to_email,
        phone_number=order_information_bill_to_phone_number
    )
    order_information = Ptsv2paymentsOrderInformation(
        amount_details=order_information_details.__dict__,
        bill_to=order_information_bill_to.__dict__,
    )
    # if transaction_id:
    #     consumerAuthenticationInformationAuthenticationTransactionId = transaction_id
    #     consumerAuthenticationInformation = Ptsv2paymentsConsumerAuthenticationInformation(
    #         authentication_transaction_id=consumerAuthenticationInformationAuthenticationTransactionId
    #     )

    #     request_obj = CreatePaymentRequest(
    #         client_reference_information=client_reference_information.__dict__,
    #         processing_information=processing_information.__dict__,
    #         order_information=order_information.__dict__,
    #         token_information=token_information.__dict__,
    #         consumer_authentication_information=consumerAuthenticationInformation.__dict__
    #     )
    # else:
    #     request_obj = CreatePaymentRequest(
    #         client_reference_information=client_reference_information.__dict__,
    #         processing_information=processing_information.__dict__,
    #         order_information=order_information.__dict__,
    #         token_information=token_information.__dict__,
    #     )

    consumer_auth_id = transaction_id
    consumer_auth_info = Ptsv2paymentsConsumerAuthenticationInformation(
        authentication_transaction_id=consumer_auth_id
    )

    request_obj_kwargs = {
        "client_reference_information": client_reference_information.__dict__,
        "processing_information": processing_information.__dict__,
        "order_information": order_information.__dict__,
        "token_information": token_information.__dict__,
        "consumer_authentication_information": consumer_auth_info.__dict__
    }
    if USE_V2_MICROFORM:
        payment_card_type = "001"
        payment_info_card = Ptsv2paymentsidrefundsPaymentInformationCard(
            type=payment_card_type
        )

        payment_information = Ptsv2paymentsPaymentInformation(
            card=payment_info_card.__dict__
        )
        request_obj_kwargs['payment_information'] = payment_information.__dict__
    # if bypass_consumer_auth_check:
    #     del request_obj_kwargs["consumer_authentication_information"]

    if screen_width:
        if ip_address:
            device_information = Ptsv2paymentsDeviceInformation(http_browser_screen_width=screen_width,
                                                                http_browser_screen_height=screen_height,
                                                                ip_address=ip_address)
        else:
            device_information = Ptsv2paymentsDeviceInformation(http_browser_screen_width=screen_width,
                                                                http_browser_screen_height=screen_height)
        request_obj_kwargs['device_information'] = device_information.__dict__

    request_obj = CreatePaymentRequest(**request_obj_kwargs)
    request_obj = del_none(request_obj.__dict__)
    request_obj = json.dumps(request_obj)

    try:
        if not isinstance(currency, str):
            currency = str(currency.value.upper())
        else:
            currency = str(currency.upper())
        client_config = config_obj.get_configuration('3ds', str(currency).upper())
        api_instance_payment = PaymentsApi(client_config)
        _, status, body_payment = api_instance_payment.create_payment(request_obj)
        response_body = json.loads(body_payment)

        with contextmanager(create_session)() as dbsession:
            tms_step = CyberSourceTMSStepCreate(
                payment_form_id=payment_form_id,
                transaction_id=transaction_id,
                response_body=response_body,
                billing_info=order_information_bill_to.__dict__,
                order_info=order_information_details.__dict__,
                request_body=json.loads(request_obj),
            )
            crud.create_cybersource_tms_step(dbsession, tms_step)
            error_info = response_body.get('errorInformation')
            if error_info and error_info.get('reason'):
                _ = error_info.get('message')
                error_code = error_info.get('reason')
                return False, error_code
            payment_instrument_id = response_body['tokenInformation']['paymentInstrument']['id']
            customer_id = response_body['tokenInformation']['customer']['id']

            result = update_customer_default_payment_instrument_card(customer_id, payment_instrument_id)
            result = json.loads(result)
            credit_card_info = schema.CreateCyberSourceCreditCard(
                brand=schema.cybersource_card_numerical_to_enum.get(result['card']['type']).value,
                currency=result['buyerInformation']['currency'],
                last_four_digit=result['_embedded']['instrumentIdentifier']['card']['number'][-4:],
                type='Credit',  # todo: need to find the actual mapping from cybersource payment instrument
                primary=True,
                token=payment_instrument_id,
                user_token=customer_id,
                bill_name=f"{db_member.first_name} {db_member.last_name}",
                bill_first_name=db_member.first_name,
                bill_last_name=db_member.last_name,
                bill_email=result['billTo']['email'],
                bill_mobile=f"+{result['billTo']['phoneNumber']}",
                card_type=result['card']['type'],
                expiry_month=result['card']['expirationMonth'],
                expiry_year=result['card']['expirationYear'],
                payment_gateway=schema.CreditCardPaymentGateway.cybersource,
                response=response_body,
                network_transaction_id=response_body['processorInformation']['networkTransactionId']
            )
            if save_cc:
                if check_cc_is_not_blacklisted(dbsession, db_member.id, result, currency, db_user.phone_number,
                                               db_user.email):
                    raise exceptions.ApolloBlacklistedCreditCard

                db_primary_cc = get_member_primary_cc(dbsession, db_member.id, result['buyerInformation']['currency'])
                if db_primary_cc:
                    db_primary_cc.primary = False

                crud.create_cc(dbsession, credit_card_info, db_member.id)

        print('------------------------------------------------------------')
        print("\nAPI RESPONSE CODE : ", status)
        print("\nAPI RESPONSE BODY : ", body_payment)
        print("\nAPI RESPONSE BODY : ", result)
        # print("\nAPI RESPONSE BODY : ", _)
        print('------------------------------------------------------------')
        return True, True
    except exceptions.ApolloBlacklistedCreditCard:
        raise exceptions.ApolloBlacklistedCreditCard
    except Exception as e:  # pylint: disable=broad-except
        print("\nException when calling PaymentsApi->create_payment: %s\n" % e)
        return False, None


# noqa: MC0001, #pylint:disable=too-many-statements, too-many-locals
def void_payment(reference_id: str, trans_id: str, is_3ds: bool, currency: str):
    client_reference_information = Ptsv2paymentsidreversalsClientReferenceInformation(
        code=reference_id,
        reconciliation_id=reference_id,
    )

    request_obj = VoidPaymentRequest(
        client_reference_information=client_reference_information.__dict__
    )

    request_obj = del_none(request_obj.__dict__)
    request_obj = json.dumps(request_obj)

    try:
        if not isinstance(currency, str):
            currency = str(currency.value.upper())
        else:
            currency = str(currency.upper())
        if is_3ds:
            client_config = config_obj.get_configuration('3ds', str(currency).upper())
        else:
            client_config = config_obj.get_configuration('n3ds', str(currency).upper())
        # client_config = config_obj.get_configuration()
        api_instance = VoidApi(client_config)
        return_data, status, body = api_instance.void_payment(request_obj, trans_id)

        print("\nAPI RESPONSE CODE : ", status)
        print("\nAPI RESPONSE BODY : ", body)
        print(f"VOID PAYMENT RETURN DATA:\n{return_data}")

        return body
    except Exception as e:  # pylint: disable=broad-except
        print("\nException when calling VoidApi->void_payment: %s\n" % e)
        raise


# Function to create a recurring payment in CyberSource
# noqa: MC0001, #pylint:disable=too-many-statements, too-many-locals
def create_recurring_payment(customer_token: str, network_transaction_id: str, user_info: dict, amount: float,
                             currency: schema.Currency, reference_number: str):
    client_reference_information = Ptsv2paymentsClientReferenceInformation(code=reference_number,
                                                                           reconciliation_id=reference_number
                                                                           )
    merchant_initiated_transaction = \
        Ptsv2paymentsProcessingInformationAuthorizationOptionsInitiatorMerchantInitiatedTransaction(
            reason='',
            previous_transaction_id=network_transaction_id
        )
    initiator = Ptsv2paymentsProcessingInformationAuthorizationOptionsInitiator(
        stored_credential_used=True,
        merchant_initiated_transaction=merchant_initiated_transaction.__dict__,
        type='MERCHANT'
    )
    processing_information = Ptsv2paymentsProcessingInformation(
        reconciliation_id=reference_number,
        capture=True,
        commerce_indicator="RECURRING",
        authorization_options=Ptsv2paymentsProcessingInformationAuthorizationOptions(
            initiator=initiator.__dict__
        ).__dict__
    )
    if COMPLY_UOB_COMPLIANCE:
        processing_information.commerce_indicator = "Internet"

    if not isinstance(currency, str):
        currency = str(currency.value.upper())
    else:
        currency = str(currency.upper())

    amount_details = Ptsv2paymentsOrderInformationAmountDetails(total_amount=str(amount), currency=str(currency))
    bill_to = Riskv1authenticationsOrderInformationBillTo(
        address1=CDG_DEFAULT_ADDRESS_1,
        # administrative_area=CDG_DEFAULT_ADMIN_AREA,
        country=CDG_DEFAULT_COUNTRY,
        locality=CDG_DEFAULT_LOCALITY,
        first_name=user_info.get('first_name'),
        last_name=user_info.get('last_name'),
        phone_number=user_info.get('phone_number'),
        email=user_info.get('email'),
        postal_code=CDG_DEFAULT_POSTAL,
    )
    order_information = Ptsv2paymentsOrderInformation(amount_details=amount_details.__dict__,
                                                      bill_to=bill_to.__dict__)

    payment_information = Ptsv2paymentsPaymentInformation(
        customer=Ptsv2paymentsPaymentInformationCustomer(id=customer_token).__dict__,
    )

    request_obj_kwargs = {
        'client_reference_information': client_reference_information.__dict__,
        'processing_information': processing_information.__dict__,
        'order_information': order_information.__dict__,
        'payment_information': payment_information.__dict__
    }

    request_obj = CreatePaymentRequest(**request_obj_kwargs)
    request_obj = del_none(request_obj.__dict__)
    request_obj = json.dumps(request_obj)

    try:
        if not isinstance(currency, str):
            currency = str(currency.value.upper())
        else:
            currency = str(currency.upper())

        client_config = config_obj.get_configuration(currency=str(currency).upper())
        api_instance = PaymentsApi(client_config)
        _, status, body = api_instance.create_payment(request_obj)
        print(f"CREATE PAYMENT API RESPONSE CODE: {status}")
        print(f"CREATE PAYMENT API RESPONSE BODY: {body}")
        return body
    except Exception as e:  # pylint: disable=broad-except
        print(f"\nException when calling PaymentsApi->create_payment (Recurring): {e}\n")
        raise e


def check_if_preauth_is_voided_cybs(payment_id, currency, payment_type):
    try:
        merchant_type = '3ds' if payment_type else 'n3ds'
        currency = currency.upper() if isinstance(currency, str) else currency.value.upper()
        client_config = config_obj.get_configuration(merchant_type, currency)
        api_instance = TransactionDetailsApi(client_config)

        # Retrieve original transaction details
        original_transaction, status, body = api_instance.get_transaction(payment_id)

        # Parse the body string as JSON
        body = json.loads(body)

        # Access the related transactions within the parsed JSON body
        related_transactions = body.get('_links', {}).get('relatedTransactions', [])
        for related in related_transactions:
            related_href = related.get('href')
            if related_href:
                # Extract the transaction ID from the URL
                related_id = related_href.split('/')[-1]
                _, related_status, related_body = api_instance.get_transaction(related_id)

                # Parse the related_body as JSON
                related_body = json.loads(related_body)

                # Check if the related transaction is an ics_payment_cancel and successful
                applications = related_body.get("applicationInformation", {}).get("applications", [])
                for app in applications:
                    if app.get("name") == "ics_payment_cancel" and app.get("rCode") == "1":
                        print(f"Pre-authorization {payment_id} has been successfully voided by ics_payment_cancel.")
                        return True

        print(f"No successful ics_payment_cancel found for pre-authorization {payment_id}.")
        return False
    except Exception as e:
        print(f"Exception when checking pre-authorization status: {e}")
        raise


# Function to create a payment in CyberSource
# noqa: MC0001, #pylint:disable=too-many-statements, too-many-locals
def create_payment_guest(guest_info: dict, transient_token_payload: str, amount: float, currency: schema.Currency,
                         reference_id: str = 'TC50171_3',
                         is_3ds: bool = False, transaction_id: str = None,
                         bypass_consumer_auth_check: bool = False):
    client_reference_information = Ptsv2paymentsClientReferenceInformation(code=reference_id,
                                                                           reconciliation_id=reference_id
                                                                           )

    processing_information = Ptsv2paymentsProcessingInformation(
        reconciliation_id=reference_id,
        commerce_indicator='Internet',
    )
    order_information_amount_details_total_amount = amount
    order_information_amount_details_currency = currency
    order_information_details = Ptsv2paymentsOrderInformationAmountDetails(
        total_amount=order_information_amount_details_total_amount,
        currency=order_information_amount_details_currency
    )

    token_information = Ptsv2paymentsTokenInformation(
        transient_token_jwt=transient_token_payload,
    )

    db_first_name = guest_info.get('first_name')
    db_last_name = guest_info.get('last_name')
    db_email = guest_info.get('email')
    db_number = guest_info.get('phone_number')
    if db_first_name is None:
        db_first_name = CDG_DEFAULT_FIRST_NAME
    if db_last_name is None:
        db_last_name = CDG_DEFAULT_LAST_NAME
    if db_email is None:
        db_email = CDG_DEFAULT_EMAIL
    if db_number is None:
        db_number = CDG_DEFAULT_NUMBER

    order_information_bill_to_first_name = db_first_name.replace(' ', '')
    order_information_bill_to_last_name = db_last_name.replace(' ', '')
    order_information_bill_to_email = db_email
    order_information_bill_to_phone_number = db_number.lstrip('+')

    order_information_bill_to_address1 = CDG_DEFAULT_ADDRESS_1
    order_information_bill_to_locality = CDG_DEFAULT_LOCALITY
    # order_information_bill_to_administrative_area = CDG_DEFAULT_ADMIN_AREA
    order_information_bill_to_postal_code = CDG_DEFAULT_POSTAL
    order_information_bill_to_country = CDG_DEFAULT_COUNTRY

    order_information_bill_to = Ptsv2paymentsOrderInformationBillTo(
        first_name=order_information_bill_to_first_name,
        last_name=order_information_bill_to_last_name,
        address1=order_information_bill_to_address1,
        locality=order_information_bill_to_locality,
        # administrative_area=order_information_bill_to_administrative_area,
        postal_code=order_information_bill_to_postal_code,
        country=order_information_bill_to_country,
        email=order_information_bill_to_email,
        phone_number=order_information_bill_to_phone_number
    )
    order_information = Ptsv2paymentsOrderInformation(
        amount_details=order_information_details.__dict__,
        bill_to=order_information_bill_to.__dict__,
    )
    request_obj_kwargs = {
        'client_reference_information': client_reference_information.__dict__,
        'processing_information': processing_information.__dict__,
        'order_information': order_information.__dict__,
        'token_information': token_information.__dict__,
    }

    if is_3ds:
        consumer_auth_info = Ptsv2paymentsConsumerAuthenticationInformation(
            authentication_transaction_id=transaction_id)

        processing_information_action_list = []
        if bypass_consumer_auth_check:
            processing_information_action_list.append("CONSUMER_AUTHENTICATION")
        else:
            processing_information_action_list.append("VALIDATE_CONSUMER_AUTHENTICATION")
        processing_information = Ptsv2paymentsProcessingInformation(
            reconciliation_id=reference_id,
            commerce_indicator='Internet',
            action_list=processing_information_action_list,
        )
        request_obj_kwargs["processing_information"] = processing_information.__dict__
        request_obj_kwargs["consumer_authentication_information"] = consumer_auth_info.__dict__

    request_obj = CreatePaymentRequest(**request_obj_kwargs)
    request_obj = del_none(request_obj.__dict__)
    request_obj = json.dumps(request_obj)

    try:
        if not isinstance(currency, str):
            currency = str(currency.value.upper())
        else:
            currency = str(currency.upper())
        merchant_type = '3ds' if is_3ds else 'n3ds'
        client_config = config_obj.get_configuration(merchant_type, str(currency).upper())
        api_instance = PaymentsApi(client_config)
        _, status, body = api_instance.create_payment(request_obj)
        print(f"CREATE PAYMENT API RESPONSE CODE: {status}")
        print(f"CREATE PAYMENT API RESPONSE BODY: {body}")
        # if auth_indicator == 0 and capture_directly:
        #     return capture_payment(json.loads(body), order_information, client_reference_information)
        return body
    except Exception as e:  # pylint: disable=broad-except
        print(f"\nException when calling PaymentsApi->create_payment: {e}\n")
