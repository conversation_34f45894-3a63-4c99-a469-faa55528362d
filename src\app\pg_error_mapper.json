[{"original_message": ["verification", "CC_05", "Transaction not approved - Do not honor / Incorrect CVV or 3D password", "Cardholder failed the 3D authentication, password entered by cardholder is incorrect and transaction is aborted - Do not honor / Incorrect CVV or 3D password"], "mapped_message": "Sorry, we couldn't verify your payment details. Please check your CVV or 3D password and try again."}, {"original_message": ["CC__B0011", "Unsupported PSP MCC, please check your profile setup in EVO Cloud"], "mapped_message": "It seems there is a configuration issue on our end. Please contact support with error code CC-B0011."}, {"original_message": ["Transaction not approved - Expired card"], "mapped_message": "Your card has expired. Please use a different card or update your card details."}, {"original_message": ["CC_54", "Transaction not permitted to cardholder"], "mapped_message": "Your card has been declined. Please contact your bank for more information."}, {"original_message": ["CAPTURE-NOT-AVAILABLE"], "mapped_message": "We were unable to process your payment at this time. Please try again later."}, {"original_message": ["CARD-NOT-SUPPORTED"], "mapped_message": "The card type you are using is not supported. Please use a different card."}, {"original_message": ["NOT-AVAILABLE-AT-THIS-MOMENT-PARES-U"], "mapped_message": "Authentication is currently unavailable. Please try again later."}, {"original_message": ["NOT-SUPPORTING-3DS-RES-A"], "mapped_message": "Your card issuer does not support 3D Secure authentication."}, {"original_message": ["TIMEOUT-ENROLL-U"], "mapped_message": "The enrollment process timed out. Please try again."}, {"original_message": ["AUTHENTICATION-FAILED-RES-N"], "mapped_message": "Authentication failed. Please verify your details and try again."}, {"original_message": ["AUTHENTICATION-REJECTED-RES-R"], "mapped_message": "Authentication was rejected. Please contact your bank for further assistance."}, {"original_message": ["TransactionId"], "mapped_message": "Transaction ID is missing. Please try again."}, {"original_message": ["Response"], "mapped_message": "A response was not received. Please try again."}, {"original_message": ["MD"], "mapped_message": "Required data is missing. Please try again."}, {"original_message": ["30159", "Suspected fraud"], "mapped_message": "Transaction failed by the card's issuing bank as a suspicious transaction. Try another card or contact your bank for more information"}, {"original_message": ["30164", "Transaction does not fulfill AMLrequirement"], "mapped_message": "Transaction failed by the card's issuing bank as a suspicious transaction. Try another card or contact your bank for more information"}, {"original_message": ["30182", "Policy"], "mapped_message": "Transaction failed by the card's issuing bank as a suspicious transaction. Try another card or contact your bank for more information"}, {"original_message": ["30183", "Fraud/Security(MasterCard use only) / Unable to verify PIN"], "mapped_message": "Transaction failed by the card's issuing bank as a suspicious transaction. Try another card or contact your bank for more information"}, {"original_message": ["30105", "Do not honor / Incorrect CVV or 3D password"], "mapped_message": "Transaction failed by the card's issuing bank. Please try another card."}, {"original_message": ["30106", "Error"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30114", "Invalid account number (no such number)"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30115", "No such issuer"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30121", "No action taken (unable to back out prior transaction)"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30125", "Unable to locate record in file, or account number is missing from the inquiry"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30139", "No Credit Account"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30141", "Pickup card (lost card)"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30146", "Closed account"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30152", "No checking account"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30153", "No savings account"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30155", "Incorrect PIN (Visa Cash-invalid or missing S1 signature)"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30179", "Updated information was found in the Mastercard ABU database. Check for new information before reattempting."], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30182", "Incorrect CVV/iCVV"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["301N7", "Decline for CVV2 failure"], "mapped_message": "Transaction failed by the card's issuing bank. The card information or card number may not be up to date. Please try another card."}, {"original_message": ["30178", "Blocked, first used. Transaction from new cardholder, and card not properly unblocked"], "mapped_message": "Transaction failed. Please ensure this card is enabled for online transactions"}, {"original_message": ["30178", "Invalid/nonexistent account specified (general)"], "mapped_message": "Transaction failed. Please ensure this card is enabled for online transactions"}, {"original_message": ["30103", "Invalid merchant or service provider"], "mapped_message": "Transaction failed. Please try again later or contact our hotline if the issue persists"}, {"original_message": ["30113", "Invalid amount (currency conversion field overflow. Visa Cash-invalid load mount)"], "mapped_message": "Transaction failed. Please try again later or contact our hotline if the issue persists"}, {"original_message": ["30119", "Re-enter transaction"], "mapped_message": "Transaction failed. Please try again later or contact our hotline if the issue persists"}, {"original_message": ["30130", "Format Error"], "mapped_message": "Transaction failed. Please try again later or contact our hotline if the issue persists"}, {"original_message": ["30184", "Invalid Authorization Life Cycle"], "mapped_message": "Transaction failed. Please try again later or contact our hotline if the issue persists"}, {"original_message": ["30192", "Destination cannot be found for routing"], "mapped_message": "Transaction failed. Please try again later or contact our hotline if the issue persists"}, {"original_message": ["30194", "Duplicate transaction detected"], "mapped_message": "Transaction failed. Please try again later or contact our hotline if the issue persists"}, {"original_message": ["30196", "System malfunction or certain field error conditions"], "mapped_message": "Transaction failed. Please try again later or contact our hotline if the issue persists"}, {"original_message": ["30101", "Refer to card issuer"], "mapped_message": "Transaction failed. Please try again later or contact your bank for more information."}, {"original_message": ["30102", "Refer to card issuer, special condition"], "mapped_message": "Transaction failed. Please try again later or contact your bank for more information."}, {"original_message": ["30112", "Invalid transaction"], "mapped_message": "Transaction failed. Please try again later or contact your bank for more information."}, {"original_message": ["30128", "File is temporarily unavailable"], "mapped_message": "Transaction failed. Please try again later or contact your bank for more information."}, {"original_message": ["30186", "Pin Validation not possible"], "mapped_message": "Transaction failed. Please try again later or contact your bank for more information."}, {"original_message": ["30190", "Cutoff is in progress"], "mapped_message": "Transaction failed. Please try again later or contact your bank for more information."}, {"original_message": ["30191", "Issuer unavailable or switch inoperative (STIP not applicable or available for this transaction)"], "mapped_message": "Transaction failed. Please try again later or contact your bank for more information."}, {"original_message": ["30163", "Security violation"], "mapped_message": "Transaction failed. Please try another card or contact your bank for more information."}, {"original_message": ["30170", "Contact Card Issuer"], "mapped_message": "Transaction failed. Please try another card or contact your bank for more information."}, {"original_message": ["30175", "Allowable number of PIN-entry tries exceeded"], "mapped_message": "Transaction failed. Please try another card or contact your bank for more information."}, {"original_message": ["30187", "Purchase Amount Only, No Cash Back Allowed"], "mapped_message": "Transaction failed. Please try another card or contact your bank for more information."}, {"original_message": ["30193", "Transaction cannot be completed; violation of law"], "mapped_message": "Transaction failed. Please try another card or contact your bank for more information."}, {"original_message": ["30157", "Transaction not permitted to cardholder (Visa Cash-incorrect routing, not a load request)"], "mapped_message": "Transaction failed. This card cannot be used for this type of transaction"}, {"original_message": ["30158", "Transaction not allowed at terminal"], "mapped_message": "Transaction failed. This card cannot be used for this type of transaction"}, {"original_message": ["30162", "Restricted card (for example, in Country Exclusion table)"], "mapped_message": "Transaction failed. This card cannot be used for this type of transaction. Try another card or contact your bank for more information"}, {"original_message": ["30161", "Activity amount limit exceeded"], "mapped_message": "Transaction failed. This card has exceeded its credit limit"}, {"original_message": ["30151", "Insufficient funds", "Insufficient Funds"], "mapped_message": "Transaction failed. This card has insufficient funds or credit limit"}, {"original_message": ["30165", "Activity count limit exceeded"], "mapped_message": "Transaction failed. This card has insufficient funds or credit limit"}, {"original_message": ["30154", "Expired card"], "mapped_message": "Transaction failed. This card may be expired"}, {"original_message": ["30104", "Pickup card"], "mapped_message": "Transaction failed. This card may have been reported as lost, stolen, for fraud or is expired. Please try another card."}, {"original_message": ["30107", "Pickup card, special condition (other than lost/stolen card)"], "mapped_message": "Transaction failed. This card may have been reported as lost, stolen, for fraud or is expired. Please try another card."}, {"original_message": ["30143", "Pickup card (stolen card)"], "mapped_message": "Transaction failed. This card may have been reported as lost, stolen, for fraud or is expired. Please try another card."}]