from datetime import datetime
from uuid import UUID
from enum import Enum
from typing import Optional, List, Union
import bcrypt
import phonenumbers

from pydantic import BaseModel, validator, constr, <PERSON><PERSON>, EmailStr, Field, root_validator

from .auth import User, SignupRequest, MembershipType, validator_non_empty_string, VerificationMethodEnum, \
    NonMigratedUser


class PermissionRequiredResource(BaseModel):
    path: str
    method: str

    @validator('path')
    def validate_path(cls, path: str):
        if path.startswith('/'):
            raise ValueError('Path cannot start with a "/".')
        return path

    @validator('method')
    def validate_method(cls, method: str):
        if method.lower() not in ('get', 'post', 'patch', 'put', 'delete',):
            raise ValueError('Invalid method.')
        return method


class PreAuthPaymentMethodEnums(str, Enum):
    credit_card = 'Credit-Card'
    wallet = 'Wallet'


class PreferredPaymentFlowEnums(str, Enum):
    credit_card = 'Credit-Card'
    wallet = 'Wallet'
    partial = 'Partial'


class ResourceAccessCheckResponse(BaseModel):
    allow_access: bool
    message: str
    resource_id: Optional[str]  # pylint:disable=unsubscriptable-object


class ResourceAccessCheck(BaseModel):
    resource_url: str
    method: str


class ResourceServer(BaseModel):
    name: str
    url: constr(min_length=3)


# A pylint issue on the Optional class https://github.com/PyCQA/pylint/issues/3882
# pylint:disable=unsubscriptable-object
class ResourceServerUpdate(BaseModel):
    name: Optional[str] = ''
    url: Optional[str] = ''

    @validator('name')
    def prevent_name_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)

    @validator('url')
    def prevent_url_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)


class ResourceServerResponse(BaseModel):
    id: UUID
    name: str
    url: str

    class Config:
        orm_mode = True


class Resource(BaseModel):
    name: str
    path: constr(min_length=3)
    scope: constr(min_length=3)
    resourceserver_id: UUID


class ResourceResponse(BaseModel):
    id: UUID
    name: str
    path: str
    scope: str

    resourceserver: ResourceServerResponse

    class Config:
        orm_mode = True


# A pylint issue on the Optional class https://github.com/PyCQA/pylint/issues/3882
# pylint:disable=unsubscriptable-object
class ResourceUpdate(BaseModel):
    name: Optional[str] = ''
    path: Optional[str] = ''
    scope: Optional[str] = ''

    @validator('path')
    def prevent_path_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)

    @validator('scope')
    def prevent_scope_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)


class StaffMembership(SignupRequest):
    organization_id: Optional[UUID]


class CustomMembership(StaffMembership):
    role_id: Optional[UUID]


class Role(BaseModel):
    organization_id: Optional[UUID]
    name: constr(min_length=3)


class RoleResponse(BaseModel):
    id: UUID
    organization_id: Optional[UUID]
    name: str
    user_count: Optional[int]

    resources: List[ResourceResponse]

    class Config:
        orm_mode = True


class Membership(BaseModel):
    first_name: str
    last_name: str
    user_id_tag: str
    organization_id: UUID
    user_id: UUID
    membership_type: MembershipType = MembershipType.regular_user
    password: str
    vehicle_model: Optional[str]  # pylint:disable=unsubscriptable-object
    allow_marketing: Optional[bool]

    @validator('password')
    def hash_password(cls, password: str, values: dict):
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt(11, b'2a')).decode('utf-8')


class MembershipSegment(str, Enum):
    new = 'New'
    recent = 'Recent'
    premium = 'Premium'


class MemberPreAuthInfo(BaseModel):
    pre_auth_currency: str
    pre_auth_amount: Optional[float]
    pre_auth_type: Optional[str]


class MemberPreAuthInfoResponse(BaseModel):
    id: UUID
    pre_auth_currency: str
    pre_auth_amount: float
    pre_auth_type: str
    created_at: datetime
    is_deleted: Optional[datetime]
    member_id: str


class GuestMembershipEnroll(BaseModel):
    user_id_tag: str
    organization_id: UUID
    user_id: UUID
    membership_type: MembershipType = MembershipType.regular_user
    vehicle_model: Optional[str]  # pylint:disable=unsubscriptable-object
    first_name: Optional[str]  # pylint:disable=unsubscriptable-object
    last_name: Optional[str]  # pylint:disable=unsubscriptable-object
    allow_marketing: Optional[bool]  # pylint:disable=unsubscriptable-object


class OperatorSharingType(str, Enum):
    shared = 'Shared'
    sub_organization = 'Sub-Organization'


class OperatorChargepoint(BaseModel):
    charge_point_id: UUID

    class Config:
        orm_mode = True


class SharedOperator(BaseModel):
    type: OperatorSharingType
    organization_id: UUID

    class Config:
        orm_mode = True


class MembershipOperatorResponse(BaseModel):
    id: UUID
    name: str
    meta: dict = {}
    organization_id: UUID
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        orm_mode = True


class OperatorResponse(BaseModel):
    id: UUID
    name: str
    meta: dict = {}
    email: Optional[EmailStr]
    description: Optional[str]
    phone_number: Optional[str]
    website: Optional[str]
    organization_id: UUID
    charge_points: List[OperatorChargepoint]
    shared_organizations: List[SharedOperator]
    memberships: List[Membership]
    created_at: datetime
    updated_at: Optional[datetime]
    is_taxable: Optional[bool]
    is_private: Optional[bool]
    whatsapp_number: Optional[str]
    ac_default_price: Optional[float]
    dc_default_price: Optional[float]

    class Config:
        orm_mode = True


class ShallowOperatorResponse(BaseModel):
    id: UUID
    name: str
    meta: dict = {}
    email: Optional[EmailStr]
    description: Optional[str]
    phone_number: Optional[str]
    website: Optional[str]
    organization_id: Optional[UUID]
    # charge_points: List[OperatorChargepoint]
    shared_organizations: List[SharedOperator]
    # memberships: List[Membership]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        orm_mode = True


class SmallOperatorResponse(BaseModel):
    id: UUID
    name: Optional[str] = ''

    class Config:
        orm_mode = True


class ExternalOrganizationResponse(BaseModel):
    # MAPPING OF NAME:
    # WHEN .from_orm() HAPPEN, original_name is returned, where original_name is name
    # friendly_name is returned as friendly_name
    # name is returned using friendly_name.
    # just follow this concept

    id: UUID
    name: str = Field(alias='party_id')
    friendly_name: Optional[str]
    meta: Optional[dict] = {}
    email: Optional[EmailStr]
    description: Optional[str]
    phone_number: Optional[str]
    whatsapp_number: Optional[str]
    images: Optional[dict] = {}
    website: Optional[str]
    # organization_id: Optional[UUID]
    # shared_organizations: Optional[List] = []
    # created_at: datetime
    # updated_at: Optional[datetime]
    name: Optional[str]

    # ================ original_name is what we see, name is what is in db ================ #
    original_name: Optional[str] = Field(alias='name')
    display_as_operator: Optional[str]
    party_prefix_trim_count: Optional[int] = 7
    party_suffix_trim_count: Optional[int] = 1

    class Config:
        orm_mode = True
        allow_population_by_field_name = True

    @root_validator
    def convert_party_id_to_friendly_name(cls, values):  # noqa : MC001
        try:
            # UI want to see name in frontend, thus we map friendly name to name instead
            friendly_name = values['friendly_name']
            if friendly_name:
                values['name'] = friendly_name
        except KeyError:
            pass
        return values


# class ShallowOperatorResponse(BaseModel):
#     name: str
#     meta: dict = {}
#     email: Optional[EmailStr]
#     description: Optional[str]
#     phone_number: Optional[str]
#     website: Optional[str]
#     organization_id: UUID
#     country_code: Optional[str]
#     party_id: Optional[str]
#     images: Optional[dict]
#     whatsapp_number: Optional[str]
#     id: UUID
#
#     created_at: datetime
#     updated_at: Optional[datetime]
#
#     class Config:
#         orm_mode = True


class ShallowOperatorConverter(ShallowOperatorResponse):
    images: Optional[str]

    class Config:
        orm_mode = True

    # @validator('images')


class Operator(BaseModel):
    name: str
    meta: dict = {}
    email: Optional[EmailStr]
    description: Optional[str]
    phone_number: Optional[str]
    website: Optional[str]
    organization_id: UUID
    country_code: Optional[str]
    party_id: Optional[str]
    images: Optional[dict]
    whatsapp_number: Optional[str]
    ac_default_price: Optional[float]
    dc_default_price: Optional[float]
    is_taxable: Optional[bool]

    @validator('phone_number')
    def is_phone_number_valid(cls, phone_number):
        try:
            check_phone_num = phonenumbers.parse(phone_number)
            if not phonenumbers.is_possible_number(check_phone_num):
                raise ValueError("Provided phone number is not valid.")
        except phonenumbers.NumberParseException:
            raise ValueError("Provided phone number is not valid.")
        return phone_number

    @validator('website')
    def validate_website(cls, website: str):
        if '.' not in website:
            raise ValueError('Website is not valid')
        return website


class OperatorUpdate(BaseModel):
    name: Optional[str]
    meta: Optional[dict]
    email: Optional[EmailStr]
    description: Optional[str]
    phone_number: Optional[str]
    website: Optional[str]
    organization_id: Optional[UUID]
    country_code: Optional[str]
    party_id: Optional[str]
    images: Optional[dict]
    whatsapp_number: Optional[str]
    is_private: Optional[bool]
    ac_default_price: Optional[float]
    dc_default_price: Optional[float]

    @validator('phone_number')
    def is_phone_number_valid(cls, phone_number):
        try:
            check_phone_num = phonenumbers.parse(phone_number)
            if not phonenumbers.is_possible_number(check_phone_num):
                raise ValueError("Provided phone number is not valid.")
        except phonenumbers.NumberParseException:
            raise ValueError("Provided phone number is not valid.")
        return phone_number

    @validator('website')
    def validate_website(cls, website: str):
        if '.' not in website:
            raise ValueError('Path cannot start with a "/".')
        return website


class CreateExternalOrganization(BaseModel):
    party_id: Optional[str]
    country_code: Optional[str]
    images: Optional[dict]
    website: Optional[str]
    description: Optional[str]

    @validator('website')
    def validate_website(cls, website: str):
        if website and '.' not in website:
            raise ValueError('Path cannot start with a "/".')
        return website


class UpdateExternalOrganization(BaseModel):
    email: Optional[str] = None
    website: Optional[str] = None
    phone_number: Optional[str] = None
    whatsapp_number: Optional[str] = None
    name: Optional[str] = None
    friendly_name: Optional[str] = None

    @validator('website')
    def validate_website(cls, website: str):
        if website and '.' not in website:
            raise ValueError('Path cannot start with a "/".')
        return website


class ShallowMembershipResponse(BaseModel):
    id: UUID
    first_name: str
    last_name: str
    user_id_tag: str
    organization_id: UUID
    user: User
    membership_type: MembershipType

    class Config:
        orm_mode = True

    @classmethod
    def from_orm(cls, obj):
        if obj.first_name is None:
            obj.first_name = ''
        if obj.last_name is None:
            obj.last_name = ''
        return super().from_orm(obj)


class RoleUpdate(BaseModel):
    name: Optional[str] = ''

    @validator('name')
    def prevent_name_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)


class RoleResources(BaseModel):
    resources: List[str]


class MembershipRoles(BaseModel):
    roles: List[str]


class MembershipOperator(BaseModel):
    operator_id: UUID


class Organization(BaseModel):
    name: constr(min_length=1)
    parent_id: Optional[UUID]


class OrganizationAuthenticationService(BaseModel):
    id: UUID
    organization_id: UUID
    secret_key: str


class OrganizationResponse(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    parent_id: Optional[UUID]
    name: str
    operators: List[OperatorResponse]
    shared_operators: List[SharedOperator]
    secret_key: Optional[OrganizationAuthenticationService]

    class Config:
        orm_mode = True


class OrganizationSimpleResponse(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    parent_id: Optional[UUID]
    name: str

    class Config:
        orm_mode = True


class OrganizationUpdate(BaseModel):
    name: Optional[str] = ''

    @validator('name')
    def prevent_name_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)


class ChargePoint(BaseModel):
    serial_number: str
    charge_box_serial_number: Optional[str]
    location: dict
    operator: Optional[str]


class ActivityLogType(Enum):
    create = 'create'
    update = 'update'
    delete = 'delete'


class AuditLogType(Enum):
    create = 'create'
    update = 'update'
    delete = 'delete'
    bulk_update = 'bulk_update'


class DunningBlockStatusEnum(Enum):
    available = 'Available'
    blocked = 'Blocked'


class DunningAdminBlockStatusEnum(Enum):
    no_status = 'No Status'
    blocked = 'Blocked'
    unblocked = 'Unblocked'


class DunningBlockReasonEnum(Enum):
    amount = 'Amount'
    number = 'Number'
    both = 'Both'
    cleared = 'Cleared'


class ActivityLog(BaseModel):
    user_id: UUID
    table_name: str
    data: Json
    type: ActivityLogType


class ActivityLogResponse(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    user_id: UUID
    table_name: str
    data: Json
    type: ActivityLogType

    class Config:
        orm_mode = True


class MembershipExtended(BaseModel):
    membership_id: str
    verification_method: Optional[VerificationMethodEnum]
    email_verified: Optional[bool]


class ManualLinkingToken(BaseModel):
    migrated_member_id: str
    target_member_id: str
    token: str
    expiration: datetime
    is_used: bool


class MembershipExtendedResponse(BaseModel):
    id: UUID
    verification_method: VerificationMethodEnum
    email_verified: bool
    phone_verified: bool
    created_at: datetime

    updated_at: Optional[datetime]
    first_name: Optional[str]
    last_name: Optional[str]
    vehicle_model: Optional[str]
    organization_id: Optional[UUID]
    user: User
    membership_type: MembershipType
    user_id_tag: str

    allow_marketing: Optional[bool]

    preferred_payment_method: Optional[str]
    preferred_payment_flow: Optional[str]
    preferred_pre_auth_info: Optional[MemberPreAuthInfoResponse]

    class Config:
        orm_mode = True

    @classmethod
    def from_orm(cls, obj: any):
        # This will flatten membership extended as the aim is to flatten both object
        if hasattr(obj, 'membership'):
            for a in dir(obj.membership):
                if not a.startswith('__') and not callable(getattr(obj.membership, a)):
                    obj.__dict__[a] = getattr(obj.membership, a)
        return super().from_orm(obj)


class OrganizationNameResponse(BaseModel):
    id: UUID
    name: str

    class Config:
        orm_mode = True


class DunningObject(BaseModel):
    currency: Optional[str] = None
    outstanding_bill_ids: Optional[List] = []
    outstanding_amount: Optional[float] = 0.0


class MembershipDunningResponse(BaseModel):
    id: UUID = None
    membership_id: Optional[UUID] = None
    created_at: datetime
    updated_at: Optional[datetime]
    block_date: Optional[datetime] = None
    block_status: Optional[DunningBlockStatusEnum] = None
    block_reason: Optional[str] = None
    admin_block_date: Optional[datetime] = None
    admin_block_status: Optional[DunningAdminBlockStatusEnum] = None
    admin_block_remark: Optional[str] = None
    dunnings: Optional[List[DunningObject]] = []
    all_outstanding_bill_ids: Optional[List] = []
    total_outstanding_amount: Optional[float] = 0.0

    class Config:
        orm_mode = True


class MembershipPrivacyAcceptanceResponse(BaseModel):
    id: UUID
    pdpa_accepted: bool
    pdpa_acceptance_date: Optional[datetime]
    marketing_consent_accepted: bool
    marketing_consent_acceptance_date: Optional[datetime]

    class Config:
        orm_mode = True


# pylint: disable=unsubscriptable-object
class MembershipPrivacyUpdate(BaseModel):
    pdpa_accepted: Optional[bool]
    pdpa_acceptance_date: Optional[datetime]
    marketing_consent_accepted: Optional[bool]
    marketing_consent_acceptance_date: Optional[datetime]


class MembershipResponse(BaseModel):
    id: UUID
    first_name: Optional[str]
    last_name: Optional[str]
    user_id_tag: str
    user: User
    organization_id: UUID
    membership_type: MembershipType

    roles: List[RoleResponse]
    operators: List[MembershipOperatorResponse]
    organization: Optional[OrganizationNameResponse]
    dunning_details: Optional[MembershipDunningResponse]
    pdpa_accepted: Optional[bool]
    pdpa_acceptance_date: Optional[datetime]
    marketing_consent_accepted: Optional[bool]
    marketing_consent_acceptance_date: Optional[datetime]

    preferred_payment_method: Optional[str]
    preferred_payment_flow: Optional[str]

    class Config:
        orm_mode = True


class DunningRepaymentLogCreate(BaseModel):
    member_id: UUID
    charging_session_id: str
    bill_id: UUID
    payment_amount: float
    payment_payload: dict
    payment_response: dict


class NonMigratedMembershipEmailResponse(BaseModel):
    id: UUID
    first_name: Optional[str]
    last_name: Optional[str]
    user: NonMigratedUser
    organization_id: UUID
    membership_type: MembershipType

    class Config:
        orm_mode = True


class MembershipDownloadResponse(BaseModel):
    id: UUID
    created_at: datetime
    first_name: Optional[str]
    last_name: Optional[str]
    user_id_tag: str
    user: User
    organization_id: UUID
    membership_type: MembershipType
    organization: Optional[OrganizationNameResponse]

    class Config:
        orm_mode = True


class IdTagResponse(BaseModel):
    id: UUID
    id_tag: str
    name: str
    type: str
    is_active: bool
    description: str
    member: MembershipResponse
    expiration: datetime

    class Config:
        orm_mode = True


class UserAuditLogResponse(BaseModel):
    id: UUID
    created_at: datetime
    membership: Optional[dict]
    first_name: Optional[str]
    last_name: Optional[str]
    module: str
    modules: list[str]
    data_before: Union[list, dict] = []
    data_after: Union[list, dict] = []
    remarks: list = []
    action: AuditLogType
    group_id: UUID

    class Config:
        orm_mode = True


class UserAccessCharger(BaseModel):
    phone_number: Optional[str]

    @validator('phone_number')
    def is_phone_number_valid(cls, phone_number):
        try:
            check_phone_num = phonenumbers.parse(phone_number)
            if not phonenumbers.is_possible_number(check_phone_num):
                raise ValueError("Provided phone number is not valid.")
        except phonenumbers.NumberParseException:
            raise ValueError("Provided phone number is not valid.")
        return phone_number


class UserAccessChargerCreate(BaseModel):
    operator_id: UUID
    membership_id: UUID


class UserResponseSmall(BaseModel):
    id: UUID
    phone_number: Optional[str]
    email: Optional[str]

    class Config:
        orm_mode = True


class UserPhoneListResponse(BaseModel):
    id: UUID
    user_id_tag: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    user: UserResponseSmall

    class Config:
        orm_mode = True


class MembershipResponseSmall(BaseModel):
    id: UUID
    user_id_tag: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    user: Optional[UserResponseSmall]

    class Config:
        orm_mode = True


class UserAccessResponse(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    member: MembershipResponseSmall
    operator_id: UUID

    class Config:
        orm_mode = True


class MembershipDunning(BaseModel):
    membership_id: str
    block_date: Optional[datetime]
    block_status: Optional[DunningBlockStatusEnum]
    block_reason: Optional[str]
    admin_block_date: Optional[datetime]
    admin_block_status: Optional[DunningAdminBlockStatusEnum]
    outstanding_amount: Optional[float]
    outstanding_bills: Optional[int]


class InitiateMembershipDunning(BaseModel):
    membership_id: str
    block_date: Optional[datetime]
    block_status: DunningBlockStatusEnum = Field(default=DunningBlockStatusEnum.available)
    block_reason: Optional[str]
    admin_block_date: Optional[datetime]
    admin_block_status: DunningAdminBlockStatusEnum = Field(default=DunningAdminBlockStatusEnum.no_status)
    dunnings: Optional[List] = []
    all_outstanding_bill_ids: Optional[List] = []
    total_outstanding_amount: Optional[float] = 0.0

    class Config:
        orm_mode = True

    # Serialize enums to strings
    def dict(self, **kwargs):
        data = super().dict(**kwargs)
        if isinstance(self.block_status, Enum):
            data["block_status"] = self.block_status.value
        if isinstance(self.admin_block_status, Enum):
            data["admin_block_status"] = self.admin_block_status.value
        return data


class MembershipUpdatePreferredPayment(BaseModel):
    preferred_payment_method: Optional[PreAuthPaymentMethodEnums]
    preferred_payment_flow: Optional[PreferredPaymentFlowEnums]
