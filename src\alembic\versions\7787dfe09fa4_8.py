"""8

Revision ID: 7787dfe09fa4
Revises: 5cb4c634887d
Create Date: 2022-09-13 08:58:41.700133

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7787dfe09fa4'
down_revision = '5cb4c634887d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_accepted_invite', sa.Column('is_deleted', sa.<PERSON>(), nullable=True))
    op.add_column('main_accepted_invite', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_activity_log', sa.Column('is_deleted', sa.<PERSON>(), nullable=True))
    op.add_column('main_activity_log', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_auth_token', sa.Column('is_deleted', sa.<PERSON>(), nullable=True))
    op.add_column('main_auth_token', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_auth_user', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_auth_user', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_callback', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_callback', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_charging_session_bill', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_charging_session_bill', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_credit_card', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_credit_card', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_id_tag', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_id_tag', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_invite', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_invite', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_label', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_label', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_membership', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_membership', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_operator', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_operator', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_organization', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_organization', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_payment_callback', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_payment_callback', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_payment_request', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_payment_request', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_promo_code', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_promo_code', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_promo_code_usage', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_promo_code_usage', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_recurring_payment', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_recurring_payment', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_reset_password_token', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_reset_password_token', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_resource', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_resource', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_resourceserver', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_resourceserver', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_role', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_role', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_subscription', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_subscription', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_subscription_card', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_subscription_card', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_subscription_custom_plan', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_subscription_custom_plan', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_subscription_fee', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_subscription_fee', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_subscription_invitation', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_subscription_invitation', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_subscription_order', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_subscription_order', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_subscription_plan', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_subscription_plan', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_vehicle', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_vehicle', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('main_wallet', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('main_wallet', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_wallet', 'deleted_at')
    op.drop_column('main_wallet', 'is_deleted')
    op.drop_column('main_vehicle', 'deleted_at')
    op.drop_column('main_vehicle', 'is_deleted')
    op.drop_column('main_subscription_plan', 'deleted_at')
    op.drop_column('main_subscription_plan', 'is_deleted')
    op.drop_column('main_subscription_order', 'deleted_at')
    op.drop_column('main_subscription_order', 'is_deleted')
    op.drop_column('main_subscription_invitation', 'deleted_at')
    op.drop_column('main_subscription_invitation', 'is_deleted')
    op.drop_column('main_subscription_fee', 'deleted_at')
    op.drop_column('main_subscription_fee', 'is_deleted')
    op.drop_column('main_subscription_custom_plan', 'deleted_at')
    op.drop_column('main_subscription_custom_plan', 'is_deleted')
    op.drop_column('main_subscription_card', 'deleted_at')
    op.drop_column('main_subscription_card', 'is_deleted')
    op.drop_column('main_subscription', 'deleted_at')
    op.drop_column('main_subscription', 'is_deleted')
    op.drop_column('main_role', 'deleted_at')
    op.drop_column('main_role', 'is_deleted')
    op.drop_column('main_resourceserver', 'deleted_at')
    op.drop_column('main_resourceserver', 'is_deleted')
    op.drop_column('main_resource', 'deleted_at')
    op.drop_column('main_resource', 'is_deleted')
    op.drop_column('main_reset_password_token', 'deleted_at')
    op.drop_column('main_reset_password_token', 'is_deleted')
    op.drop_column('main_recurring_payment', 'deleted_at')
    op.drop_column('main_recurring_payment', 'is_deleted')
    op.drop_column('main_promo_code_usage', 'deleted_at')
    op.drop_column('main_promo_code_usage', 'is_deleted')
    op.drop_column('main_promo_code', 'deleted_at')
    op.drop_column('main_promo_code', 'is_deleted')
    op.drop_column('main_payment_request', 'deleted_at')
    op.drop_column('main_payment_request', 'is_deleted')
    op.drop_column('main_payment_callback', 'deleted_at')
    op.drop_column('main_payment_callback', 'is_deleted')
    op.drop_column('main_organization', 'deleted_at')
    op.drop_column('main_organization', 'is_deleted')
    op.drop_column('main_operator', 'deleted_at')
    op.drop_column('main_operator', 'is_deleted')
    op.drop_column('main_membership', 'deleted_at')
    op.drop_column('main_membership', 'is_deleted')
    op.drop_column('main_label', 'deleted_at')
    op.drop_column('main_label', 'is_deleted')
    op.drop_column('main_invite', 'deleted_at')
    op.drop_column('main_invite', 'is_deleted')
    op.drop_column('main_id_tag', 'deleted_at')
    op.drop_column('main_id_tag', 'is_deleted')
    op.drop_column('main_credit_card', 'deleted_at')
    op.drop_column('main_credit_card', 'is_deleted')
    op.drop_column('main_charging_session_bill', 'deleted_at')
    op.drop_column('main_charging_session_bill', 'is_deleted')
    op.drop_column('main_callback', 'deleted_at')
    op.drop_column('main_callback', 'is_deleted')
    op.drop_column('main_auth_user', 'deleted_at')
    op.drop_column('main_auth_user', 'is_deleted')
    op.drop_column('main_auth_token', 'deleted_at')
    op.drop_column('main_auth_token', 'is_deleted')
    op.drop_column('main_activity_log', 'deleted_at')
    op.drop_column('main_activity_log', 'is_deleted')
    op.drop_column('main_accepted_invite', 'deleted_at')
    op.drop_column('main_accepted_invite', 'is_deleted')
    # ### end Alembic commands ###
