import random
from contextlib import contextmanager
from unittest.mock import patch
import pytest
from datetime import datetime, timedelta
from jose import jwt

from faker import Faker
from fastapi.testclient import TestClient

from app.main import app, ROOT_PATH
from app import schema, settings
from app.schema import MembershipType
from app.tests.factories import (UserFactory, VerificationTokenFactory, OrganizationFactory,
                                 MembershipFactory, )
from app.database import SessionLocal, create_session, Base, engine

fake = Faker()
client = TestClient(app)


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def faker_phone_number(fake: Faker) -> str:
    random_number = ''.join(random.choice('123456789') for _ in range(8))
    random_number = '+601' + random_number
    return random_number


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VerificationTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


@patch('app.routers.auth.send_otp')
def test_post_signin_password_with_valid_data_succeeds(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }

    url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/password'

    valid_data = {
        'phone_number': '',
        'password': 'password',
        'organization_id': ''
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}'
        )
        db.commit()

        valid_data['phone_number'] = f'{user.phone_number}'
        valid_data['organization_id'] = f'{organization.id}'

    response = client.post(url, json=valid_data)

    assert response.status_code == 200
    assert len(response.json()['auth_token']) > 0
    assert send_otp_mock.called is False


def test_post_signin_staff_password_with_valid_data_succeeds(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'

    valid_data = {
        'email': '',
        'password': 'password',
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization.id
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        valid_data['email'] = user.email
        valid_data['organization_id'] = f'{organization.id}'

    response = client.post(url, json=valid_data)

    assert response.status_code == 200
    assert len(response.json()['auth_token']) > 0


def test_post_signin_substaff_password_with_valid_data_succeeds(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'

    valid_data = {
        'email': '',
        'password': 'password',
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization.id
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.sub_staff,
        )
        db.commit()

        valid_data['email'] = user.email
        valid_data['organization_id'] = f'{organization.id}'

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization.id
        )
        db.commit()

    response = client.post(url, json=valid_data)

    assert response.status_code == 200
    assert len(response.json()['auth_token']) > 0


@patch('app.routers.auth.send_otp')
def test_post_signin_otp_with_valid_data_succeeds(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }

    url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/otp'

    valid_data = {
        'phone_number': '',
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization.id
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}'
        )
        db.commit()

        valid_data['phone_number'] = f'{user.phone_number}'
        valid_data['organization_id'] = f'{organization.id}'

    response = client.post(url, json=valid_data)

    assert response.status_code == 200
    assert send_otp_mock.called is True


@patch('app.routers.auth.verify_otp')
def test_post_verify_signin_otp_with_correct_otp_returns_auth_token(verify_otp_mock, test_db):
    class RetVal:
        status = 'approved'

    verify_otp_mock.return_value = RetVal()

    # url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/otp/verify'

    valid_data = {
        'phone_number': '',
        'token': fake.pystr()
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization.id
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}'
        )
        db.commit()

        valid_data['phone_number'] = f'{user.phone_number}'
        valid_data['organization_id'] = f'{organization.id}'

    # response = client.post(url, json=valid_data)

    # assert response.status_code == 200
    # assert len(response.json()['auth_token']) > 0
    # assert verify_otp_mock.called is True


@patch('app.routers.auth.send_otp')
def test_post_signin_password_with_invalid_phone_number_fails(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }

    url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/password'

    valid_data = {
        'phone_number': '',
        'password': 'IamAPassword123',
        'organization_id': ''
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization.id
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}'
        )
        db.commit()

        valid_data['phone_number'] = f'{user.phone_number}0000'
        valid_data['organization_id'] = f'{organization.id}'

    response = client.post(url, json=valid_data)

    assert response.status_code == 400
    assert response.json()['detail'] == 'User not found.'
    assert send_otp_mock.called is False


@patch('app.routers.auth.send_otp')
def test_post_signin_password_with_invalid_password_fails(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }

    url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/password'

    valid_data = {
        'phone_number': '',
        'password': 'IamAPassword123_incorrect',
        'organization_id': ''
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization.id,
            user_type=schema.UserType.regular
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}'
        )
        db.commit()

        valid_data['phone_number'] = f'{user.phone_number}'
        valid_data['organization_id'] = f'{organization.id}'

    response = client.post(url, json=valid_data)

    assert response.status_code == 400
    assert response.json()['detail'] == 'Email ID and password does not matches.'
    assert send_otp_mock.called is False


@patch('app.routers.auth.send_otp')
def test_post_signin_otp_with_invalid_phone_number_fails(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }

    url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/otp'

    valid_data = {
        'phone_number': '',
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization.id
        )
        db.commit()

        valid_data['phone_number'] = f'{user.phone_number}000000'

    response = client.post(url, json=valid_data)

    assert response.status_code == 400
    assert response.json()['detail'] == 'User not found.'
    assert send_otp_mock.called is False


def test_email_validation_not_found(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/validate-email'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": f'{staff.id}',
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    staff_email = staff.email.strip(staff.email[:2])
    response = client.get(url, headers={'authorization': token}, params={'email': staff_email})
    assert response.status_code == 400
    assert response.json() == {'detail': 'The provided email ID is not found, please re-check'}


def test_email_validation_no_organization_linked(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/validate-email'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff1 = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )

        staff2 = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )

        db.commit()
        staff1_id = str(staff1.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff1_id,
            membership_type=MembershipType.staff,
        )

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": f'{staff1.id}',
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        response = client.get(url, headers={'authorization': token}, params={'email': staff2.email})

    assert response.status_code == 400
    assert response.json() == {'detail': 'No Organization Linked to this email'}


def test_email_validation_success(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/validate-email'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": f'{staff.id}',
            "membership_id": f'{mem1.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    response = client.get(url, headers={'authorization': token}, params={'email': staff.email})
    assert response.status_code == 200


def test_invalid_email_validation(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/validate-email'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem1 = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": f'{staff.id}',
            "membership_id": f'{mem1.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    staff_email = staff.email.replace('@', '')
    response = client.get(url, headers={'authorization': token}, params={'email': staff_email})
    assert response.status_code == 422
