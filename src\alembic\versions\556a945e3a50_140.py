"""140

Revision ID: 556a945e3a50
Revises: 3c89552e24d0
Create Date: 2024-12-04 15:43:59.394588

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '556a945e3a50'
down_revision = '3c89552e24d0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_auth_user', sa.Column('user_type', sa.String(), nullable=True))
    op.drop_index('unique_email_organization_id_guest_app_id', table_name='main_auth_user')
    op.create_index('unique_email_organization_id_guest_app_id', 'main_auth_user', ['email', 'organization_id', 'user_type', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted AND NOT is_guest'))
    op.drop_index('unique_phone_number_organization_id_guest_app_id', table_name='main_auth_user')
    op.create_index('unique_phone_number_organization_id_guest_app_id', 'main_auth_user', ['phone_number', 'organization_id', 'user_type', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted AND NOT is_guest'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('unique_phone_number_organization_id_guest_app_id', table_name='main_auth_user', postgresql_where=sa.text('NOT is_deleted AND NOT is_guest'))
    op.create_index('unique_phone_number_organization_id_guest_app_id', 'main_auth_user', ['phone_number', 'organization_id', 'is_deleted'], unique=False)
    op.drop_index('unique_email_organization_id_guest_app_id', table_name='main_auth_user', postgresql_where=sa.text('NOT is_deleted AND NOT is_guest'))
    op.create_index('unique_email_organization_id_guest_app_id', 'main_auth_user', ['email', 'organization_id', 'is_deleted'], unique=False)
    op.drop_column('main_auth_user', 'user_type')
    # ### end Alembic commands ###
