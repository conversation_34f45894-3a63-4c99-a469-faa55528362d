import json
from datetime import datetime
from typing import List

from sqlalchemy.exc import Integ<PERSON><PERSON><PERSON>r, NoResultFound
from sqlalchemy.orm import Session, Query, joinedload, contains_eager
from sqlalchemy.sql import and_, func

from app import models, schema, exceptions, settings
from app.schema.v2 import VehicleResponseNoHistories, VehicleHistoryUpdate, EmaidStatus
from app.models.association import membership_vehicle_association_table
from app.models.organization import Membership
from app.models.vehicle import Vehicle, Autocharge, Emaid
from .base import BaseCRUD
from .organization import MembershipCRUD

CHARGER_URL_PREFIX_V1 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'


class VehicleCRUD(BaseCRUD):
    model = models.Vehicle

    # @classmethod
    # def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
    #     membership = cls.membership()
    #     if any([
    #         membership.user.is_superuser,
    #         membership.membership_type == schema.MembershipType.staff,
    #         membership.membership_type == schema.MembershipType.sub_staff,
    #         membership.membership_type == schema.MembershipType.custom,
    #     ]):
    #         return True
    #     raise exceptions.ApolloPermissionError()

    # @classmethod
    # def can_create(cls, dbsession: Session, data, *args, **kwargs):
    #     membership = cls.membership()
    #     if any([
    #         membership.user.is_superuser,
    #         membership.membership_type == schema.MembershipType.staff,
    #         membership.membership_type == schema.MembershipType.sub_staff,
    #         membership.membership_type == schema.MembershipType.custom,
    #     ]):
    #         return True
    #     raise exceptions.ApolloPermissionError()


class ContractCertificateOrderCRUD(BaseCRUD):
    model = models.ContractCertificateOrder


def get_vehicle(db: Session, vehicle_id: str) -> models.Vehicle:
    try:
        db_vehicle = VehicleCRUD.get(db, vehicle_id)
        return db_vehicle
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Vehicle')


def get_vehicle_v2(db: Session, vehicle_id: str) -> models.Vehicle:
    '''Retrieve a specific vehicle along with membership details'''
    query = (
        VehicleCRUD.query(db)
        .options(
            joinedload(Vehicle.emaid),
            joinedload(Vehicle.membership).joinedload(Membership.user),
        )
        .join(membership_vehicle_association_table)
        .join(Membership)
        .outerjoin(
            Autocharge,
            and_(
                Autocharge.vehicle_id == Vehicle.id,
                Autocharge.is_deleted == False  # pylint: disable=singleton-comparison # noqa: E712
            )
        )
        .options(contains_eager(Vehicle.autocharge))  # Eager load Autocharge only if not deleted
        .filter(Vehicle.id == vehicle_id)  # Filter for the specific vehicle_id
    )
    vehicle = query.first()
    return vehicle


def get_vehicle_with_valid_emaid(db: Session, vehicle_id: str, emaid: str) -> models.Vehicle:
    try:
        query = (
            VehicleCRUD.query(db)
            .options(
                joinedload(Vehicle.emaid),
                joinedload(Vehicle.membership).joinedload(Membership.user),
            )
            .join(membership_vehicle_association_table)
            .join(Membership)
            .join(Emaid)
            .filter(Vehicle.id == vehicle_id)
            .filter(Emaid.emaid == emaid)
            .filter(Emaid.status.in_([
                EmaidStatus.active,
                EmaidStatus.disabled,
                EmaidStatus.deactivated,
                EmaidStatus.failed
            ]))
        )
        vehicle = query.first()
        return vehicle
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Vehicle')


def get_vehicle_list(db: Session) -> List[models.Vehicle]:
    db_vehicle_list = VehicleCRUD.query(db).all()
    return db_vehicle_list


def get_vehicle_with_pcid(db: Session, pcid) -> models.Vehicle:
    '''Get vehicle with specific pcid'''
    db_vehicle_list = VehicleCRUD.query(db).filter(models.Vehicle.pcid == pcid).all()
    return db_vehicle_list


def get_vehicle_list_with_details(db: Session, filters: dict,  # noqa: MC0001
                                  filters_param: schema.VehicleFiltersParam) -> Query:  # noqa: MC0001
    '''Vehicles list along with membership details'''
    query = (
        VehicleCRUD.query(db)
        .options(
            joinedload(Vehicle.emaid),
            joinedload(Vehicle.membership).joinedload(Membership.user)
        )
        .join(membership_vehicle_association_table)
        .outerjoin(Emaid)
        .join(Membership)
        .join(models.User)
        .outerjoin(
            Autocharge,
            and_(
                Autocharge.vehicle_id == Vehicle.id,
                Autocharge.is_deleted == False  # pylint: disable=singleton-comparison # noqa: E712
            )
        )
        .options(contains_eager(Vehicle.autocharge))
    )

    if filters.get("is_pnc_only"):
        query = query.filter(Vehicle.emaid.any())  # Filter vehicles that have emaid

    if filters.get("is_ac_only"):
        query = query.filter(Autocharge.id.isnot(None))

    if filters_param:
        if filters_param.vehicle_reg_number:
            query = query.filter(Vehicle.registration_number.ilike(f'%{filters_param.vehicle_reg_number}%'))
        if filters_param.brand:
            query = query.filter(Vehicle.brand.ilike(f'%{filters_param.brand}%'))
        if filters_param.model:
            query = query.filter(Vehicle.model.ilike(f'%{filters_param.model}%'))
        if filters_param.pcid:
            query = query.filter(Vehicle.pcid.ilike(f'%{filters_param.pcid}%'))
        if filters_param.emaid:
            query = query.filter(Emaid.emaid.ilike(f'%{filters_param.emaid}%'))
        if filters_param.mac_address:
            query = query.filter(Autocharge.mac_address.ilike(f'%{filters_param.mac_address}%'))
        if filters_param.owner:
            query = query.filter(
                func.concat(models.Membership.first_name, ' ',
                            models.Membership.last_name).ilike(f'%{filters_param.owner}%')
            )
        if filters_param.email:
            query = query.filter(models.User.email.ilike(f'%{filters_param.email}%'))
        if filters_param.phone:
            query = query.filter(models.User.phone_number.ilike(f'%{filters_param.phone}%'))
        if filters_param.pnc_status:
            query = query.filter(Emaid.status.ilike(f'%{filters_param.pnc_status}%'))
        # if filters_param.pnc_expiry:
        #     query = query.filter(Emaid.contract_end.ilike(f'%{filters_param.pnc_expiry}%'))
        if filters_param.ac_status:
            query = query.filter(Autocharge.status.ilike(f'%{filters_param.ac_status}%'))
        # if filters_param.expiry:
        #     query = query.filter(Autocharge.expiry.ilike(f'%{filters_param.expiry}%'))
    return query


def get_membership_vehicle_list(db: Session, member_id: str, check_permission: bool = True) -> List[models.Vehicle]:
    try:
        db_mem = MembershipCRUD.get(db, member_id, check_permission=check_permission)
        return db_mem.vehicles
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_membership_vehicle_list_query(db: Session, member_id: str, check_permission: bool = True):
    try:
        # Query the Membership table and join the Vehicle table
        vehicles_query = db.query(models.Vehicle).join(
            MembershipCRUD.model.vehicles
        ).filter(
            MembershipCRUD.model.id == member_id,
            MembershipCRUD.model.is_deleted.is_(False),
            models.Vehicle.is_deleted.is_(False)
        )
        return vehicles_query  # Return the query instead of the list
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_membership_by_vehicle_id(db: Session, vehicle_id: str, check_permission: bool = True) -> models.Membership:
    try:

        db_mem = MembershipCRUD.query(db, check_permission=check_permission).join(
            membership_vehicle_association_table,
            models.Membership.id == membership_vehicle_association_table.c.main_membership_id).join(
            models.Vehicle, models.Vehicle.id == membership_vehicle_association_table.c.main_vehicle_id).filter(
            models.Vehicle.id == vehicle_id).one()
        return db_mem
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Subscription')


def create_vehicle(db: Session, vehicle_data: schema.Vehicle) -> models.Vehicle:
    try:
        db_vehicle = VehicleCRUD.add(db, vehicle_data.dict())
        return db_vehicle
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloVehicleError()


def create_vehicle_v2(db: Session, vehicle_data: models.Vehicle) -> models.Vehicle:
    try:
        db_vehicle = VehicleCRUD.add(db, vehicle_data.dict())
        return db_vehicle

    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloVehicleError()


def add_membership_vehicle(db: Session, vehicle_id: str, member_id: str) -> List[models.Vehicle]:
    db_vehicle = get_vehicle(db, vehicle_id)

    try:
        db_mem = MembershipCRUD.get(db, member_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')

    try:
        db_mem.vehicles.append(db_vehicle)
        db.commit()
        db.refresh(db_mem)
        return db_mem.vehicles
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloVehicleError()


def delete_vehicle(db: Session, vehicle_id: str) -> models.Vehicle:
    try:
        VehicleCRUD.delete(db, vehicle_id)
        return vehicle_id
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Vehicle')


def update_vehicle(db: Session, vehicle_data: dict, vehicle_id: str) -> models.Vehicle:
    try:
        db_vehicle = VehicleCRUD.update(db, vehicle_id, vehicle_data.dict(exclude_unset=True, exclude_defaults=True))
        return db_vehicle
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Vehicle')


def remove_pcid_from_vehicle(db: Session, vehicle_id: str) -> models.Vehicle:
    '''Remove PCID from vehicle'''
    try:
        db_vehicle = get_vehicle(db, vehicle_id)
        db_vehicle.pcid = None
        db.commit()
        db.refresh(db_vehicle)

        return db_vehicle
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Vehicle')


def remove_membership_vehicle(db: Session, vehicle_id: str, member_id: str) -> models.Vehicle:
    db_vehicle = get_vehicle(db, vehicle_id)
    try:
        db_mem = MembershipCRUD.get(db, member_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')

    try:
        db_mem.vehicles.remove(db_vehicle)
        db.commit()
        db.refresh(db_mem)
        return db_mem.vehicles
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Vehicle')


def update_vehicle_history(db: Session, db_vehicle_id: str):
    db_vehicle = get_vehicle(db, db_vehicle_id)

    new_vehicle_history = json.loads(VehicleResponseNoHistories.from_orm(db_vehicle).json())

    new_vehicle_history['timestamp'] = datetime.now().timestamp()
    db_vehicle_histories = json.loads(json.dumps(db_vehicle.vehicle_histories))
    db_vehicle_histories.append(new_vehicle_history)

    vehicle_update_schema = VehicleHistoryUpdate(vehicle_histories=db_vehicle_histories)
    db_vehicle = update_vehicle(db, vehicle_update_schema, db_vehicle.id)
    return db_vehicle


def get_contract_cert_orders(db: Session) -> models.ContractCertificateOrder:
    '''Get all contract certificate orders'''
    contract_certificate_order_list = (
        ContractCertificateOrderCRUD
        .query(db)
        .options(joinedload(models.ContractCertificateOrder.emaid))
        .all()
    )

    return contract_certificate_order_list
