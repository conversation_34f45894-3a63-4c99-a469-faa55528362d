from contextlib import contextmanager
from unittest.mock import patch
import pytest
import secrets

import jwt

from datetime import datetime, timedelta

from faker import Faker
from fastapi.testclient import TestClient
from sqlalchemy.exc import IntegrityError, MultipleResultsFound

from app import exceptions, crud, schema, settings
from app.crud.auth import MembershipCRUD, UserCRUD
from app.main import app, ROOT_PATH
from app.tests.factories import (UserFactory, VerificationTokenFactory, OrganizationFactory,
                                 ResourceServerFactory, ResourceFactory,
                                 RoleFactory, MembershipFactory, GlobalRoleFactory,
                                 OrganizationAuthenticationServiceFactory, )
from app.database import SessionLocal, create_session, Base, engine
from app.models import Membership, User, ActivityLog
from app.schema import MembershipType, ActivityLogType

fake = Faker()
client = TestClient(app)

V2_ORG_BASE_URL = f'{ROOT_PATH}/api/v1/organization'


def faker_phone_number(fake: Faker) -> str:
    return f'+91 {fake.msisdn()[3:]}'


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VerificationTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        GlobalRoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


# def test_patch_valid_user_profile_succeeds(test_db):
#     valid_data = {
#         'first_name': fake.first_name(),
#         'last_name': fake.last_name()
#     }

#     with contextmanager(override_create_session)() as db:
#         staff = UserFactory(
#             is_verified=True,
#             verification_method='email'
#         )
#         db.commit()

#         parent_org = OrganizationFactory()
#         db.commit()

#         org = OrganizationFactory(
#             parent_id=f'{parent_org.id}',
#         )
#         db.commit()

#         membership = MembershipFactory(
#             organization_id=f'{org.id}',
#             user_id=f'{staff.id}',
#             membership_type=schema.MembershipType.regular_user,

#         )
#         db.commit()

#         create_res_server_and_roles(db, membership, str(org.id), fr'{V2_ORG_BASE_URL}/.*',
#                                     'get,patch,post,delete', 'apollo-main')
#         token = jwt.encode({
#             "exp": datetime.utcnow() + timedelta(days=1),
#             "user_id": str(staff.id),
#             "membership_id": f'{membership.id}',
#         },
#             settings.JWT_SECRET,
#             algorithm=schema.JWT_ALGORITHM,
#         )

#     url = f'{ROOT_PATH}/api/v1/organization/update-profile'

#     response = client.patch(url, json=valid_data, headers={'authorization': token})

#     assert response.status_code == 200
#     assert response.json()['first_name'] == valid_data['first_name']
#     assert response.json()['last_name'] == valid_data['last_name']


# def test_patch_invalid_user_profile_fails_user_not_found(test_db):
#     invalid_data = {
#         'first_name': fake.first_name(),
#         'last_name': fake.last_name(),

#     }

#     with contextmanager(override_create_session)() as db:
#         staff = UserFactory(
#             is_verified=True,
#             verification_method='email'
#         )
#         db.commit()

#         org = OrganizationFactory()
#         db.commit(

#         )
#         membership = MembershipFactory(
#             organization_id=f'{org.id}',
#             user_id=f'{staff.id}',
#             membership_type=schema.MembershipType.regular_user,
#         )
#         db.commit()

#         create_res_server_and_roles(db, membership, str(org.id), fr'{V2_ORG_BASE_URL}/.*',
#                                     'get,patch,post,delete', 'apollo-main')
#         token = jwt.encode({
#             "exp": datetime.utcnow() + timedelta(days=1),
#             "user_id": str(staff.id),
#             "membership_id": f'{membership.id}',
#         },
#             settings.JWT_SECRET,
#             algorithm=schema.JWT_ALGORITHM,
#         )

#     url = f'{ROOT_PATH}/api/v1/organization/update-profile'
#     response = client.patch(url, json=invalid_data, headers={'authorization': token})

#     assert response.status_code == 400


def test_patch_invalid_user_profile_fails_body_error(test_db):
    invalid_data = {
        'password': 'password',
        'confirm_password': 'password123'
    }

    with contextmanager(override_create_session)() as db:
        org = OrganizationFactory()
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=org.id
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{org.id}',
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.regular_user,
        )
        db.commit()

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=f'{org.id}',
            organization_id=f'{org.id}',
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, membership, str(org.id), fr'{V2_ORG_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": str(staff.id),
            "membership_id": f'{membership.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ROOT_PATH}/api/v1/organization/update-profile'
        response = client.patch(url, json=invalid_data, headers={'authorization': token, 'x-api-key': secret_key,
                                                                 'x-api-sid': f'{org.id}'})

    assert response.status_code == 200
