import logging
import math
# from datetime import datetime

from fastapi import APIRouter, Depends, Request, HTTPException, status
from sqlalchemy.orm import Session

from app import settings, schema, crud, exceptions
from app.database import create_session
from app.permissions import x_api_key
from app.utils import (
    CHARGER_URL_PREFIX,
    decode_auth_token_from_headers,
    generate_charger_header,
    send_request,
    # get_default_subscription,
    RouteErrorHandlerProxy,
    apply_subscription_discount_connector,
)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/campaign",
    tags=["v2 campaign"],
    dependencies=[Depends(x_api_key)],
    route_class=RouteErrorHandlerProxy,
)


@router.post(
    "/promo_code/validate",
    status_code=status.HTTP_200_OK,
    response_model=schema.CampaignPromoCodeResponse,
)  # pylint: disable=too-many-branches
async def validate_campaign_promo_code(  # noqa: MC0001
        request: Request,
        payload: schema.CampaignPromoCodeValidation,
        dbsession: Session = Depends(create_session),
):
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
        headers = generate_charger_header(dbsession, membership_id)

    except (ValueError, KeyError):
        err_detail = "Promo code cannot be used at the moment."
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=err_detail)

    try:
        url = f'{CHARGER_URL_PREFIX}/connector/{payload.connector_id}'
        response = await send_request('GET', url=url, headers=headers)
        connector = response.json()
    except HTTPException:
        err_detail = "Promo code cannot be used at the moment."
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=err_detail)

    try:
        discounted_amount = apply_subscription_discount_connector(dbsession, membership_id, str(payload.connector_id),
                                                                  connector['billing_unit_fee'])
    except Exception as exc:
        logging.error("Error when applying connector discount %s", str(exc))
        err_detail = "Promo code not valid at this location."
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=err_detail)

    tolerance = 0.1
    subscription_applied = False
    if not math.isclose(discounted_amount, connector['billing_unit_fee'], rel_tol=0, abs_tol=tolerance):
        subscription_applied = True

    try:
        location_id = connector["charge_point"]["location"]["id"]
    except KeyError:
        err_detail = "Promo code cannot be used at the moment."
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=err_detail)

    try:
        db_campaign_promo_code = crud.validate_campaign_promo_code(dbsession, payload.promo_code, location_id,
                                                                   membership_id, subscription_applied)
    except exceptions.ApolloObjectDoesNotExist:
        err_detail = "Invalid promo code."
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=err_detail)
    except (
            exceptions.ApolloCampaignPromoCodeHasExpiredError,
            exceptions.ApolloCampaignPromoCodeLimitExceededError,
            exceptions.ApolloCampaignPromoCodeMissingLocationError,
            exceptions.ApolloCampaignPromoCodeLocationError,
            exceptions.ApolloCampaignPromoCodeInvalidHoursError,
            exceptions.ApolloCampaignPromoCodeInvalidMembershipSegmentError,
            exceptions.ApolloCampaignPromoCodeNotStackableError,
    ) as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

    return db_campaign_promo_code
