version: '3.1'

services:
  postgres:
    container_name: main-postgres
    restart: always
    image: postgres:13.0
    env_file: .env
    volumes:
      - apollo-main-postgres-data:/var/lib/postgresql/data
    ports:
      - 5432:5432

  rabbitmq:
    container_name: main-rabbitmq
    restart: always
    image: rabbitmq:3-management
    env_file: .env
    ports:
      - 5672:5672
      - 15672:15672

  sonarscanner-v1:
    build:
      context: .
      dockerfile: ./docker/Dockerfile.SonarScanner
    container_name: sonarscanner-v1
    env_file: .env
    depends_on:
      - postgres
      - rabbitmq
    volumes:
      - ./src/sonarqube/sonar-project-v1.properties:/app/sonar-project.properties
      - ./src/sonarqube/.coveragerc-v1:/app/.coveragerc
      - shared-data:/app

  sonarscanner-v2:
    build:
      context: .
      dockerfile: ./docker/Dockerfile.SonarScanner
    container_name: sonarscanner-v2
    env_file: .env
    depends_on:
      - postgres
      - rabbitmq
    volumes:
      - ./src/sonarqube/sonar-project-v2.properties:/app/sonar-project.properties
      - ./src/sonarqube/.coveragerc-v2:/app/.coveragerc
      - shared-data:/app

volumes:
  shared-data:
  apollo-main-postgres-data: