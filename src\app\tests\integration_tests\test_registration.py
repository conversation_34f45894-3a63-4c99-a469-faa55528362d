from contextlib import contextmanager
from unittest.mock import patch
import pytest

from faker import Faker
from fastapi.testclient import TestClient

from app.main import app, ROOT_PATH
from app.schema import MembershipType, MembershipResponse
from app.tests.factories import (UserFactory, VerificationTokenFactory, OrganizationFactory,
                                 WalletFactory, GlobalRoleFactory, MembershipFactory)
from app.database import SessionLocal, create_session, Base, engine


fake = Faker()
client = TestClient(app)


def faker_phone_number(fake: Faker) -> str:
    return f'+91 {fake.msisdn()[3:]}'


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        WalletFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VerificationTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        GlobalRoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


@patch('app.routers.auth.send_otp')
def test_post_signup_with_valid_data_succeeds(send_otp_mock, test_db):

    url = f'{ROOT_PATH}/api/v1/csms/auth/signup'

    valid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': fake.first_name(),
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'IamAPassword123!',
    }

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        org = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        GlobalRoleFactory(
            name='Regular User',
            is_global=True,
            organization_id=None
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=org.id
        )
        db.commit()

        MembershipFactory(
            organization_id=org.id,
            user_id=f'{staff.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        send_otp_mock.return_value = {
            'status': 'success',
        }

        valid_data['organization_id'] = str(org.id)

    response = client.post(url, json=valid_data)

    assert response.json()['detail'] == 'Verification code has been sent to your phone number.'
    assert response.status_code == 200
    assert send_otp_mock.called is True


@patch('app.routers.auth.send_otp')
def test_post_signup_with_invalid_data_fails(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }

    url = f'{ROOT_PATH}/api/v1/csms/auth/signup'

    invalid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': '',
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'IamAPassword123!',
    }

    with contextmanager(override_create_session)() as db:
        org = OrganizationFactory()
        db.commit()

        invalid_data['organization_id'] = str(org.id)

    response = client.post(url, json=invalid_data)

    assert response.status_code == 422
    assert send_otp_mock.called is False


@patch('app.routers.auth.send_otp')
def test_post_signup_with_unmatched_passwords_fails(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }

    url = f'{ROOT_PATH}/api/v1/csms/auth/signup'

    invalid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': fake.first_name(),
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'i_am_clearly_unmatched',
    }

    with contextmanager(override_create_session)() as db:
        org = OrganizationFactory()
        db.commit()

        invalid_data['organization_id'] = str(org.id)

    response = client.post(url, json=invalid_data)

    assert response.status_code == 422
    assert send_otp_mock.called is False
    assert response.json()['detail'][0]['msg'] == 'Passwords must match.'


@patch('app.routers.auth.SendGridMail._send_mail')
def test_post_verification_method_with_valid_data_succeeds(_send_email_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/verification-method'

    valid_data = {
        'verification_method': 'email',
        'email': '',
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(organization_id=organization_id)
        db.commit()

        valid_data['email'] = user.email

    response = client.post(url, json=valid_data)

    assert response.status_code == 200


def test_post_phone_verification_method_without_phone_number_fails(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/verification-method'

    invalid_data = {
        'verification_method': 'phone',
        'user_id': '',
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(organization_id=organization_id)
        db.commit()

        invalid_data['user_id'] = str(user.id)

    response = client.post(url, json=invalid_data)

    assert response.status_code == 422


@patch('app.routers.auth.SendGridMail._send_mail')
def test_post_verification_method_with_invalid_data_fails(_send_email_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/verification-method'

    invalid_data = {
        'verification_method': 'not_in_the_selection',
        'user_id': '',
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(organization_id=organization_id)
        db.commit()

        invalid_data['user_id'] = str(user.id)

    response = client.post(url, json=invalid_data)

    assert response.status_code == 422


def test_post_verify_with_valid_data_succeeds(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/verify'

    valid_data = {}

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(organization_id=organization_id)
        db.commit()

        vtoken = VerificationTokenFactory(user_id=str(user.id))
        db.commit()

        valid_data['email'] = user.email
        valid_data['token'] = vtoken.token

    response = client.post(url, json=valid_data)
    assert response.status_code == 200


def test_post_verify_with_invalid_data_fails(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/verify'

    invalid_data = {}

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(organization_id=organization_id)
        db.commit()

        VerificationTokenFactory(user_id=str(user.id))
        db.commit()

        invalid_data['email'] = user.email
        invalid_data['token'] = 'something_else'

    response = client.post(url, json=invalid_data)
    assert response.status_code == 400
