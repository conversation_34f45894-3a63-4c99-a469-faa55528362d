from datetime import datetime, timedelta
from unittest.mock import patch
from contextlib import contextmanager
from urllib.parse import urlencode
import pytest
import uuid
import secrets

import jwt
from fastapi.testclient import TestClient
from sqlalchemy.exc import IntegrityError, MultipleResultsFound

from app.crud.auth import MembershipCRUD, UserCRUD
from app import models, crud, schema, settings
from app.models import Membership, User, ActivityLog
from app.main import app, ROOT_PATH
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory, CreditCardFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory,
                                 GlobalRoleFactory, SubscriptionFeeFactory,
                                 SubscriptionPlanFactory, SubscriptionFactory, SubscriptionCustomPlanFactory,
                                 SubscriptionCardFactory, SubscriptionInvitationFactory,
                                 OrganizationAuthenticationServiceFactory, )
from app.tests.mocks.async_client import MockAsyncClientGeneratorConnector
from app.database import SessionLocal, create_session, Base, engine
from app.schema import MembershipType, JWT_ALGORITHM

client = TestClient(app)

V2_SUB_BASE_URL = f'{ROOT_PATH}/api/v1/subscriptions'

member_subscription_information = {
    'full_name': 'Bon Woo',
    'identification_number': '990101020391',
    'address_1': '7/18 B Jalan Permata Mutiara',
    'address_2': '',
    'postal_code': '71760',
    'city': 'Bangi',
    'state': 'Selangor',
    'country': 'Malasyia',
    'phone_number': '+601121220311',
    'email': '<EMAIL>',
    'vehicle_manufacturer': 'PRD0123',
    'vehicle_model': 'MYVI X',
    'vehicle_registration_number': 'QKQ676',
    'delivery_option': True,
    'allow_marketing': True
}


def create_user_with_auth_token(include_user_id=False, include_membership_id=False, include_organization_id=False,
                                root_path: str = '', res_path: str = '', res_scope: str = '', is_superuser=False):
    data = {}

    signin_data = {
        'email': '',
        'password': 'password',
    }
    with contextmanager(override_create_session)() as db:
        root_organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            is_superuser=is_superuser,
            organization_id=root_organization.id
        )
        db.commit()

        organization = OrganizationFactory(parent_id=f'{root_organization.id}')
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        res_server = ResourceServerFactory(root_path=root_path)

        res = ResourceFactory(
            path=res_path,
            scope=res_scope,
        )

        try:
            GlobalRoleFactory(
                name='Staff',
                is_global=True,
                organization_id=None,
            )
            GlobalRoleFactory(
                name='Sub Staff',
                is_global=True,
                organization_id=None,
            )
            GlobalRoleFactory(
                name='Regular User',
                is_global=True,
                organization_id=None
            )
            db.commit()
        except (IntegrityError, MultipleResultsFound):
            db.rollback()

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=f'{organization.id}')

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(f'{membership.id}')
        membership.roles.append(role)
        db.commit()

        if include_user_id:
            data['user_id'] = str(user.id)

        if include_membership_id:
            data['membership_id'] = str(membership.id)

        if include_organization_id:
            data['organization_id'] = str(organization.id)

        signin_data['email'] = str(user.email)
        signin_data['organization_id'] = str(organization.id)

    url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
    response = client.post(url, json=signin_data)

    data['auth_token'] = response.json()['auth_token']

    return


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionCustomPlanFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionPlanFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionFeeFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionInvitationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


def test_get_subscription_plan_sucess(test_db):
    with contextmanager(override_create_session)() as db:
        org = OrganizationFactory()
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=org.id
        )
        db.commit()

        organization_id = str(org.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{org.id}',
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        subscription_plan_id = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id.id)
        db.commit()

        create_res_server_and_roles(db, membership, f'{org.id}', fr'{V2_SUB_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": str(staff.id),
            "membership_id": f'{membership.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    url = f'{ROOT_PATH}/api/v1/subscriptions/plan'

    response = client.get(url, headers={'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id})

    assert response.json()['success']
    assert len(response.json()['data']) == 1


def test_get_subscription_plan_fails_authorization_error(test_db):
    with contextmanager(override_create_session)() as db:
        org = OrganizationFactory()
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=org.id
        )
        db.commit()

        organization_id = str(org.id)

        membership = MembershipFactory(
            organization_id=f'{org.id}',
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()

        create_res_server_and_roles(db, membership, f'{org.id}', fr'{V2_SUB_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

    url = f'{ROOT_PATH}/api/v1/subscriptions/plan'

    response = client.get(url, headers={'authorization': None})

    assert response.json()['error']['code'] == 9000


def test_get_user_subscription_sucess(test_db):
    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()

        SubscriptionFactory(member_id=mem.id, subscription_plan_id=subscription.id, is_default=False)
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, fr'{V2_SUB_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": str(staff.id),
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    url = f'{ROOT_PATH}/api/v1/subscriptions/member'
    response = client.get(url, headers={'authorization': token})

    assert response.json()['success']
    assert len(response.json()['data']) == 1


def test_get_user_subscription_error_validation(test_db):
    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()

        SubscriptionFactory(member_id=mem.id, subscription_plan_id=subscription.id, is_default=False)
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, fr'{V2_SUB_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

    url = f'{ROOT_PATH}/api/v1/subscriptions/member'
    response = client.get(url, headers={'authorization': None})

    assert response.json()['error']['code'] == 9000


def test_mobile_update_default_subscription_plan_success(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/member'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        subscription_plan_2 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_2 = str(subscription_plan_2.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()
        mem_id = str(mem.id)

        SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id_1, is_default=False)
        db.commit()
        new_default_sub = SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id_2,
                                              is_default=False)
        db.commit()
        new_default_sub_id = str(new_default_sub.id)

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.patch(f'{url}/{new_default_sub.id}', headers={'authorization': auth_token})

        assert response.json()['success']

    with contextmanager(override_create_session)() as db:
        db_sub = db.query(models.Subscription).filter(models.Subscription.id == new_default_sub_id).one()
        assert db_sub.is_default is True


def test_mobile_update_default_subscription_plan_body_failure(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/member'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()
        mem_id = str(mem.id)

        SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id_1, is_default=False)
        db.commit()
        default_sub_id = '012sdadv123'

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.patch(f'{url}/{default_sub_id}', headers={'authorization': auth_token})

        assert response.json()['error']['code'] == 4222


def test_mobile_update_default_subscription_plan_no_result_found(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/member'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()
        mem_id = str(mem.id)

        SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id_1, is_default=False)
        db.commit()
        default_sub_id = uuid.uuid1()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.patch(f'{url}/{default_sub_id}', headers={'authorization': auth_token})

        assert response.json()['error']['code'] == 4040


def test_get_mobile_subscription_invitation(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/invitation'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_invitation = SubscriptionInvitationFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_invitation_id = str(subscription_invitation.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}/{subscription_invitation_id}', headers={'authorization': auth_token})
        assert response.json()['success']


def test_get_mobile_subscription_invitation_body_failure(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/invitation'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)
        subscription_invitation_id = 'test123'

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}/{subscription_invitation_id}', headers={'authorization': auth_token})

        assert response.json()['error']['code'] == 4222


def test_get_mobile_subscription_invitation_not_found(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/invitation'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)
        subscription_invitation_id = uuid.uuid1()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}/{subscription_invitation_id}', headers={'authorization': auth_token})

        assert response.json()['error']['code'] == 4040


def test_check_subscription_invitation_success(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/check_invitation_code'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id, allow_invitation_code=True)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_invitation = SubscriptionInvitationFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_invitation_plan_id = str(subscription_invitation.subscription_plan_id)
        subscription_invitation_code = subscription_invitation.code

        url_data = {
            'subscription_plan_id': subscription_invitation_plan_id,
            'code': subscription_invitation_code
        }

        url_param = urlencode(url_data)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}/?{url_param}', headers={'authorization': auth_token})

        assert response.json()['success']


def test_check_subscription_invitation_body_failure(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/check_invitation_code'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_invitation = SubscriptionInvitationFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_invitation_code = subscription_invitation.code

        url_data = {
            'subscription_plan_id': 'test123',
            'code': subscription_invitation_code
        }

        url_param = urlencode(url_data)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}/?{url_param}', headers={'authorization': auth_token})

        assert response.json()['error']['code'] == 4222


def test_check_subscription_invitation_not_found(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/check_invitation_code'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_invitation = SubscriptionInvitationFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_invitation_code = subscription_invitation.code

        url_data = {
            'subscription_plan_id': uuid.uuid1(),
            'code': subscription_invitation_code
        }

        url_param = urlencode(url_data)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}/?{url_param}', headers={'authorization': auth_token})

        assert response.json()['error']['code'] == 4040


def test_mobile_member_subscription_details_success(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/member/details'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()
        mem_id = str(mem.id)

        subscription = SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id_1,
                                           is_default=False)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/member/details.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}?subscription_id={subscription.id}', headers={'authorization': auth_token})
        assert response.json()['success']


def test_mobile_member_subscription_details_body_failure(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/member/details'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()
        mem_id = str(mem.id)

        SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id_1, is_default=False)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/member/details.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}?subscription_id=test_id', headers={'authorization': auth_token})
        assert response.json()['error']['code'] == 4222


def test_mobile_member_subscription_details_not_found(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/member/details'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()
        mem_id = str(mem.id)

        SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id_1, is_default=False)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/member/details.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}?subscription_id={uuid.uuid1()}', headers={'authorization': auth_token})
        assert response.json()['error']['code'] == 4040


def test_mobile_register_member_success(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/register-subscription'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='membership_fee')
        db.commit()
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='delivery_fee')
        db.commit()

        SubscriptionCardFactory(subscription_plan_id=subscription_plan_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()

        data = {
            'subscription_plan_id': subscription_plan_id,
            'is_default': True,
            'meta': member_subscription_information,
            'payable_fees': [
                'membership_fee', 'delivery_fee'
            ],
            'currency': 'MYR'
        }

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert response.json()['success']


def test_mobile_register_member_fail_private_no_code(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/register-subscription'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id, is_private=True)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='membership_fee')
        db.commit()
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='delivery_fee')
        db.commit()

        SubscriptionCardFactory(subscription_plan_id=subscription_plan_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()

        data = {
            'subscription_plan_id': subscription_plan_id,
            'is_default': True,
            'meta': member_subscription_information,
            'payable_fees': [
                'membership_fee', 'delivery_fee'
            ],
            'currency': 'MYR'
        }

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert not response.json()['success']
        assert response.json()['error']['message'][0] == 'No invitation code provided'


def test_mobile_register_member_fail_private_with_code_but_no_allow_invitation_code(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/register-subscription'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id, is_private=True)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='membership_fee')
        db.commit()
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='delivery_fee')
        db.commit()

        SubscriptionCardFactory(subscription_plan_id=subscription_plan_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()

        data = {
            'subscription_plan_id': subscription_plan_id,
            'is_default': True,
            'meta': member_subscription_information,
            'payable_fees': [
                'membership_fee', 'delivery_fee'
            ],
            'currency': 'MYR',
            'invitation_code': 'invitation_code'
        }

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert not response.json()['success']
        assert response.json()['error']['message'][0] == 'Invitation code cannot be used'


def test_mobile_register_member_fail_private_with_bad_code_but_allow_invitation_code(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/register-subscription'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id, is_private=True,
                                                    allow_invitation_code=True)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='membership_fee')
        db.commit()
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='delivery_fee')
        db.commit()

        SubscriptionCardFactory(subscription_plan_id=subscription_plan_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()

        data = {
            'subscription_plan_id': subscription_plan_id,
            'is_default': True,
            'meta': member_subscription_information,
            'payable_fees': [
                'membership_fee', 'delivery_fee'
            ],
            'currency': 'MYR',
            'invitation_code': 'invitation_code'
        }

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert not response.json()['success']
        assert response.json()['error']['message'][0] == 'Invitation code is invalid'


def test_mobile_register_member_success_private_with_invitation_code(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/register-subscription'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id, is_private=True,
                                                    allow_invitation_code=True)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='membership_fee')
        db.commit()
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='delivery_fee')
        db.commit()

        SubscriptionCardFactory(subscription_plan_id=subscription_plan_id)
        db.commit()

        db_invitation = SubscriptionInvitationFactory(subscription_plan_id=subscription_plan_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()

        data = {
            'subscription_plan_id': subscription_plan_id,
            'is_default': True,
            'meta': member_subscription_information,
            'payable_fees': [
                'membership_fee', 'delivery_fee'
            ],
            'currency': 'MYR',
            'invitation_code': db_invitation.code
        }

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert response.json()['success']


def test_mobile_register_member_body_failure(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/register-subscription'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()

        data = {
            'subscription_plan_id': '31233',
            'is_default': True,
            'payable_fees': [
                {
                    'name': 'Test fee 1',
                    'amount': 10.0
                },
                {
                    'name': 'Test fee 2',
                    'amount': 25.0
                },
            ],
            'currency': 'MYR'
        }

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert response.json()['error']['code'] == 4222


def test_mobile_register_member_not_found(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/register-subscription'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()

        data = {
            'subscription_plan_id': f'{uuid.uuid1()}',
            'meta': member_subscription_information,
            'is_default': True,
            'payable_fees': [
                {
                    'name': 'Test fee 1',
                    'amount': 10.0
                },
                {
                    'name': 'Test fee 2',
                    'amount': 25.0
                },
            ],
            'currency': 'MYR'
        }

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert response.json()['error']['code'] == 4040


def test_mobile_renew_member_success(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/member/renewal/'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id, is_active=True)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='renewal_fee')
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()
        mem_id = str(mem.id)

        subscription = SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id,
                                           is_default=False)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/member/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(f'{url}?subscription_id={subscription.id}', headers={'authorization': auth_token})

        assert response.json()['success']


def test_mobile_renew_member_not_found(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/member/renewal/'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        SubscriptionPlanFactory(organization_id=organization_id, is_active=True)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/member/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(f'{url}?subscription_id={uuid.uuid1()}', headers={'authorization': auth_token})

        assert response.json()['error']['code'] == 4040


def test_mobile_renew_member_body_failure(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/member/renewal/'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id, is_active=True)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()
        mem_id = str(mem.id)

        SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id,
                            is_default=False)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/member/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(f'{url}?subscription_id=123123', headers={'authorization': auth_token})

        assert response.json()['error']['code'] == 4222


def test_mobile_renew_member_in_active_subscription_plan(test_db):
    url = f'{ROOT_PATH}/api/v1/subscriptions/member/renewal/'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id, is_active=False)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)
        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id, name='renewal_fee')
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()
        mem_id = str(mem.id)

        subscription = SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id,
                                           is_default=False)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/subscriptions/member/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(f'{url}?subscription_id={subscription.id}', headers={'authorization': auth_token})

        assert response.json()['success']
