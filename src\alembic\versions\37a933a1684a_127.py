"""127

Revision ID: 37a933a1684a
Revises: 6fa642e95a72
Create Date: 2024-10-21 13:01:56.453282

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '37a933a1684a'
down_revision = '6fa642e95a72'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_id_tag', sa.Column('autocharge_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key('main_id_tag_fk_autochage', 'main_id_tag', 'main_autocharge', ['autocharge_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('main_id_tag_fk_autochage', 'main_id_tag', type_='foreignkey')
    op.drop_column('main_id_tag', 'autocharge_id')
    # ### end Alembic commands ###
