"""176

Revision ID: ba3896e5bf21
Revises: 01eb6c926deb
Create Date: 2025-06-04 16:15:12.067455

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ba3896e5bf21'
down_revision = '01eb6c926deb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_e_credit_note', sa.Column('currency_code', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('payment_terms', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('bill_reference_number', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('is_self_issued', sa.<PERSON>(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('is_consolidated_invoice', sa.<PERSON>(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('total_net_amount', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('total_tax_amount', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('total_charge_amount', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('total_discount_value', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('total_payable_amount', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('total_tax_excluded_amount', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('total_tax_included_amount', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('invoice_tax', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('invoice_discount', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('invoice_total_excluding_tax', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('quantity', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('measurement', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('unit_price', sa.String(), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('sub_total', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('currency_code', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('payment_terms', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('bill_reference_number', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('is_self_issued', sa.Boolean(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('is_consolidated_invoice', sa.Boolean(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('total_net_amount', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('total_tax_amount', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('total_charge_amount', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('total_discount_value', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('total_payable_amount', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('total_tax_excluded_amount', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('total_tax_included_amount', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('invoice_tax', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_e_invoice', sa.Column('invoice_discount', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_e_invoice', sa.Column('invoice_total_excluding_tax', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('quantity', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('measurement', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('unit_price', sa.String(), nullable=True))
    op.add_column('main_e_invoice', sa.Column('sub_total', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_e_invoice', 'sub_total')
    op.drop_column('main_e_invoice', 'unit_price')
    op.drop_column('main_e_invoice', 'measurement')
    op.drop_column('main_e_invoice', 'quantity')
    op.drop_column('main_e_invoice', 'invoice_total_excluding_tax')
    op.drop_column('main_e_invoice', 'invoice_discount')
    op.drop_column('main_e_invoice', 'invoice_tax')
    op.drop_column('main_e_invoice', 'total_tax_included_amount')
    op.drop_column('main_e_invoice', 'total_tax_excluded_amount')
    op.drop_column('main_e_invoice', 'total_payable_amount')
    op.drop_column('main_e_invoice', 'total_discount_value')
    op.drop_column('main_e_invoice', 'total_charge_amount')
    op.drop_column('main_e_invoice', 'total_tax_amount')
    op.drop_column('main_e_invoice', 'total_net_amount')
    op.drop_column('main_e_invoice', 'is_consolidated_invoice')
    op.drop_column('main_e_invoice', 'is_self_issued')
    op.drop_column('main_e_invoice', 'bill_reference_number')
    op.drop_column('main_e_invoice', 'payment_terms')
    op.drop_column('main_e_invoice', 'currency_code')
    op.drop_column('main_e_credit_note', 'sub_total')
    op.drop_column('main_e_credit_note', 'unit_price')
    op.drop_column('main_e_credit_note', 'measurement')
    op.drop_column('main_e_credit_note', 'quantity')
    op.drop_column('main_e_credit_note', 'invoice_total_excluding_tax')
    op.drop_column('main_e_credit_note', 'invoice_discount')
    op.drop_column('main_e_credit_note', 'invoice_tax')
    op.drop_column('main_e_credit_note', 'total_tax_included_amount')
    op.drop_column('main_e_credit_note', 'total_tax_excluded_amount')
    op.drop_column('main_e_credit_note', 'total_payable_amount')
    op.drop_column('main_e_credit_note', 'total_discount_value')
    op.drop_column('main_e_credit_note', 'total_charge_amount')
    op.drop_column('main_e_credit_note', 'total_tax_amount')
    op.drop_column('main_e_credit_note', 'total_net_amount')
    op.drop_column('main_e_credit_note', 'is_consolidated_invoice')
    op.drop_column('main_e_credit_note', 'is_self_issued')
    op.drop_column('main_e_credit_note', 'bill_reference_number')
    op.drop_column('main_e_credit_note', 'payment_terms')
    op.drop_column('main_e_credit_note', 'currency_code')
    # ### end Alembic commands ###
