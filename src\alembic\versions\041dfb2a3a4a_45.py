"""45

Revision ID: 041dfb2a3a4a
Revises: 58eb64d14d04
Create Date: 2023-07-12 21:11:01.364244

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '041dfb2a3a4a'
down_revision = '58eb64d14d04'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_charging_session_bill', sa.Column('total_amount', sa.Numeric(scale=2), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_charging_session_bill', 'total_amount')
    # ### end Alembic commands ###
