"""90

Revision ID: 77f96ad68d3c
Revises: 954a8dd8b70b
Create Date: 2024-05-02 13:46:25.702314

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '77f96ad68d3c'
down_revision = '954a8dd8b70b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_user_access_operator',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('operator_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('membership_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['membership_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['operator_id'], ['main_operator.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('membership_id', 'operator_id', name='_unique_operator_user')
    )
    op.add_column('main_operator', sa.Column('is_private', sa.Boolean(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_operator', 'is_private')
    op.drop_table('main_user_access_operator')
    # ### end Alembic commands ###
