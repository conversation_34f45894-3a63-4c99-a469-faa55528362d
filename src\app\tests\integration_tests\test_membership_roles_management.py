from contextlib import contextmanager
import pytest

from faker import Faker
from fastapi.testclient import TestClient

from app.main import app, ROOT_PATH
from app.tests.factories import (UserFactory, VerificationTokenFactory, OrganizationFactory,
                                 ResourceServerFactory, ResourceFactory,
                                 RoleFactory, MembershipFactory, )
from app.database import SessionLocal, create_session, Base, engine
from app.schema import MembershipType


fake = Faker()
client = TestClient(app)


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VerificationTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


# Staff-level endpoints (SysAdmin access only)


def test_attach_existing_role_to_staff_membership_succeeds(test_db):
    data = {
        'roles': []
    }

    organization_id = None
    membership_id = None
    auth_token = None
    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        admin = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=str(parent_organization.id)
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{parent_organization.id}',
            user_id=f'{admin.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{staff.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        # setup resource for testserver endpoint
        rs = ResourceServerFactory(root_path='apollo-main')
        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/membership/roles/.+/add/?',
            scope='get,patch,post,delete',
            resourceserver=rs
        )

        role = RoleFactory(organization_id=organization_id)

        # attach resource to created role for staff
        role.resources.append(res)
        db.commit()

        # assign role to staff
        mem.roles.append(role)
        db.commit()

        for _item in range(2):
            role = RoleFactory(organization=organization)
            db.commit()

            data['roles'].append(str(role.id))

        # login staff and attach auth token to client.get call's header
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'

        login_data = {
            'email': admin.email,
            'password': 'password',
            'organization_id': f'{parent_organization.id}',
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/membership/roles/{membership_id}/add'
    response = client.post(url, json=data, headers={'authorization': auth_token})

    assert response.status_code == 200
    assert len(response.json()['roles']) == 3


def test_attach_non_existing_role_to_staff_membership_fails(test_db):
    data = {
        'roles': []
    }

    organization_id = None
    membership_id = None
    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        admin = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=str(parent_organization.id)
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{parent_organization.id}',
            user_id=f'{admin.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{staff.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        # setup resource for testserver endpoint
        rs = ResourceServerFactory(root_path='apollo-main')
        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/membership/roles/.+/add/?',
            scope='get,patch,post,delete',
            resourceserver=rs
        )

        role = RoleFactory(organization_id=organization_id)

        # attach resource to created role for staff
        role.resources.append(res)
        db.commit()

        # assign role to staff
        mem.roles.append(role)
        db.commit()

        for _item in range(2):
            role = RoleFactory(organization=organization)
            db.commit()

            data['roles'].append(str(role.id))

        role = RoleFactory(organization=organization)
        db.commit()

        data['roles'].append(str(role.id))

        db.delete(role)
        db.commit()

        # login staff and attach auth token to client.get call's header
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'

        login_data = {
            'email': admin.email,
            'password': 'password',
            'organization_id': f'{parent_organization.id}',
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/membership/roles/{membership_id}/add'
    response = client.post(url, json=data, headers={'authorization': auth_token})

    assert response.status_code == 400


def test_remove_existing_role_from_staff_membership_succeeds(test_db):
    data = {
        'roles': []
    }

    organization_id = None
    membership_id = None
    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        admin = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=str(parent_organization.id)
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{parent_organization.id}',
            user_id=f'{admin.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        # setup resource for testserver endpoint
        rs = ResourceServerFactory(root_path='apollo-main')
        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/membership/roles/.+/remove/?',
            scope='get,patch,post,delete',
            resourceserver=rs
        )

        role = RoleFactory(organization_id=organization_id)

        # attach resource to created role for staff
        role.resources.append(res)
        db.commit()

        # assign role to staff
        mem.roles.append(role)
        db.commit()

        for _item in range(2):
            role = RoleFactory(organization=organization)
            db.commit()

            mem.roles.append(role)
        db.commit()

        data['roles'].append(str(mem.roles[0].id))

        # login staff and attach auth token to client.get call's header
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'

        login_data = {
            'email': admin.email,
            'password': 'password',
            'organization_id': f'{parent_organization.id}',
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/membership/roles/{membership_id}/remove'
    response = client.post(url, json=data, headers={'authorization': auth_token})

    assert response.status_code == 200
    assert len(response.json()['roles']) == 2


def test_remove_non_existing_role_from_staff_membership_succeeds(test_db):
    data = {
        'roles': []
    }

    organization_id = None
    membership_id = None
    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=str(parent_organization.id)
        )
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{staff.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        # setup resource for testserver endpoint
        rs = ResourceServerFactory(root_path='apollo-main')
        res = ResourceFactory(
            path=f'{ROOT_PATH}/api/v1/csms/organization/.+/membership/roles/.+/remove/?',
            scope='get,patch,post,delete',
            resourceserver=rs
        )

        role = RoleFactory(organization_id=organization_id)

        # attach resource to created role for staff
        role.resources.append(res)
        db.commit()

        # assign role to staff
        mem.roles.append(role)
        db.commit()

        for _item in range(2):
            role = RoleFactory(organization=organization)
            db.commit()

            mem.roles.append(role)
        db.commit()

        role = RoleFactory(organization=organization)
        db.commit()

        mem.roles.append(role)
        db.commit()

        data['roles'].append(str(role.id))

        db.delete(role)
        db.commit()

        # login staff and attach auth token to client.get call's header
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'

        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    url = f'{ROOT_PATH}/api/v1/csms/organization/{organization_id}/membership/roles/{membership_id}/remove'
    response = client.post(url, json=data, headers={'authorization': auth_token})

    assert response.status_code == 200
    assert len(response.json()['roles']) == 3
