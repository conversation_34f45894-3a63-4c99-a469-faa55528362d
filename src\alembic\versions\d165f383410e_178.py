"""178

Revision ID: d165f383410e
Revises: ebdcc4c5175b
Create Date: 2025-06-09 22:26:09.354639

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd165f383410e'
down_revision = 'ebdcc4c5175b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_payment_request', sa.Column('pre_auth_outstanding_amount', sa.String(), nullable=True))
    op.add_column('main_payment_request', sa.Column('pre_auth_outstanding_capture_status', sa.String(), nullable=True))
    op.add_column('main_payment_request', sa.Column('pre_auth_outstanding_order_id', sa.String(), nullable=True))
    op.add_column('main_payment_request', sa.Column('pay_via_preauth_and_recurring', sa.<PERSON><PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_payment_request', 'pay_via_preauth_and_recurring')
    op.drop_column('main_payment_request', 'pre_auth_outstanding_order_id')
    op.drop_column('main_payment_request', 'pre_auth_outstanding_capture_status')
    op.drop_column('main_payment_request', 'pre_auth_outstanding_amount')
    # ### end Alembic commands ###
