"""137

Revision ID: dac228556e44
Revises: cd547a1df08a
Create Date: 2024-12-16 14:22:21.050394

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'dac228556e44'
down_revision = 'cd547a1df08a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_auth_external_token', sa.Column('is_lta', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_auth_external_token', 'is_lta')
    # ### end Alembic commands ###
