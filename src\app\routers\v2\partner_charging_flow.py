import json
import logging

from fastapi import APIRouter, Depends, Request, HTTPException, Security
from fastapi.responses import JSONResponse
from fastapi.security.api_key import APIKeyHeader

from sqlalchemy.exc import IntegrityError

from app import schema, settings, models
from app.crud import get_charging_session_bill_by_charging_session, \
    charging_bill_done, charging_bill_failed, \
    PartnerPaymentNotificationCRUD, get_payment_reqeust_by_invoice_number, payment_request_done
from app.database import create_session, SessionLocal
from app.middlewares import set_admin_as_context_user
from app.schema import PartnerPaymentStatus, PartnerPaymentNotificationCreate, \
    BasicMessage

from app.permissions import x_api_key
from app.utils import (
    send_request, RouteErrorHandlerProxy,
    get_charge_history_record,
    generate_charger_header_unauthorized, get_partner_id_by_oas_id,
    # get_partner_accessible_charge_points
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix=f"/{settings.MAIN_ROOT_PATH}/api/v1/partner",
    tags=['partner charging flow', ],
    dependencies=[Depends(x_api_key)],
    route_class=RouteErrorHandlerProxy
)

OCPI_URL = f'{settings.MAIN_OCPI_DOMAIN}/{settings.OCPI_PREFIX}'

CHARGER_URL_PREFIX_V2 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'
CHARGER_URL_PREFIX_V1 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'

API_KEY_NAME = "x-api-key"
SID_KEY_NAME = "x-api-sid"

api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)
api_sid_header = APIKeyHeader(name=SID_KEY_NAME, auto_error=False)

unauthorized_path = [
    'charge_point/serial/',
    'connector/charge_points/',
]


async def log_activity(user_id: str, table_name: str, data: dict,
                       log_type: schema.ActivityLogType, dbsession: SessionLocal = Depends(create_session)):
    db_activity = models.ActivityLog(user_id=user_id, table_name=table_name, data=data, type=log_type)
    dbsession.add(db_activity)
    dbsession.commit()


@router.get('/charger/charge_point/serial/{serial_number}')
async def get_charge_point_by_serial_number(request: Request, serial_number: str,
                                            api_sid_header: str = Security(api_sid_header),
                                            dbsession: SessionLocal = Depends(create_session)):
    """
    For Partner (Payment Terminal) to get Charge Point by Serial Number
    Authenticated depending on x-api-key
    :param str serial_number
    """
    headers = generate_charger_header_unauthorized(dbsession, api_sid_header)
    # accessible_charge_points = get_partner_accessible_charge_points(dbsession, api_sid_header)
    # headers['accessible_charge_points'] = json.dumps(accessible_charge_points)
    logger.debug('header for request to charger service: %s', headers)
    url = f'{CHARGER_URL_PREFIX_V2}/charge_point/serial/{serial_number}'
    method = request.method
    response = await send_request(method, url, headers=headers)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/charger/start-charging/{connector_id}/{partner_token_id}')
async def start_charging_partner_proxy(request: Request, connector_id: str,
                                       partner_token_id: str,
                                       api_sid_header: str = Security(api_sid_header),
                                       dbsession: SessionLocal = Depends(create_session)):
    """
    Start charging for third party partner (eg: payment terminal) based on connector,
    this is a proxy to the charger API but with additional addons.
    For instance: charging flow, which check connector avaiablility, connector reservation, user id, permission
    and etc.

    :param str connector_id
    :param str partner_token_id
    """
    headers = generate_charger_header_unauthorized(dbsession, api_sid_header)
    partner_info = get_partner_id_by_oas_id(dbsession, api_sid_header)
    # accessible_charge_points = get_partner_accessible_charge_points(dbsession, api_sid_header)
    # headers['accessible_charge_points'] = json.dumps(accessible_charge_points)
    headers['id_tag'] = f'PTNR-{partner_info.party_id}'
    data = {
        'partner_charging_session': {
            'partner_id': str(api_sid_header),
            'partner_token_id': str(partner_token_id),
            'status': 'PENDING',
        },
    }

    # start charging
    # calling v1 to avoid the issue of v2 not working / not updated
    path = f'partner/connector/{connector_id}/remote-start'
    url = f'{CHARGER_URL_PREFIX_V1}/{path}'
    response = await send_request('POST', url, data=json.dumps(data), headers=headers)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/charger/stop-charging/{transaction_id}')
async def stop_charging_session(request: Request, transaction_id: int,
                                api_sid_header: str = Security(api_sid_header),
                                dbsession: SessionLocal = Depends(create_session)):
    """
    Stop charging services based on transaction id, it will trigger to stop the charger activity, as-well as updating
    the charging session data

    :param str transaction_id
    """
    headers = generate_charger_header_unauthorized(dbsession, api_sid_header)
    # accessible_charge_points = get_partner_accessible_charge_points(dbsession, api_sid_header)
    # headers['accessible_charge_points'] = json.dumps(accessible_charge_points)
    partner_info = get_partner_id_by_oas_id(dbsession, api_sid_header)
    headers['id_tag'] = f'PTNR-{partner_info.party_id}'

    get_session_path = f"partner/charging/{transaction_id}"
    get_session_url = f'{CHARGER_URL_PREFIX_V1}/{get_session_path}'
    cs_response = await send_request('GET', get_session_url, headers=headers)

    if not cs_response.json():
        raise HTTPException(status_code=404, detail='Charging Session Not Found')

    logger.debug('header for request to charger service: %s', headers)
    url = f'{CHARGER_URL_PREFIX_V1}/partner/connector/remote-stop/{transaction_id}'
    response = await send_request('POST', url, headers=headers)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.get('/charger/charge-usage/{session_id}')
async def get_charge_usage(request: Request, session_id: str,
                           api_sid_header: str = Security(api_sid_header),
                           dbsession: SessionLocal = Depends(create_session)):
    """
    Get the charge usage (usage history) for the particular session

    :param str charging_session_id
    """
    headers = generate_charger_header_unauthorized(dbsession, api_sid_header)
    # accessible_charge_points = get_partner_accessible_charge_points(dbsession, api_sid_header)
    # headers['accessible_charge_points'] = json.dumps(accessible_charge_points)

    logger.debug('header for request to charger service: %s', headers)
    url = f'{CHARGER_URL_PREFIX_V1}/partner/session/{session_id}'
    method = request.method
    data = await request.body()

    # stop charging
    # calling v1 to avoid the issue of v2 not working / not updated
    response = await send_request(method, url, data=data, headers=headers)
    if response.status_code == 200:
        response_json = response.json()
        charging_record = get_charge_history_record(response_json, dbsession, is_partner=True)
        # charging_record = map_charging_session_id_tag_with_member(dbsession, charging_record)
        return JSONResponse(charging_record, status_code=response.status_code)

    if response.status_code == 500:
        raise HTTPException(status_code=404, detail='No charging session found')

    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/payment/notification/{session_id}')
async def partner_payment_notification(request: Request, session_id: str,
                                       data: schema.PartnerPaymentNotification,
                                       api_sid_header: str = Security(api_sid_header),
                                       dbsession: SessionLocal = Depends(create_session)):
    """
    Endpoint for partner to POST payment status

    :param str charging_session_id
    """
    set_admin_as_context_user(dbsession)
    headers = generate_charger_header_unauthorized(dbsession, api_sid_header)
    # accessible_charge_points = get_partner_accessible_charge_points(dbsession, api_sid_header)
    # headers['accessible_charge_points'] = json.dumps(accessible_charge_points)

    logger.debug('header for request to charger service: %s', headers)
    cs_bill = get_charging_session_bill_by_charging_session(dbsession, session_id)

    if data.status == PartnerPaymentStatus.success:
        charging_bill_done(dbsession, cs_bill.id)
    if data.status == PartnerPaymentStatus.failed:
        charging_bill_failed(dbsession, cs_bill.id)

    db_pr = get_payment_reqeust_by_invoice_number(dbsession, cs_bill.invoice_number)
    payment_request_done(dbsession, db_pr.id)

    ppn_schema = PartnerPaymentNotificationCreate(
        partner_id=api_sid_header,
        charging_session_bill_id=cs_bill.id,
        status=data.status,
        message=data.message,
        charging_session_id=session_id
    ).dict()

    try:
        PartnerPaymentNotificationCRUD.add(dbsession, ppn_schema)
    except IntegrityError:
        dbsession.rollback()
        logger.error('Failed to create Payment Notification')

    return BasicMessage(detail='')


@router.post('/payment/repush/{session_id}')
async def repush_invoice_to_partner(request: Request, session_id: str,
                                    api_sid_header: str = Security(api_sid_header),
                                    dbsession: SessionLocal = Depends(create_session)):
    """
    Endpoint for partner to POST payment status

    :param str charging_session_id
    """
    headers = generate_charger_header_unauthorized(dbsession, api_sid_header)
    # accessible_charge_points = get_partner_accessible_charge_points(dbsession, api_sid_header)
    # headers['accessible_charge_points'] = json.dumps(accessible_charge_points)

    logger.debug('header for request to charger service: %s', headers)
    url = f'{CHARGER_URL_PREFIX_V1}/partner/repush/session/{session_id}'
    method = request.method
    data = await request.body()

    # stop charging
    # calling v1 to avoid the issue of v2 not working / not updated
    response = await send_request(method, url, data=data, headers=headers)
    if response.status_code in [200, 201, 202, 203, 204]:
        return BasicMessage(detail='Pushing via POST request')

    if response.status_code in [404, 500]:
        raise HTTPException(status_code=404, detail='No charging session found')

    if response.status_code in [403]:
        raise HTTPException(status_code=403, detail='Charging session not completed yet')

    return BasicMessage(detail='Pushing error, please check with administrator')
