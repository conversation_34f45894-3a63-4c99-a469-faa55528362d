from contextlib import contextmanager
from contextvars import ContextVar
import re
import uuid
from jose import jwt
from fastapi import Request

from sqlalchemy.orm import Session
from sqlalchemy.exc import NoResultFound
from starlette.responses import PlainTextResponse, JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

from app import models, exceptions, schema, settings
from app.database import create_session

REQUEST_USER = "request_user"
REQUEST_AUDIT = "request_audit"

_request_user_ctx_var = ContextVar(REQUEST_USER, default=None)
_request_audit_ctx_var = ContextVar(REQUEST_AUDIT, default=None)


def get_request_user() -> schema.MembershipResponse:
    """
    get the request user data
    """
    return _request_user_ctx_var.get()


def get_request_audit() -> dict:
    """
    get the request audit log setting
    """
    return _request_audit_ctx_var.get()


def set_admin_as_context_user(db: Session):
    db_membership = db.query(models.Membership).join(models.User).filter(
        models.User.is_superuser.is_(True)
    ).first()
    membership = schema.MembershipResponse.from_orm(db_membership)
    _request_user_ctx_var.set(membership)
    return membership


def set_member_as_context_user(db: Session, membership_id):
    try:
        db_membership = db.query(models.Membership).filter(models.Membership.id == membership_id).one()
        membership = schema.MembershipResponse.from_orm(db_membership)
    except NoResultFound:
        membership = None

    _request_user_ctx_var.set(membership)


# Call this function to deactive audit for specific route
def deactivate_audit():
    audit_setting = {'audit': False}
    _request_audit_ctx_var.set(audit_setting)
    return audit_setting


class RequestContextMiddleware(BaseHTTPMiddleware):

    async def dispatch(
            self, request: Request, call_next: RequestResponseEndpoint
    ):
        membership = None

        token = request.headers.get('authorization')
        if token:
            token = token.removeprefix(f'{schema.TokenTypeEnum.bearer} ')
            try:
                auth_token_data = jwt.decode(token, key=settings.JWT_SECRET,
                                             algorithms=[schema.JWT_ALGORITHM, ],
                                             options={'verify_exp': False})
            except jwt.JWTError:
                return JSONResponse(status_code=400, content={'detail': 'Token validation error'})

            membership_id = auth_token_data.get('membership_id')
            if membership_id:
                with contextmanager(create_session)() as db:
                    db: Session
                    try:
                        db_membership = db.query(models.Membership).filter(models.Membership.id == membership_id).one()
                        membership = schema.MembershipResponse.from_orm(db_membership)
                    except NoResultFound:
                        membership = None

        audit_setting = {
            'audit': False,
            'api': f'{request.method} {request.url.path}',
        }
        csms = re.match('.*/csms/.*', audit_setting['api'])
        group_id = request.headers.get('X-group-id')
        if csms:
            audit_setting['audit'] = True
            audit_setting['group_id'] = group_id if group_id else uuid.uuid4()

        request_token = _request_user_ctx_var.set(membership)
        request_audit = _request_audit_ctx_var.set(audit_setting)
        try:
            response = await call_next(request)
        except exceptions.ApolloPermissionError as e:
            response = PlainTextResponse(e.__str__(), status_code=403)

        _request_user_ctx_var.reset(request_token)
        _request_audit_ctx_var.reset(request_audit)

        return response
