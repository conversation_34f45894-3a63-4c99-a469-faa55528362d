
import logging

from fastapi import APIRouter, Depends, HTTPException, Request, status
from app import settings
from app.permissions import x_api_key
from app.utils import send_request

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/ess",
    tags=['energy storage system', ],
    dependencies=[Depends(x_api_key)]
)


@router.put('/{ess_id}', status_code=status.HTTP_200_OK)
async def update_ess_info(request: Request, ess_id: str):
    data = await request.body()
    ess_charger_url = f'{CHARGER_URL_PREFIX}/energy_storage_system/{ess_id}'
    response = await send_request(method='PATCH', url=ess_charger_url, data=data)
    if response.status_code != status.HTTP_200_OK:
        raise HTTPException(status_code=response.status_code, detail=response.json())
    return response.json()
