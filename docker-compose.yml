version: '3.1'

x-main: &main
  restart: always
  build:
    context: .
    dockerfile: ./docker/Dockerfile.main
  env_file: .env
  volumes:
    - ./src:/app

services:
  postgres:
    container_name: main-postgres
    restart: always
    image: postgres:13.0
    env_file: .env
    volumes:
      - apollo-main-postgres-data:/var/lib/postgresql/data
    ports:
      - 5432:5432

  rabbitmq:
    container_name: main-rabbitmq
    restart: always
    image: rabbitmq:3-management
    env_file: .env
    ports:
      - 5672:5672
      - 15672:15672

  apollo-main:
    <<: *main
    container_name: apollo-main
    depends_on:
      - postgres
      - rabbitmq
    environment:
      - PORT=8001
    ports:
      - 8001:8001

  apollo-ocpi:
    <<: *main
    container_name: apollo-ocpi
    depends_on:
      - postgres
      - rabbitmq
    environment:
      - PORT=8002
      - MODULE_NAME=app.ocpi
    ports:
      - 8002:8002

  apollo-main-celery:
    <<: *main
    container_name: apollo-main-celery
    depends_on:
      - apollo-main
    command: pipenv run celery -A app.celery worker -l INFO

  apollo-main-flower:
    <<: *main
    container_name: apollo-main-flower
    depends_on:
      - apollo-main
    command: pipenv run celery -A app.celery flower -l INFO

volumes:
  apollo-main-postgres-data:
