"""96

Revision ID: c4fb1db1a5f4
Revises: 7568df9eb42c
Create Date: 2024-05-23 15:47:44.745620

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c4fb1db1a5f4'
down_revision = '7568df9eb42c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_credit_card', sa.Column('user_token', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_credit_card', 'user_token')
    # ### end Alembic commands ###
