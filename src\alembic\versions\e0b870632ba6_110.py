"""110

Revision ID: e0b870632ba6
Revises: 314e072becb2
Create Date: 2024-07-31 21:38:36.077022

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e0b870632ba6'
down_revision = '314e072becb2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_payment_request', sa.Column('preauth_bill_via_recurring', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_payment_request', 'preauth_bill_via_recurring')
    # ### end Alembic commands ###
