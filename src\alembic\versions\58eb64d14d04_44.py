"""44

Revision ID: 58eb64d14d04
Revises: 9c520e23fb39
Create Date: 2023-07-12 09:07:53.813715

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '58eb64d14d04'
down_revision = '9c520e23fb39'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_page_resource',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('view', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('create', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('edit', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('delete', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_page_resource')
    # ### end Alembic commands ###
