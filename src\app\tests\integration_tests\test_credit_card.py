from datetime import datetime, timed<PERSON>ta
from contextlib import contextmanager
import pytest

from unittest.mock import patch

import jwt
from faker import Faker
from fastapi.testclient import TestClient

from app import settings, schema, models
from app.main import app, ROOT_PATH
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory, CreditCardFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory, )
from app.database import SessionLocal, create_session, Base, engine
from app.tests.mocks.async_client import MockAsyncClientGeneratorPaymentIPN

fake = Faker()
client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session

CREDIT_CARD_BASE_URL = f'{ROOT_PATH}/api/v1/csms/cc'


def test_create_credit_card(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=str(organization.id)
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/'
        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 307
        assert response.headers.get('location') is not None

        db_activity = db.query(models.ActivityLog).order_by(models.ActivityLog.created_at).first()
        assert db_activity.type == schema.ActivityLogType.create


def test_get_credit_cards_list(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        CreditCardFactory(member_id=membership_id, primary=True, last_four_digit='1111')
        db.commit()

        CreditCardFactory(member_id=membership_id, primary=False, last_four_digit='2222')
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 200
        assert len(response.json()['items']) == 2


def test_get_credit_card(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        cc = CreditCardFactory(member_id=membership_id, primary=True)
        db.commit()

        CreditCardFactory(member_id=membership_id, primary=False)
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/{cc.id}'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 200
        assert response.json()['id'] == str(cc.id)
        assert response.json()['member']['first_name'] is not None
        assert response.json()['member']['last_name'] is not None
        assert response.json()['member']['organization_id'] is not None
        assert response.json()['member']['user']['email'] is not None
        assert response.json()['member']['user']['id'] == str(staff.id)


def test_set_credit_card_as_primary(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        CreditCardFactory(member_id=membership_id, primary=True)
        db.commit()

        cc_2 = CreditCardFactory(member_id=membership_id, primary=False)
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/{cc_2.id}'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 200
        assert response.json()['primary'] is True


# @patch('app.routers.credit_card.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_delete_credit_card(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        cc_1 = CreditCardFactory(member_id=membership_id, primary=True)
        db.commit()

        CreditCardFactory(member_id=membership_id, primary=False)
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/{cc_1.id}'
        response = client.delete(url, headers={'authorization': token})
        assert response.status_code == 200


def test_get_credit_card_user_details(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        CreditCardFactory(member_id=membership_id, primary=True)
        db.commit()

        CreditCardFactory(member_id=membership_id, primary=False)
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 200
        assert response.json()['items'][0]['member']['first_name'] is not None
        assert response.json()['items'][0]['member']['last_name'] is not None
        assert response.json()['items'][0]['member']['organization_id'] is not None
        assert response.json()['items'][0]['member']['user']['phone_number'] is not None
        assert response.json()['items'][0]['member']['user']['email'] is not None


def test_blacklist_credit_card(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        cc_1 = CreditCardFactory(member_id=membership_id, primary=True)
        db.commit()

        CreditCardFactory(member_id=membership_id, primary=False)
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/blacklist/{cc_1.id}'
        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 200

        query = db.query(models.BlacklistCreditCard).filter()
        assert query.count() == 1
        assert query[0].email == staff.email
        assert query[0].phone_number == staff.phone_number
        db.commit()

        query = db.query(models.CreditCard).filter(models.CreditCard.id == cc_1.id)
        assert query.count() == 1
        assert query[0].status == schema.CreditCardStatus.blacklisted


def test_blacklist_card_readd(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        cc_1 = CreditCardFactory(member_id=membership_id, primary=True)
        db.commit()

        CreditCardFactory(member_id=membership_id, primary=False)
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/blacklist/{cc_1.id}'
        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 200

        query = db.query(models.BlacklistCreditCard).filter()
        assert query.count() == 1
        assert query[0].email == staff.email
        assert query[0].phone_number == staff.phone_number
        db.commit()

        query = db.query(models.CreditCard).filter(models.CreditCard.id == cc_1.id)
        assert query.count() == 1
        assert query[0].status == schema.CreditCardStatus.blacklisted
