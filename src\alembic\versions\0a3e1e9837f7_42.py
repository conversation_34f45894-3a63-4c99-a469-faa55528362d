"""42

Revision ID: 0a3e1e9837f7
Revises: 32b061fdff45
Create Date: 2023-07-04 14:37:09.321007

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0a3e1e9837f7'
down_revision = '32b061fdff45'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_external_organization', sa.Column('friendly_name', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_external_organization', 'friendly_name')
    # ### end Alembic commands ###
