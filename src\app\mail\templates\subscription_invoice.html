<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <!-- CSS only -->

    <style type="text/css">:root {}
::after {box-sizing:border-box}
::before {box-sizing:border-box}
@media (prefers-reduced-motion: no-preference) {
    :root {
        scroll-behavior: smooth
        }
    }
@media (min-width: 1200px) {
    .h1, h1 {
        font-size: 2.5rem
        }
    }
@media (min-width: 1200px) {
    .h2, h2 {
        font-size: 2rem
        }
    }
@media (min-width: 1200px) {
    .h3, h3 {
        font-size: 1.75rem
        }
    }
@media (min-width: 1200px) {
    .h4, h4 {
        font-size: 1.5rem
        }
    }
a:hover {}
a:not([href]):not([class]) {color:inherit;text-decoration:none}
a:not([href]):not([class]):hover {color:inherit;text-decoration:none}
button:focus:not(:focus-visible) {outline:0}
select:disabled {opacity:1}
[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {display:none}
[type=button]:not(:disabled) {cursor:pointer}
[type=reset]:not(:disabled) {cursor:pointer}
[type=submit]:not(:disabled) {cursor:pointer}
button:not(:disabled) {cursor:pointer}
::-moz-focus-inner {padding:0;border-style:none}
@media (min-width: 1200px) {
    legend {
        font-size: 1.5rem
        }
    }
::-webkit-datetime-edit-day-field {padding:0}
::-webkit-datetime-edit-fields-wrapper {padding:0}
::-webkit-datetime-edit-hour-field {padding:0}
::-webkit-datetime-edit-minute {padding:0}
::-webkit-datetime-edit-month-field {padding:0}
::-webkit-datetime-edit-text {padding:0}
::-webkit-datetime-edit-year-field {padding:0}
::-webkit-inner-spin-button {height:auto}
::-webkit-search-decoration {-webkit-appearance:none}
::-webkit-color-swatch-wrapper {padding:0}
::-webkit-file-upload-button {font:inherit;-webkit-appearance:button}
::file-selector-button {font:inherit;-webkit-appearance:button}
@media (min-width: 1200px) {
    .display-1 {
        font-size: 5rem
        }
    }
@media (min-width: 1200px) {
    .display-2 {
        font-size: 4.5rem
        }
    }
@media (min-width: 1200px) {
    .display-3 {
        font-size: 4rem
        }
    }
@media (min-width: 1200px) {
    .display-4 {
        font-size: 3.5rem
        }
    }
@media (min-width: 1200px) {
    .display-5 {
        font-size: 3rem
        }
    }
@media (min-width: 1200px) {
    .display-6 {
        font-size: 2.5rem
        }
    }
.list-inline-item:not(:last-child) {margin-right:0.5rem}
.blockquote-footer::before {content:"— "}
@media (min-width: 576px) {
    .container, .container-sm {
        max-width: 540px
        }
    }
@media (min-width: 768px) {
    .container, .container-md, .container-sm {
        max-width: 720px
        }
    }
@media (min-width: 992px) {
    .container, .container-lg, .container-md, .container-sm {
        max-width: 960px
        }
    }
@media (min-width: 1200px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl {
        max-width: 1140px
        }
    }
@media (min-width: 1400px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
        max-width: 1320px
        }
    }
@media (min-width: 576px) {
    .col-sm {
        flex: 1 0 0%
        }
    .row-cols-sm-auto > * {
        flex: 0 0 auto;
        width: auto
        }
    .row-cols-sm-1 > * {
        flex: 0 0 auto;
        width: 100%
        }
    .row-cols-sm-2 > * {
        flex: 0 0 auto;
        width: 50%
        }
    .row-cols-sm-3 > * {
        flex: 0 0 auto;
        width: 33.333333%
        }
    .row-cols-sm-4 > * {
        flex: 0 0 auto;
        width: 25%
        }
    .row-cols-sm-5 > * {
        flex: 0 0 auto;
        width: 20%
        }
    .row-cols-sm-6 > * {
        flex: 0 0 auto;
        width: 16.666667%
        }
    .col-sm-auto {
        flex: 0 0 auto;
        width: auto
        }
    .col-sm-1 {
        flex: 0 0 auto;
        width: 8.333333%
        }
    .col-sm-2 {
        flex: 0 0 auto;
        width: 16.666667%
        }
    .col-sm-3 {
        flex: 0 0 auto;
        width: 25%
        }
    .col-sm-4 {
        flex: 0 0 auto;
        width: 33.333333%
        }
    .col-sm-5 {
        flex: 0 0 auto;
        width: 41.666667%
        }
    .col-sm-6 {
        flex: 0 0 auto;
        width: 50%
        }
    .col-sm-7 {
        flex: 0 0 auto;
        width: 58.333333%
        }
    .col-sm-8 {
        flex: 0 0 auto;
        width: 66.666667%
        }
    .col-sm-9 {
        flex: 0 0 auto;
        width: 75%
        }
    .col-sm-10 {
        flex: 0 0 auto;
        width: 83.333333%
        }
    .col-sm-11 {
        flex: 0 0 auto;
        width: 91.666667%
        }
    .col-sm-12 {
        flex: 0 0 auto;
        width: 100%
        }
    .offset-sm-0 {
        margin-left: 0
        }
    .offset-sm-1 {
        margin-left: 8.333333%
        }
    .offset-sm-2 {
        margin-left: 16.666667%
        }
    .offset-sm-3 {
        margin-left: 25%
        }
    .offset-sm-4 {
        margin-left: 33.333333%
        }
    .offset-sm-5 {
        margin-left: 41.666667%
        }
    .offset-sm-6 {
        margin-left: 50%
        }
    .offset-sm-7 {
        margin-left: 58.333333%
        }
    .offset-sm-8 {
        margin-left: 66.666667%
        }
    .offset-sm-9 {
        margin-left: 75%
        }
    .offset-sm-10 {
        margin-left: 83.333333%
        }
    .offset-sm-11 {
        margin-left: 91.666667%
        }
    }
@media (min-width: 768px) {
    .col-md {
        flex: 1 0 0%
        }
    .row-cols-md-auto > * {
        flex: 0 0 auto;
        width: auto
        }
    .row-cols-md-1 > * {
        flex: 0 0 auto;
        width: 100%
        }
    .row-cols-md-2 > * {
        flex: 0 0 auto;
        width: 50%
        }
    .row-cols-md-3 > * {
        flex: 0 0 auto;
        width: 33.333333%
        }
    .row-cols-md-4 > * {
        flex: 0 0 auto;
        width: 25%
        }
    .row-cols-md-5 > * {
        flex: 0 0 auto;
        width: 20%
        }
    .row-cols-md-6 > * {
        flex: 0 0 auto;
        width: 16.666667%
        }
    .col-md-auto {
        flex: 0 0 auto;
        width: auto
        }
    .col-md-1 {
        flex: 0 0 auto;
        width: 8.333333%
        }
    .col-md-2 {
        flex: 0 0 auto;
        width: 16.666667%
        }
    .col-md-3 {
        flex: 0 0 auto;
        width: 25%
        }
    .col-md-4 {
        flex: 0 0 auto;
        width: 33.333333%
        }
    .col-md-5 {
        flex: 0 0 auto;
        width: 41.666667%
        }
    .col-md-6 {
        flex: 0 0 auto;
        width: 50%
        }
    .col-md-7 {
        flex: 0 0 auto;
        width: 58.333333%
        }
    .col-md-8 {
        flex: 0 0 auto;
        width: 66.666667%
        }
    .col-md-9 {
        flex: 0 0 auto;
        width: 75%
        }
    .col-md-10 {
        flex: 0 0 auto;
        width: 83.333333%
        }
    .col-md-11 {
        flex: 0 0 auto;
        width: 91.666667%
        }
    .col-md-12 {
        flex: 0 0 auto;
        width: 100%
        }
    .offset-md-0 {
        margin-left: 0
        }
    .offset-md-1 {
        margin-left: 8.333333%
        }
    .offset-md-2 {
        margin-left: 16.666667%
        }
    .offset-md-3 {
        margin-left: 25%
        }
    .offset-md-4 {
        margin-left: 33.333333%
        }
    .offset-md-5 {
        margin-left: 41.666667%
        }
    .offset-md-6 {
        margin-left: 50%
        }
    .offset-md-7 {
        margin-left: 58.333333%
        }
    .offset-md-8 {
        margin-left: 66.666667%
        }
    .offset-md-9 {
        margin-left: 75%
        }
    .offset-md-10 {
        margin-left: 83.333333%
        }
    .offset-md-11 {
        margin-left: 91.666667%
        }
    }
@media (min-width: 992px) {
    .col-lg {
        flex: 1 0 0%
        }
    .row-cols-lg-auto > * {
        flex: 0 0 auto;
        width: auto
        }
    .row-cols-lg-1 > * {
        flex: 0 0 auto;
        width: 100%
        }
    .row-cols-lg-2 > * {
        flex: 0 0 auto;
        width: 50%
        }
    .row-cols-lg-3 > * {
        flex: 0 0 auto;
        width: 33.333333%
        }
    .row-cols-lg-4 > * {
        flex: 0 0 auto;
        width: 25%
        }
    .row-cols-lg-5 > * {
        flex: 0 0 auto;
        width: 20%
        }
    .row-cols-lg-6 > * {
        flex: 0 0 auto;
        width: 16.666667%
        }
    .col-lg-auto {
        flex: 0 0 auto;
        width: auto
        }
    .col-lg-1 {
        flex: 0 0 auto;
        width: 8.333333%
        }
    .col-lg-2 {
        flex: 0 0 auto;
        width: 16.666667%
        }
    .col-lg-3 {
        flex: 0 0 auto;
        width: 25%
        }
    .col-lg-4 {
        flex: 0 0 auto;
        width: 33.333333%
        }
    .col-lg-5 {
        flex: 0 0 auto;
        width: 41.666667%
        }
    .col-lg-6 {
        flex: 0 0 auto;
        width: 50%
        }
    .col-lg-7 {
        flex: 0 0 auto;
        width: 58.333333%
        }
    .col-lg-8 {
        flex: 0 0 auto;
        width: 66.666667%
        }
    .col-lg-9 {
        flex: 0 0 auto;
        width: 75%
        }
    .col-lg-10 {
        flex: 0 0 auto;
        width: 83.333333%
        }
    .col-lg-11 {
        flex: 0 0 auto;
        width: 91.666667%
        }
    .col-lg-12 {
        flex: 0 0 auto;
        width: 100%
        }
    .offset-lg-0 {
        margin-left: 0
        }
    .offset-lg-1 {
        margin-left: 8.333333%
        }
    .offset-lg-2 {
        margin-left: 16.666667%
        }
    .offset-lg-3 {
        margin-left: 25%
        }
    .offset-lg-4 {
        margin-left: 33.333333%
        }
    .offset-lg-5 {
        margin-left: 41.666667%
        }
    .offset-lg-6 {
        margin-left: 50%
        }
    .offset-lg-7 {
        margin-left: 58.333333%
        }
    .offset-lg-8 {
        margin-left: 66.666667%
        }
    .offset-lg-9 {
        margin-left: 75%
        }
    .offset-lg-10 {
        margin-left: 83.333333%
        }
    .offset-lg-11 {
        margin-left: 91.666667%
        }
    }
@media (min-width: 1200px) {
    .col-xl {
        flex: 1 0 0%
        }
    .row-cols-xl-auto > * {
        flex: 0 0 auto;
        width: auto
        }
    .row-cols-xl-1 > * {
        flex: 0 0 auto;
        width: 100%
        }
    .row-cols-xl-2 > * {
        flex: 0 0 auto;
        width: 50%
        }
    .row-cols-xl-3 > * {
        flex: 0 0 auto;
        width: 33.333333%
        }
    .row-cols-xl-4 > * {
        flex: 0 0 auto;
        width: 25%
        }
    .row-cols-xl-5 > * {
        flex: 0 0 auto;
        width: 20%
        }
    .row-cols-xl-6 > * {
        flex: 0 0 auto;
        width: 16.666667%
        }
    .col-xl-auto {
        flex: 0 0 auto;
        width: auto
        }
    .col-xl-1 {
        flex: 0 0 auto;
        width: 8.333333%
        }
    .col-xl-2 {
        flex: 0 0 auto;
        width: 16.666667%
        }
    .col-xl-3 {
        flex: 0 0 auto;
        width: 25%
        }
    .col-xl-4 {
        flex: 0 0 auto;
        width: 33.333333%
        }
    .col-xl-5 {
        flex: 0 0 auto;
        width: 41.666667%
        }
    .col-xl-6 {
        flex: 0 0 auto;
        width: 50%
        }
    .col-xl-7 {
        flex: 0 0 auto;
        width: 58.333333%
        }
    .col-xl-8 {
        flex: 0 0 auto;
        width: 66.666667%
        }
    .col-xl-9 {
        flex: 0 0 auto;
        width: 75%
        }
    .col-xl-10 {
        flex: 0 0 auto;
        width: 83.333333%
        }
    .col-xl-11 {
        flex: 0 0 auto;
        width: 91.666667%
        }
    .col-xl-12 {
        flex: 0 0 auto;
        width: 100%
        }
    .offset-xl-0 {
        margin-left: 0
        }
    .offset-xl-1 {
        margin-left: 8.333333%
        }
    .offset-xl-2 {
        margin-left: 16.666667%
        }
    .offset-xl-3 {
        margin-left: 25%
        }
    .offset-xl-4 {
        margin-left: 33.333333%
        }
    .offset-xl-5 {
        margin-left: 41.666667%
        }
    .offset-xl-6 {
        margin-left: 50%
        }
    .offset-xl-7 {
        margin-left: 58.333333%
        }
    .offset-xl-8 {
        margin-left: 66.666667%
        }
    .offset-xl-9 {
        margin-left: 75%
        }
    .offset-xl-10 {
        margin-left: 83.333333%
        }
    .offset-xl-11 {
        margin-left: 91.666667%
        }
    }
@media (min-width: 1400px) {
    .col-xxl {
        flex: 1 0 0%
        }
    .row-cols-xxl-auto > * {
        flex: 0 0 auto;
        width: auto
        }
    .row-cols-xxl-1 > * {
        flex: 0 0 auto;
        width: 100%
        }
    .row-cols-xxl-2 > * {
        flex: 0 0 auto;
        width: 50%
        }
    .row-cols-xxl-3 > * {
        flex: 0 0 auto;
        width: 33.333333%
        }
    .row-cols-xxl-4 > * {
        flex: 0 0 auto;
        width: 25%
        }
    .row-cols-xxl-5 > * {
        flex: 0 0 auto;
        width: 20%
        }
    .row-cols-xxl-6 > * {
        flex: 0 0 auto;
        width: 16.666667%
        }
    .col-xxl-auto {
        flex: 0 0 auto;
        width: auto
        }
    .col-xxl-1 {
        flex: 0 0 auto;
        width: 8.333333%
        }
    .col-xxl-2 {
        flex: 0 0 auto;
        width: 16.666667%
        }
    .col-xxl-3 {
        flex: 0 0 auto;
        width: 25%
        }
    .col-xxl-4 {
        flex: 0 0 auto;
        width: 33.333333%
        }
    .col-xxl-5 {
        flex: 0 0 auto;
        width: 41.666667%
        }
    .col-xxl-6 {
        flex: 0 0 auto;
        width: 50%
        }
    .col-xxl-7 {
        flex: 0 0 auto;
        width: 58.333333%
        }
    .col-xxl-8 {
        flex: 0 0 auto;
        width: 66.666667%
        }
    .col-xxl-9 {
        flex: 0 0 auto;
        width: 75%
        }
    .col-xxl-10 {
        flex: 0 0 auto;
        width: 83.333333%
        }
    .col-xxl-11 {
        flex: 0 0 auto;
        width: 91.666667%
        }
    .col-xxl-12 {
        flex: 0 0 auto;
        width: 100%
        }
    .offset-xxl-0 {
        margin-left: 0
        }
    .offset-xxl-1 {
        margin-left: 8.333333%
        }
    .offset-xxl-2 {
        margin-left: 16.666667%
        }
    .offset-xxl-3 {
        margin-left: 25%
        }
    .offset-xxl-4 {
        margin-left: 33.333333%
        }
    .offset-xxl-5 {
        margin-left: 41.666667%
        }
    .offset-xxl-6 {
        margin-left: 50%
        }
    .offset-xxl-7 {
        margin-left: 58.333333%
        }
    .offset-xxl-8 {
        margin-left: 66.666667%
        }
    .offset-xxl-9 {
        margin-left: 75%
        }
    .offset-xxl-10 {
        margin-left: 83.333333%
        }
    .offset-xxl-11 {
        margin-left: 91.666667%
        }
    }
.table > :not(caption) > * > * {padding:0.5rem 0.5rem;border-bottom-width:1px}
.table-sm > :not(caption) > * > * {padding:0.25rem 0.25rem}
.table-bordered > :not(caption) > * {border-width:1px 0}
.table-bordered > :not(caption) > * > * {border-width:0 1px}
.table-borderless > :not(caption) > * > * {border-bottom-width:0}
.table-borderless > :not(:first-child) {border-top-width:0}
.table-striped > tbody > tr:nth-of-type(odd) > * {}
.table-striped-columns > :not(caption) > tr > :nth-child(2n) {}
.table-hover > tbody > tr:hover > * {}
@media (max-width: 575.98px) {
    .table-responsive-sm {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch
        }
    }
@media (max-width: 767.98px) {
    .table-responsive-md {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch
        }
    }
@media (max-width: 991.98px) {
    .table-responsive-lg {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch
        }
    }
@media (max-width: 1199.98px) {
    .table-responsive-xl {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch
        }
    }
@media (max-width: 1399.98px) {
    .table-responsive-xxl {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch
        }
    }
@media (prefers-reduced-motion: reduce) {
    .form-control {
        transition: none
        }
    }
.form-control[type=file]:not(:disabled):not([readonly]) {cursor:pointer}
.form-control:focus {color:#212529;background-color:#fff;border-color:#86b7fe;outline:0;box-shadow:0 0 0 0.25rem rgba(13, 110, 253, 0.25)}
.form-control::-webkit-date-and-time-value {height:1.5em}
.form-control::-moz-placeholder {color:#6c757d;opacity:1}
.form-control::placeholder {color:#6c757d;opacity:1}
.form-control:disabled {background-color:#e9ecef;opacity:1}
.form-control::-webkit-file-upload-button {padding:0.375rem 0.75rem;margin:-0.375rem -0.75rem;-webkit-margin-end:0.75rem;margin-inline-end:0.75rem;color:#212529;background-color:#e9ecef;pointer-events:none;border-color:inherit;border-style:solid;border-width:0;border-inline-end-width:1px;border-radius:0;-webkit-transition:color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;transition:color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out}
.form-control::file-selector-button {padding:0.375rem 0.75rem;margin:-0.375rem -0.75rem;-webkit-margin-end:0.75rem;margin-inline-end:0.75rem;color:#212529;background-color:#e9ecef;pointer-events:none;border-color:inherit;border-style:solid;border-width:0;border-inline-end-width:1px;border-radius:0;transition:color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out}
@media (prefers-reduced-motion: reduce) {
    .form-control::-webkit-file-upload-button {
        -webkit-transition: none;
        transition: none
        }
    .form-control::file-selector-button {
        transition: none
        }
    }
.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {background-color:#dde0e3}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {background-color:#dde0e3}
.form-control-plaintext:focus {outline:0}
.form-control-sm::-webkit-file-upload-button {padding:0.25rem 0.5rem;margin:-0.25rem -0.5rem;-webkit-margin-end:0.5rem;margin-inline-end:0.5rem}
.form-control-sm::file-selector-button {padding:0.25rem 0.5rem;margin:-0.25rem -0.5rem;-webkit-margin-end:0.5rem;margin-inline-end:0.5rem}
.form-control-lg::-webkit-file-upload-button {padding:0.5rem 1rem;margin:-0.5rem -1rem;-webkit-margin-end:1rem;margin-inline-end:1rem}
.form-control-lg::file-selector-button {padding:0.5rem 1rem;margin:-0.5rem -1rem;-webkit-margin-end:1rem;margin-inline-end:1rem}
.form-control-color:not(:disabled):not([readonly]) {cursor:pointer}
.form-control-color::-moz-color-swatch {border-radius:0.375rem;border:0}
.form-control-color::-webkit-color-swatch {border-radius:0.375rem}
@media (prefers-reduced-motion: reduce) {
    .form-select {
        transition: none
        }
    }
.form-select:focus {border-color:#86b7fe;outline:0;box-shadow:0 0 0 0.25rem rgba(13, 110, 253, 0.25)}
.form-select[size]:not([size="1"]) {padding-right:0.75rem;background-image:none}
.form-select:disabled {background-color:#e9ecef}
.form-select:-moz-focusring {color:transparent;text-shadow:0 0 0 #212529}
.form-check-input:active {filter:brightness(90%)}
.form-check-input:focus {border-color:#86b7fe;outline:0;box-shadow:0 0 0 0.25rem rgba(13, 110, 253, 0.25)}
.form-check-input:checked {background-color:#0d6efd;border-color:#0d6efd}
.form-check-input:checked[type=checkbox] {background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e")}
.form-check-input:checked[type=radio] {background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e")}
.form-check-input[type=checkbox]:indeterminate {background-color:#0d6efd;border-color:#0d6efd;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e")}
.form-check-input:disabled {pointer-events:none;filter:none;opacity:0.5}
.form-check-input:disabled ~ .form-check-label {cursor:default;opacity:0.5}
@media (prefers-reduced-motion: reduce) {
    .form-switch .form-check-input {
        transition: none
        }
    }
.form-switch .form-check-input:focus {background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2386b7fe'/%3e%3c/svg%3e")}
.form-switch .form-check-input:checked {background-position:right center;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e")}
.btn-check:disabled + .btn {pointer-events:none;filter:none;opacity:0.65}
.form-range:focus {outline:0}
.form-range:focus::-webkit-slider-thumb {box-shadow:0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25)}
.form-range:focus::-moz-range-thumb {box-shadow:0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25)}
.form-range::-moz-focus-outer {border:0}
.form-range::-webkit-slider-thumb {width:1rem;height:1rem;margin-top:-0.25rem;background-color:#0d6efd;border:0;border-radius:1rem;-webkit-transition:background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;transition:background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;-webkit-appearance:none;appearance:none}
@media (prefers-reduced-motion: reduce) {
    .form-range::-webkit-slider-thumb {
        -webkit-transition: none;
        transition: none
        }
    }
.form-range::-webkit-slider-runnable-track {width:100%;height:0.5rem;color:transparent;cursor:pointer;background-color:#dee2e6;border-color:transparent;border-radius:1rem}
.form-range::-moz-range-thumb {width:1rem;height:1rem;background-color:#0d6efd;border:0;border-radius:1rem;-moz-transition:background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;transition:background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;-moz-appearance:none;appearance:none}
@media (prefers-reduced-motion: reduce) {
    .form-range::-moz-range-thumb {
        -moz-transition: none;
        transition: none
        }
    }
.form-range::-moz-range-track {width:100%;height:0.5rem;color:transparent;cursor:pointer;background-color:#dee2e6;border-color:transparent;border-radius:1rem}
.form-range:disabled {pointer-events:none}
.form-range:disabled::-webkit-slider-thumb {background-color:#adb5bd}
.form-range:disabled::-moz-range-thumb {background-color:#adb5bd}
@media (prefers-reduced-motion: reduce) {
    .form-floating > label {
        transition: none
        }
    }
.form-floating > .form-control-plaintext::-moz-placeholder {color:transparent}
.form-floating > .form-control::-moz-placeholder {color:transparent}
.form-floating > .form-control-plaintext::placeholder {color:transparent}
.form-floating > .form-control::placeholder {color:transparent}
.form-floating > .form-control-plaintext:not(:-moz-placeholder-shown) {padding-top:1.625rem;padding-bottom:0.625rem}
.form-floating > .form-control:not(:-moz-placeholder-shown) {padding-top:1.625rem;padding-bottom:0.625rem}
.form-floating > .form-control-plaintext:focus {padding-top:1.625rem;padding-bottom:0.625rem}
.form-floating > .form-control-plaintext:not(:placeholder-shown) {padding-top:1.625rem;padding-bottom:0.625rem}
.form-floating > .form-control:focus {padding-top:1.625rem;padding-bottom:0.625rem}
.form-floating > .form-control:not(:placeholder-shown) {padding-top:1.625rem;padding-bottom:0.625rem}
.form-floating > .form-control-plaintext:-webkit-autofill {padding-top:1.625rem;padding-bottom:0.625rem}
.form-floating > .form-control:-webkit-autofill {padding-top:1.625rem;padding-bottom:0.625rem}
.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {opacity:0.65;transform:scale(0.85) translatey(-0.5rem) translatex(0.15rem)}
.form-floating > .form-control:focus ~ label {opacity:0.65;transform:scale(0.85) translatey(-0.5rem) translatex(0.15rem)}
.form-floating > .form-control:not(:placeholder-shown) ~ label {opacity:0.65;transform:scale(0.85) translatey(-0.5rem) translatex(0.15rem)}
.form-floating > .form-control:-webkit-autofill ~ label {opacity:0.65;transform:scale(0.85) translatey(-0.5rem) translatex(0.15rem)}
.input-group > .form-control:focus {z-index:3}
.input-group > .form-floating:focus-within {z-index:3}
.input-group > .form-select:focus {z-index:3}
.input-group .btn:focus {z-index:3}
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3) {border-top-right-radius:0;border-bottom-right-radius:0}
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control {border-top-right-radius:0;border-bottom-right-radius:0}
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select {border-top-right-radius:0;border-bottom-right-radius:0}
.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {border-top-right-radius:0;border-bottom-right-radius:0}
.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4) {border-top-right-radius:0;border-bottom-right-radius:0}
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-control {border-top-right-radius:0;border-bottom-right-radius:0}
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-select {border-top-right-radius:0;border-bottom-right-radius:0}
.input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {border-top-right-radius:0;border-bottom-right-radius:0}
.input-group > .form-floating:not(:first-child) > .form-control {margin-left:-1px;border-top-left-radius:0;border-bottom-left-radius:0}
.input-group > .form-floating:not(:first-child) > .form-select {margin-left:-1px;border-top-left-radius:0;border-bottom-left-radius:0}
.input-group > :not(:first-child):not(.dropdown-menu):not(.form-floating):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {margin-left:-1px;border-top-left-radius:0;border-bottom-left-radius:0}
.was-validated :valid ~ .valid-feedback {display:block}
.was-validated :valid ~ .valid-tooltip {display:block}
.was-validated .form-control:valid {border-color:#198754;padding-right:calc(1.5em + 0.75rem);background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right calc(0.375em + 0.1875rem) center;background-size:calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)}
.form-control.is-valid:focus {border-color:#198754;box-shadow:0 0 0 0.25rem rgba(25, 135, 84, 0.25)}
.was-validated .form-control:valid:focus {border-color:#198754;box-shadow:0 0 0 0.25rem rgba(25, 135, 84, 0.25)}
.was-validated textarea.form-control:valid {padding-right:calc(1.5em + 0.75rem);background-position:top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem)}
.was-validated .form-select:valid {border-color:#198754}
.form-select.is-valid:not([multiple]):not([size]) {padding-right:4.125rem;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");background-position:right 0.75rem center, center right 2.25rem;background-size:16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)}
.form-select.is-valid:not([multiple])[size="1"] {padding-right:4.125rem;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");background-position:right 0.75rem center, center right 2.25rem;background-size:16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)}
.was-validated .form-select:valid:not([multiple]):not([size]) {padding-right:4.125rem;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");background-position:right 0.75rem center, center right 2.25rem;background-size:16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)}
.was-validated .form-select:valid:not([multiple])[size="1"] {padding-right:4.125rem;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");background-position:right 0.75rem center, center right 2.25rem;background-size:16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)}
.form-select.is-valid:focus {border-color:#198754;box-shadow:0 0 0 0.25rem rgba(25, 135, 84, 0.25)}
.was-validated .form-select:valid:focus {border-color:#198754;box-shadow:0 0 0 0.25rem rgba(25, 135, 84, 0.25)}
.was-validated .form-control-color:valid {}
.was-validated .form-check-input:valid {border-color:#198754}
.form-check-input.is-valid:checked {background-color:#198754}
.was-validated .form-check-input:valid:checked {background-color:#198754}
.form-check-input.is-valid:focus {box-shadow:0 0 0 0.25rem rgba(25, 135, 84, 0.25)}
.was-validated .form-check-input:valid:focus {box-shadow:0 0 0 0.25rem rgba(25, 135, 84, 0.25)}
.was-validated .form-check-input:valid ~ .form-check-label {color:#198754}
.was-validated .input-group .form-control:valid {z-index:1}
.was-validated .input-group .form-select:valid {z-index:1}
.input-group .form-control.is-valid:focus {z-index:3}
.input-group .form-select.is-valid:focus {z-index:3}
.was-validated .input-group .form-control:valid:focus {z-index:3}
.was-validated .input-group .form-select:valid:focus {z-index:3}
.was-validated :invalid ~ .invalid-feedback {display:block}
.was-validated :invalid ~ .invalid-tooltip {display:block}
.was-validated .form-control:invalid {border-color:#dc3545;padding-right:calc(1.5em + 0.75rem);background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right calc(0.375em + 0.1875rem) center;background-size:calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)}
.form-control.is-invalid:focus {border-color:#dc3545;box-shadow:0 0 0 0.25rem rgba(220, 53, 69, 0.25)}
.was-validated .form-control:invalid:focus {border-color:#dc3545;box-shadow:0 0 0 0.25rem rgba(220, 53, 69, 0.25)}
.was-validated textarea.form-control:invalid {padding-right:calc(1.5em + 0.75rem);background-position:top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem)}
.was-validated .form-select:invalid {border-color:#dc3545}
.form-select.is-invalid:not([multiple]):not([size]) {padding-right:4.125rem;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");background-position:right 0.75rem center, center right 2.25rem;background-size:16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)}
.form-select.is-invalid:not([multiple])[size="1"] {padding-right:4.125rem;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");background-position:right 0.75rem center, center right 2.25rem;background-size:16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)}
.was-validated .form-select:invalid:not([multiple]):not([size]) {padding-right:4.125rem;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");background-position:right 0.75rem center, center right 2.25rem;background-size:16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)}
.was-validated .form-select:invalid:not([multiple])[size="1"] {padding-right:4.125rem;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");background-position:right 0.75rem center, center right 2.25rem;background-size:16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)}
.form-select.is-invalid:focus {border-color:#dc3545;box-shadow:0 0 0 0.25rem rgba(220, 53, 69, 0.25)}
.was-validated .form-select:invalid:focus {border-color:#dc3545;box-shadow:0 0 0 0.25rem rgba(220, 53, 69, 0.25)}
.was-validated .form-control-color:invalid {}
.was-validated .form-check-input:invalid {border-color:#dc3545}
.form-check-input.is-invalid:checked {background-color:#dc3545}
.was-validated .form-check-input:invalid:checked {background-color:#dc3545}
.form-check-input.is-invalid:focus {box-shadow:0 0 0 0.25rem rgba(220, 53, 69, 0.25)}
.was-validated .form-check-input:invalid:focus {box-shadow:0 0 0 0.25rem rgba(220, 53, 69, 0.25)}
.was-validated .form-check-input:invalid ~ .form-check-label {color:#dc3545}
.was-validated .input-group .form-control:invalid {z-index:2}
.was-validated .input-group .form-select:invalid {z-index:2}
.input-group .form-control.is-invalid:focus {z-index:3}
.input-group .form-select.is-invalid:focus {z-index:3}
.was-validated .input-group .form-control:invalid:focus {z-index:3}
.was-validated .input-group .form-select:invalid:focus {z-index:3}
@media (prefers-reduced-motion: reduce) {
    .btn {
        transition: none
        }
    }
.btn:hover {}
.btn-check:focus + .btn {outline:0}
.btn:focus {outline:0}
.btn-check:active + .btn {}
.btn-check:checked + .btn {}
.btn:active {}
.btn-check:active + .btn:focus {}
.btn-check:checked + .btn:focus {}
.btn.active:focus {}
.btn.show:focus {}
.btn:active:focus {}
.btn:disabled {pointer-events:none}
fieldset:disabled .btn {pointer-events:none}
.btn-link:focus {}
.btn-link:hover {}
@media (prefers-reduced-motion: reduce) {
    .fade {
        transition: none
        }
    }
.fade:not(.show) {opacity:0}
.collapse:not(.show) {display:none}
@media (prefers-reduced-motion: reduce) {
    .collapsing {
        transition: none
        }
    }
@media (prefers-reduced-motion: reduce) {
    .collapsing.collapse-horizontal {
        transition: none
        }
    }
.dropdown-toggle::after {display:inline-block;margin-left:0.255em;vertical-align:0.255em;content:"";border-top:0.3em solid;border-right:0.3em solid transparent;border-bottom:0;border-left:0.3em solid transparent}
.dropdown-toggle:empty::after {margin-left:0}
@media (min-width: 576px) {
    .dropdown-menu-sm-start[data-bs-popper] {
        right: auto;
        left: 0
        }
    .dropdown-menu-sm-end[data-bs-popper] {
        right: 0;
        left: auto
        }
    }
@media (min-width: 768px) {
    .dropdown-menu-md-start[data-bs-popper] {
        right: auto;
        left: 0
        }
    .dropdown-menu-md-end[data-bs-popper] {
        right: 0;
        left: auto
        }
    }
@media (min-width: 992px) {
    .dropdown-menu-lg-start[data-bs-popper] {
        right: auto;
        left: 0
        }
    .dropdown-menu-lg-end[data-bs-popper] {
        right: 0;
        left: auto
        }
    }
@media (min-width: 1200px) {
    .dropdown-menu-xl-start[data-bs-popper] {
        right: auto;
        left: 0
        }
    .dropdown-menu-xl-end[data-bs-popper] {
        right: 0;
        left: auto
        }
    }
@media (min-width: 1400px) {
    .dropdown-menu-xxl-start[data-bs-popper] {
        right: auto;
        left: 0
        }
    .dropdown-menu-xxl-end[data-bs-popper] {
        right: 0;
        left: auto
        }
    }
.dropup .dropdown-toggle::after {display:inline-block;margin-left:0.255em;vertical-align:0.255em;content:"";border-top:0;border-right:0.3em solid transparent;border-bottom:0.3em solid;border-left:0.3em solid transparent}
.dropup .dropdown-toggle:empty::after {margin-left:0}
.dropend .dropdown-toggle::after {display:inline-block;margin-left:0.255em;vertical-align:0.255em;content:"";border-top:0.3em solid transparent;border-right:0;border-bottom:0.3em solid transparent;border-left:0.3em solid}
.dropend .dropdown-toggle:empty::after {margin-left:0}
.dropend .dropdown-toggle::after {vertical-align:0}
.dropstart .dropdown-toggle::after {display:inline-block;margin-left:0.255em;vertical-align:0.255em;content:""}
.dropstart .dropdown-toggle::after {display:none}
.dropstart .dropdown-toggle::before {display:inline-block;margin-right:0.255em;vertical-align:0.255em;content:"";border-top:0.3em solid transparent;border-right:0.3em solid;border-bottom:0.3em solid transparent}
.dropstart .dropdown-toggle:empty::after {margin-left:0}
.dropstart .dropdown-toggle::before {vertical-align:0}
.dropdown-item:focus {}
.dropdown-item:hover {}
.dropdown-item:active {text-decoration:none}
.dropdown-item:disabled {pointer-events:none;background-color:transparent}
.btn-group-vertical > .btn-check:checked + .btn {z-index:1}
.btn-group-vertical > .btn-check:focus + .btn {z-index:1}
.btn-group-vertical > .btn:active {z-index:1}
.btn-group-vertical > .btn:focus {z-index:1}
.btn-group-vertical > .btn:hover {z-index:1}
.btn-group > .btn-check:checked + .btn {z-index:1}
.btn-group > .btn-check:focus + .btn {z-index:1}
.btn-group > .btn:active {z-index:1}
.btn-group > .btn:focus {z-index:1}
.btn-group > .btn:hover {z-index:1}
.btn-group > .btn-group:not(:first-child) {margin-left:-1px}
.btn-group > .btn:not(:first-child) {margin-left:-1px}
.btn-group > .btn-group:not(:last-child) > .btn {border-top-right-radius:0;border-bottom-right-radius:0}
.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {border-top-right-radius:0;border-bottom-right-radius:0}
.btn-group > .btn-group:not(:first-child) > .btn {border-top-left-radius:0;border-bottom-left-radius:0}
.btn-group > .btn:nth-child(n+3) {border-top-left-radius:0;border-bottom-left-radius:0}
.btn-group > :not(.btn-check) + .btn {border-top-left-radius:0;border-bottom-left-radius:0}
.dropdown-toggle-split::after {margin-left:0}
.dropend .dropdown-toggle-split::after {margin-left:0}
.dropup .dropdown-toggle-split::after {margin-left:0}
.dropstart .dropdown-toggle-split::before {margin-right:0}
.btn-group-vertical > .btn-group:not(:first-child) {margin-top:-1px}
.btn-group-vertical > .btn:not(:first-child) {margin-top:-1px}
.btn-group-vertical > .btn-group:not(:last-child) > .btn {border-bottom-right-radius:0;border-bottom-left-radius:0}
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle) {border-bottom-right-radius:0;border-bottom-left-radius:0}
.btn-group-vertical > .btn-group:not(:first-child) > .btn {border-top-left-radius:0;border-top-right-radius:0}
@media (prefers-reduced-motion: reduce) {
    .nav-link {
        transition: none
        }
    }
.nav-link:focus {}
.nav-link:hover {}
.nav-tabs .nav-link:focus {isolation:isolate}
.nav-tabs .nav-link:hover {isolation:isolate}
.nav-tabs .nav-link:disabled {background-color:transparent;border-color:transparent}
.nav-pills .nav-link:disabled {background-color:transparent;border-color:transparent}
.navbar-brand:focus {}
.navbar-brand:hover {}
.navbar-text a:focus {}
.navbar-text a:hover {}
@media (prefers-reduced-motion: reduce) {
    .navbar-toggler {
        transition: none
        }
    }
.navbar-toggler:hover {text-decoration:none}
.navbar-toggler:focus {text-decoration:none;outline:0}
@media (min-width: 576px) {
    .navbar-expand-sm {
        flex-wrap: nowrap;
        justify-content: flex-start
        }
    .navbar-expand-sm .navbar-nav {
        flex-direction: row
        }
    .navbar-expand-sm .navbar-nav .dropdown-menu {
        position: absolute
        }
    .navbar-expand-sm .navbar-nav-scroll {
        overflow: visible
        }
    .navbar-expand-sm .navbar-collapse {
        display: flex;
        flex-basis: auto
        }
    .navbar-expand-sm .navbar-toggler {
        display: none
        }
    .navbar-expand-sm .offcanvas {
        position: static;
        z-index: auto;
        flex-grow: 1;
        width: auto;
        height: auto;
        visibility: visible;
        background-color: transparent;
        border: 0;
        transform: none;
        transition: none
        }
    .navbar-expand-sm .offcanvas .offcanvas-header {
        display: none
        }
    .navbar-expand-sm .offcanvas .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible
        }
    }
@media (min-width: 768px) {
    .navbar-expand-md {
        flex-wrap: nowrap;
        justify-content: flex-start
        }
    .navbar-expand-md .navbar-nav {
        flex-direction: row
        }
    .navbar-expand-md .navbar-nav .dropdown-menu {
        position: absolute
        }
    .navbar-expand-md .navbar-nav-scroll {
        overflow: visible
        }
    .navbar-expand-md .navbar-collapse {
        display: flex;
        flex-basis: auto
        }
    .navbar-expand-md .navbar-toggler {
        display: none
        }
    .navbar-expand-md .offcanvas {
        position: static;
        z-index: auto;
        flex-grow: 1;
        width: auto;
        height: auto;
        visibility: visible;
        background-color: transparent;
        border: 0;
        transform: none;
        transition: none
        }
    .navbar-expand-md .offcanvas .offcanvas-header {
        display: none
        }
    .navbar-expand-md .offcanvas .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible
        }
    }
@media (min-width: 992px) {
    .navbar-expand-lg {
        flex-wrap: nowrap;
        justify-content: flex-start
        }
    .navbar-expand-lg .navbar-nav {
        flex-direction: row
        }
    .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute
        }
    .navbar-expand-lg .navbar-nav-scroll {
        overflow: visible
        }
    .navbar-expand-lg .navbar-collapse {
        display: flex;
        flex-basis: auto
        }
    .navbar-expand-lg .navbar-toggler {
        display: none
        }
    .navbar-expand-lg .offcanvas {
        position: static;
        z-index: auto;
        flex-grow: 1;
        width: auto;
        height: auto;
        visibility: visible;
        background-color: transparent;
        border: 0;
        transform: none;
        transition: none
        }
    .navbar-expand-lg .offcanvas .offcanvas-header {
        display: none
        }
    .navbar-expand-lg .offcanvas .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible
        }
    }
@media (min-width: 1200px) {
    .navbar-expand-xl {
        flex-wrap: nowrap;
        justify-content: flex-start
        }
    .navbar-expand-xl .navbar-nav {
        flex-direction: row
        }
    .navbar-expand-xl .navbar-nav .dropdown-menu {
        position: absolute
        }
    .navbar-expand-xl .navbar-nav-scroll {
        overflow: visible
        }
    .navbar-expand-xl .navbar-collapse {
        display: flex;
        flex-basis: auto
        }
    .navbar-expand-xl .navbar-toggler {
        display: none
        }
    .navbar-expand-xl .offcanvas {
        position: static;
        z-index: auto;
        flex-grow: 1;
        width: auto;
        height: auto;
        visibility: visible;
        background-color: transparent;
        border: 0;
        transform: none;
        transition: none
        }
    .navbar-expand-xl .offcanvas .offcanvas-header {
        display: none
        }
    .navbar-expand-xl .offcanvas .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible
        }
    }
@media (min-width: 1400px) {
    .navbar-expand-xxl {
        flex-wrap: nowrap;
        justify-content: flex-start
        }
    .navbar-expand-xxl .navbar-nav {
        flex-direction: row
        }
    .navbar-expand-xxl .navbar-nav .dropdown-menu {
        position: absolute
        }
    .navbar-expand-xxl .navbar-nav-scroll {
        overflow: visible
        }
    .navbar-expand-xxl .navbar-collapse {
        display: flex;
        flex-basis: auto
        }
    .navbar-expand-xxl .navbar-toggler {
        display: none
        }
    .navbar-expand-xxl .offcanvas {
        position: static;
        z-index: auto;
        flex-grow: 1;
        width: auto;
        height: auto;
        visibility: visible;
        background-color: transparent;
        border: 0;
        transform: none;
        transition: none
        }
    .navbar-expand-xxl .offcanvas .offcanvas-header {
        display: none
        }
    .navbar-expand-xxl .offcanvas .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible
        }
    }
@media (min-width: 576px) {
    .card-group {
        display: flex;
        flex-flow: row wrap
        }
    .card-group > .card {
        flex: 1 0 0%;
        margin-bottom: 0
        }
    .card-group > .card + .card {
        margin-left: 0;
        border-left: 0
        }
    .card-group > .card:not(:last-child) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0
        }
    .card-group > .card:not(:last-child) .card-header, .card-group > .card:not(:last-child) .card-img-top {
        border-top-right-radius: 0
        }
    .card-group > .card:not(:last-child) .card-footer, .card-group > .card:not(:last-child) .card-img-bottom {
        border-bottom-right-radius: 0
        }
    .card-group > .card:not(:first-child) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0
        }
    .card-group > .card:not(:first-child) .card-header, .card-group > .card:not(:first-child) .card-img-top {
        border-top-left-radius: 0
        }
    .card-group > .card:not(:first-child) .card-footer, .card-group > .card:not(:first-child) .card-img-bottom {
        border-bottom-left-radius: 0
        }
    }
@media (prefers-reduced-motion: reduce) {
    .accordion-button {
        transition: none
        }
    }
.accordion-button:not(.collapsed) {}
.accordion-button:not(.collapsed)::after {}
.accordion-button::after {flex-shrink:0;margin-left:auto;content:"";background-repeat:no-repeat}
@media (prefers-reduced-motion: reduce) {
    .accordion-button::after {
        transition: none
        }
    }
.accordion-button:hover {z-index:2}
.accordion-button:focus {z-index:3;outline:0}
.accordion-item:first-of-type {}
.accordion-item:first-of-type .accordion-button {}
.accordion-item:not(:first-of-type) {border-top:0}
.accordion-item:last-of-type {}
.accordion-item:last-of-type .accordion-button.collapsed {}
.accordion-item:last-of-type .accordion-collapse {}
.breadcrumb-item + .breadcrumb-item::before {float:left}
@media (prefers-reduced-motion: reduce) {
    .page-link {
        transition: none
        }
    }
.page-link:hover {z-index:2}
.page-link:focus {z-index:3;outline:0}
.page-item:not(:first-child) .page-link {margin-left:-1px}
.page-item:first-child .page-link {}
.page-item:last-child .page-link {}
.badge:empty {display:none}
@media (prefers-reduced-motion: reduce) {
    .progress-bar {
        transition: none
        }
    }
@media (prefers-reduced-motion: reduce) {
    .progress-bar-animated {
        -webkit-animation: none;
        animation: none
        }
    }
.list-group-numbered > .list-group-item::before {content:counters(section, ".") ". ";counter-increment:section}
.list-group-item-action:focus {z-index:1;text-decoration:none}
.list-group-item-action:hover {z-index:1;text-decoration:none}
.list-group-item-action:active {}
.list-group-item:disabled {pointer-events:none}
@media (min-width: 576px) {
    .list-group-horizontal-sm {
        flex-direction: row
        }
    .list-group-horizontal-sm > .list-group-item:first-child {
        border-top-right-radius: 0
        }
    .list-group-horizontal-sm > .list-group-item:last-child {
        border-bottom-left-radius: 0
        }
    .list-group-horizontal-sm > .list-group-item.active {
        margin-top: 0
        }
    .list-group-horizontal-sm > .list-group-item + .list-group-item {
        border-left-width: 0
        }
    }
@media (min-width: 768px) {
    .list-group-horizontal-md {
        flex-direction: row
        }
    .list-group-horizontal-md > .list-group-item:first-child {
        border-top-right-radius: 0
        }
    .list-group-horizontal-md > .list-group-item:last-child {
        border-bottom-left-radius: 0
        }
    .list-group-horizontal-md > .list-group-item.active {
        margin-top: 0
        }
    .list-group-horizontal-md > .list-group-item + .list-group-item {
        border-left-width: 0
        }
    }
@media (min-width: 992px) {
    .list-group-horizontal-lg {
        flex-direction: row
        }
    .list-group-horizontal-lg > .list-group-item:first-child {
        border-top-right-radius: 0
        }
    .list-group-horizontal-lg > .list-group-item:last-child {
        border-bottom-left-radius: 0
        }
    .list-group-horizontal-lg > .list-group-item.active {
        margin-top: 0
        }
    .list-group-horizontal-lg > .list-group-item + .list-group-item {
        border-left-width: 0
        }
    }
@media (min-width: 1200px) {
    .list-group-horizontal-xl {
        flex-direction: row
        }
    .list-group-horizontal-xl > .list-group-item:first-child {
        border-top-right-radius: 0
        }
    .list-group-horizontal-xl > .list-group-item:last-child {
        border-bottom-left-radius: 0
        }
    .list-group-horizontal-xl > .list-group-item.active {
        margin-top: 0
        }
    .list-group-horizontal-xl > .list-group-item + .list-group-item {
        border-left-width: 0
        }
    }
@media (min-width: 1400px) {
    .list-group-horizontal-xxl {
        flex-direction: row
        }
    .list-group-horizontal-xxl > .list-group-item:first-child {
        border-top-right-radius: 0
        }
    .list-group-horizontal-xxl > .list-group-item:last-child {
        border-bottom-left-radius: 0
        }
    .list-group-horizontal-xxl > .list-group-item.active {
        margin-top: 0
        }
    .list-group-horizontal-xxl > .list-group-item + .list-group-item {
        border-left-width: 0
        }
    }
.list-group-item-primary.list-group-item-action:focus {color:#084298;background-color:#bacbe6}
.list-group-item-primary.list-group-item-action:hover {color:#084298;background-color:#bacbe6}
.list-group-item-secondary.list-group-item-action:focus {color:#41464b;background-color:#cbccce}
.list-group-item-secondary.list-group-item-action:hover {color:#41464b;background-color:#cbccce}
.list-group-item-success.list-group-item-action:focus {color:#0f5132;background-color:#bcd0c7}
.list-group-item-success.list-group-item-action:hover {color:#0f5132;background-color:#bcd0c7}
.list-group-item-info.list-group-item-action:focus {color:#055160;background-color:#badce3}
.list-group-item-info.list-group-item-action:hover {color:#055160;background-color:#badce3}
.list-group-item-warning.list-group-item-action:focus {color:#664d03;background-color:#e6dbb9}
.list-group-item-warning.list-group-item-action:hover {color:#664d03;background-color:#e6dbb9}
.list-group-item-danger.list-group-item-action:focus {color:#842029;background-color:#dfc2c4}
.list-group-item-danger.list-group-item-action:hover {color:#842029;background-color:#dfc2c4}
.list-group-item-light.list-group-item-action:focus {color:#636464;background-color:#e5e5e5}
.list-group-item-light.list-group-item-action:hover {color:#636464;background-color:#e5e5e5}
.list-group-item-dark.list-group-item-action:focus {color:#141619;background-color:#bebebf}
.list-group-item-dark.list-group-item-action:hover {color:#141619;background-color:#bebebf}
.btn-close:hover {color:#000;text-decoration:none;opacity:0.75}
.btn-close:focus {outline:0;box-shadow:0 0 0 0.25rem rgba(13, 110, 253, 0.25);opacity:1}
.btn-close:disabled {pointer-events:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;opacity:0.25}
.toast:not(.show) {display:none}
.toast-container > :not(:last-child) {}
@media (prefers-reduced-motion: reduce) {
    .modal.fade .modal-dialog {
        transition: none
        }
    }
@media (min-width: 576px) {
    .modal-dialog {
        margin-right: auto;
        margin-left: auto
        }
    }


@media (max-width: 575.98px) {
    .modal-fullscreen-sm-down {
        width: 100vw;
        max-width: none;
        height: 100%;
        margin: 0
        }
    .modal-fullscreen-sm-down .modal-content {
        height: 100%;
        border: 0;
        border-radius: 0
        }
    .modal-fullscreen-sm-down .modal-footer, .modal-fullscreen-sm-down .modal-header {
        border-radius: 0
        }
    .modal-fullscreen-sm-down .modal-body {
        overflow-y: auto
        }
    }
@media (max-width: 767.98px) {
    .modal-fullscreen-md-down {
        width: 100vw;
        max-width: none;
        height: 100%;
        margin: 0
        }
    .modal-fullscreen-md-down .modal-content {
        height: 100%;
        border: 0;
        border-radius: 0
        }
    .modal-fullscreen-md-down .modal-footer, .modal-fullscreen-md-down .modal-header {
        border-radius: 0
        }
    .modal-fullscreen-md-down .modal-body {
        overflow-y: auto
        }
    }
@media (max-width: 991.98px) {
    .modal-fullscreen-lg-down {
        width: 100vw;
        max-width: none;
        height: 100%;
        margin: 0
        }
    .modal-fullscreen-lg-down .modal-content {
        height: 100%;
        border: 0;
        border-radius: 0
        }
    .modal-fullscreen-lg-down .modal-footer, .modal-fullscreen-lg-down .modal-header {
        border-radius: 0
        }
    .modal-fullscreen-lg-down .modal-body {
        overflow-y: auto
        }
    }
@media (max-width: 1199.98px) {
    .modal-fullscreen-xl-down {
        width: 100vw;
        max-width: none;
        height: 100%;
        margin: 0
        }
    .modal-fullscreen-xl-down .modal-content {
        height: 100%;
        border: 0;
        border-radius: 0
        }
    .modal-fullscreen-xl-down .modal-footer, .modal-fullscreen-xl-down .modal-header {
        border-radius: 0
        }
    .modal-fullscreen-xl-down .modal-body {
        overflow-y: auto
        }
    }
@media (max-width: 1399.98px) {
    .modal-fullscreen-xxl-down {
        width: 100vw;
        max-width: none;
        height: 100%;
        margin: 0
        }
    .modal-fullscreen-xxl-down .modal-content {
        height: 100%;
        border: 0;
        border-radius: 0
        }
    .modal-fullscreen-xxl-down .modal-footer, .modal-fullscreen-xxl-down .modal-header {
        border-radius: 0
        }
    .modal-fullscreen-xxl-down .modal-body {
        overflow-y: auto
        }
    }
.tooltip .tooltip-arrow::before {position:absolute;content:"";border-color:transparent;border-style:solid}
.bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before {top:-1px}
.bs-tooltip-top .tooltip-arrow::before {top:-1px}
.bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before {right:-1px}
.bs-tooltip-end .tooltip-arrow::before {right:-1px}
.bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before {bottom:-1px}
.bs-tooltip-bottom .tooltip-arrow::before {bottom:-1px}
.bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before {left:-1px}
.bs-tooltip-start .tooltip-arrow::before {left:-1px}
.popover .popover-arrow::after {position:absolute;display:block;content:"";border-color:transparent;border-style:solid;border-width:0}
.popover .popover-arrow::before {position:absolute;display:block;content:"";border-color:transparent;border-style:solid;border-width:0}
.bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {}
.bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before {}
.bs-popover-top > .popover-arrow::after {}
.bs-popover-top > .popover-arrow::before {}
.bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before {bottom:0}
.bs-popover-top > .popover-arrow::before {bottom:0}
.bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {}
.bs-popover-top > .popover-arrow::after {}
.bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {}
.bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before {}
.bs-popover-end > .popover-arrow::after {}
.bs-popover-end > .popover-arrow::before {}
.bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before {left:0}
.bs-popover-end > .popover-arrow::before {left:0}
.bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {}
.bs-popover-end > .popover-arrow::after {}
.bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {}
.bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before {}
.bs-popover-bottom > .popover-arrow::after {}
.bs-popover-bottom > .popover-arrow::before {}
.bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before {top:0}
.bs-popover-bottom > .popover-arrow::before {top:0}
.bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {}
.bs-popover-bottom > .popover-arrow::after {}
.bs-popover-auto[data-popper-placement^=bottom] .popover-header::before {position:absolute;top:0;left:50%;display:block;content:""}
.bs-popover-bottom .popover-header::before {position:absolute;top:0;left:50%;display:block;content:""}
.bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {}
.bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before {}
.bs-popover-start > .popover-arrow::after {}
.bs-popover-start > .popover-arrow::before {}
.bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before {right:0}
.bs-popover-start > .popover-arrow::before {right:0}
.bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {}
.bs-popover-start > .popover-arrow::after {}
.popover-header:empty {display:none}
.carousel-inner::after {display:block;clear:both;content:""}
@media (prefers-reduced-motion: reduce) {
    .carousel-item {
        transition: none
        }
    }
.carousel-item-next:not(.carousel-item-start) {transform:translatex(100%)}
.carousel-item-prev:not(.carousel-item-end) {transform:translatex(-100%)}
@media (prefers-reduced-motion: reduce) {
    .carousel-fade .active.carousel-item-end, .carousel-fade .active.carousel-item-start {
        transition: none
        }
    }
@media (prefers-reduced-motion: reduce) {
    .carousel-control-next, .carousel-control-prev {
        transition: none
        }
    }
.carousel-control-next:focus {color:#fff;text-decoration:none;outline:0;opacity:0.9}
.carousel-control-next:hover {color:#fff;text-decoration:none;outline:0;opacity:0.9}
.carousel-control-prev:focus {color:#fff;text-decoration:none;outline:0;opacity:0.9}
.carousel-control-prev:hover {color:#fff;text-decoration:none;outline:0;opacity:0.9}
@media (prefers-reduced-motion: reduce) {
    .carousel-indicators [data-bs-target] {
        transition: none
        }
    }

@media (max-width: 575.98px) {
    .offcanvas-sm {
        position: fixed;
        bottom: 0;
        z-index: 1045;
        display: flex;
        flex-direction: column;
        max-width: 100%;
        visibility: hidden;
        background-clip: padding-box;
        outline: 0;
        transition: transform 0.3s ease-in-out
        }
    }
@media (max-width: 575.98px) and (prefers-reduced-motion: reduce) {
    .offcanvas-sm {
        transition: none
        }
    }
@media (max-width: 575.98px) {
    .offcanvas-sm.offcanvas-start {
        top: 0;
        left: 0;
        transform: translatex(-100%)
        }
    }
@media (max-width: 575.98px) {
    .offcanvas-sm.offcanvas-end {
        top: 0;
        right: 0;
        transform: translatex(100%)
        }
    }
@media (max-width: 575.98px) {
    .offcanvas-sm.offcanvas-top {
        top: 0;
        right: 0;
        left: 0;
        max-height: 100%;
        transform: translatey(-100%)
        }
    }
@media (max-width: 575.98px) {
    .offcanvas-sm.offcanvas-bottom {
        right: 0;
        left: 0;
        max-height: 100%;
        transform: translatey(100%)
        }
    }
@media (max-width: 575.98px) {
    .offcanvas-sm.show:not(.hiding), .offcanvas-sm.showing {
        transform: none
        }
    }
@media (max-width: 575.98px) {
    .offcanvas-sm.hiding, .offcanvas-sm.show, .offcanvas-sm.showing {
        visibility: visible
        }
    }
@media (min-width: 576px) {
    .offcanvas-sm {
        background-color: transparent
        }
    .offcanvas-sm .offcanvas-header {
        display: none
        }
    .offcanvas-sm .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible;
        background-color: transparent
        }
    }
@media (max-width: 767.98px) {
    .offcanvas-md {
        position: fixed;
        bottom: 0;
        z-index: 1045;
        display: flex;
        flex-direction: column;
        max-width: 100%;
        visibility: hidden;
        background-clip: padding-box;
        outline: 0;
        transition: transform 0.3s ease-in-out
        }
    }
@media (max-width: 767.98px) and (prefers-reduced-motion: reduce) {
    .offcanvas-md {
        transition: none
        }
    }
@media (max-width: 767.98px) {
    .offcanvas-md.offcanvas-start {
        top: 0;
        left: 0;
        transform: translatex(-100%)
        }
    }
@media (max-width: 767.98px) {
    .offcanvas-md.offcanvas-end {
        top: 0;
        right: 0;
        transform: translatex(100%)
        }
    }
@media (max-width: 767.98px) {
    .offcanvas-md.offcanvas-top {
        top: 0;
        right: 0;
        left: 0;
        max-height: 100%;
        transform: translatey(-100%)
        }
    }
@media (max-width: 767.98px) {
    .offcanvas-md.offcanvas-bottom {
        right: 0;
        left: 0;
        max-height: 100%;
        transform: translatey(100%)
        }
    }
@media (max-width: 767.98px) {
    .offcanvas-md.show:not(.hiding), .offcanvas-md.showing {
        transform: none
        }
    }
@media (max-width: 767.98px) {
    .offcanvas-md.hiding, .offcanvas-md.show, .offcanvas-md.showing {
        visibility: visible
        }
    }
@media (min-width: 768px) {
    .offcanvas-md {
        background-color: transparent
        }
    .offcanvas-md .offcanvas-header {
        display: none
        }
    .offcanvas-md .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible;
        background-color: transparent
        }
    }
@media (max-width: 991.98px) {
    .offcanvas-lg {
        position: fixed;
        bottom: 0;
        z-index: 1045;
        display: flex;
        flex-direction: column;
        max-width: 100%;
        visibility: hidden;
        background-clip: padding-box;
        outline: 0;
        transition: transform 0.3s ease-in-out
        }
    }
@media (max-width: 991.98px) and (prefers-reduced-motion: reduce) {
    .offcanvas-lg {
        transition: none
        }
    }
@media (max-width: 991.98px) {
    .offcanvas-lg.offcanvas-start {
        top: 0;
        left: 0;
        transform: translatex(-100%)
        }
    }
@media (max-width: 991.98px) {
    .offcanvas-lg.offcanvas-end {
        top: 0;
        right: 0;
        transform: translatex(100%)
        }
    }
@media (max-width: 991.98px) {
    .offcanvas-lg.offcanvas-top {
        top: 0;
        right: 0;
        left: 0;
        max-height: 100%;
        transform: translatey(-100%)
        }
    }
@media (max-width: 991.98px) {
    .offcanvas-lg.offcanvas-bottom {
        right: 0;
        left: 0;
        max-height: 100%;
        transform: translatey(100%)
        }
    }
@media (max-width: 991.98px) {
    .offcanvas-lg.show:not(.hiding), .offcanvas-lg.showing {
        transform: none
        }
    }
@media (max-width: 991.98px) {
    .offcanvas-lg.hiding, .offcanvas-lg.show, .offcanvas-lg.showing {
        visibility: visible
        }
    }
@media (min-width: 992px) {
    .offcanvas-lg {
        background-color: transparent
        }
    .offcanvas-lg .offcanvas-header {
        display: none
        }
    .offcanvas-lg .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible;
        background-color: transparent
        }
    }
@media (max-width: 1199.98px) {
    .offcanvas-xl {
        position: fixed;
        bottom: 0;
        z-index: 1045;
        display: flex;
        flex-direction: column;
        max-width: 100%;
        visibility: hidden;
        background-clip: padding-box;
        outline: 0;
        transition: transform 0.3s ease-in-out
        }
    }
@media (max-width: 1199.98px) and (prefers-reduced-motion: reduce) {
    .offcanvas-xl {
        transition: none
        }
    }
@media (max-width: 1199.98px) {
    .offcanvas-xl.offcanvas-start {
        top: 0;
        left: 0;
        transform: translatex(-100%)
        }
    }
@media (max-width: 1199.98px) {
    .offcanvas-xl.offcanvas-end {
        top: 0;
        right: 0;
        transform: translatex(100%)
        }
    }
@media (max-width: 1199.98px) {
    .offcanvas-xl.offcanvas-top {
        top: 0;
        right: 0;
        left: 0;
        max-height: 100%;
        transform: translatey(-100%)
        }
    }
@media (max-width: 1199.98px) {
    .offcanvas-xl.offcanvas-bottom {
        right: 0;
        left: 0;
        max-height: 100%;
        transform: translatey(100%)
        }
    }
@media (max-width: 1199.98px) {
    .offcanvas-xl.show:not(.hiding), .offcanvas-xl.showing {
        transform: none
        }
    }
@media (max-width: 1199.98px) {
    .offcanvas-xl.hiding, .offcanvas-xl.show, .offcanvas-xl.showing {
        visibility: visible
        }
    }
@media (min-width: 1200px) {
    .offcanvas-xl {
        background-color: transparent
        }
    .offcanvas-xl .offcanvas-header {
        display: none
        }
    .offcanvas-xl .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible;
        background-color: transparent
        }
    }
@media (max-width: 1399.98px) {
    .offcanvas-xxl {
        position: fixed;
        bottom: 0;
        z-index: 1045;
        display: flex;
        flex-direction: column;
        max-width: 100%;
        visibility: hidden;
        background-clip: padding-box;
        outline: 0;
        transition: transform 0.3s ease-in-out
        }
    }
@media (max-width: 1399.98px) and (prefers-reduced-motion: reduce) {
    .offcanvas-xxl {
        transition: none
        }
    }
@media (max-width: 1399.98px) {
    .offcanvas-xxl.offcanvas-start {
        top: 0;
        left: 0;
        transform: translatex(-100%)
        }
    }
@media (max-width: 1399.98px) {
    .offcanvas-xxl.offcanvas-end {
        top: 0;
        right: 0;
        transform: translatex(100%)
        }
    }
@media (max-width: 1399.98px) {
    .offcanvas-xxl.offcanvas-top {
        top: 0;
        right: 0;
        left: 0;
        max-height: 100%;
        transform: translatey(-100%)
        }
    }
@media (max-width: 1399.98px) {
    .offcanvas-xxl.offcanvas-bottom {
        right: 0;
        left: 0;
        max-height: 100%;
        transform: translatey(100%)
        }
    }
@media (max-width: 1399.98px) {
    .offcanvas-xxl.show:not(.hiding), .offcanvas-xxl.showing {
        transform: none
        }
    }
@media (max-width: 1399.98px) {
    .offcanvas-xxl.hiding, .offcanvas-xxl.show, .offcanvas-xxl.showing {
        visibility: visible
        }
    }
@media (min-width: 1400px) {
    .offcanvas-xxl {
        background-color: transparent
        }
    .offcanvas-xxl .offcanvas-header {
        display: none
        }
    .offcanvas-xxl .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible;
        background-color: transparent
        }
    }
@media (prefers-reduced-motion: reduce) {
    .offcanvas {
        transition: none
        }
    }
.offcanvas.show:not(.hiding) {transform:none}
.placeholder.btn::before {display:inline-block;content:""}
.clearfix::after {display:block;clear:both;content:""}
.link-primary:focus {color:#0a58ca}
.link-primary:hover {color:#0a58ca}
.link-secondary:focus {color:#565e64}
.link-secondary:hover {color:#565e64}
.link-success:focus {color:#146c43}
.link-success:hover {color:#146c43}
.link-info:focus {color:#3dd5f3}
.link-info:hover {color:#3dd5f3}
.link-warning:focus {color:#ffcd39}
.link-warning:hover {color:#ffcd39}
.link-danger:focus {color:#b02a37}
.link-danger:hover {color:#b02a37}
.link-light:focus {color:#f9fafb}
.link-light:hover {color:#f9fafb}
.link-dark:focus {color:#1a1e21}
.link-dark:hover {color:#1a1e21}
.ratio::before {display:block;content:""}
@media (min-width: 576px) {
    .sticky-sm-top {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 1020
        }
    .sticky-sm-bottom {
        position: -webkit-sticky;
        position: sticky;
        bottom: 0;
        z-index: 1020
        }
    }
@media (min-width: 768px) {
    .sticky-md-top {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 1020
        }
    .sticky-md-bottom {
        position: -webkit-sticky;
        position: sticky;
        bottom: 0;
        z-index: 1020
        }
    }
@media (min-width: 992px) {
    .sticky-lg-top {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 1020
        }
    .sticky-lg-bottom {
        position: -webkit-sticky;
        position: sticky;
        bottom: 0;
        z-index: 1020
        }
    }
@media (min-width: 1200px) {
    .sticky-xl-top {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 1020
        }
    .sticky-xl-bottom {
        position: -webkit-sticky;
        position: sticky;
        bottom: 0;
        z-index: 1020
        }
    }
@media (min-width: 1400px) {
    .sticky-xxl-top {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 1020
        }
    .sticky-xxl-bottom {
        position: -webkit-sticky;
        position: sticky;
        bottom: 0;
        z-index: 1020
        }
    }
.visually-hidden-focusable:not(:focus):not(:focus-within) {position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0}
.stretched-link::after {position:absolute;top:0;right:0;bottom:0;left:0;z-index:1;content:""}
@media (min-width: 576px) {
    .float-sm-start {
        float: left
        }
    .float-sm-end {
        float: right
        }
    .float-sm-none {
        float: none
        }
    .d-sm-inline {
        display: inline
        }
    .d-sm-inline-block {
        display: inline-block
        }
    .d-sm-block {
        display: block
        }
    .d-sm-grid {
        display: grid
        }
    .d-sm-table {
        display: table
        }
    .d-sm-table-row {
        display: table-row
        }
    .d-sm-table-cell {
        display: table-cell
        }
    .d-sm-flex {
        display: flex
        }
    .d-sm-inline-flex {
        display: inline-flex
        }
    .d-sm-none {
        display: none
        }
    .flex-sm-fill {
        flex: 1 1 auto
        }
    .flex-sm-row {
        flex-direction: row
        }
    .flex-sm-column {
        flex-direction: column
        }
    .flex-sm-row-reverse {
        flex-direction: row-reverse
        }
    .flex-sm-column-reverse {
        flex-direction: column-reverse
        }
    .flex-sm-grow-0 {
        flex-grow: 0
        }
    .flex-sm-grow-1 {
        flex-grow: 1
        }
    .flex-sm-shrink-0 {
        flex-shrink: 0
        }
    .flex-sm-shrink-1 {
        flex-shrink: 1
        }
    .flex-sm-wrap {
        flex-wrap: wrap
        }
    .flex-sm-nowrap {
        flex-wrap: nowrap
        }
    .flex-sm-wrap-reverse {
        flex-wrap: wrap-reverse
        }
    .justify-content-sm-start {
        justify-content: flex-start
        }
    .justify-content-sm-end {
        justify-content: flex-end
        }
    .justify-content-sm-center {
        justify-content: center
        }
    .justify-content-sm-between {
        justify-content: space-between
        }
    .justify-content-sm-around {
        justify-content: space-around
        }
    .justify-content-sm-evenly {
        justify-content: space-evenly
        }
    .align-items-sm-start {
        align-items: flex-start
        }
    .align-items-sm-end {
        align-items: flex-end
        }
    .align-items-sm-center {
        align-items: center
        }
    .align-items-sm-baseline {
        align-items: baseline
        }
    .align-items-sm-stretch {
        align-items: stretch
        }
    .align-content-sm-start {
        align-content: flex-start
        }
    .align-content-sm-end {
        align-content: flex-end
        }
    .align-content-sm-center {
        align-content: center
        }
    .align-content-sm-between {
        align-content: space-between
        }
    .align-content-sm-around {
        align-content: space-around
        }
    .align-content-sm-stretch {
        align-content: stretch
        }
    .align-self-sm-auto {
        align-self: auto
        }
    .align-self-sm-start {
        align-self: flex-start
        }
    .align-self-sm-end {
        align-self: flex-end
        }
    .align-self-sm-center {
        align-self: center
        }
    .align-self-sm-baseline {
        align-self: baseline
        }
    .align-self-sm-stretch {
        align-self: stretch
        }
    .order-sm-first {
        order: -1
        }
    .order-sm-0 {
        order: 0
        }
    .order-sm-1 {
        order: 1
        }
    .order-sm-2 {
        order: 2
        }
    .order-sm-3 {
        order: 3
        }
    .order-sm-4 {
        order: 4
        }
    .order-sm-5 {
        order: 5
        }
    .order-sm-last {
        order: 6
        }
    .m-sm-0 {
        margin: 0
        }
    .m-sm-1 {
        margin: 0.25rem
        }
    .m-sm-2 {
        margin: 0.5rem
        }
    .m-sm-3 {
        margin: 1rem
        }
    .m-sm-4 {
        margin: 1.5rem
        }
    .m-sm-5 {
        margin: 3rem
        }
    .m-sm-auto {
        margin: auto
        }
    .mx-sm-0 {
        margin-right: 0;
        margin-left: 0
        }
    .mx-sm-1 {
        margin-right: 0.25rem;
        margin-left: 0.25rem
        }
    .mx-sm-2 {
        margin-right: 0.5rem;
        margin-left: 0.5rem
        }
    .mx-sm-3 {
        margin-right: 1rem;
        margin-left: 1rem
        }
    .mx-sm-4 {
        margin-right: 1.5rem;
        margin-left: 1.5rem
        }
    .mx-sm-5 {
        margin-right: 3rem;
        margin-left: 3rem
        }
    .mx-sm-auto {
        margin-right: auto;
        margin-left: auto
        }
    .my-sm-0 {
        margin-top: 0;
        margin-bottom: 0
        }
    .my-sm-1 {
        margin-top: 0.25rem;
        margin-bottom: 0.25rem
        }
    .my-sm-2 {
        margin-top: 0.5rem;
        margin-bottom: 0.5rem
        }
    .my-sm-3 {
        margin-top: 1rem;
        margin-bottom: 1rem
        }
    .my-sm-4 {
        margin-top: 1.5rem;
        margin-bottom: 1.5rem
        }
    .my-sm-5 {
        margin-top: 3rem;
        margin-bottom: 3rem
        }
    .my-sm-auto {
        margin-top: auto;
        margin-bottom: auto
        }
    .mt-sm-0 {
        margin-top: 0
        }
    .mt-sm-1 {
        margin-top: 0.25rem
        }
    .mt-sm-2 {
        margin-top: 0.5rem
        }
    .mt-sm-3 {
        margin-top: 1rem
        }
    .mt-sm-4 {
        margin-top: 1.5rem
        }
    .mt-sm-5 {
        margin-top: 3rem
        }
    .mt-sm-auto {
        margin-top: auto
        }
    .me-sm-0 {
        margin-right: 0
        }
    .me-sm-1 {
        margin-right: 0.25rem
        }
    .me-sm-2 {
        margin-right: 0.5rem
        }
    .me-sm-3 {
        margin-right: 1rem
        }
    .me-sm-4 {
        margin-right: 1.5rem
        }
    .me-sm-5 {
        margin-right: 3rem
        }
    .me-sm-auto {
        margin-right: auto
        }
    .mb-sm-0 {
        margin-bottom: 0
        }
    .mb-sm-1 {
        margin-bottom: 0.25rem
        }
    .mb-sm-2 {
        margin-bottom: 0.5rem
        }
    .mb-sm-3 {
        margin-bottom: 1rem
        }
    .mb-sm-4 {
        margin-bottom: 1.5rem
        }
    .mb-sm-5 {
        margin-bottom: 3rem
        }
    .mb-sm-auto {
        margin-bottom: auto
        }
    .ms-sm-0 {
        margin-left: 0
        }
    .ms-sm-1 {
        margin-left: 0.25rem
        }
    .ms-sm-2 {
        margin-left: 0.5rem
        }
    .ms-sm-3 {
        margin-left: 1rem
        }
    .ms-sm-4 {
        margin-left: 1.5rem
        }
    .ms-sm-5 {
        margin-left: 3rem
        }
    .ms-sm-auto {
        margin-left: auto
        }
    .p-sm-0 {
        padding: 0
        }
    .p-sm-1 {
        padding: 0.25rem
        }
    .p-sm-2 {
        padding: 0.5rem
        }
    .p-sm-3 {
        padding: 1rem
        }
    .p-sm-4 {
        padding: 1.5rem
        }
    .p-sm-5 {
        padding: 3rem
        }
    .px-sm-0 {
        padding-right: 0;
        padding-left: 0
        }
    .px-sm-1 {
        padding-right: 0.25rem;
        padding-left: 0.25rem
        }
    .px-sm-2 {
        padding-right: 0.5rem;
        padding-left: 0.5rem
        }
    .px-sm-3 {
        padding-right: 1rem;
        padding-left: 1rem
        }
    .px-sm-4 {
        padding-right: 1.5rem;
        padding-left: 1.5rem
        }
    .px-sm-5 {
        padding-right: 3rem;
        padding-left: 3rem
        }
    .py-sm-0 {
        padding-top: 0;
        padding-bottom: 0
        }
    .py-sm-1 {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem
        }
    .py-sm-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem
        }
    .py-sm-3 {
        padding-top: 1rem;
        padding-bottom: 1rem
        }
    .py-sm-4 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem
        }
    .py-sm-5 {
        padding-top: 3rem;
        padding-bottom: 3rem
        }
    .pt-sm-0 {
        padding-top: 0
        }
    .pt-sm-1 {
        padding-top: 0.25rem
        }
    .pt-sm-2 {
        padding-top: 0.5rem
        }
    .pt-sm-3 {
        padding-top: 1rem
        }
    .pt-sm-4 {
        padding-top: 1.5rem
        }
    .pt-sm-5 {
        padding-top: 3rem
        }
    .pe-sm-0 {
        padding-right: 0
        }
    .pe-sm-1 {
        padding-right: 0.25rem
        }
    .pe-sm-2 {
        padding-right: 0.5rem
        }
    .pe-sm-3 {
        padding-right: 1rem
        }
    .pe-sm-4 {
        padding-right: 1.5rem
        }
    .pe-sm-5 {
        padding-right: 3rem
        }
    .pb-sm-0 {
        padding-bottom: 0
        }
    .pb-sm-1 {
        padding-bottom: 0.25rem
        }
    .pb-sm-2 {
        padding-bottom: 0.5rem
        }
    .pb-sm-3 {
        padding-bottom: 1rem
        }
    .pb-sm-4 {
        padding-bottom: 1.5rem
        }
    .pb-sm-5 {
        padding-bottom: 3rem
        }
    .ps-sm-0 {
        padding-left: 0
        }
    .ps-sm-1 {
        padding-left: 0.25rem
        }
    .ps-sm-2 {
        padding-left: 0.5rem
        }
    .ps-sm-3 {
        padding-left: 1rem
        }
    .ps-sm-4 {
        padding-left: 1.5rem
        }
    .ps-sm-5 {
        padding-left: 3rem
        }
    .gap-sm-0 {
        gap: 0
        }
    .gap-sm-1 {
        gap: 0.25rem
        }
    .gap-sm-2 {
        gap: 0.5rem
        }
    .gap-sm-3 {
        gap: 1rem
        }
    .gap-sm-4 {
        gap: 1.5rem
        }
    .gap-sm-5 {
        gap: 3rem
        }
    .text-sm-start {
        text-align: left
        }
    .text-sm-end {
        text-align: right
        }
    .text-sm-center {
        text-align: center
        }
    }
@media (min-width: 768px) {
    .float-md-start {
        float: left
        }
    .float-md-end {
        float: right
        }
    .float-md-none {
        float: none
        }
    .d-md-inline {
        display: inline
        }
    .d-md-inline-block {
        display: inline-block
        }
    .d-md-block {
        display: block
        }
    .d-md-grid {
        display: grid
        }
    .d-md-table {
        display: table
        }
    .d-md-table-row {
        display: table-row
        }
    .d-md-table-cell {
        display: table-cell
        }
    .d-md-flex {
        display: flex
        }
    .d-md-inline-flex {
        display: inline-flex
        }
    .d-md-none {
        display: none
        }
    .flex-md-fill {
        flex: 1 1 auto
        }
    .flex-md-row {
        flex-direction: row
        }
    .flex-md-column {
        flex-direction: column
        }
    .flex-md-row-reverse {
        flex-direction: row-reverse
        }
    .flex-md-column-reverse {
        flex-direction: column-reverse
        }
    .flex-md-grow-0 {
        flex-grow: 0
        }
    .flex-md-grow-1 {
        flex-grow: 1
        }
    .flex-md-shrink-0 {
        flex-shrink: 0
        }
    .flex-md-shrink-1 {
        flex-shrink: 1
        }
    .flex-md-wrap {
        flex-wrap: wrap
        }
    .flex-md-nowrap {
        flex-wrap: nowrap
        }
    .flex-md-wrap-reverse {
        flex-wrap: wrap-reverse
        }
    .justify-content-md-start {
        justify-content: flex-start
        }
    .justify-content-md-end {
        justify-content: flex-end
        }
    .justify-content-md-center {
        justify-content: center
        }
    .justify-content-md-between {
        justify-content: space-between
        }
    .justify-content-md-around {
        justify-content: space-around
        }
    .justify-content-md-evenly {
        justify-content: space-evenly
        }
    .align-items-md-start {
        align-items: flex-start
        }
    .align-items-md-end {
        align-items: flex-end
        }
    .align-items-md-center {
        align-items: center
        }
    .align-items-md-baseline {
        align-items: baseline
        }
    .align-items-md-stretch {
        align-items: stretch
        }
    .align-content-md-start {
        align-content: flex-start
        }
    .align-content-md-end {
        align-content: flex-end
        }
    .align-content-md-center {
        align-content: center
        }
    .align-content-md-between {
        align-content: space-between
        }
    .align-content-md-around {
        align-content: space-around
        }
    .align-content-md-stretch {
        align-content: stretch
        }
    .align-self-md-auto {
        align-self: auto
        }
    .align-self-md-start {
        align-self: flex-start
        }
    .align-self-md-end {
        align-self: flex-end
        }
    .align-self-md-center {
        align-self: center
        }
    .align-self-md-baseline {
        align-self: baseline
        }
    .align-self-md-stretch {
        align-self: stretch
        }
    .order-md-first {
        order: -1
        }
    .order-md-0 {
        order: 0
        }
    .order-md-1 {
        order: 1
        }
    .order-md-2 {
        order: 2
        }
    .order-md-3 {
        order: 3
        }
    .order-md-4 {
        order: 4
        }
    .order-md-5 {
        order: 5
        }
    .order-md-last {
        order: 6
        }
    .m-md-0 {
        margin: 0
        }
    .m-md-1 {
        margin: 0.25rem
        }
    .m-md-2 {
        margin: 0.5rem
        }
    .m-md-3 {
        margin: 1rem
        }
    .m-md-4 {
        margin: 1.5rem
        }
    .m-md-5 {
        margin: 3rem
        }
    .m-md-auto {
        margin: auto
        }
    .mx-md-0 {
        margin-right: 0;
        margin-left: 0
        }
    .mx-md-1 {
        margin-right: 0.25rem;
        margin-left: 0.25rem
        }
    .mx-md-2 {
        margin-right: 0.5rem;
        margin-left: 0.5rem
        }
    .mx-md-3 {
        margin-right: 1rem;
        margin-left: 1rem
        }
    .mx-md-4 {
        margin-right: 1.5rem;
        margin-left: 1.5rem
        }
    .mx-md-5 {
        margin-right: 3rem;
        margin-left: 3rem
        }
    .mx-md-auto {
        margin-right: auto;
        margin-left: auto
        }
    .my-md-0 {
        margin-top: 0;
        margin-bottom: 0
        }
    .my-md-1 {
        margin-top: 0.25rem;
        margin-bottom: 0.25rem
        }
    .my-md-2 {
        margin-top: 0.5rem;
        margin-bottom: 0.5rem
        }
    .my-md-3 {
        margin-top: 1rem;
        margin-bottom: 1rem
        }
    .my-md-4 {
        margin-top: 1.5rem;
        margin-bottom: 1.5rem
        }
    .my-md-5 {
        margin-top: 3rem;
        margin-bottom: 3rem
        }
    .my-md-auto {
        margin-top: auto;
        margin-bottom: auto
        }
    .mt-md-0 {
        margin-top: 0
        }
    .mt-md-1 {
        margin-top: 0.25rem
        }
    .mt-md-2 {
        margin-top: 0.5rem
        }
    .mt-md-3 {
        margin-top: 1rem
        }
    .mt-md-4 {
        margin-top: 1.5rem
        }
    .mt-md-5 {
        margin-top: 3rem
        }
    .mt-md-auto {
        margin-top: auto
        }
    .me-md-0 {
        margin-right: 0
        }
    .me-md-1 {
        margin-right: 0.25rem
        }
    .me-md-2 {
        margin-right: 0.5rem
        }
    .me-md-3 {
        margin-right: 1rem
        }
    .me-md-4 {
        margin-right: 1.5rem
        }
    .me-md-5 {
        margin-right: 3rem
        }
    .me-md-auto {
        margin-right: auto
        }
    .mb-md-0 {
        margin-bottom: 0
        }
    .mb-md-1 {
        margin-bottom: 0.25rem
        }
    .mb-md-2 {
        margin-bottom: 0.5rem
        }
    .mb-md-3 {
        margin-bottom: 1rem
        }
    .mb-md-4 {
        margin-bottom: 1.5rem
        }
    .mb-md-5 {
        margin-bottom: 3rem
        }
    .mb-md-auto {
        margin-bottom: auto
        }
    .ms-md-0 {
        margin-left: 0
        }
    .ms-md-1 {
        margin-left: 0.25rem
        }
    .ms-md-2 {
        margin-left: 0.5rem
        }
    .ms-md-3 {
        margin-left: 1rem
        }
    .ms-md-4 {
        margin-left: 1.5rem
        }
    .ms-md-5 {
        margin-left: 3rem
        }
    .ms-md-auto {
        margin-left: auto
        }
    .p-md-0 {
        padding: 0
        }
    .p-md-1 {
        padding: 0.25rem
        }
    .p-md-2 {
        padding: 0.5rem
        }
    .p-md-3 {
        padding: 1rem
        }
    .p-md-4 {
        padding: 1.5rem
        }
    .p-md-5 {
        padding: 3rem
        }
    .px-md-0 {
        padding-right: 0;
        padding-left: 0
        }
    .px-md-1 {
        padding-right: 0.25rem;
        padding-left: 0.25rem
        }
    .px-md-2 {
        padding-right: 0.5rem;
        padding-left: 0.5rem
        }
    .px-md-3 {
        padding-right: 1rem;
        padding-left: 1rem
        }
    .px-md-4 {
        padding-right: 1.5rem;
        padding-left: 1.5rem
        }
    .px-md-5 {
        padding-right: 3rem;
        padding-left: 3rem
        }
    .py-md-0 {
        padding-top: 0;
        padding-bottom: 0
        }
    .py-md-1 {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem
        }
    .py-md-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem
        }
    .py-md-3 {
        padding-top: 1rem;
        padding-bottom: 1rem
        }
    .py-md-4 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem
        }
    .py-md-5 {
        padding-top: 3rem;
        padding-bottom: 3rem
        }
    .pt-md-0 {
        padding-top: 0
        }
    .pt-md-1 {
        padding-top: 0.25rem
        }
    .pt-md-2 {
        padding-top: 0.5rem
        }
    .pt-md-3 {
        padding-top: 1rem
        }
    .pt-md-4 {
        padding-top: 1.5rem
        }
    .pt-md-5 {
        padding-top: 3rem
        }
    .pe-md-0 {
        padding-right: 0
        }
    .pe-md-1 {
        padding-right: 0.25rem
        }
    .pe-md-2 {
        padding-right: 0.5rem
        }
    .pe-md-3 {
        padding-right: 1rem
        }
    .pe-md-4 {
        padding-right: 1.5rem
        }
    .pe-md-5 {
        padding-right: 3rem
        }
    .pb-md-0 {
        padding-bottom: 0
        }
    .pb-md-1 {
        padding-bottom: 0.25rem
        }
    .pb-md-2 {
        padding-bottom: 0.5rem
        }
    .pb-md-3 {
        padding-bottom: 1rem
        }
    .pb-md-4 {
        padding-bottom: 1.5rem
        }
    .pb-md-5 {
        padding-bottom: 3rem
        }
    .ps-md-0 {
        padding-left: 0
        }
    .ps-md-1 {
        padding-left: 0.25rem
        }
    .ps-md-2 {
        padding-left: 0.5rem
        }
    .ps-md-3 {
        padding-left: 1rem
        }
    .ps-md-4 {
        padding-left: 1.5rem
        }
    .ps-md-5 {
        padding-left: 3rem
        }
    .gap-md-0 {
        gap: 0
        }
    .gap-md-1 {
        gap: 0.25rem
        }
    .gap-md-2 {
        gap: 0.5rem
        }
    .gap-md-3 {
        gap: 1rem
        }
    .gap-md-4 {
        gap: 1.5rem
        }
    .gap-md-5 {
        gap: 3rem
        }
    .text-md-start {
        text-align: left
        }
    .text-md-end {
        text-align: right
        }
    .text-md-center {
        text-align: center
        }
    }
@media (min-width: 992px) {
    .float-lg-start {
        float: left
        }
    .float-lg-end {
        float: right
        }
    .float-lg-none {
        float: none
        }
    .d-lg-inline {
        display: inline
        }
    .d-lg-inline-block {
        display: inline-block
        }
    .d-lg-block {
        display: block
        }
    .d-lg-grid {
        display: grid
        }
    .d-lg-table {
        display: table
        }
    .d-lg-table-row {
        display: table-row
        }
    .d-lg-table-cell {
        display: table-cell
        }
    .d-lg-flex {
        display: flex
        }
    .d-lg-inline-flex {
        display: inline-flex
        }
    .d-lg-none {
        display: none
        }
    .flex-lg-fill {
        flex: 1 1 auto
        }
    .flex-lg-row {
        flex-direction: row
        }
    .flex-lg-column {
        flex-direction: column
        }
    .flex-lg-row-reverse {
        flex-direction: row-reverse
        }
    .flex-lg-column-reverse {
        flex-direction: column-reverse
        }
    .flex-lg-grow-0 {
        flex-grow: 0
        }
    .flex-lg-grow-1 {
        flex-grow: 1
        }
    .flex-lg-shrink-0 {
        flex-shrink: 0
        }
    .flex-lg-shrink-1 {
        flex-shrink: 1
        }
    .flex-lg-wrap {
        flex-wrap: wrap
        }
    .flex-lg-nowrap {
        flex-wrap: nowrap
        }
    .flex-lg-wrap-reverse {
        flex-wrap: wrap-reverse
        }
    .justify-content-lg-start {
        justify-content: flex-start
        }
    .justify-content-lg-end {
        justify-content: flex-end
        }
    .justify-content-lg-center {
        justify-content: center
        }
    .justify-content-lg-between {
        justify-content: space-between
        }
    .justify-content-lg-around {
        justify-content: space-around
        }
    .justify-content-lg-evenly {
        justify-content: space-evenly
        }
    .align-items-lg-start {
        align-items: flex-start
        }
    .align-items-lg-end {
        align-items: flex-end
        }
    .align-items-lg-center {
        align-items: center
        }
    .align-items-lg-baseline {
        align-items: baseline
        }
    .align-items-lg-stretch {
        align-items: stretch
        }
    .align-content-lg-start {
        align-content: flex-start
        }
    .align-content-lg-end {
        align-content: flex-end
        }
    .align-content-lg-center {
        align-content: center
        }
    .align-content-lg-between {
        align-content: space-between
        }
    .align-content-lg-around {
        align-content: space-around
        }
    .align-content-lg-stretch {
        align-content: stretch
        }
    .align-self-lg-auto {
        align-self: auto
        }
    .align-self-lg-start {
        align-self: flex-start
        }
    .align-self-lg-end {
        align-self: flex-end
        }
    .align-self-lg-center {
        align-self: center
        }
    .align-self-lg-baseline {
        align-self: baseline
        }
    .align-self-lg-stretch {
        align-self: stretch
        }
    .order-lg-first {
        order: -1
        }
    .order-lg-0 {
        order: 0
        }
    .order-lg-1 {
        order: 1
        }
    .order-lg-2 {
        order: 2
        }
    .order-lg-3 {
        order: 3
        }
    .order-lg-4 {
        order: 4
        }
    .order-lg-5 {
        order: 5
        }
    .order-lg-last {
        order: 6
        }
    .m-lg-0 {
        margin: 0
        }
    .m-lg-1 {
        margin: 0.25rem
        }
    .m-lg-2 {
        margin: 0.5rem
        }
    .m-lg-3 {
        margin: 1rem
        }
    .m-lg-4 {
        margin: 1.5rem
        }
    .m-lg-5 {
        margin: 3rem
        }
    .m-lg-auto {
        margin: auto
        }
    .mx-lg-0 {
        margin-right: 0;
        margin-left: 0
        }
    .mx-lg-1 {
        margin-right: 0.25rem;
        margin-left: 0.25rem
        }
    .mx-lg-2 {
        margin-right: 0.5rem;
        margin-left: 0.5rem
        }
    .mx-lg-3 {
        margin-right: 1rem;
        margin-left: 1rem
        }
    .mx-lg-4 {
        margin-right: 1.5rem;
        margin-left: 1.5rem
        }
    .mx-lg-5 {
        margin-right: 3rem;
        margin-left: 3rem
        }
    .mx-lg-auto {
        margin-right: auto;
        margin-left: auto
        }
    .my-lg-0 {
        margin-top: 0;
        margin-bottom: 0
        }
    .my-lg-1 {
        margin-top: 0.25rem;
        margin-bottom: 0.25rem
        }
    .my-lg-2 {
        margin-top: 0.5rem;
        margin-bottom: 0.5rem
        }
    .my-lg-3 {
        margin-top: 1rem;
        margin-bottom: 1rem
        }
    .my-lg-4 {
        margin-top: 1.5rem;
        margin-bottom: 1.5rem
        }
    .my-lg-5 {
        margin-top: 3rem;
        margin-bottom: 3rem
        }
    .my-lg-auto {
        margin-top: auto;
        margin-bottom: auto
        }
    .mt-lg-0 {
        margin-top: 0
        }
    .mt-lg-1 {
        margin-top: 0.25rem
        }
    .mt-lg-2 {
        margin-top: 0.5rem
        }
    .mt-lg-3 {
        margin-top: 1rem
        }
    .mt-lg-4 {
        margin-top: 1.5rem
        }
    .mt-lg-5 {
        margin-top: 3rem
        }
    .mt-lg-auto {
        margin-top: auto
        }
    .me-lg-0 {
        margin-right: 0
        }
    .me-lg-1 {
        margin-right: 0.25rem
        }
    .me-lg-2 {
        margin-right: 0.5rem
        }
    .me-lg-3 {
        margin-right: 1rem
        }
    .me-lg-4 {
        margin-right: 1.5rem
        }
    .me-lg-5 {
        margin-right: 3rem
        }
    .me-lg-auto {
        margin-right: auto
        }
    .mb-lg-0 {
        margin-bottom: 0
        }
    .mb-lg-1 {
        margin-bottom: 0.25rem
        }
    .mb-lg-2 {
        margin-bottom: 0.5rem
        }
    .mb-lg-3 {
        margin-bottom: 1rem
        }
    .mb-lg-4 {
        margin-bottom: 1.5rem
        }
    .mb-lg-5 {
        margin-bottom: 3rem
        }
    .mb-lg-auto {
        margin-bottom: auto
        }
    .ms-lg-0 {
        margin-left: 0
        }
    .ms-lg-1 {
        margin-left: 0.25rem
        }
    .ms-lg-2 {
        margin-left: 0.5rem
        }
    .ms-lg-3 {
        margin-left: 1rem
        }
    .ms-lg-4 {
        margin-left: 1.5rem
        }
    .ms-lg-5 {
        margin-left: 3rem
        }
    .ms-lg-auto {
        margin-left: auto
        }
    .p-lg-0 {
        padding: 0
        }
    .p-lg-1 {
        padding: 0.25rem
        }
    .p-lg-2 {
        padding: 0.5rem
        }
    .p-lg-3 {
        padding: 1rem
        }
    .p-lg-4 {
        padding: 1.5rem
        }
    .p-lg-5 {
        padding: 3rem
        }
    .px-lg-0 {
        padding-right: 0;
        padding-left: 0
        }
    .px-lg-1 {
        padding-right: 0.25rem;
        padding-left: 0.25rem
        }
    .px-lg-2 {
        padding-right: 0.5rem;
        padding-left: 0.5rem
        }
    .px-lg-3 {
        padding-right: 1rem;
        padding-left: 1rem
        }
    .px-lg-4 {
        padding-right: 1.5rem;
        padding-left: 1.5rem
        }
    .px-lg-5 {
        padding-right: 3rem;
        padding-left: 3rem
        }
    .py-lg-0 {
        padding-top: 0;
        padding-bottom: 0
        }
    .py-lg-1 {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem
        }
    .py-lg-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem
        }
    .py-lg-3 {
        padding-top: 1rem;
        padding-bottom: 1rem
        }
    .py-lg-4 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem
        }
    .py-lg-5 {
        padding-top: 3rem;
        padding-bottom: 3rem
        }
    .pt-lg-0 {
        padding-top: 0
        }
    .pt-lg-1 {
        padding-top: 0.25rem
        }
    .pt-lg-2 {
        padding-top: 0.5rem
        }
    .pt-lg-3 {
        padding-top: 1rem
        }
    .pt-lg-4 {
        padding-top: 1.5rem
        }
    .pt-lg-5 {
        padding-top: 3rem
        }
    .pe-lg-0 {
        padding-right: 0
        }
    .pe-lg-1 {
        padding-right: 0.25rem
        }
    .pe-lg-2 {
        padding-right: 0.5rem
        }
    .pe-lg-3 {
        padding-right: 1rem
        }
    .pe-lg-4 {
        padding-right: 1.5rem
        }
    .pe-lg-5 {
        padding-right: 3rem
        }
    .pb-lg-0 {
        padding-bottom: 0
        }
    .pb-lg-1 {
        padding-bottom: 0.25rem
        }
    .pb-lg-2 {
        padding-bottom: 0.5rem
        }
    .pb-lg-3 {
        padding-bottom: 1rem
        }
    .pb-lg-4 {
        padding-bottom: 1.5rem
        }
    .pb-lg-5 {
        padding-bottom: 3rem
        }
    .ps-lg-0 {
        padding-left: 0
        }
    .ps-lg-1 {
        padding-left: 0.25rem
        }
    .ps-lg-2 {
        padding-left: 0.5rem
        }
    .ps-lg-3 {
        padding-left: 1rem
        }
    .ps-lg-4 {
        padding-left: 1.5rem
        }
    .ps-lg-5 {
        padding-left: 3rem
        }
    .gap-lg-0 {
        gap: 0
        }
    .gap-lg-1 {
        gap: 0.25rem
        }
    .gap-lg-2 {
        gap: 0.5rem
        }
    .gap-lg-3 {
        gap: 1rem
        }
    .gap-lg-4 {
        gap: 1.5rem
        }
    .gap-lg-5 {
        gap: 3rem
        }
    .text-lg-start {
        text-align: left
        }
    .text-lg-end {
        text-align: right
        }
    .text-lg-center {
        text-align: center
        }
    }
@media (min-width: 1200px) {
    .float-xl-start {
        float: left
        }
    .float-xl-end {
        float: right
        }
    .float-xl-none {
        float: none
        }
    .d-xl-inline {
        display: inline
        }
    .d-xl-inline-block {
        display: inline-block
        }
    .d-xl-block {
        display: block
        }
    .d-xl-grid {
        display: grid
        }
    .d-xl-table {
        display: table
        }
    .d-xl-table-row {
        display: table-row
        }
    .d-xl-table-cell {
        display: table-cell
        }
    .d-xl-flex {
        display: flex
        }
    .d-xl-inline-flex {
        display: inline-flex
        }
    .d-xl-none {
        display: none
        }
    .flex-xl-fill {
        flex: 1 1 auto
        }
    .flex-xl-row {
        flex-direction: row
        }
    .flex-xl-column {
        flex-direction: column
        }
    .flex-xl-row-reverse {
        flex-direction: row-reverse
        }
    .flex-xl-column-reverse {
        flex-direction: column-reverse
        }
    .flex-xl-grow-0 {
        flex-grow: 0
        }
    .flex-xl-grow-1 {
        flex-grow: 1
        }
    .flex-xl-shrink-0 {
        flex-shrink: 0
        }
    .flex-xl-shrink-1 {
        flex-shrink: 1
        }
    .flex-xl-wrap {
        flex-wrap: wrap
        }
    .flex-xl-nowrap {
        flex-wrap: nowrap
        }
    .flex-xl-wrap-reverse {
        flex-wrap: wrap-reverse
        }
    .justify-content-xl-start {
        justify-content: flex-start
        }
    .justify-content-xl-end {
        justify-content: flex-end
        }
    .justify-content-xl-center {
        justify-content: center
        }
    .justify-content-xl-between {
        justify-content: space-between
        }
    .justify-content-xl-around {
        justify-content: space-around
        }
    .justify-content-xl-evenly {
        justify-content: space-evenly
        }
    .align-items-xl-start {
        align-items: flex-start
        }
    .align-items-xl-end {
        align-items: flex-end
        }
    .align-items-xl-center {
        align-items: center
        }
    .align-items-xl-baseline {
        align-items: baseline
        }
    .align-items-xl-stretch {
        align-items: stretch
        }
    .align-content-xl-start {
        align-content: flex-start
        }
    .align-content-xl-end {
        align-content: flex-end
        }
    .align-content-xl-center {
        align-content: center
        }
    .align-content-xl-between {
        align-content: space-between
        }
    .align-content-xl-around {
        align-content: space-around
        }
    .align-content-xl-stretch {
        align-content: stretch
        }
    .align-self-xl-auto {
        align-self: auto
        }
    .align-self-xl-start {
        align-self: flex-start
        }
    .align-self-xl-end {
        align-self: flex-end
        }
    .align-self-xl-center {
        align-self: center
        }
    .align-self-xl-baseline {
        align-self: baseline
        }
    .align-self-xl-stretch {
        align-self: stretch
        }
    .order-xl-first {
        order: -1
        }
    .order-xl-0 {
        order: 0
        }
    .order-xl-1 {
        order: 1
        }
    .order-xl-2 {
        order: 2
        }
    .order-xl-3 {
        order: 3
        }
    .order-xl-4 {
        order: 4
        }
    .order-xl-5 {
        order: 5
        }
    .order-xl-last {
        order: 6
        }
    .m-xl-0 {
        margin: 0
        }
    .m-xl-1 {
        margin: 0.25rem
        }
    .m-xl-2 {
        margin: 0.5rem
        }
    .m-xl-3 {
        margin: 1rem
        }
    .m-xl-4 {
        margin: 1.5rem
        }
    .m-xl-5 {
        margin: 3rem
        }
    .m-xl-auto {
        margin: auto
        }
    .mx-xl-0 {
        margin-right: 0;
        margin-left: 0
        }
    .mx-xl-1 {
        margin-right: 0.25rem;
        margin-left: 0.25rem
        }
    .mx-xl-2 {
        margin-right: 0.5rem;
        margin-left: 0.5rem
        }
    .mx-xl-3 {
        margin-right: 1rem;
        margin-left: 1rem
        }
    .mx-xl-4 {
        margin-right: 1.5rem;
        margin-left: 1.5rem
        }
    .mx-xl-5 {
        margin-right: 3rem;
        margin-left: 3rem
        }
    .mx-xl-auto {
        margin-right: auto;
        margin-left: auto
        }
    .my-xl-0 {
        margin-top: 0;
        margin-bottom: 0
        }
    .my-xl-1 {
        margin-top: 0.25rem;
        margin-bottom: 0.25rem
        }
    .my-xl-2 {
        margin-top: 0.5rem;
        margin-bottom: 0.5rem
        }
    .my-xl-3 {
        margin-top: 1rem;
        margin-bottom: 1rem
        }
    .my-xl-4 {
        margin-top: 1.5rem;
        margin-bottom: 1.5rem
        }
    .my-xl-5 {
        margin-top: 3rem;
        margin-bottom: 3rem
        }
    .my-xl-auto {
        margin-top: auto;
        margin-bottom: auto
        }
    .mt-xl-0 {
        margin-top: 0
        }
    .mt-xl-1 {
        margin-top: 0.25rem
        }
    .mt-xl-2 {
        margin-top: 0.5rem
        }
    .mt-xl-3 {
        margin-top: 1rem
        }
    .mt-xl-4 {
        margin-top: 1.5rem
        }
    .mt-xl-5 {
        margin-top: 3rem
        }
    .mt-xl-auto {
        margin-top: auto
        }
    .me-xl-0 {
        margin-right: 0
        }
    .me-xl-1 {
        margin-right: 0.25rem
        }
    .me-xl-2 {
        margin-right: 0.5rem
        }
    .me-xl-3 {
        margin-right: 1rem
        }
    .me-xl-4 {
        margin-right: 1.5rem
        }
    .me-xl-5 {
        margin-right: 3rem
        }
    .me-xl-auto {
        margin-right: auto
        }
    .mb-xl-0 {
        margin-bottom: 0
        }
    .mb-xl-1 {
        margin-bottom: 0.25rem
        }
    .mb-xl-2 {
        margin-bottom: 0.5rem
        }
    .mb-xl-3 {
        margin-bottom: 1rem
        }
    .mb-xl-4 {
        margin-bottom: 1.5rem
        }
    .mb-xl-5 {
        margin-bottom: 3rem
        }
    .mb-xl-auto {
        margin-bottom: auto
        }
    .ms-xl-0 {
        margin-left: 0
        }
    .ms-xl-1 {
        margin-left: 0.25rem
        }
    .ms-xl-2 {
        margin-left: 0.5rem
        }
    .ms-xl-3 {
        margin-left: 1rem
        }
    .ms-xl-4 {
        margin-left: 1.5rem
        }
    .ms-xl-5 {
        margin-left: 3rem
        }
    .ms-xl-auto {
        margin-left: auto
        }
    .p-xl-0 {
        padding: 0
        }
    .p-xl-1 {
        padding: 0.25rem
        }
    .p-xl-2 {
        padding: 0.5rem
        }
    .p-xl-3 {
        padding: 1rem
        }
    .p-xl-4 {
        padding: 1.5rem
        }
    .p-xl-5 {
        padding: 3rem
        }
    .px-xl-0 {
        padding-right: 0;
        padding-left: 0
        }
    .px-xl-1 {
        padding-right: 0.25rem;
        padding-left: 0.25rem
        }
    .px-xl-2 {
        padding-right: 0.5rem;
        padding-left: 0.5rem
        }
    .px-xl-3 {
        padding-right: 1rem;
        padding-left: 1rem
        }
    .px-xl-4 {
        padding-right: 1.5rem;
        padding-left: 1.5rem
        }
    .px-xl-5 {
        padding-right: 3rem;
        padding-left: 3rem
        }
    .py-xl-0 {
        padding-top: 0;
        padding-bottom: 0
        }
    .py-xl-1 {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem
        }
    .py-xl-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem
        }
    .py-xl-3 {
        padding-top: 1rem;
        padding-bottom: 1rem
        }
    .py-xl-4 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem
        }
    .py-xl-5 {
        padding-top: 3rem;
        padding-bottom: 3rem
        }
    .pt-xl-0 {
        padding-top: 0
        }
    .pt-xl-1 {
        padding-top: 0.25rem
        }
    .pt-xl-2 {
        padding-top: 0.5rem
        }
    .pt-xl-3 {
        padding-top: 1rem
        }
    .pt-xl-4 {
        padding-top: 1.5rem
        }
    .pt-xl-5 {
        padding-top: 3rem
        }
    .pe-xl-0 {
        padding-right: 0
        }
    .pe-xl-1 {
        padding-right: 0.25rem
        }
    .pe-xl-2 {
        padding-right: 0.5rem
        }
    .pe-xl-3 {
        padding-right: 1rem
        }
    .pe-xl-4 {
        padding-right: 1.5rem
        }
    .pe-xl-5 {
        padding-right: 3rem
        }
    .pb-xl-0 {
        padding-bottom: 0
        }
    .pb-xl-1 {
        padding-bottom: 0.25rem
        }
    .pb-xl-2 {
        padding-bottom: 0.5rem
        }
    .pb-xl-3 {
        padding-bottom: 1rem
        }
    .pb-xl-4 {
        padding-bottom: 1.5rem
        }
    .pb-xl-5 {
        padding-bottom: 3rem
        }
    .ps-xl-0 {
        padding-left: 0
        }
    .ps-xl-1 {
        padding-left: 0.25rem
        }
    .ps-xl-2 {
        padding-left: 0.5rem
        }
    .ps-xl-3 {
        padding-left: 1rem
        }
    .ps-xl-4 {
        padding-left: 1.5rem
        }
    .ps-xl-5 {
        padding-left: 3rem
        }
    .gap-xl-0 {
        gap: 0
        }
    .gap-xl-1 {
        gap: 0.25rem
        }
    .gap-xl-2 {
        gap: 0.5rem
        }
    .gap-xl-3 {
        gap: 1rem
        }
    .gap-xl-4 {
        gap: 1.5rem
        }
    .gap-xl-5 {
        gap: 3rem
        }
    .text-xl-start {
        text-align: left
        }
    .text-xl-end {
        text-align: right
        }
    .text-xl-center {
        text-align: center
        }
    }
@media (min-width: 1400px) {
    .float-xxl-start {
        float: left
        }
    .float-xxl-end {
        float: right
        }
    .float-xxl-none {
        float: none
        }
    .d-xxl-inline {
        display: inline
        }
    .d-xxl-inline-block {
        display: inline-block
        }
    .d-xxl-block {
        display: block
        }
    .d-xxl-grid {
        display: grid
        }
    .d-xxl-table {
        display: table
        }
    .d-xxl-table-row {
        display: table-row
        }
    .d-xxl-table-cell {
        display: table-cell
        }
    .d-xxl-flex {
        display: flex
        }
    .d-xxl-inline-flex {
        display: inline-flex
        }
    .d-xxl-none {
        display: none
        }
    .flex-xxl-fill {
        flex: 1 1 auto
        }
    .flex-xxl-row {
        flex-direction: row
        }
    .flex-xxl-column {
        flex-direction: column
        }
    .flex-xxl-row-reverse {
        flex-direction: row-reverse
        }
    .flex-xxl-column-reverse {
        flex-direction: column-reverse
        }
    .flex-xxl-grow-0 {
        flex-grow: 0
        }
    .flex-xxl-grow-1 {
        flex-grow: 1
        }
    .flex-xxl-shrink-0 {
        flex-shrink: 0
        }
    .flex-xxl-shrink-1 {
        flex-shrink: 1
        }
    .flex-xxl-wrap {
        flex-wrap: wrap
        }
    .flex-xxl-nowrap {
        flex-wrap: nowrap
        }
    .flex-xxl-wrap-reverse {
        flex-wrap: wrap-reverse
        }
    .justify-content-xxl-start {
        justify-content: flex-start
        }
    .justify-content-xxl-end {
        justify-content: flex-end
        }
    .justify-content-xxl-center {
        justify-content: center
        }
    .justify-content-xxl-between {
        justify-content: space-between
        }
    .justify-content-xxl-around {
        justify-content: space-around
        }
    .justify-content-xxl-evenly {
        justify-content: space-evenly
        }
    .align-items-xxl-start {
        align-items: flex-start
        }
    .align-items-xxl-end {
        align-items: flex-end
        }
    .align-items-xxl-center {
        align-items: center
        }
    .align-items-xxl-baseline {
        align-items: baseline
        }
    .align-items-xxl-stretch {
        align-items: stretch
        }
    .align-content-xxl-start {
        align-content: flex-start
        }
    .align-content-xxl-end {
        align-content: flex-end
        }
    .align-content-xxl-center {
        align-content: center
        }
    .align-content-xxl-between {
        align-content: space-between
        }
    .align-content-xxl-around {
        align-content: space-around
        }
    .align-content-xxl-stretch {
        align-content: stretch
        }
    .align-self-xxl-auto {
        align-self: auto
        }
    .align-self-xxl-start {
        align-self: flex-start
        }
    .align-self-xxl-end {
        align-self: flex-end
        }
    .align-self-xxl-center {
        align-self: center
        }
    .align-self-xxl-baseline {
        align-self: baseline
        }
    .align-self-xxl-stretch {
        align-self: stretch
        }
    .order-xxl-first {
        order: -1
        }
    .order-xxl-0 {
        order: 0
        }
    .order-xxl-1 {
        order: 1
        }
    .order-xxl-2 {
        order: 2
        }
    .order-xxl-3 {
        order: 3
        }
    .order-xxl-4 {
        order: 4
        }
    .order-xxl-5 {
        order: 5
        }
    .order-xxl-last {
        order: 6
        }
    .m-xxl-0 {
        margin: 0
        }
    .m-xxl-1 {
        margin: 0.25rem
        }
    .m-xxl-2 {
        margin: 0.5rem
        }
    .m-xxl-3 {
        margin: 1rem
        }
    .m-xxl-4 {
        margin: 1.5rem
        }
    .m-xxl-5 {
        margin: 3rem
        }
    .m-xxl-auto {
        margin: auto
        }
    .mx-xxl-0 {
        margin-right: 0;
        margin-left: 0
        }
    .mx-xxl-1 {
        margin-right: 0.25rem;
        margin-left: 0.25rem
        }
    .mx-xxl-2 {
        margin-right: 0.5rem;
        margin-left: 0.5rem
        }
    .mx-xxl-3 {
        margin-right: 1rem;
        margin-left: 1rem
        }
    .mx-xxl-4 {
        margin-right: 1.5rem;
        margin-left: 1.5rem
        }
    .mx-xxl-5 {
        margin-right: 3rem;
        margin-left: 3rem
        }
    .mx-xxl-auto {
        margin-right: auto;
        margin-left: auto
        }
    .my-xxl-0 {
        margin-top: 0;
        margin-bottom: 0
        }
    .my-xxl-1 {
        margin-top: 0.25rem;
        margin-bottom: 0.25rem
        }
    .my-xxl-2 {
        margin-top: 0.5rem;
        margin-bottom: 0.5rem
        }
    .my-xxl-3 {
        margin-top: 1rem;
        margin-bottom: 1rem
        }
    .my-xxl-4 {
        margin-top: 1.5rem;
        margin-bottom: 1.5rem
        }
    .my-xxl-5 {
        margin-top: 3rem;
        margin-bottom: 3rem
        }
    .my-xxl-auto {
        margin-top: auto;
        margin-bottom: auto
        }
    .mt-xxl-0 {
        margin-top: 0
        }
    .mt-xxl-1 {
        margin-top: 0.25rem
        }
    .mt-xxl-2 {
        margin-top: 0.5rem
        }
    .mt-xxl-3 {
        margin-top: 1rem
        }
    .mt-xxl-4 {
        margin-top: 1.5rem
        }
    .mt-xxl-5 {
        margin-top: 3rem
        }
    .mt-xxl-auto {
        margin-top: auto
        }
    .me-xxl-0 {
        margin-right: 0
        }
    .me-xxl-1 {
        margin-right: 0.25rem
        }
    .me-xxl-2 {
        margin-right: 0.5rem
        }
    .me-xxl-3 {
        margin-right: 1rem
        }
    .me-xxl-4 {
        margin-right: 1.5rem
        }
    .me-xxl-5 {
        margin-right: 3rem
        }
    .me-xxl-auto {
        margin-right: auto
        }
    .mb-xxl-0 {
        margin-bottom: 0
        }
    .mb-xxl-1 {
        margin-bottom: 0.25rem
        }
    .mb-xxl-2 {
        margin-bottom: 0.5rem
        }
    .mb-xxl-3 {
        margin-bottom: 1rem
        }
    .mb-xxl-4 {
        margin-bottom: 1.5rem
        }
    .mb-xxl-5 {
        margin-bottom: 3rem
        }
    .mb-xxl-auto {
        margin-bottom: auto
        }
    .ms-xxl-0 {
        margin-left: 0
        }
    .ms-xxl-1 {
        margin-left: 0.25rem
        }
    .ms-xxl-2 {
        margin-left: 0.5rem
        }
    .ms-xxl-3 {
        margin-left: 1rem
        }
    .ms-xxl-4 {
        margin-left: 1.5rem
        }
    .ms-xxl-5 {
        margin-left: 3rem
        }
    .ms-xxl-auto {
        margin-left: auto
        }
    .p-xxl-0 {
        padding: 0
        }
    .p-xxl-1 {
        padding: 0.25rem
        }
    .p-xxl-2 {
        padding: 0.5rem
        }
    .p-xxl-3 {
        padding: 1rem
        }
    .p-xxl-4 {
        padding: 1.5rem
        }
    .p-xxl-5 {
        padding: 3rem
        }
    .px-xxl-0 {
        padding-right: 0;
        padding-left: 0
        }
    .px-xxl-1 {
        padding-right: 0.25rem;
        padding-left: 0.25rem
        }
    .px-xxl-2 {
        padding-right: 0.5rem;
        padding-left: 0.5rem
        }
    .px-xxl-3 {
        padding-right: 1rem;
        padding-left: 1rem
        }
    .px-xxl-4 {
        padding-right: 1.5rem;
        padding-left: 1.5rem
        }
    .px-xxl-5 {
        padding-right: 3rem;
        padding-left: 3rem
        }
    .py-xxl-0 {
        padding-top: 0;
        padding-bottom: 0
        }
    .py-xxl-1 {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem
        }
    .py-xxl-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem
        }
    .py-xxl-3 {
        padding-top: 1rem;
        padding-bottom: 1rem
        }
    .py-xxl-4 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem
        }
    .py-xxl-5 {
        padding-top: 3rem;
        padding-bottom: 3rem
        }
    .pt-xxl-0 {
        padding-top: 0
        }
    .pt-xxl-1 {
        padding-top: 0.25rem
        }
    .pt-xxl-2 {
        padding-top: 0.5rem
        }
    .pt-xxl-3 {
        padding-top: 1rem
        }
    .pt-xxl-4 {
        padding-top: 1.5rem
        }
    .pt-xxl-5 {
        padding-top: 3rem
        }
    .pe-xxl-0 {
        padding-right: 0
        }
    .pe-xxl-1 {
        padding-right: 0.25rem
        }
    .pe-xxl-2 {
        padding-right: 0.5rem
        }
    .pe-xxl-3 {
        padding-right: 1rem
        }
    .pe-xxl-4 {
        padding-right: 1.5rem
        }
    .pe-xxl-5 {
        padding-right: 3rem
        }
    .pb-xxl-0 {
        padding-bottom: 0
        }
    .pb-xxl-1 {
        padding-bottom: 0.25rem
        }
    .pb-xxl-2 {
        padding-bottom: 0.5rem
        }
    .pb-xxl-3 {
        padding-bottom: 1rem
        }
    .pb-xxl-4 {
        padding-bottom: 1.5rem
        }
    .pb-xxl-5 {
        padding-bottom: 3rem
        }
    .ps-xxl-0 {
        padding-left: 0
        }
    .ps-xxl-1 {
        padding-left: 0.25rem
        }
    .ps-xxl-2 {
        padding-left: 0.5rem
        }
    .ps-xxl-3 {
        padding-left: 1rem
        }
    .ps-xxl-4 {
        padding-left: 1.5rem
        }
    .ps-xxl-5 {
        padding-left: 3rem
        }
    .gap-xxl-0 {
        gap: 0
        }
    .gap-xxl-1 {
        gap: 0.25rem
        }
    .gap-xxl-2 {
        gap: 0.5rem
        }
    .gap-xxl-3 {
        gap: 1rem
        }
    .gap-xxl-4 {
        gap: 1.5rem
        }
    .gap-xxl-5 {
        gap: 3rem
        }
    .text-xxl-start {
        text-align: left
        }
    .text-xxl-end {
        text-align: right
        }
    .text-xxl-center {
        text-align: center
        }
    }
@media (min-width: 1200px) {
    .fs-1 {
        font-size: 2.5rem
        }
    .fs-2 {
        font-size: 2rem
        }
    .fs-3 {
        font-size: 1.75rem
        }
    .fs-4 {
        font-size: 1.5rem
        }
    }
@media print {
    .d-print-inline {
        display: inline
        }
    .d-print-inline-block {
        display: inline-block
        }
    .d-print-block {
        display: block
        }
    .d-print-grid {
        display: grid
        }
    .d-print-table {
        display: table
        }
    .d-print-table-row {
        display: table-row
        }
    .d-print-table-cell {
        display: table-cell
        }
    .d-print-flex {
        display: flex
        }
    .d-print-inline-flex {
        display: inline-flex
        }
    .d-print-none {
        display: none
        }
    }
    </style>
    <style>@media only screen and (max-width: 600px) {
    .card-body {
        width: 100%
        }
    b {
        font-size: 9pt
        }
    p {
        font-size: 9pt
        }
    }
    </style>
</head>

<body style="-webkit-tap-highlight-color:transparent; -webkit-text-size-adjust:100%; margin:0; width:100%" width="100%">
<div class="card card-body" width="570"
     style="background-clip:border-box; display:flex; flex-direction:column; min-width:0; position:relative; word-wrap:break-word; flex:1 1 auto; margin-left:auto; margin-right:auto; overflow-x:hidden; width:570px">
    <div id="invoice" style="margin:5px 5%;">
        <div class="center" style="margin: 20px 0 20px 0;" align="center">
            <a href="https://chargev-ygt.com" target="_blank" style="text-decoration:underline">
                <img src="https://chargev-python-logo.s3.ap-southeast-1.amazonaws.com/chargev-logo-new-no-bg.png"
                     width="60%" style="vertical-align:middle" valign="middle">
            </a>
        </div>
        <p align="center" style="margin-bottom:1rem; margin-top:0">Thanks for subscribing, {{member.first_name}}
            {{member.last_name}}!</p>
        <div>
            <h4 style="font-weight:500; line-height:1.2; margin-bottom:0.5rem; margin-top:0; font-size:calc(1.275rem + 0.3vw); float:left; width:100%"
                width="100%">
                <b style="font-weight:bolder">INVOICE #{{invoice_number}}</b>
            </h4>
            <p style="margin-bottom:1rem; margin-top:0; max-width:100%">
                Date: {{ created_at.strftime("%d-%B-%Y") }} <span style="float:right">{{ created_at.strftime("%H:%M %p") }}</span>
            </p>
            <div class="pull-left" style="float:left">
                <address style="font-style:normal; line-height:inherit; margin-bottom:1rem">
                    <h3 style="font-weight:500; line-height:1.2; margin-bottom:0.5rem; margin-top:0; font-size:calc(1.3rem + 0.6vw); color:#F2651F">
                        <b style="font-weight:bolder">
                            {{member.first_name}} {{member.last_name}}
                        </b>
                    </h3>
                    <p class="text-muted m-l-5" style="margin-bottom:1rem; margin-top:0; color:#6c757d">
                        <img src="https://chargev-python-logo.s3.ap-southeast-1.amazonaws.com/mail_logo.png"
                             width="16px" style="vertical-align:middle" valign="middle">
                        {{member.user.email}}
                        <br>
                        <img src="https://chargev-python-logo.s3.ap-southeast-1.amazonaws.com/phone_logo.png"
                             width="16px" style="vertical-align:middle" valign="middle">
                        {{member.user.phone_number}}
                    </p>
                </address>
            </div>
        </div>
        <div class="table-responsive m-t-40" style="-webkit-overflow-scrolling:touch; overflow-x:auto; clear:both">
            <table class="table table-borderless"
                   style="border-collapse:collapse !important; caption-side:bottom; margin-bottom:1rem; vertical-align:top; width:100%"
                   width="100%" cellpadding="0" cellspacing="0" role="presentation" valign="top">
                <tr style="border-color:inherit; border-style:solid; border-width:0">
                    <th class="text-left"
                        style="text-align:left; border-color:inherit; border-style:solid; border-width:0; border-bottom:2px solid black; border-top:2px solid black; font-size:9pt; padding:10px 0"
                        align="left">
                        Description
                    </th>
                    <th class="text-right"
                        style="text-align:-webkit-match-parent; border-color:inherit; border-style:solid; border-width:0; border-bottom:2px solid black; border-top:2px solid black; font-size:9pt; padding:10px 0; width:50%"
                        align="-webkit-match-parent"></th>
                    <th class="text-right"
                        style="text-align:-webkit-match-parent; border-color:inherit; border-style:solid; border-width:0; border-bottom:2px solid black; border-top:2px solid black; font-size:9pt; padding:10px 0"
                        align="right">
                        Total amount
                    </th>
                </tr>
                <tr style="border-color:inherit; border-style:solid; border-width:0">
                    <td class="text-left"
                        style="border-color:inherit; border-style:solid; border-width:0; padding:10px 0; text-align:left; width:30%"
                        align="left" width="30%">
                        <b style="font-weight:bolder">Billing Details</b>
                    </td>
                </tr>
                <tr style="border-color:inherit; border-style:solid; border-width:0">
                    <td class="text-left"
                        style="border-color:inherit; border-style:solid; border-width:0; font-size:9pt; padding:10px 0; text-align:left; width:30%"
                        align="left" width="30%">
                        Product Name:
                    </td>
                    <td style="border-color:inherit; border-style:solid; border-width:0"></td>
                    <td align="right" style="border-color:inherit; border-style:solid; border-width:0; font-size:9pt; width:33.34%">
                        {{subscription_order.subscription.subscription_plan.name }}
                    </td>
                </tr>
                {% for item in subscription_order.payable_fees %}
                <tr style="border-color:inherit; border-style:solid; border-width:0">
                    <td class="text-left"
                        style="border-color:inherit; border-style:solid; border-width:0; font-size:9pt; padding:10px 0; text-align:left; width:30%"
                        align="left" width="30%">
                        {{item.name }}:
                    </td>
                    <td style="border-color:inherit; border-style:solid; border-width:0"></td>
                    <td align="right" style="border-color:inherit; border-style:solid; border-width:0; font-size:9pt; width:33.34%">
                        {{currency.value}} {{"{:.2f}".format(item.amount)}}
                    </td>
                </tr>
                {% endfor %}
                {% if subscription_order.discount_amount %}
                <tr style="border-color:inherit; border-style:solid; border-width:0">
                    <td class="text-left"
                        style="border-color:inherit; border-style:solid; border-width:0; font-size:9pt; padding:10px 0; text-align:left; width:30%"
                        align="left" width="30%">
                        Discount:
                    </td>
                    <td style="border-color:inherit; border-style:solid; border-width:0"></td>
                    <td align="right" style="border-color:inherit; border-style:solid; border-width:0; font-size:9pt; width:33.34%">
                        -{{currency.value}} {{"{:.2f}".format(subscription_order.discount_amount | float)}}
                    </td>
                </tr>
                {% endif %}
                <tr style="border-color:inherit; border-style:solid; border-width:0">
                    <td style="border-color:inherit; border-style:solid; border-width:0; border-top:2px solid black; font-size:9pt; margin:10px 0 10px 0; padding:10px 0 10px 0">
                    </td>
                    <td colspan="2" align="right"
                        style="border-color:inherit; border-style:solid; border-width:0; border-top:2px solid black; font-size:9pt; margin:10px 0 10px 0; padding:10px 0 10px 0">
                        <b style="font-weight:bolder">Total: {{currency.value}}
                            {{
                                "{:.2f}".format(
                                    (subscription_order.payable_fees | map(attribute='amount') | sum) 
                                    - subscription_order.discount_amount | float
                                )
                            }}
                        </b>
                    </td>
                </tr>
            </table>
        </div>
        <hr style="border:0; border-top:2.5px solid; color:black; margin:0; opacity:0; width:100%" width="100%">
        <div class="m-t-30 text-center" style="text-align:center; font-size:9pt; margin:30px 0" align="center">
            <p style="margin-bottom:1rem; margin-top:0; text-align:center" align="center">
                Powered by
            </p>
            <p style="margin-bottom:1rem; margin-top:0; font-size:9pt; text-align:center" align="center">
                <a href="https://chargev-ygt.com" style="text-decoration:underline">chargEV</a>
            </p>
        </div>
    </div>
</div>


</body>
</html>