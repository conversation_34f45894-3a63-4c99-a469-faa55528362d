"""146

Revision ID: 47108ae7b6c5
Revises: 0d0f5fddbacf
Create Date: 2024-12-24 08:46:26.634090

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '47108ae7b6c5'
down_revision = '0d0f5fddbacf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_subscription_operator_plan',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('operator_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('ac_price', sa.Numeric(scale=5), nullable=True),
    sa.Column('dc_price', sa.Numeric(scale=5), nullable=True),
    sa.Column('remark', sa.Text(), nullable=True),
    sa.Column('subscription_plan_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['operator_id'], ['main_operator.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['subscription_plan_id'], ['main_subscription_plan.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_subscription_tariff_plan',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('tariff_tag', sa.String(), nullable=True),
    sa.Column('ac_price', sa.Numeric(scale=5), nullable=True),
    sa.Column('dc_price', sa.Numeric(scale=5), nullable=True),
    sa.Column('remark', sa.Text(), nullable=True),
    sa.Column('subscription_plan_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['subscription_plan_id'], ['main_subscription_plan.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_subscription_tariff_plan')
    op.drop_table('main_subscription_operator_plan')
    # ### end Alembic commands ###
