"""36

Revision ID: f3dec22f6779
Revises: f40d3de36fe5
Create Date: 2023-05-17 13:57:18.092132

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f3dec22f6779'
down_revision = 'f40d3de36fe5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_charging_session_bill', sa.Column('invoice_number', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_charging_session_bill', 'invoice_number')
    # ### end Alembic commands ###
