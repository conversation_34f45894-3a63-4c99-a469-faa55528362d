"""87

Revision ID: 2c6cf9b90045
Revises: 7518bf679b3b
Create Date: 2024-04-23 14:03:32.058002

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2c6cf9b90045'
down_revision = '7518bf679b3b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_payment_gateway_reconcilation', sa.Column('transaction_cost', sa.Float(), nullable=True))
    op.add_column('main_payment_gateway_reconcilation', sa.Column('billing_mobile_number', sa.String(), nullable=True))
    op.add_column('main_payment_gateway_reconcilation', sa.Column('transaction_fee', sa.Float(), nullable=True))
    op.add_column('main_payment_gateway_reconcilation', sa.Column('expiry_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('main_payment_gateway_reconcilation', sa.Column('status_description', sa.String(), nullable=True))
    op.add_column('main_payment_gateway_reconcilation', sa.Column('paid_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('main_payment_gateway_reconcilation', sa.Column('capture_ref_id', sa.String(), nullable=True))
    op.add_column('main_payment_gateway_reconcilation', sa.Column('refund_ref_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_payment_gateway_reconcilation', 'refund_ref_id')
    op.drop_column('main_payment_gateway_reconcilation', 'capture_ref_id')
    op.drop_column('main_payment_gateway_reconcilation', 'paid_date')
    op.drop_column('main_payment_gateway_reconcilation', 'status_description')
    op.drop_column('main_payment_gateway_reconcilation', 'expiry_date')
    op.drop_column('main_payment_gateway_reconcilation', 'transaction_fee')
    op.drop_column('main_payment_gateway_reconcilation', 'billing_mobile_number')
    op.drop_column('main_payment_gateway_reconcilation', 'transaction_cost')
    # ### end Alembic commands ###