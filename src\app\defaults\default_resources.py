# pylint: disable=too-many-lines
STAFF_ALLOWED_RESOURCES = [
    # Resources
    {
        'name': 'Allows to view/create resource',
        'path': r'/apollo-main/api/v1/csms/resources/resource/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/create resource',
        'path': r'/apollo-main/api/v1/csms/resources/resource/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create resource group',
        'path': r'/apollo-main/api/v1/csms/resources/resource_server/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/create resource group',
        'path': r'/apollo-main/api/v1/csms/resources/resource_server/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Organization
    {
        'name': 'Allows to view/create organization',
        'path': r'/apollo-main/api/v1/csms/organization/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/create organization',
        'path': r'/apollo-main/api/v1/csms/organization/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create admin',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/staff/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete admin',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/staff/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create staff',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/sub_staff/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete staff',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/sub_staff/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create customer',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/regular/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete customer',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/regular/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },

    {
        'name': 'Allows to add a role to any user',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/roles/.+/add/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to delete a role from any user',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/roles/.+/remove/?$',
        'scope': 'post',
        'require_auth_token': True,
    },

    {
        'name': 'Allows to view/create roles',
        'path': r'/apollo-main/api/v1/csms/organization/.+/role/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete roles',
        'path': r'/apollo-main/api/v1/csms/organization/.+/role/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },

    {
        'name': 'Allows to add a resource access to role',
        'path': r'/apollo-main/api/v1/csms/organization/.+/role/.+/add/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to remove a resource access from role',
        'path': r'/apollo-main/api/v1/csms/organization/.+/role/.+/remove/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to update profile',
        'path': r'/apollo-main/api/v1/organization/update-profile/?$',
        'scope': 'patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to update profile',
        'path': r'/apollo-main/api/v1/organization/update-profile/.+',
        'scope': 'patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to regenerate api key',
        'path': r'/apollo-main/api/v1/csms/organization/.+/regenerate-api-key',
        'scope': 'patch',
        'require_auth_token': True,
    },
    # User
    {
        'name': 'Allows to view users',
        'path': r'/apollo-main/api/v1/csms/user/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # User v2
    {
        'name': 'Allows to delete users account',
        'path': r'/apollo-main/api/v1/user/terminate_account',
        'scope': 'delete',
        'require_auth_token': True,
    },
    # Invites
    {
        'name': 'Allows to view/create invite',
        'path': r'/apollo-main/api/v1/csms/invites/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete invite',
        'path': r'/apollo-main/api/v1/csms/invites/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },
    # Favorite Charge Locations
    {
        'name': 'Allows to view/update favorite charge locations',
        'path': r'/apollo-main/api/v1/favorite_locations/?$',
        'scope': 'get,post,put',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update favorite charge locations',
        'path': r'/apollo-main/api/v1/favorite_locations/ocpi?$',
        'scope': 'get,post,put',
        'require_auth_token': True,
    },
    # Payments
    {
        'name': 'Allows to Manage payment requests',
        'path': r'/apollo-main/api/v1/csms/payment/payment-request/.+',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to Manage payment response',
        'path': r'/apollo-main/api/v1/csms/payment/payment-callback/?$',
        'scope': 'get,post',
        'require_auth_token': False,
    },
    # Credit Cards
    {
        'name': 'Allows to view/add credit card',
        'path': r'/apollo-main/api/v1/csms/cc/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete credit cards',
        'path': r'/apollo-main/api/v1/csms/cc/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # V2 Credit Cards
    {
        'name': 'Allows to view/add credit card',
        'path': r'/apollo-main/api/v1/cc/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete credit cards',
        'path': r'/apollo-main/api/v1/cc/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # ID Tags
    {
        'name': 'Allows to view and create ID tags',
        'path': r'/apollo-main/api/v1/csms/id_tag/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete ID tags',
        'path': r'/apollo-main/api/v1/csms/id_tag/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Wallet
    {
        'name': 'Allows to view/create wallet',
        'path': r'/apollo-main/api/v1/csms/wallet/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to manually topup to a wallet without redeemption',
        'path': r'/apollo-main/api/v1/csms/wallet/manual-topup/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to manually deudct from a wallet',
        'path': r'/apollo-main/api/v1/csms/wallet/manual-deduct/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete wallet',
        'path': r'/apollo-main/api/v1/csms/wallet/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Promotion Code
    {
        'name': 'Allows to assign/remove a promo code to any users',
        'path': r'/apollo-main/api/v1/csms/promo_code/.*/remaining/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add promo code',
        'path': r'/apollo-main/api/v1/csms/promo_code/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Full-access to manage promo code',
        'path': r'/apollo-main/api/v1/csms/promo_code/.+',
        'scope': 'delete,patch,get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/edit promo code',
        'path': r'/apollo-main/api/v1/csms/promo_code/.+',
        'scope': 'get,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to assign/remove a promo code to any organizations',
        'path': r'/apollo-main/api/v1/csms/promo_code/.*/organizations/?$',
        'scope': 'delete,patch,get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to assign/remove a promo code to any charge points',
        'path': r'/apollo-main/api/v1/csms/promo_code/.*/charge_points/?$',
        'scope': 'delete,patch,get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to assign/remove a promo code to any members',
        'path': r'/apollo-main/api/v1/csms/promo_code/.*/members/?$',
        'scope': 'delete,patch,get,post',
        'require_auth_token': True,
    },
    # Vehicle
    {
        'name': 'Allows to view/add vehicle',
        'path': r'/apollo-main/api/v1/csms/vehicle/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Full-access to manage vehicle',
        'path': r'/apollo-main/api/v1/csms/vehicle/.+',
        'scope': 'get,post,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view member vehicle',
        'path': r'/apollo-main/api/v1/csms/vehicle/membership/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to add vehicle',
        'path': r'/apollo-main/api/v1/csms/vehicle/membership/.+',
        'scope': 'post,delete',
        'require_auth_token': True,
    },
    # Operator
    {
        'name': 'Allows to view/create operators',
        'path': r'/apollo-main/api/v1/csms/operator/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete operators',
        'path': r'/apollo-main/api/v1/csms/operator/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to add/remove charge point to/from operator',
        'path': r'/apollo-main/api/v1/csms/operator/.+/charge_point/?$',
        'scope': 'post,delete',
        'require_auth_token': True,
    },
    # Invoice
    {
        'name': 'Allows to view an invoice',
        'path': r'/apollo-main/api/v1/csms/invoice/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create an invoice',
        'path': r'/apollo-main/api/v1/csms/invoice/.+',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view an invoice',
        'path': r'/apollo-main/api/v1/invoice/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view an invoice',
        'path': r'/apollo-main/api/v1/invoice/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Charger
    {
        'name': 'Allows to view/create charge point',
        'path': r'/apollo-main/api/v1/csms/charger/charge_point/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view charge point small',
        'path': r'/apollo-main/api/v1/csms/charger/charge_point/small/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Full-access to manage charge points',
        'path': r'/apollo-main/api/v1/csms/charger/charge_point/.+',
        'scope': 'post,get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to start charging',
        'path': r'/apollo-main/api/v1/csms/charger/start-charging/.+',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to start charging',
        'path': r'/apollo-main/api/v1/charger/start-charging/.+',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to stop charging',
        'path': r'/apollo-main/api/v1/charger/stop-charging/.+',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to get charge usage',
        'path': r'/apollo-main/api/v1/charger/charge-usage/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create charge point',
        'path': r'/apollo-main/api/v1/charger/charge_point/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Full-access to manage charge points',
        'path': r'/apollo-main/api/v1/charger/charge_point/.+',
        'scope': 'post,get,delete,patch',
        'require_auth_token': True,
    },
    # Operation
    {
        'name': 'Allows to view charger operations requests',
        'path': r'/apollo-main/api/v1/csms/charger/operation/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows tto view an individual charger operation',
        'path': r'/apollo-main/api/v1/csms/charger/operation/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Charging
    {
        'name': 'Allows to view all charging session history',
        'path': r'/apollo-main/api/v1/csms/charger/charging/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view individual charging session',
        'path': r'/apollo-main/api/v1/csms/charger/charging/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view all charging session history',
        'path': r'/apollo-main/api/v1/charger/charging/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view individual charging session',
        'path': r'/apollo-main/api/v1/charger/charging/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get curent charging session from ruby',
        'path': r'/apollo-main/api/v1/charger/current-session/ruby/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Location
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/csms/charger/location/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/csms/charger/location/.+',
        'scope': 'get,patch,delete',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to set operating hours',
        'path': r'/apollo-main/api/v1/csms/charger/location/set-hours/.+',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/charger/location/?$',
        'scope': 'get,post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/charger/location/.+',
        'scope': 'get,patch,delete',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/charger/location/marker/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/charger/location/marker/.+',
        'scope': 'get,patch,delete',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/location/?$',
        'scope': 'get,post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/location/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/location/marker/?$',
        'scope': 'get,post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/location/marker/.+',
        'scope': 'get,patch,delete',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view all (including OCPI) location',
        'path': r'/apollo-main/api/v1/location/ocpi/?$',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view (including OCPI) location',
        'path': r'/apollo-main/api/v1/location/ocpi/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view all (including OCPI) simplfied location',
        'path': r'/apollo-main/api/v1/location/marker/?$',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view (including OCPI) simplfied location',
        'path': r'/apollo-main/api/v1/location/ocpi/marker/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view (including OCPI) location by serial number',
        'path': r'/apollo-main/api/v1/location/ocpi/serial/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    # Log
    {
        'name': 'Allows to view OCCP logs',
        'path': r'/apollo-main/api/v1/csms/charger/log/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Chargepoint Connector
    {
        'name': 'Allows to view/create a connector',
        'path': r'/apollo-main/api/v1/csms/charger/connector/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Full-access to manage connectors',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+',
        'scope': 'post,get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create a connector',
        'path': r'/apollo-main/api/v1/csms/charger/ocpi/location/?$',
        'scope': 'get,post,put',
        'require_auth_token': False,
    },
    {
        'name': 'Full-access to manage connectors',
        'path': r'/apollo-main/api/v1/csms/charger/ocpi/location/.+',
        'scope': 'post,get,delete,patch,put',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view charge point connectors',
        'path': r'/apollo-main/api/v1/csms/charger/connector/charge_point/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to send OCPP message ',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+/ocpp/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to reserve connector',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+/reserve/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to cancel connector reservation',
        'path': r'/apollo-main/api/v1/csms/charger/connector/cancel_reservation/.+/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to start connector remotely',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+/remote-start/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to stop connector remotely',
        'path': r'/apollo-main/api/v1/csms/charger/connector/remote-stop/.+/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get charge point based on serial number',
        'path': r'/apollo-main/api/v1/charger/connector/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get charge point by id',
        'path': r'/apollo-main/api/v1/charger/connector/charge_points/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get charge point by id',
        'path': r'/apollo-main/api/v1/charger/connector/charge_points/discount/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get charge point by connector id',
        'path': r'/apollo-main/api/v1/charger/charging/charging_sessions/connector/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get charge point by serial number',
        'path': r'/apollo-main/api/v1/charger/charging/charging_sessions/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get start charging from ruby',
        'path': r'/apollo-main/api/v1/charger/start-charging/ruby/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get billing request from ruby',
        'path': r'/apollo-main/api/v1/charger/billing-request/ruby/.+',
        'scope': 'post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get start charging from ruby',
        'path': r'/apollo-main/api/v1/charger/stop-charging/ruby/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get ocpp status from ruby',
        'path': r'/apollo-main/api/v1/charger/ocpp-status/ruby/serial-number/.+',
        'scope': 'post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get ocpp status from ruby',
        'path': r'/apollo-main/api/v1/charger/ocpp-status/ruby/serial-number/?$',
        'scope': 'post',
        'require_auth_token': False,
    },
    # Connector Type
    {
        'name': 'Allows to view/create a connector types',
        'path': r'/apollo-main/api/v1/csms/charger/connectortype/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete connector types',
        'path': r'/apollo-main/api/v1/csms/charger/connectortype/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions
    {
        'name': 'Allows to view/add subscriptions',
        'path': r'/apollo-main/api/v1/csms/subscriptions/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions',
        'path': r'/apollo-main/api/v1/csms/subscriptions/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Member Subscriptions
    {
        'name': 'Allows to view/add subscriptions',
        'path': r'/apollo-main/api/v1/csms/subscriptions/member/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions',
        'path': r'/apollo-main/api/v1/csms/subscriptions/member/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions Plans
    {
        'name': 'Allows to view/add subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/plan/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/plan/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions Custom Plans
    {
        'name': 'Allows to view/add subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/custom-plan/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/custom-plan/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions Card
    {
        'name': 'Allows to view/add subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/card/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/card/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions Invitation
    {
        'name': 'Allows to view/add subscriptions invitations',
        'path': r'/apollo-main/api/v1/csms/subscriptions/invitation/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/invitation/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions connector pricing
    {
        'name': 'Allows to view connector pricing',
        'path': r'/apollo-main/api/v1/csms/subscriptions/connector-pricing/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/connector-pricing/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Users Subscription
    {
        'name': 'Allows to view/add subscription plan',
        'path': r'/apollo-main/api/v1/subscriptions/plan/.+',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add subscription plan',
        'path': r'/apollo-main/api/v1/subscriptions/plan/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/member/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/member/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/register-subscription/.+',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/register-subscription/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add subscriptions invitations',
        'path': r'/apollo-main/api/v1/subscriptions/invitation/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions cards',
        'path': r'/apollo-main/api/v1/subscriptions/invitation/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows mobile app to get charge point discounted price',
        'path': r'/apollo-main/api/v1/charger/location/discount/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows mobile app user to view their balance',
        'path': r'/apollo-main/api/v1/wallet/balance',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user to create packages',
        'path': r'/apollo-main/api/v1/csms/wallet/packages/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user to view specific and update packages',
        'path': r'/apollo-main/api/v1/csms/wallet/packages/.+',
        'scope': 'get,patch,delete',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user to download packages',
        'path': r'/apollo-main/api/v1/csms/wallet/packages/download/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user generate invitation code',
        'path': r'/apollo-main/api/v1/csms/wallet/packages/invite/bulk/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user generate invitation code',
        'path': r'/apollo-main/api/v1/csms/wallet/packages/invite/download/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user get, delete and update invitation code',
        'path': r'/apollo-main/api/v1/csms/wallet/packages/invite/.+',
        'scope': 'get,patch,delete',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user get wallet transactions by wallet id',
        'path': r'/apollo-main/api/v1/csms/wallet/.+/transactions/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user get wallet transactions',
        'path': r'/apollo-main/api/v1/csms/wallet/transactions/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    # API Key
    {
        'name': 'Allows to create/get API Key',
        'path': r'/apollo-main/api/v1/csms/organization_authorization_service/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Full-access to manage API Key',
        'path': r'/apollo-main/api/v1/csms/organization_authorization_service/.+',
        'scope': 'delete,patch,get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/edit API Key',
        'path': r'/apollo-main/api/v1/csms/organization_authorization_service/.+',
        'scope': 'get,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to regenerate API Key',
        'path': r'/apollo-main/api/v1/csms/organization_authorization_service/.*/regenerate',
        'scope': 'post',
        'require_auth_token': True,
    },
]

SUB_STAFF_ALLOWED_RESOURCES = [
    # Resources
    {
        'name': 'Allows to view/create resource',
        'path': r'/apollo-main/api/v1/csms/resources/resource/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/create resource',
        'path': r'/apollo-main/api/v1/csms/resources/resource/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create resource group',
        'path': r'/apollo-main/api/v1/csms/resources/resource_server/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/create resource group',
        'path': r'/apollo-main/api/v1/csms/resources/resource_server/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Organization
    {
        'name': 'Allows to view/create organization',
        'path': r'/apollo-main/api/v1/csms/organization/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/create organization',
        'path': r'/apollo-main/api/v1/csms/organization/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create admin',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/staff/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete admin',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/staff/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create staff',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/sub_staff/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete staff',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/sub_staff/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create customer',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/regular/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete customer',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/regular/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create custom user',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/custom/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete custom user',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/custom/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },

    {
        'name': 'Allows to add a role to any user',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/roles/.+/add/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to delete a role from any user',
        'path': r'/apollo-main/api/v1/csms/organization/.+/membership/roles/.+/remove/?$',
        'scope': 'post',
        'require_auth_token': True,
    },

    {
        'name': 'Allows to view/create roles',
        'path': r'/apollo-main/api/v1/csms/organization/.+/role/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete roles',
        'path': r'/apollo-main/api/v1/csms/organization/.+/role/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },

    {
        'name': 'Allows to add a resource access to role',
        'path': r'/apollo-main/api/v1/csms/organization/.+/role/.+/add/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to remove a resource access from role',
        'path': r'/apollo-main/api/v1/csms/organization/.+/role/.+/remove/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to update profile',
        'path': r'/apollo-main/api/v1/organization/update-profile/?$',
        'scope': 'patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to update profile',
        'path': r'/apollo-main/api/v1/organization/update-profile/.+',
        'scope': 'patch',
        'require_auth_token': True,
    },
    # User
    {
        'name': 'Allows to view users',
        'path': r'/apollo-main/api/v1/csms/user/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # User v2
    {
        'name': 'Allows to delete users account',
        'path': r'/apollo-main/api/v1/user/terminate_account',
        'scope': 'delete',
        'require_auth_token': True,
    },
    # Invites
    {
        'name': 'Allows to view/create invite',
        'path': r'/apollo-main/api/v1/csms/invites/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete invite',
        'path': r'/apollo-main/api/v1/csms/invites/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },
    # ID Tags
    {
        'name': 'Allows to view and create ID tags',
        'path': r'/apollo-main/api/v1/csms/id_tag/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete ID tags',
        'path': r'/apollo-main/api/v1/csms/id_tag/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Vehicle
    {
        'name': 'Allows to view/add vehicle',
        'path': r'/apollo-main/api/v1/csms/vehicle/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Full-access to manage vehicle',
        'path': r'/apollo-main/api/v1/csms/vehicle/.+',
        'scope': 'get,post,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view member vehicle',
        'path': r'/apollo-main/api/v1/csms/vehicle/membership/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to add vehicle',
        'path': r'/apollo-main/api/v1/csms/vehicle/membership/.+',
        'scope': 'post,delete',
        'require_auth_token': True,
    },
    # Operator
    {
        'name': 'Allows to view/create operators',
        'path': r'/apollo-main/api/v1/csms/operator/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete operators',
        'path': r'/apollo-main/api/v1/csms/operator/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to add/remove charge point to/from operator',
        'path': r'/apollo-main/api/v1/csms/operator/.+/charge_point/?$',
        'scope': 'post,delete',
        'require_auth_token': True,
    },
    # Charger
    {
        'name': 'Allows to view/create charge point',
        'path': r'/apollo-main/api/v1/csms/charger/charge_point/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view charge point small',
        'path': r'/apollo-main/api/v1/csms/charger/charge_point/small/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Full-access to manage charge points',
        'path': r'/apollo-main/api/v1/csms/charger/charge_point/.+',
        'scope': 'post,get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create charge point',
        'path': r'/apollo-main/api/v1/charger/charge_point/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Full-access to manage charge points',
        'path': r'/apollo-main/api/v1/charger/charge_point/.+',
        'scope': 'post,get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to start charging',
        'path': r'/apollo-main/api/v1/csms/charger/start-charging/.+',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to start charging',
        'path': r'/apollo-main/api/v1/charger/start-charging/.+',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to stop charging',
        'path': r'/apollo-main/api/v1/charger/stop-charging/.+',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to get charge usage',
        'path': r'/apollo-main/api/v1/charger/charge-usage/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Operation
    {
        'name': 'Allows to view charger operations requests',
        'path': r'/apollo-main/api/v1/csms/charger/operation/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows tto view an individual charger operation',
        'path': r'/apollo-main/api/v1/csms/charger/operation/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Charging
    {
        'name': 'Allows to view all charging session history',
        'path': r'/apollo-main/api/v1/csms/charger/charging/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view individual charging session',
        'path': r'/apollo-main/api/v1/csms/charger/charging/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view all charging session history',
        'path': r'/apollo-main/api/v1/csms/charger/charging/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view all charging session history',
        'path': r'/apollo-main/api/v1/charger/charging/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view individual charging session',
        'path': r'/apollo-main/api/v1/csms/charger/charging/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view individual charging session',
        'path': r'/apollo-main/api/v1/charger/charging/charging_sessions/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view individual charging session',
        'path': r'/apollo-main/api/v1/charger/charging/charging_sessions/connector/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get curent charging session from ruby',
        'path': r'/apollo-main/api/v1/charger/current-session/ruby/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Location
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/csms/charger/location/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/csms/charger/location/.+',
        'scope': 'get,patch,delete',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/charger/location/?$',
        'scope': 'get,post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/charger/location/.+',
        'scope': 'get,patch,delete',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/charger/location/marker/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/charger/location/marker/.+',
        'scope': 'get,patch,delete',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/location/marker/?$',
        'scope': 'get,post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/location/marker/.+',
        'scope': 'get,patch,delete',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to set operating hours',
        'path': r'/apollo-main/api/v1/csms/charger/location/set-hours/.+',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/location/?$',
        'scope': 'get,post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/location/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view all (including OCPI) location',
        'path': r'/apollo-main/api/v1/location/ocpi/?$',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view (including OCPI) location',
        'path': r'/apollo-main/api/v1/location/ocpi/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view all (including OCPI) simplfied location',
        'path': r'/apollo-main/api/v1/location/marker/?$',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view (including OCPI) simplfied location',
        'path': r'/apollo-main/api/v1/location/ocpi/marker/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view (including OCPI) location by serial number',
        'path': r'/apollo-main/api/v1/location/ocpi/serial/.+',
        'scope': 'get',
        'require_auth_token': False,
    },

    # Log
    {
        'name': 'Allows to view OCCP logs',
        'path': r'/apollo-main/api/v1/csms/charger/log/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Chargepoint Connector
    {
        'name': 'Allows to view/create a connector',
        'path': r'/apollo-main/api/v1/csms/charger/connector/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Full-access to manage connectors',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+',
        'scope': 'post,get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create a connector',
        'path': r'/apollo-main/api/v1/csms/charger/ocpi/location/?$',
        'scope': 'get,post,put',
        'require_auth_token': False,
    },
    {
        'name': 'Full-access to manage connectors',
        'path': r'/apollo-main/api/v1/csms/charger/ocpi/location/.+',
        'scope': 'post,get,delete,patch,put',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view charge point connectors',
        'path': r'/apollo-main/api/v1/csms/charger/connector/charge_point/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to send OCPP message ',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+/ocpp/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to reserve connector',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+/reserve/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to cancel connector reservation',
        'path': r'/apollo-main/api/v1/csms/charger/connector/cancel_reservation/.+/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to start connector remotely',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+/remote-start/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to view all charging session history',
        'path': r'/apollo-main/api/v1/csms/charger/charging-invoices/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    # User v2
    {
        'name': 'Allows to delete users account',
        'path': r'/apollo-main/api/v1/user/terminate_account',
        'scope': 'delete',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to stop connector remotely',
        'path': r'/apollo-main/api/v1/csms/charger/connector/remote-stop/.+/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get charge point based on serial number',
        'path': r'/apollo-main/api/v1/charger/connector/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get charge point by id',
        'path': r'/apollo-main/api/v1/charger/connector/charge_points/.+',
        'scope': 'post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get charge point by id',
        'path': r'/apollo-main/api/v1/charger/connector/charge_points/discount/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get start charging from ruby',
        'path': r'/apollo-main/api/v1/charger/start-charging/ruby/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get billing request from ruby',
        'path': r'/apollo-main/api/v1/charger/billing-request/ruby/.+',
        'scope': 'post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get start charging from ruby',
        'path': r'/apollo-main/api/v1/charger/stop-charging/ruby/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get ocpp status from ruby',
        'path': r'/apollo-main/api/v1/charger/ocpp-status/ruby/serial-number/.+',
        'scope': 'post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get ocpp status from ruby',
        'path': r'/apollo-main/api/v1/charger/ocpp-status/ruby/serial-number/?$',
        'scope': 'post',
        'require_auth_token': False,
    },
    # Connector Type
    {
        'name': 'Allows to view/create a connector types',
        'path': r'/apollo-main/api/v1/csms/charger/connectortype/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete connector types',
        'path': r'/apollo-main/api/v1/csms/charger/connectortype/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions
    {
        'name': 'Allows to view/add subscriptions',
        'path': r'/apollo-main/api/v1/csms/subscriptions/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions',
        'path': r'/apollo-main/api/v1/csms/subscriptions/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Member Subscriptions
    {
        'name': 'Allows to view/add subscriptions',
        'path': r'/apollo-main/api/v1/csms/subscriptions/member/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions',
        'path': r'/apollo-main/api/v1/csms/subscriptions/member/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Member Subscriptions by Admin
    {
        'name': 'Allows to view/add subscriptions by Admin',
        'path': r'/apollo-main/api/v1/csms/subscriptions/admin/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions by Admin',
        'path': r'/apollo-main/api/v1/csms/subscriptions/admin/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions Plans
    {
        'name': 'Allows to view/add subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/plan/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/plan/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions Custom Plans
    {
        'name': 'Allows to view/add subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/custom-plan/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/custom-plan/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions Card
    {
        'name': 'Allows to view/add subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/card/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/card/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions Invitation
    {
        'name': 'Allows to view/add subscriptions invitations',
        'path': r'/apollo-main/api/v1/csms/subscriptions/invitation/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/invitation/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions connector pricing
    {
        'name': 'Allows to view connector pricing',
        'path': r'/apollo-main/api/v1/csms/subscriptions/connector-pricing/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/connector-pricing/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Users Subscription
    {
        'name': 'Allows to view/add subscription plan',
        'path': r'/apollo-main/api/v1/subscriptions/plan/.+',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add subscription plan',
        'path': r'/apollo-main/api/v1/subscriptions/plan/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/member/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/member/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/register-subscription/.+',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/register-subscription/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add subscriptions invitations',
        'path': r'/apollo-main/api/v1/subscriptions/invitation/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions cards',
        'path': r'/apollo-main/api/v1/subscriptions/invitation/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows mobile app to get charge point discounted price',
        'path': r'/apollo-main/api/v1/charger/location/discount/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows mobile app user to view their balance',
        'path': r'/apollo-main/api/v1/wallet/balance',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user to create packages',
        'path': r'/apollo-main/api/v1/csms/wallet/packages/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user to view specific and update packages',
        'path': r'/apollo-main/api/v1/csms/wallet/packages/.+',
        'scope': 'get,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user to download packages',
        'path': r'/apollo-main/api/v1/csms/wallet/packages/download/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user generate invitation code',
        'path': r'/apollo-main/api/v1/csms/wallet/packages/invite/bulk/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user generate invitation code',
        'path': r'/apollo-main/api/v1/csms/wallet/packages/invite/download/?$',
        'scope': 'get',
        'require_auth_token': True,
    },

    {
        'name': 'Allows CSMS user get wallet transactions by wallet id',
        'path': r'/apollo-main/api/v1/csms/wallet/.+/transactions/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create wallet',
        'path': r'/apollo-main/api/v1/csms/wallet/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows CSMS user get wallet transactions',
        'path': r'/apollo-main/api/v1/csms/wallet/transactions/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    # API Key
    {
        'name': 'Allows to get API Key',
        'path': r'/apollo-main/api/v1/csms/organization_authorization_service/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view API Key',
        'path': r'/apollo-main/api/v1/csms/organization_authorization_service/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
]

REGULAR_USER_ALLOWED_RESOURCES = [
    # User
    {
        'name': 'Allows user to update profile',
        'path': r'/apollo-main/api/v1/organization/update-profile/?$',
        'scope': 'patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to update profile',
        'path': r'/apollo-main/api/v1/organization/update-profile/.+',
        'scope': 'patch',
        'require_auth_token': True,
    },
    # Invites
    {
        'name': 'Allows to view/create invite',
        'path': r'/apollo-main/api/v1/csms/invites/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update/delete invite',
        'path': r'/apollo-main/api/v1/csms/invites/.+',
        'scope': 'delete,patch',
        'require_auth_token': True,
    },
    # Favorite Charge Points
    {
        'name': 'Allows to view/create favorite charge points',
        'path': r'/apollo-main/api/v1/csms/favorite_cp/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to delete favorite charge points',
        'path': r'/apollo-main/api/v1/csms/favorite_cp/.+',
        'scope': 'delete',
        'require_auth_token': True,
    },
    # Favorite Charge Locations
    {
        'name': 'Allows to view/update favorite charge locations',
        'path': r'/apollo-main/api/v1/favorite_locations/?$',
        'scope': 'get,post,put',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update favorite charge locations',
        'path': r'/apollo-main/api/v1/favorite_locations/ocpi?$',
        'scope': 'get,post,put',
        'require_auth_token': True,
    },
    # Payments
    {
        'name': 'Allows to Manage payment requests',
        'path': r'/apollo-main/api/v1/csms/payment/payment-request/.+',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to Manage payment response',
        'path': r'/apollo-main/api/v1/csms/payment/payment-callback/?$',
        'scope': 'get,post',
        'require_auth_token': False,
    },
    # Credit Cards
    {
        'name': 'Allows to view/add credit card',
        'path': r'/apollo-main/api/v1/csms/cc/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete credit card',
        'path': r'/apollo-main/api/v1/csms/cc/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # V2 Credit Cards
    {
        'name': 'Allows to view/add credit card',
        'path': r'/apollo-main/api/v1/cc/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete credit cards',
        'path': r'/apollo-main/api/v1/cc/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Wallet
    {
        'name': 'Allows to view/create wallet',
        'path': r'/apollo-main/api/v1/csms/wallet/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete wallet',
        'path': r'/apollo-main/api/v1/csms/wallet/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Promotion Code
    {
        'name': 'Allows to view a promo codes related to any users',
        'path': r'/apollo-main/api/v1/csms/promo_code/.+/remaining/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view promo code',
        'path': r'/apollo-main/api/v1/csms/promo_code/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Vehicle
    {
        'name': 'Allows to view member vehicle',
        'path': r'/apollo-main/api/v1/csms/vehicle/membership/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to add vehicle',
        'path': r'/apollo-main/api/v1/csms/vehicle/membership/.+',
        'scope': 'post,delete',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view vehicle',
        'path': r'/apollo-main/api/v1/csms/vehicle/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Operator
    {
        'name': 'Allows to view operators',
        'path': r'/apollo-main/api/v1/csms/operator/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view operators',
        'path': r'/apollo-main/api/v1/csms/operator/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Invoice
    {
        'name': 'Allows to view an invoice',
        'path': r'/apollo-main/api/v1/csms/invoice/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view an invoice',
        'path': r'/apollo-main/api/v1/csms/invoice/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view an invoice',
        'path': r'/apollo-main/api/v1/invoice/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view an invoice',
        'path': r'/apollo-main/api/v1/invoice/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Charger
    {
        'name': 'Allows to view charge point',
        'path': r'/apollo-main/api/v1/csms/charger/charge_point/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view charge point',
        'path': r'/apollo-main/api/v1/csms/charger/charge_point/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to check charge point OCPP status',
        'path': r'/apollo-main/api/v1/csms/charger/charge_point/.+/ocpp/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to start charging',
        'path': r'/apollo-main/api/v1/csms/charger/start-charging/.+',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to start charging',
        'path': r'/apollo-main/api/v1/charger/start-charging/.+',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to stop charging',
        'path': r'/apollo-main/api/v1/charger/stop-charging/.+',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to get charge usage',
        'path': r'/apollo-main/api/v1/charger/charge-usage/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view charge point',
        'path': r'/apollo-main/api/v1/charger/charge_point/?$',
        'scope': 'get',
        'require_auth_token': True,
    },

    {
        'name': 'Allows to view charge point',
        'path': r'/apollo-main/api/v1/charger/charge_point/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to check charge point OCPP status',
        'path': r'/apollo-main/api/v1/charger/charge_point/.+/ocpp/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to get charge point by serial number',
        'path': r'/apollo-main/api/v1/charger/charge_point/serial/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    # Operation
    {
        'name': 'Allows to view charger operation requests',
        'path': r'/apollo-main/api/v1/csms/charger/operation/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view individual charger operation',
        'path': r'/apollo-main/api/v1/csms/charger/operation/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Charging
    {
        'name': 'Allows to view individual charging session',
        'path': r'/apollo-main/api/v1/csms/charger/charging/user/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view current charging session',
        'path': r'/apollo-main/api/v1/csms/charger/charging/current/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view individual charging session',
        'path': r'/apollo-main/api/v1/csms/charger/charging/user/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view current charging session',
        'path': r'/apollo-main/api/v1/csms/charger/charging/current/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view individual charging session',
        'path': r'/apollo-main/api/v1/charger/charging/user/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view current charging session',
        'path': r'/apollo-main/api/v1/charger/charging/current/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view individual charging session',
        'path': r'/apollo-main/api/v1/charger/charging/charging_sessions/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view individual charging session',
        'path': r'/apollo-main/api/v1/charger/charging/charging_sessions/connector/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Location
    {
        'name': 'Allows to view connector',
        'path': r'/apollo-main/api/v1/charger/connector/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view connector',
        'path': r'/apollo-main/api/v1/charger/connector/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get curent charging session from ruby',
        'path': r'/apollo-main/api/v1/charger/current-session/ruby/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Location
    {
        'name': 'Allows to view Location',
        'path': r'/apollo-main/api/v1/csms/charger/location/?$',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view Location',
        'path': r'/apollo-main/api/v1/charger/location/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view Location',
        'path': r'/apollo-main/api/v1/charger/location/?$',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view Location',
        'path': r'/apollo-main/api/v1/charger/location/marker/?$',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/charger/location/marker/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/location/?$',
        'scope': 'get,post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/location/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view and create Location',
        'path': r'/apollo-main/api/v1/location/marker/?$',
        'scope': 'get,post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view/update/delete location',
        'path': r'/apollo-main/api/v1/location/marker/.+',
        'scope': 'get,patch,delete',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view all (including OCPI) location',
        'path': r'/apollo-main/api/v1/location/ocpi/?$',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view (including OCPI) location',
        'path': r'/apollo-main/api/v1/location/ocpi/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view all (including OCPI) simplfied location',
        'path': r'/apollo-main/api/v1/location/marker/?$',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view (including OCPI) simplfied location',
        'path': r'/apollo-main/api/v1/location/ocpi/marker/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view (including OCPI) location by serial number',
        'path': r'/apollo-main/api/v1/location/ocpi/serial/.+',
        'scope': 'get',
        'require_auth_token': False,
    },

    # Chargepoint Connector
    {
        'name': 'Allows to view a connector',
        'path': r'/apollo-main/api/v1/csms/charger/connector/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view a connector',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/create a connector',
        'path': r'/apollo-main/api/v1/csms/charger/ocpi/location/?$',
        'scope': 'get,post,put',
        'require_auth_token': False,
    },
    {
        'name': 'Full-access to manage connectors',
        'path': r'/apollo-main/api/v1/csms/charger/ocpi/location/.+',
        'scope': 'post,get,delete,patch,put',
        'require_auth_token': False,
    },
    {
        'name': 'Allows to view charge point connectors',
        'path': r'/apollo-main/api/v1/csms/charger/connector/charge_point/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to send OCPP message ',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+/ocpp/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to reserve connector',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+/reserve/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to cancel connector reservation',
        'path': r'/apollo-main/api/v1/csms/charger/connector/cancel_reservation/.+/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to start connector remotely',
        'path': r'/apollo-main/api/v1/csms/charger/connector/.+/remote-start/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to stop connector remotely',
        'path': r'/apollo-main/api/v1/csms/charger/connector/remote-stop/.+/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get charge point based on serial number',
        'path': r'/apollo-main/api/v1/charger/connector/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get charge point by id',
        'path': r'/apollo-main/api/v1/charger/connector/charge_points/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get charge point by id',
        'path': r'/apollo-main/api/v1/charger/connector/charge_points/discount/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get charge point by connector id',
        'path': r'/apollo-main/api/v1/charger/charging/charging_sessions/connector/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get charge point by serial number',
        'path': r'/apollo-main/api/v1/charger/charging/charging_sessions/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get start charging from ruby',
        'path': r'/apollo-main/api/v1/charger/start-charging/ruby/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get billing request from ruby',
        'path': r'/apollo-main/api/v1/charger/billing-request/ruby/.+',
        'scope': 'post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get start charging from ruby',
        'path': r'/apollo-main/api/v1/charger/stop-charging/ruby/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows user to get ocpp status from ruby',
        'path': r'/apollo-main/api/v1/charger/ocpp-status/ruby/serial-number/.+',
        'scope': 'post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows user to get ocpp status from ruby',
        'path': r'/apollo-main/api/v1/charger/ocpp-status/ruby/serial-number/?$',
        'scope': 'post',
        'require_auth_token': False,
    },
    # Connector Type
    {
        'name': 'Allow to view a connector types',
        'path': r'/apollo-main/api/v1/csms/charger/connectortype/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allow to view a connector types',
        'path': r'/apollo-main/api/v1/csms/charger/connectortype/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Subscriptions
    {
        'name': 'Allows to view/add subscriptions',
        'path': r'/apollo-main/api/v1/csms/subscriptions/member/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions',
        'path': r'/apollo-main/api/v1/csms/subscriptions/member/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions Plans
    {
        'name': 'Allows to view subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/plan/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/plan/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Subscriptions Custom Plans
    {
        'name': 'Allows to view subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/custom-plan/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions plans',
        'path': r'/apollo-main/api/v1/csms/subscriptions/custom-plan/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Subscriptions Card
    {
        'name': 'Allows to view subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/card/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/card/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Subscriptions Invitation
    {
        'name': 'Allows to view subscriptions invitations',
        'path': r'/apollo-main/api/v1/csms/subscriptions/invitation/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/invitation/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions invitations',
        'path': r'/apollo-main/api/v1/subscriptions/invitation/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions cards',
        'path': r'/apollo-main/api/v1/subscriptions/invitation/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions invitations',
        'path': r'/apollo-main/api/v1/subscriptions/check_invitation_code/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions cards',
        'path': r'/apollo-main/api/v1/subscriptions/check_invitation_code/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to update subscriptions plans renew',
        'path': r'/apollo-main/api/v1/subscriptions/member/renewal/?$',
        'scope': 'post',
        'require_auth_token': True,
    },
    # Subscriptions Payable Fees
    {
        'name': 'Allows to view/add subscriptions payable fee',
        'path': r'/apollo-main/api/v1/csms/subscriptions/fee/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions payable fee',
        'path': r'/apollo-main/api/v1/csms/subscriptions/fee/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions payable fee',
        'path': r'/apollo-main/api/v1/csms/subscriptions/fee/.+',
        'scope': 'get,delete,patch',
        'require_auth_token': True,
    },
    # Subscriptions connector pricing
    {
        'name': 'Allows to view connector pricing',
        'path': r'/apollo-main/api/v1/csms/subscriptions/connector-pricing/?$',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view subscriptions cards',
        'path': r'/apollo-main/api/v1/csms/subscriptions/connector-pricing/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # Users Subscription
    {
        'name': 'Allows to view/add subscription plan',
        'path': r'/apollo-main/api/v1/subscriptions/plan/.+',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add subscription plan',
        'path': r'/apollo-main/api/v1/subscriptions/plan/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/member/.+',
        'scope': 'get,patch',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/member/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/register-subscription/.+',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add user subscriptions',
        'path': r'/apollo-main/api/v1/subscriptions/register-subscription/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/add subscriptions invitations',
        'path': r'/apollo-main/api/v1/subscriptions/invitation/?$',
        'scope': 'get,post',
        'require_auth_token': True,
    },
    {
        'name': 'Allows to view/update/delete subscriptions cards',
        'path': r'/apollo-main/api/v1/subscriptions/invitation/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    # User
    {
        'name': 'Allows to delete users account',
        'path': r'/apollo-main/api/v1/user/terminate_account',
        'scope': 'delete',
        'require_auth_token': True,
    },
    # Migration
    {
        'name': 'Allows third party services to perform billing-request (Meant for migration)',
        'path': r'/apollo-main/api/v1/charger/billing-request/ruby/.+',
        'scope': 'post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows third party services to perform start-transaction (Meant for migration)',
        'path': r'/apollo-main/api/v1/charger/start-transaction/ruby/?$',
        'scope': 'post',
        'require_auth_token': False,
    },
    {
        'name': 'Allows third party services to update of session (Meant for migration)',
        'path': r'/apollo-main/api/v1/charger/update-session/ruby/.+',
        'scope': 'patch',
        'require_auth_token': False,
    },
    {
        'name': 'Allows mobile app to get charge point discounted price',
        'path': r'/apollo-main/api/v1/charger/location/discount/.+',
        'scope': 'get',
        'require_auth_token': False,
    },
    {
        'name': 'Allows mobile app user to view their wallet balance',
        'path': r'/apollo-main/api/v1/wallet/balance',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows mobile app user to view their wallet histories',
        'path': r'/apollo-main/api/v1/wallet/histories',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows mobile app user to view all wallet packages',
        'path': r'/apollo-main/api/v1/packages',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows mobile app user to check their packages redeem code validity',
        'path': r'/apollo-main/api/v1/packages/.+',
        'scope': 'get',
        'require_auth_token': True,
    },
    {
        'name': 'Allows mobile app user to redeem packages',
        'path': r'/apollo-main/api/v1/packages/redeem',
        'scope': 'post',
        'require_auth_token': True,
    },
]
