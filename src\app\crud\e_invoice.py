import logging
from datetime import datetime

from sqlalchemy.exc import NoResultFound, IntegrityError
from sqlalchemy.orm import Session

from app import models, schema, settings, exceptions
from .base import BaseCRUD

CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'

logger = logging.getLogger(__name__)


class EInvoiceCRUD(BaseCRUD):
    model = models.EInvoice


class EInvoiceRequestCRUD(BaseCRUD):
    model = models.EInvoiceRequest


class ECreditNoteCRUD(BaseCRUD):
    model = models.ECreditNote


class ECreditNoteRequestCRUD(BaseCRUD):
    model = models.ECreditNoteRequest


def get_e_invoice_by_id(db: Session, e_invoice_id: str) -> schema.EInvoiceResponse:
    query = EInvoiceCRUD.query(db).filter(
        models.EInvoice.id == e_invoice_id,
    )
    return query.first()


def get_e_invoice_by_csb_id(db: Session, charging_session_bill_id: str) -> schema.EInvoiceResponse:
    query = EInvoiceCRUD.query(db).filter(
        models.EInvoice.charging_session_bill_id == charging_session_bill_id,
    )
    return query.first()


def get_e_invoice_by_charging_session_id(db: Session, charging_session_id: str) -> schema.EInvoiceResponse:
    query = EInvoiceCRUD.query(db).filter(
        models.EInvoice.charging_session_id == charging_session_id,
    )
    return query.first()


def get_e_invoice_by_number(db: Session, e_invoice_number: str) -> schema.EInvoiceResponse:
    query = EInvoiceCRUD.query(db).filter(
        models.EInvoice.e_invoice_number == e_invoice_number,
    )
    return query.first()


def get_e_cn_by_number(db: Session, e_credit_note_number: str) -> schema.ECreditNoteResponse:
    query = ECreditNoteCRUD.query(db).filter(
        models.ECreditNote.e_credit_note_number == e_credit_note_number,
    )
    return query.first()


async def update_invoices_with_callback(dbsession: Session, invoice_numbers: list[str], callback_data: dict) -> None:
    accepted_documents = {
        doc.get("invoiceCodeNumber"): doc
        for doc in callback_data.get("acceptedDocuments", [])
        if doc.get("invoiceCodeNumber")
    }

    rejected_documents = {
        doc.get("invoiceCodeNumber")
        for doc in callback_data.get("rejectedDocuments", [])
        if doc.get("invoiceCodeNumber")
    }

    for number in invoice_numbers:
        e_invoice = get_e_invoice_by_number(dbsession, number)
        if e_invoice:
            status = None
            lhdn_uid = None
            lhdn_qr = None
            lhdn_validated_date_time = None
            last_rejection_date = None
            status_timeline = e_invoice.status_timeline or []
            new_timeline = list(status_timeline)

            if number in accepted_documents:
                status = schema.EInvoiceStatus.validated
                new_status_timeline = {
                    "status": schema.EInvoiceStatus.validated.value,
                    "action": schema.EInvoiceAction.callback.value,
                    "status_date": datetime.now().isoformat()
                }
                new_timeline.append(new_status_timeline)
                doc = accepted_documents[number]
                lhdn_uid = doc.get("uuid")
                lhdn_qr = doc.get("qrCode")
                lhdn_validated_date_time = doc.get("dateTimeValidated")

            elif number in rejected_documents:
                status = schema.EInvoiceStatus.rejected
                new_status_timeline = {
                    "status": schema.EInvoiceStatus.rejected.value,
                    "action": schema.EInvoiceAction.callback.value,
                    "status_date": datetime.now().isoformat()
                }
                new_timeline.append(new_status_timeline)
                last_rejection_date = datetime.now()

            e_invoice_update = schema.EInvoiceUpdate(
                status=status,
                status_timeline=new_timeline,
                submission_callback=callback_data,
                lhdn_uid=lhdn_uid,
                lhdn_qr=lhdn_qr,
                lhdn_validated_date_time=lhdn_validated_date_time,
            )
            if last_rejection_date:
                e_invoice_update.last_rejection_date = last_rejection_date
            update_e_invoice(dbsession, e_invoice.id, e_invoice_update)


async def update_cn_with_callback(dbsession: Session, invoice_numbers: list[str], callback_data: dict) -> None:
    accepted_documents = {
        doc.get("invoiceCodeNumber"): doc
        for doc in callback_data.get("acceptedDocuments", [])
        if doc.get("invoiceCodeNumber")
    }

    rejected_documents = {
        doc.get("invoiceCodeNumber")
        for doc in callback_data.get("rejectedDocuments", [])
        if doc.get("invoiceCodeNumber")
    }

    for number in invoice_numbers:
        e_cn = get_e_cn_by_number(dbsession, number)
        if e_cn:
            status = None
            lhdn_uid = None
            lhdn_qr = None
            lhdn_validated_date_time = None
            last_rejection_date = None
            status_timeline = e_cn.status_timeline or []
            new_timeline = list(status_timeline)

            if number in accepted_documents:
                status = schema.ECreditNoteStatus.validated
                new_status_timeline = {
                    "status": schema.ECreditNoteStatus.validated.value,
                    "action": schema.ECreditNoteAction.callback.value,
                    "status_date": datetime.now().isoformat()
                }
                new_timeline.append(new_status_timeline)
                doc = accepted_documents[number]
                lhdn_uid = doc.get("uuid")
                lhdn_qr = doc.get("qrCode")
                lhdn_validated_date_time = doc.get("dateTimeValidated")

            elif number in rejected_documents:
                status = schema.ECreditNoteStatus.rejected
                new_status_timeline = {
                    "status": schema.ECreditNoteStatus.rejected.value,
                    "action": schema.ECreditNoteAction.callback.value,
                    "status_date": datetime.now().isoformat()
                }
                new_timeline.append(new_status_timeline)
                last_rejection_date = datetime.now()

            e_cn_update = schema.ECreditNoteUpdate(
                status=status,
                status_timeline=new_timeline,
                submission_callback=callback_data,
                lhdn_uid=lhdn_uid,
                lhdn_qr=lhdn_qr,
                lhdn_validated_date_time=lhdn_validated_date_time
            )
            if last_rejection_date:
                e_cn_update.last_rejection_date = last_rejection_date
            update_e_credit_note(dbsession, e_cn.id, e_cn_update)


def get_e_credit_note_by_id(db: Session, e_credit_note_id: str) -> schema.ECreditNoteResponse:
    query = ECreditNoteCRUD.query(db).filter(
        models.ECreditNote.id == e_credit_note_id,
    )
    return query.first()


def update_e_invoice(db: Session, e_invoice_id: str,
                     e_invoice_update: schema.EInvoiceUpdate) -> schema.EInvoiceResponse:
    try:
        db_e_invoice = EInvoiceCRUD.update(db, e_invoice_id,
                                           e_invoice_update.dict(exclude_unset=True,
                                                                 exclude_defaults=True),
                                           )
        return db_e_invoice
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='EInvoice')


def update_e_credit_note(db: Session, e_credit_note_id: str,
                         e_invoice_update: schema.ECreditNoteUpdate) -> schema.ECreditNoteResponse:
    try:
        db_e_invoice = ECreditNoteCRUD.update(db, e_credit_note_id,
                                              e_invoice_update.dict(exclude_unset=True,
                                                                    exclude_defaults=True),
                                              )
        return db_e_invoice
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='EInvoice')


def create_e_invoice(db: Session, e_invoice_data: schema.EInvoice) -> models.EInvoice:
    try:
        db_e_invoice = EInvoiceCRUD.add(db, e_invoice_data.dict())
        return db_e_invoice
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloCreditCardError()


def create_e_cn(db: Session, e_cn_data: schema.ECreditNoteCreate) -> models.ECreditNote:
    try:
        db_e_cn_data = ECreditNoteCRUD.add(db, e_cn_data.dict())
        return db_e_cn_data
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloCreditCardError()


def get_e_credit_note_by_payment_refund_id(db: Session, payment_refund_id: str) -> schema.ECreditNoteResponse:
    query = ECreditNoteCRUD.query(db).filter(
        models.ECreditNote.payment_refund_id == payment_refund_id,
    )
    return query.first()
