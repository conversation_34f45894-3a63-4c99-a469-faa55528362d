from contextlib import contextmanager
from sqlalchemy.exc import IntegrityError
from app import models, schema, exceptions
from app.crud.base import BaseCRUD
from app.database import create_session


class DynamicLTALogCRUD(BaseCRUD):
    model = models.DynamicLTALog


def create_dynamic_lta_log(lta_data: schema.DynamicLTALog) -> models.DynamicLTALog:
    with contextmanager(create_session)() as db:
        try:
            db_dynamic_lta = DynamicLTALogCRUD.add(db, lta_data.dict())
            return db_dynamic_lta
        except IntegrityError:
            db.rollback()
            raise exceptions.ApolloDynamicLTAError()
