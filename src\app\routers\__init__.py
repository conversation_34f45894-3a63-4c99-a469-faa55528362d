from .resources import router as resource_router
from .organization_memberships import router as organization_memberships_router
from .roles import router as roles_router
from .organizations import router as organizations_router
from .organization_auth_service import router as organizations_auth_service_router
from .invites import router as invites_router
from .operators import router as operators_router
from .charger import router as charger_router
from .payments import router as payments_router
from .credit_card import router as cc_router
from .promo_codes import router as promo_code_router
from .campaign import router as campaign_router
from .wallets import router as wallet_router
from .vehicles import router as vehicle_router
from .vehicles import router_webhook as vehicle_webhook
from .id_tag import router as id_tag_router
from .favorite_charge_points import router as fav_cp_router
from .invoice import router as invoice_router
from .e_invoice import router as e_invoice_router
from .auth import router as auth_router
from .user import router as user_router
from .subscriptions import router as subscriptions_router
from .external_auth import router as external_auth_router
from .ocpi import router as ocpi_router
from .powercable import router as power_cable_router
from .cybersource import router as cybersource_router
from .energy_storage_system import router as enery_storage_system_router
from .certificates_management import router as certificates_management_router
from .email_report import router as email_report_router
from .log import router as log_router
from .notification_service import router as notification_router
from .outlier import router as outlier_router
