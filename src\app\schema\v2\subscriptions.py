from datetime import datetime
from uuid import UUID
from enum import Enum
from typing import Optional, List, Union

from pydantic import BaseModel, Field, validator
from ..payment import CommercialInvoiceType
from ..payment import Currency
from ..subscription import SubscriptionCustomPlanResponse
from ..organization import ShallowMembershipResponse


class SubscriptionStatus(str, Enum):
    pending = "Pending"
    failed = "Failed"
    success = "Success"


# pylint: disable=unsubscriptable-object
class MobileSubscriptionPlanCategory(str, Enum):
    fixed = 'Fixed'
    percentage = 'Percentage'


class MobileSubscriptionStatus(str, Enum):
    pending = 'Pending'
    failed = 'Failed'
    success = 'Success'


class MobileSubscriptionFee(BaseModel):
    amount: float = 0.0
    name: str


class MobileSubscriptionFeeResponse(BaseModel):
    amount: float = 0.0
    name: str

    class Config:
        orm_mode = True


class MemberSubscriptionInformationData(BaseModel):
    full_name: str
    identification_number: str
    address_1: str
    address_2: Optional[str] = ""
    postal_code: str
    city: str
    state: str
    country: str
    phone_number: str
    email: str
    vehicle_manufacturer: str
    vehicle_model: str
    vehicle_registration_number: str
    delivery_option: bool
    allow_marketing: bool = True


class MobileSubscriptionRegistration(BaseModel):
    subscription_plan_id: UUID
    invitation_code: Optional[str]
    meta: MemberSubscriptionInformationData
    status: SubscriptionStatus = SubscriptionStatus.pending
    is_default: bool = False
    payable_fees: List
    currency: Currency


class MobileSubscription(BaseModel):
    subscription_plan_id: UUID
    invitation_code: Optional[str]
    meta: Optional[MemberSubscriptionInformationData]
    status: MobileSubscriptionStatus
    is_default: bool = False
    start_date: datetime
    end_date: datetime
    number: str


class MobileSubscriptionPlan(BaseModel):
    name: str
    description: str
    category: MobileSubscriptionPlanCategory
    amount: float
    is_active: bool
    organization_id: UUID
    is_private: Optional[bool]
    allow_invitation_code: Optional[bool] = False
    is_renewable: Optional[bool] = True
    payment_currency: Optional[str] = 'MYR'

    class Config:
        allow_population_by_field_name = True


class MobileSubscriptionPlanResponse(MobileSubscriptionPlan):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]

    subscription_fees: Optional[List[MobileSubscriptionFee]]

    class Config:
        orm_mode = True
        allow_population_by_field_name = True


class MobileSubscriptionPlanFullResponse(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    subscription_plan: MobileSubscriptionPlanResponse
    custom_plan: Optional[List[SubscriptionCustomPlanResponse]]
    subscription_plan_id: UUID
    invitation_code: Optional[str]
    meta: Optional[dict] = {}
    status: MobileSubscriptionStatus
    is_default: bool = False
    start_date: datetime
    end_date: datetime
    number: str

    class Config:
        orm_mode = True
        allow_population_by_field_name = True


class ShallowMobileSubscriptionPlanResponse(BaseModel):
    name: str
    description: str

    class Config:
        orm_mode = True


class MobileSubscriptionResponse(BaseModel):
    id: UUID = Field(alias='subscription_id')
    created_at: datetime
    updated_at: Optional[datetime]
    member_id: UUID
    subscription_plan: MobileSubscriptionPlanResponse
    subscription_plan_id: UUID
    invitation_code: Optional[str]
    meta: Optional[dict]
    status: MobileSubscriptionStatus
    is_default: bool = False
    start_date: datetime
    end_date: datetime
    number: str
    has_invoice: bool = False

    class Config:
        orm_mode = True
        allow_population_by_field_name = True


class SubscriptionInvitationResponse(BaseModel):
    id: UUID
    member_id: Optional[UUID]
    discount_amount: Optional[float]
    created_at: datetime
    updated_at: Optional[datetime]
    linked_at: Optional[datetime]
    code: str
    subscription_plan_id: UUID

    class Config:
        orm_mode = True


class MobileSubscriptionPlanName(BaseModel):
    name: str

    class Config:
        orm_mode = True


class MobileSubscriptionInfo(BaseModel):
    subscription_plan_id: Optional[UUID]
    subscription_plan: Optional[MobileSubscriptionPlanName]
    start_date: Optional[datetime]
    end_date: datetime

    class Config:
        orm_mode = True


class PayableFee(BaseModel):
    amount: float
    name: str


class MobileSubscriptionOrder(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    subscription: Optional[MobileSubscriptionInfo]
    discount_amount: str
    payable_fees: Union[List[PayableFee], dict]  # noqa 

    @validator('discount_amount')
    def validate_amount(cls, discount_amount):
        return "{:.2f}".format(float(discount_amount))

    class Config:
        orm_mode = True


class MobileSubscriptionInvoice(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    member: ShallowMembershipResponse
    type: CommercialInvoiceType
    currency: Currency
    amount: str
    invoice_number: str
    subscription_order: MobileSubscriptionOrder

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))

    class Config:
        orm_mode = True


class CommercialInvoice(BaseModel):
    type: CommercialInvoiceType
    amount: str
    currency: Currency = Currency.myr
    subscription_order_id: str
    invoice_number: Optional[str]
