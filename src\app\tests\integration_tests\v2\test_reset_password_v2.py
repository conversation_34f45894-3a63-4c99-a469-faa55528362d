from contextlib import contextmanager
from datetime import datetime, timed<PERSON><PERSON>
from unittest.mock import patch
from uuid import uuid4

import secrets
import pytest
from faker import Faker
from fastapi.testclient import TestClient

from app.database import SessionLocal, create_session, Base, engine
from app.main import app
from app.schema import MembershipType, argon_ph, UserType
from app.tests.factories import (UserFactory, ResetPasswordTokenFactory, OrganizationFactory,
                                 MembershipFactory, MembershipExtendedFactory,
                                 OrganizationAuthenticationServiceFactory)

fake = Faker()
client = TestClient(app)


def faker_phone_number(fake: Faker) -> str:
    return f'+91 {fake.msisdn()[3:]}'


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResetPasswordTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipExtendedFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


@patch('app.routers.v2.auth.send_otp')
def test_post_reset_password_with_valid_data_succeeds(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }
    valid_data = {}
    url = 'apollo-main/api/v1/auth/reset-password'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            organization_id=organization.id,
            user_type=UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()

        ResetPasswordTokenFactory(user_id=str(user.id))
        db.commit()

        valid_data['phone_number'] = str(user.phone_number)
        valid_data['organization_id'] = f'{organization.id}'

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
    assert response.status_code == 200


def test_post_reset_password_with_valid_data_within_rate_limit_fails(test_db):
    valid_data = {}
    url = 'apollo-main/api/v1/auth/reset-password'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            organization_id=organization.id,
            user_type=UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        rptoken = ResetPasswordTokenFactory(user_id=str(user.id), created_at=datetime.now() + timedelta(hours=5))
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True,
                                  reset_password_token=rptoken)
        db.commit()

        ResetPasswordTokenFactory(user_id=str(user.id))
        db.commit()

        valid_data['phone_number'] = str(user.phone_number)
        valid_data['organization_id'] = f'{organization.id}'

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
    assert response.json()['error']['message'] == ['Reset password token has not expired yet.']


def test_post_reset_password_with_unverified_account_fails(test_db):
    url = 'apollo-main/api/v1/auth/reset-password'

    valid_data = {
        'email': ''
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=False,
            organization_id=organization.id,
            user_type=UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=False)
        db.commit()

        valid_data['phone_number'] = str(user.phone_number)
        valid_data['organization_id'] = f'{organization.id}'

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)

    assert response.json()['error']['message'] == ['User not found, please register.']


def test_post_reset_password_with_invalid_data_fails(test_db):
    url = 'apollo-main/api/v1/auth/reset-password'

    valid_data = {
        'phone_number': '+60123456789',
        'organization_id': str(uuid4())
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)

    assert response.json()['error']['message'] == ['User not found, please register.']


@patch('app.routers.v2.auth.verify_otp')
def test_post_reset_password_confirm_with_valid_code_succeeds(verify_otp_mock, test_db):
    class RetVal:
        status = 'approved'

    verify_otp_mock.return_value = RetVal()

    url = 'apollo-main/api/v1/auth/reset-password-confirm'

    valid_data = {
        'token': '',
        'new_password': '123@abcDef',
        'new_password_confirm': '123@abcDef',
        'organization_id': '',
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='phone',
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()

        valid_data['phone_number'] = str(user.phone_number)
        valid_data['organization_id'] = f'{organization.id}'
        valid_data['token'] = 'token'

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
    assert response.json()['message'] == 'Password has been successfully reset.'


@patch('app.routers.v2.auth.verify_otp')
def test_post_reset_password_confirm_with_invalid_code_fails(verify_otp_mock, test_db):
    class RetVal:
        status = 'declined'

    verify_otp_mock.return_value = RetVal()

    url = 'apollo-main/api/v1/auth/reset-password-confirm'

    valid_data = {
        'token': '',
        'new_password': '123@abcDef',
        'new_password_confirm': '123@abcDef',
        'organization_id': '',
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='phone',
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()

        valid_data['phone_number'] = str(user.phone_number)
        valid_data['organization_id'] = f'{organization.id}'
        valid_data['token'] = f'wrong-token-123'

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
    assert response.json()['error']['message'] == ['Invalid token.']


def test_post_reset_password_confirm_with_unmatched_pw_fails(test_db):
    url = 'apollo-main/api/v1/auth/reset-password-confirm'

    valid_data = {
        'email': '',
        'token': '',
        'new_password': '123abc',
        'new_password_confirm': '123abcdddd',
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='phone',
            phone_number=faker_phone_number(fake),
            organization_id=organization.id,
            user_type=UserType.regular
        )
        db.commit()

        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id), phone_verified=True)
        db.commit()

        valid_data['phone_number'] = str(user.phone_number)
        valid_data['organization_id'] = f'{organization.id}'
        valid_data['token'] = f'token-123'

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)

    assert response.status_code == 200
