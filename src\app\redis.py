import base64
from datetime import date, datetime, time
import hashlib
import hmac
import json
import logging
import ssl
from uuid import UUID
import aioredis

from app import schema, settings

USER_INFO = 'USER-INFO'
logger = logging.Logger(__name__)


class RedisPool:
    """Singleton class to manage Redis connection pool."""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RedisPool, cls).__new__(cls)
            cls._pool = None
        return cls._instance

    @staticmethod
    async def get_ssl_context():
        # Decode Base64-encoded CA certificate
        decoded_cert = base64.b64decode(settings.REDIS_SSL_CERT).decode('utf-8')

        # Create SSL context and load the CA cert
        ssl_context = ssl.create_default_context(cadata=decoded_cert)

        return ssl_context

    async def get_pool(self):
        redis_kwargs = {
            "encoding": "utf-8",
            "decode_responses": True,
        }

        if settings.REDIS_REQUIRE_PASSWORD:
            redis_kwargs["password"] = str(settings.REDIS_PASSWORD)

        if self._pool is None:
            if settings.REDIS_IS_SECURE:
                # ssl_context = await self.get_ssl_context()  # Await the SSL context creation
                self._pool = await aioredis.from_url(
                    settings.REDIS_MASTER_USER_URL,
                    **redis_kwargs
                )
            else:
                self._pool = await aioredis.from_url(
                    settings.REDIS_MASTER_USER_URL,
                    **redis_kwargs
                )
        return self._pool


def hash_token(token: str) -> str:
    """Hash a bearer token using HMAC-SHA256 for fast lookup."""
    return hmac.new(settings.SECRET_KEY.encode(), token.encode(), hashlib.sha256).hexdigest()


def json_serializer(obj):
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    if isinstance(obj, time):
        return obj.strftime('%H:%M:%S')
    if isinstance(obj, UUID):
        return str(obj)
    raise TypeError(f"Type {type(obj)} not serializable")


async def fetch_and_process_key(pool, key):
    """Fetch and process individual key data from Redis."""
    try:
        value = await pool.get(key)
        if value:
            return json.loads(value)
    except aioredis.RedisError as e:
        logger.error("Redis error fetching key %s: %s", key, str(e))
        return None


async def get_redis_key(key_name):
    try:
        pool = await RedisPool().get_pool()
        data = await fetch_and_process_key(pool, key_name)
        return data
    except aioredis.RedisError as e:
        logger.error("Redis error occurred: %s", str(e))


async def update_redis_data(key, data):
    """Update data in Redis."""
    try:
        pool = await RedisPool().get_pool()
        await pool.set(key, json.dumps(data, default=json_serializer))
    except aioredis.RedisError as e:
        logger.error("Failing to update Redis with error: %s", str(e))


async def validate_user_token_from_redis(bearer_token, member_id):
    try:
        key_name = USER_INFO + '-' + str(member_id)
        result = await get_redis_key(key_name)
        if result:
            token = bearer_token.removeprefix(f'{schema.TokenTypeEnum.bearer} ')
            return hmac.compare_digest(hash_token(token), result['token'])
        return False
    except Exception as e:  # pylint: disable=broad-except
        logger.error("Redis Generic Error: %s", str(e))


async def update_user_token_in_redis(member_id, bearer_token):
    try:
        key_name = USER_INFO + '-' + str(member_id)
        hashed_token = hash_token(bearer_token)
        user_info_dict = {
            'member_id': member_id,
            'token': hashed_token
        }
        await update_redis_data(key_name, user_info_dict)
    except Exception as e:  # pylint: disable=broad-except
        logger.error("Redis Generic Error: %s", str(e))
