"""149

Revision ID: 79653997ed0e
Revises: 16c6ebc58c9d
Create Date: 2025-02-20 14:49:43.271003

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '79653997ed0e'
down_revision = '16c6ebc58c9d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_reporting_task', sa.Column('success', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###    
    op.drop_column('main_reporting_task', 'success')
    # ### end Alembic commands ###
