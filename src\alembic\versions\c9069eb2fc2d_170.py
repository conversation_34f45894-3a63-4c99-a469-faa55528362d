"""170

Revision ID: c9069eb2fc2d
Revises: aee64642fd9b
Create Date: 2025-05-27 11:55:17.349509

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c9069eb2fc2d'
down_revision = 'aee64642fd9b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_e_invoice', sa.Column('submission_payload', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_e_invoice', sa.Column('submission_response', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_e_invoice', sa.Column('submission_callback', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_e_invoice', 'submission_callback')
    op.drop_column('main_e_invoice', 'submission_response')
    op.drop_column('main_e_invoice', 'submission_payload')
    # ### end Alembic commands ###
