import json
import logging
from typing import List

from fastapi import APIRouter, HTTPException, Depends, Request
from sqlalchemy.orm import Session
from twilio.rest import Client

from app import settings, schema, crud, exceptions
from app.database import create_session, SessionLocal
from app.permissions import permission, x_api_key
from app.schema import WalletPackageRedemption
from app.settings import APOLLO_MAIN_DOMAIN, MAIN_ROOT_PATH
from app.utils import (
    decode_auth_token_from_headers,
    RouteErrorHandler
)

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

main_path = f"{APOLLO_MAIN_DOMAIN}/{MAIN_ROOT_PATH}/api/v1"

twilio_client = Client(settings.TWILIO['ACCOUNT_SID'], settings.TWILIO['AUTH_TOKEN'])

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/packages",
    tags=['v2 packages', ],
    dependencies=[Depends(permission), Depends(x_api_key)],
    route_class=RouteErrorHandler,
)


@router.get('', response_model=List[schema.WalletPackageResponse],
            tags=['wallet', ])
async def get_packages(request: Request, dbsession: SessionLocal = Depends(create_session),
                       is_private: bool = None,
                       is_active: bool = True,
                       is_redeemable: bool = None, currency: str = 'MYR'):
    db_packages = crud.get_wallet_packages(db=dbsession,
                                           is_private=is_private,
                                           is_redeemable=is_redeemable,
                                           is_active=is_active,
                                           currency=currency,
                                           query_only=False)

    return db_packages


@router.get('/{redeem_code}', response_model=schema.WalletPackageResponse,
            tags=['wallet', ])
async def get_wallet_package_based_on_code(request: Request, redeem_code: str,  # noqa: MC0001
                                           dbsession: Session = Depends(create_session)):
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
    except Exception:
        raise HTTPException(401, 'Authorization token is missing.')

    try:
        db_wallet_invitation = crud.get_wallet_package_invitation_by_code(dbsession, redeem_code)

    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=403, detail='This code is not valid or expired. Please check and try again.')

    if db_wallet_invitation.linked_at is not None:
        if str(db_wallet_invitation.wallet.member_id) == membership_id:
            err_detail = 'You have already redeemed this code.'
        else:
            err_detail = 'This code is not valid or expired. Please check and try again.'
        raise HTTPException(status_code=403, detail=err_detail)

    try:
        wallet_package = crud.get_wallet_package(dbsession, db_wallet_invitation.wallet_package_id)
        if not wallet_package.is_active:
            raise HTTPException(status_code=403, detail='Package not available.')
        if not wallet_package.is_redeemable:
            raise HTTPException(status_code=403, detail='Package not redeemable.')
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=403, detail='Package not available.')

    wallet_package.price = 0.00
    return wallet_package


@router.post('/redeem', tags=['wallet', ])
async def redeem_package(request: Request, data: WalletPackageRedemption,  # noqa: MC0001
                         dbsession: Session = Depends(create_session)):
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
    except Exception:
        raise HTTPException(401, 'Authorization token is missing.')

    try:
        db_wallet_invitation = crud.get_wallet_package_invitation_by_code(dbsession, data.redeem_code)

    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=403, detail='This code is not valid or expired. Please check and try again.')

    if db_wallet_invitation.linked_at is not None:
        if str(db_wallet_invitation.wallet.member_id) == membership_id:
            err_detail = 'You have already redeemed this code.'
        else:
            err_detail = 'This code is not valid or expired. Please check and try again.'
        raise HTTPException(status_code=403, detail=err_detail)

    try:
        wallet_package = crud.get_wallet_package(dbsession, db_wallet_invitation.wallet_package_id)
        if not wallet_package.is_active:
            raise HTTPException(status_code=403, detail='Package not available.')
        if not wallet_package.is_redeemable:
            raise HTTPException(status_code=403, detail='Package not redeemable.')
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=403, detail='Package not available.')

    currency = wallet_package.currency
    db_wallet_package_id = wallet_package.id
    db_wallet_package_amount = wallet_package.price
    db_wallet_package_discount_fee = wallet_package.price
    db_wallet_package_credit_amount = wallet_package.credit_amount
    db_wallet_package_meta = json.loads(schema.WalletPackageResponse.from_orm(wallet_package).json())

    db_wallet = crud.get_wallet_by_member_and_currency(dbsession, membership_id, currency)
    db_wallet_amount = float(db_wallet.balance) + float(db_wallet_package_credit_amount)
    crud.use_package_invitation(dbsession, wallet_id=db_wallet.id, wallet_package_invitation_id=db_wallet_invitation.id)

    db_package_order = crud.create_wallet_package_order(dbsession, db_wallet_package=wallet_package,
                                                        currency=currency,
                                                        wallet_package_id=db_wallet_package_id,
                                                        db_wallet_package_amount=db_wallet_package_amount,
                                                        db_wallet_package_discount_fee=db_wallet_package_discount_fee,
                                                        wallet_id=db_wallet.id)

    if db_package_order.status == schema.WalletPackageOrderStatus.success:
        db_wallet = crud.topup_wallet_from_redeemed_package(dbsession, data.redeem_code, membership_id, db_wallet.id,
                                                            currency,
                                                            db_wallet_package_credit_amount,
                                                            str(round(db_wallet_amount, 2)), db_wallet_package_id,
                                                            db_wallet_package_meta)

        wallet_package = schema.WalletPackageResponse.from_orm(wallet_package).dict()
        wallet_package['balance'] = db_wallet.balance
        return wallet_package

    print("Error")
