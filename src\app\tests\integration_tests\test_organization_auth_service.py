from contextlib import contextmanager
import pytest
from faker import Faker
import random
import jwt
from datetime import datetime, timedelta
import secrets
import uuid

from fastapi.testclient import TestClient

from app import schema, settings
from app.main import app, ROOT_PATH
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory, CreditCardFactory, IDTagFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory,
                                 MembershipExtendedFactory, OrganizationAuthenticationServiceFactory)
from app.database import SessionLocal, create_session, Base, engine


client = TestClient(app)
fake = Faker()

ORGANIZATION_AUTH_BASE_URL = f'{ROOT_PATH}/api/v1/csms/organization_authorization_service'


def faker_phone_number(fake: Faker) -> str:
    random_number = ''.join(random.choice('123456789') for _ in range(8))
    random_number = '+601' + random_number
    return random_number


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        IDTagFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipExtendedFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


def test_add_oas(test_db):
    url = f'{ORGANIZATION_AUTH_BASE_URL}/'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(mem.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        data = {
            'organization_id': organization_id,
            'name': 'test'
        }

        response = client.post(url, headers={'authorization': token}, json=data)
        assert response.status_code == 201
        assert response.json().get('organization_id') == organization_id
        assert response.json().get('secret_key') is not None


def test_add_oas_not_superuser(test_db):
    url = f'{ORGANIZATION_AUTH_BASE_URL}/'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(mem.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        data = {
            'organization_id': organization_id,
            'name': 'string'
        }

        response = client.post(url, headers={'authorization': token}, json=data)
        assert response.status_code == 400
        assert response.json().get('detail') == (
            'You do not have rights to create API Key, please contact Administrator.'
        )


def test_get_oas(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        secret_key = secrets.token_hex(32)
        oas = OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key),
            name='test'
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        url = f'{ORGANIZATION_AUTH_BASE_URL}/{str(oas.id)}'

        response = client.get(url)
        assert response.status_code == 200
        assert response.json().get('organization_id') == organization_id


def test_get_oas_not_exist(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        url = f'{ORGANIZATION_AUTH_BASE_URL}/{str(uuid.uuid4())}'

        response = client.get(url)
        assert response.status_code == 400
        assert response.json().get('detail') == 'OrganizationAuthenticationService object does not exist.'


def test_regenerate_oas_not_superuser(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        secret_key = secrets.token_hex(32)
        oas = OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(mem.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ORGANIZATION_AUTH_BASE_URL}/{str(oas.id)}/regenerate'

        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 400
        assert response.json().get('detail') == ('You do not have rights to regenerate API Key, '
                                                 'please contact Administrator.')


def test_regenerate_oas_not_editable(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        secret_key = secrets.token_hex(32)
        oas = OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(mem.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ORGANIZATION_AUTH_BASE_URL}/{str(oas.id)}/regenerate'

        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 400
        assert response.json().get('detail') == 'The Selected API Key is not editable.'


def test_update_oas(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        organization2 = OrganizationFactory()
        db.commit()
        organization_id_2 = str(organization2.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        secret_key = secrets.token_hex(32)
        oas = OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key),
            is_editable=True,
            name='test'
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(mem.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        data = {
            'organization_id': organization_id_2,
            'name': 'new_name',
            'is_editable': False
        }

        url = f'{ORGANIZATION_AUTH_BASE_URL}/{str(oas.id)}'

        response = client.patch(url, headers={'authorization': token}, json=data)
        assert response.status_code == 200
        assert response.json().get('name') == 'new_name'
        assert response.json().get('organization_id') == organization_id_2
        assert response.json().get('is_editable') is False


def test_update_oas_not_superuser(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        secret_key = secrets.token_hex(32)
        oas = OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key),
            is_editable=True,
            name='test'
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(mem.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        data = {
            'name': 'new_name',
            'is_editable': False
        }

        url = f'{ORGANIZATION_AUTH_BASE_URL}/{str(oas.id)}'

        response = client.patch(url, headers={'authorization': token}, json=data)
        assert response.status_code == 400
        assert response.json().get('detail') == ('You do not have rights to update API Key, '
                                                 'please contact Administrator.')


def test_update_oas_not_editable(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        secret_key = secrets.token_hex(32)
        oas = OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key),
            is_editable=False,
            name='test'
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(mem.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        data = {
            'name': 'new_name',
            'is_editable': False
        }

        url = f'{ORGANIZATION_AUTH_BASE_URL}/{str(oas.id)}'

        response = client.patch(url, headers={'authorization': token}, json=data)
        assert response.status_code == 400
        assert response.json().get('detail') == 'The Selected API Key is not editable.'


def test_delete_oas(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        secret_key = secrets.token_hex(32)
        oas = OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key),
            is_editable=True,
            name='test'
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(mem.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ORGANIZATION_AUTH_BASE_URL}/{str(oas.id)}'

        response = client.delete(url, headers={'authorization': token})
        print('<<< response', response.json())
        assert response.status_code == 200


def test_delete_oas_not_superuser(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        secret_key = secrets.token_hex(32)
        oas = OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key),
            is_editable=True,
            name='test'
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(mem.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ORGANIZATION_AUTH_BASE_URL}/{str(oas.id)}'

        response = client.delete(url, headers={'authorization': token})
        print('<<< response', response.json())
        assert response.status_code == 400
        assert response.json().get('detail') == ('You do not have rights to update API Key, '
                                                 'please contact Administrator.')


def test_delete_oas_not_editable(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()

        secret_key = secrets.token_hex(32)
        oas = OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key),
            is_editable=False,
            name='test'
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, f'{ORGANIZATION_AUTH_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now() + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(mem.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ORGANIZATION_AUTH_BASE_URL}/{str(oas.id)}'

        response = client.delete(url, headers={'authorization': token})
        assert response.status_code == 400
        assert response.json().get('detail') == 'The Selected API Key is not editable.'
