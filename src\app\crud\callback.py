from typing import List

from sqlalchemy import func
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app import models, schema, exceptions

from .base import BaseCRUD


class CallbackCRUD(BaseCRUD):
    model = models.Callback

    @classmethod
    def query(cls, dbsession: Session, *columns):
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        raise exceptions.ApolloPermissionError()


class OCPIHistoriesCRUD(BaseCRUD):
    model = models.OCPIHistories


def get_callback_list(db: Session) -> List[models.Callback]:
    query = CallbackCRUD.query(db).order_by(func.desc(models.Callback.created_at))
    return query.all()


def create_callback(db: Session, callback_data: schema.Callback) -> models.Callback:
    try:
        db_callback = CallbackCRUD.add(db, callback_data.dict())
        return db_callback
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloCreditCardError()


def create_ocpi_histories(db: Session, ocpi_histories: schema.OCPIHistories) -> models.OCPIHistories:
    try:
        db_callback = OCPIHistoriesCRUD.add(db, ocpi_histories.dict())
        return db_callback
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloError()
