'''Autocharge Schema'''
from enum import Enum
from typing import Optional
from pydantic import BaseModel


class AutochargeStatus(str, Enum):
    '''Status for Autocharge'''
    pending = 'Pending'
    active = 'Active'
    disabled = 'Disabled'  # Paused. Can be re-enabled.
    deactivated = 'Deactivated'


class Autocharge(BaseModel):
    '''Autocharge Schema'''
    mac_address: Optional[str]  # pylint: disable=unsubscriptable-object
    status: AutochargeStatus

    class Config:
        orm_mode = True


class AutochargeCreate(BaseModel):
    '''Autocharge Create Schema'''
    mac_address: Optional[str]  # pylint: disable=unsubscriptable-object
    status: AutochargeStatus = AutochargeStatus.pending
    vehicle_id: str


class AutochargeUpdate(BaseModel):
    '''Autocharge Update Schema'''
    status: Optional[str]  # pylint: disable=unsubscriptable-object
