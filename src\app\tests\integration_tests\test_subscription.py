from datetime import datetime, timed<PERSON>ta
from unittest.mock import patch
from contextlib import contextmanager
import pytest
import uuid

import jwt
from fastapi.testclient import TestClient

from app import models, crud
from app.main import app, ROOT_PATH
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory, CreditCardFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory,
                                 SubscriptionPlanFactory, SubscriptionFactory, SubscriptionCustomPlanFactory,
                                 SubscriptionCardFactory, SubscriptionInvitationFactory, SubscriptionFeeFactory)
from app.tests.mocks.async_client import MockAsyncClientGeneratorConnector
from app.database import SessionLocal, create_session, Base, engine
from app.schema import MembershipType, JWT_ALGORITHM

client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionCustomPlanFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionPlanFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionInvitationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionFeeFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


def test_create_subscription_plan(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/plan'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        data = {
            'name': 'name',
            'description': 'description',
            'category': 'Fixed',
            'amount': 10.0,
            'is_active': True,
            'organization_id': organization_id,
            'is_invitation_only': False,
            'allow_invitation_code': False,
            'renewal_fee': 10.0,
            'delivery_fee': 10.0,
            'delivery_fee_my': 10.0
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert response.status_code == 201
        assert response.json()['name'] == 'name'
        assert response.json()['id'] is not None


def test_update_subscription_plan(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/plan'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_id = str(subscription.id)

        data = {
            'amount': 50.0
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.patch(f'{url}/{subscription_id}', json=data, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['amount'] == 50.0


def test_get_subscription_plan(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/plan'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_id = str(subscription.id)
        subscription_name = subscription.name

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}/{subscription_id}', headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['name'] == subscription_name


def test_delete_subscription_plan(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/plan'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_id = str(subscription.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.delete(f'{url}/{subscription_id}', headers={'authorization': auth_token})

        assert response.status_code == 204


def test_get_subscription_plan_list(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/plan'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()

        SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(url, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert len(response.json()['items']) == 2


def test_create_subscription_fee(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/fee'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        data = {
            'subscription_plan_id': subscription_plan_id,
            'name': 'test',
            'amount': 1.0,
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert response.status_code == 201
        assert response.json()['name'] is not None


def test_get_subscription_fee(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/fee'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_fee = SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_fee_id = str(subscription_fee.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}/{subscription_fee_id}', headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['name'] is not None


def test_update_subscription_fee(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/fee'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_fee = SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_fee_id = str(subscription_fee.id)

        data = {
            'name': 'test_1',
            'amount': 1.4,
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.patch(f'{url}/{subscription_fee_id}', json=data, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['name'] == data['name']


def test_delete_subscription_fee(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/fee'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_fee = SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_fee_id = str(subscription_fee.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.delete(f'{url}/{subscription_fee_id}', headers={'authorization': auth_token})

        assert response.status_code == 204


def test_get_subscription_fee_list(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/fee'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id_1)
        db.commit()

        SubscriptionFeeFactory(subscription_plan_id=subscription_plan_id_1, name='test_2')
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(url, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert len(response.json()['items']) == 2


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorConnector)
def test_create_subscription_custom_plan(async_client_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/custom-plan'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        data = {
            'amount': 1.25,
            'connector_id': str(uuid.uuid4()),
            'subscription_plan_id': subscription_plan_id,
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert response.status_code == 201
        assert response.json()['amount'] == 1.25
        assert response.json()['id'] is not None


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorConnector)
def test_update_subscription_custom_plan(async_client_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/custom-plan'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_custom_plan = SubscriptionCustomPlanFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_custom_plan_id = str(subscription_custom_plan.id)

        data = {
            'amount': 1.25
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.patch(f'{url}/{subscription_custom_plan_id}',
                                json=data, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['amount'] == 1.25


def test_get_subscription_custom_plan(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/custom-plan'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_custom_plan = SubscriptionCustomPlanFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_custom_plan_id = str(subscription_custom_plan.id)
        subscription_custom_plan_amount = subscription_custom_plan.amount

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}/{subscription_custom_plan_id}', headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['amount'] == subscription_custom_plan_amount


def test_delete_subscription_custom_plan(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/custom-plan'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_custom_plan = SubscriptionCustomPlanFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_custom_plan_id = str(subscription_custom_plan.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.delete(f'{url}/{subscription_custom_plan_id}', headers={'authorization': auth_token})

        assert response.status_code == 204


def test_get_subscription_custom_plan_list(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/custom-plan'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        subscription_plan_2 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_2 = str(subscription_plan_2.id)

        SubscriptionCustomPlanFactory(subscription_plan_id=subscription_plan_id_1)
        db.commit()

        SubscriptionCustomPlanFactory(subscription_plan_id=subscription_plan_id_2)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(url, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert len(response.json()['items']) == 2


def test_create_subscription_card(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/card'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        data = {
            'subscription_plan_id': subscription_plan_id,
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert response.status_code == 201
        assert response.json()['number'] is not None


def test_get_subscription_card(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/card'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_card = SubscriptionCardFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_card_id = str(subscription_card.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}/{subscription_card_id}', headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['number'] is not None


def test_update_subscription_card(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/card'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_card = SubscriptionCardFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_card_id = str(subscription_card.id)

        data = {
            'number': crud.create_subscription_card_number()
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.patch(f'{url}/{subscription_card_id}', json=data, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['number'] == data['number']


def test_delete_subscription_card(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/card'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_card = SubscriptionCardFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_card_id = str(subscription_card.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.delete(f'{url}/{subscription_card_id}', headers={'authorization': auth_token})

        assert response.status_code == 204


def test_get_subscription_card_list(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/card'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        subscription_plan_2 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_2 = str(subscription_plan_2.id)

        SubscriptionCardFactory(subscription_plan_id=subscription_plan_id_1)
        db.commit()

        SubscriptionCardFactory(subscription_plan_id=subscription_plan_id_2)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(url, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert len(response.json()['items']) == 2


def test_create_subscription_invitation(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/invitation'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        data = {
            'code': 'yRCda2DVJe',
            'subscription_plan_id': subscription_plan_id,
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert response.status_code == 201
        assert response.json()['code'] == data['code']


def test_get_subscription_invitation(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/invitation'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_invitation = SubscriptionInvitationFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_invitation_id = str(subscription_invitation.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(f'{url}/{subscription_invitation_id}', headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['code'] is not None


def test_update_subscription_invitation(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/invitation'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_invitation = SubscriptionInvitationFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_invitation_id = str(subscription_invitation.id)

        data = {
            'code': 'yRCda2DVJe'
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.patch(f'{url}/{subscription_invitation_id}', json=data, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['code'] == data['code']


def test_delete_subscription_invitation(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/invitation'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        subscription_invitation = SubscriptionInvitationFactory(subscription_plan_id=subscription_plan_id)
        db.commit()
        subscription_invitation_id = str(subscription_invitation.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.delete(f'{url}/{subscription_invitation_id}', headers={'authorization': auth_token})

        assert response.status_code == 204


def test_get_subscription_invitation_list(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/invitation'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        subscription_plan_2 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_2 = str(subscription_plan_2.id)

        SubscriptionInvitationFactory(subscription_plan_id=subscription_plan_id_1)
        db.commit()

        SubscriptionInvitationFactory(subscription_plan_id=subscription_plan_id_2)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/.*',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(url, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert len(response.json()['items']) == 2


def test_register_member(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/member'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id = str(subscription_plan.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()

        data = {
            'subscription_plan_id': subscription_plan_id,
            'is_default': True,
            'payable_fees': [
                {
                    'name': 'Test fee 1',
                    'amount': 10.0
                },
                {
                    'name': 'Test fee 2',
                    'amount': 25.0
                },
            ],
            'currency': 'MYR'
        }

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.post(url, json=data, headers={'authorization': auth_token})

        assert response.status_code == 307

    with contextmanager(override_create_session)() as db:
        db_sub = db.query(models.Subscription).one()
        assert db_sub.is_default is True


def test_change_default_plan(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/member'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        subscription_plan_2 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_2 = str(subscription_plan_2.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()
        mem_id = str(mem.id)

        SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id_1, is_default=False)
        db.commit()
        new_default_sub = SubscriptionFactory(member_id=mem_id, subscription_plan_id=subscription_plan_id_2,
                                              is_default=False)
        db.commit()
        new_default_sub_id = str(new_default_sub.id)

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.patch(f'{url}/{new_default_sub.id}', headers={'authorization': auth_token})

        assert response.status_code == 200

    with contextmanager(override_create_session)() as db:
        db_sub = db.query(models.Subscription).filter(models.Subscription.id == new_default_sub_id).one()
        assert db_sub.is_default is True


def test_get_subscription_list(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/subscriptions/member'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        subscription_plan_2 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_2 = str(subscription_plan_2.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )
        db.commit()
        mem_user_id = str(mem.id)

        SubscriptionFactory(member_id=mem_user_id, subscription_plan_id=subscription_plan_id_1, is_default=False)
        db.commit()
        SubscriptionFactory(member_id=mem_user_id, subscription_plan_id=subscription_plan_id_2, is_default=False)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        response = client.get(url, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert len(response.json()['items']) == 2


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorConnector)
def test_get_connector_pricing_without_subscription(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        url = f'{ROOT_PATH}/api/v1/csms/subscriptions/connector-pricing/fad3f746-6b61-4cb8-ac78-a384625f0b87'
        response = client.get(url, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['billing_unit_fee'] == 1.25


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorConnector)
def test_get_connector_pricing_with_percentage_subscription(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        subscription_plan = SubscriptionPlanFactory(organization_id=organization_id, amount=5, category='Percentage')
        db.commit()

        SubscriptionFactory(member_id=f'{mem.id}', subscription_plan_id=f'{subscription_plan.id}', is_default=True)
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/subscriptions/?',
                                    'get,patch,post,delete', 'apollo-main')

        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

        url = f'{ROOT_PATH}/api/v1/csms/subscriptions/connector-pricing/fad3f746-6b61-4cb8-ac78-a384625f0b87'
        response = client.get(url, headers={'authorization': auth_token})

        assert response.status_code == 200
        assert response.json()['billing_unit_fee'] == 1.19
