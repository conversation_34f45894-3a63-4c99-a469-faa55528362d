"""108

Revision ID: 4949660ec2df
Revises: c719982dab27
Create Date: 2024-06-12 06:38:22.202662

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4949660ec2df'
down_revision = 'c719982dab27'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_payment_request', sa.Column('reference_number', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_payment_request', 'reference_number')
    # ### end Alembic commands ###
