import hashlib
import logging
from datetime import datetime, timezone, timedelta

from urllib.parse import urlencode, quote_plus
from zoneinfo import ZoneInfo

import jwt
from fastapi import APIRouter, HTTPException, Depends, Request, status

from app import schema
from app import settings, crud, exceptions
from app.crud import get_charging_session_bill_by_charging_session, get_payment_refund_by_refund_id, \
    get_payment_request, \
    get_payment_request_by_charging_session_bill_id, get_charging_session_bill, get_e_credit_note_by_id
from app.database import create_session, SessionLocal
from app.permissions import x_api_key
from app.schema import EInvoiceStatus, ECreditNoteStatus
from app.utils import decode_auth_token_from_headers, RouteErrorHandler, MAIN_URL_PREFIX
from app.e_invoice_utils import validate_e_invoice_status_for_mobile_display, \
    validate_e_credit_note_status_for_mobile_display

logger = logging.getLogger(__name__)

CHARGER_URL_PREFIX_V2 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'
ROOT_PATH = settings.MAIN_ROOT_PATH
E_INVOICE_CALL_BACK_EXPIRY_MINUTES = int(f'{settings.E_INVOICE_CALL_BACK_EXPIRY_MINUTES}')

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/e_invoice",
    tags=['v2 e_invoice'],
    dependencies=[
        # Depends(permission),
        Depends(x_api_key)
    ],
    route_class=RouteErrorHandler
)


@router.get("/request/{charging_session_id}", status_code=status.HTTP_200_OK)
async def request_e_invoice_submission_url(request: Request,  # pylint: disable=too-many-locals
                                           charging_session_id: str,
                                           dbsession: SessionLocal = Depends(create_session)):
    """
    Request e-Invoice Submission URL

    :param str charging_session_id: Target E-Invoice ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        e_invoice = None
        charging_session_bill = get_charging_session_bill_by_charging_session(dbsession, charging_session_id)
        if not charging_session_bill:
            raise HTTPException(403, "No invoice generated, cannot request for e-invoice.")

        if charging_session_bill:
            charging_session_bill = validate_e_invoice_status_for_mobile_display(dbsession, charging_session_bill,
                                                                                 update_data=False)
            e_invoice = charging_session_bill.e_invoice
        db_pr = get_payment_request_by_charging_session_bill_id(dbsession, charging_session_bill.id)
        db_member_id = str(db_pr.member_id)

        if db_member_id != membership_id:
            raise HTTPException(403, "Invoice owner missmatch, rejecting.")

        if e_invoice is not None:
            if e_invoice.status == EInvoiceStatus.not_applicable:
                raise HTTPException(403, "E-Invoice cannot be requested at this time.")

            e_invoice_base_url = settings.E_INVOICE_REQUEST_BASE_URL
            e_invoice_company_name = settings.E_INVOICE_REQUEST_COMPANY_NAME
            e_invoice_company_id = settings.E_INVOICE_COMPANY_ID

            invoice_number = e_invoice.e_invoice_number
            # e_invoice_date = e_invoice.e_invoice_date_time
            e_invoice_date = e_invoice.e_invoice_date_time.astimezone(ZoneInfo("Asia/Kuala_Lumpur"))
            e_invoice_dt = e_invoice_date.strftime("%Y-%m-%dT%H:%M:%S")
            e_invoice_date_only = e_invoice_date.date().isoformat()
            total_amount_str = f"{float(charging_session_bill.total_amount):.2f}"
            origin = "mobile"
            buyer_internal_code = db_member_id

            token = jwt.encode(
                {
                    "exp": datetime.utcnow() + timedelta(minutes=E_INVOICE_CALL_BACK_EXPIRY_MINUTES),
                    "charging_session_id": str(charging_session_bill.charging_session_id),
                },
                settings.E_INVOICE_JWT_SECRET,
                algorithm=schema.JWT_ALGORITHM,
            )

            # query_string = urlencode({'key': token})
            redirect_url = (f'{MAIN_URL_PREFIX}/api/v1/csms/e_invoice/redirect/?'
                            f'key={token}')
            params = {
                "cid": e_invoice_company_id,
                "invoiceNumber": invoice_number,
                "dt": e_invoice_date_only,
                "totalAmount": total_amount_str,
                "origin": origin,
                "buyerInternalCode": buyer_internal_code,
            }
            hash_parts = [
                e_invoice_company_id,
                invoice_number,
                e_invoice_dt,
                total_amount_str,
                origin,
                buyer_internal_code
            ]

            if settings.E_INVOICE_ADD_REDIRECT_URL:
                hash_parts.append(redirect_url)
                params["redirectUrl"] = redirect_url

            # Generate hash
            hash_input = "@".join(hash_parts)
            params["hash"] = hashlib.sha256(hash_input.encode("utf-8")).hexdigest()
            # Final URL
            submission_url = f"{e_invoice_base_url}/{e_invoice_company_name}?{urlencode(params)}"

            action_data = []
            response = {
                "action_url": submission_url,
                "action_method": 'GET',
                "action_data": action_data
            }
            # e_invoice_update = schema.EInvoiceUpdate(
            #     status=EInvoiceStatus.requested,
            #     last_submission_date=datetime.now(timezone.utc)
            # )
            # _ = crud.update_e_invoice(dbsession, e_invoice.id, e_invoice_update)
            return response

        raise HTTPException(403, "No e-invoice can be requested at this time.")
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/{charging_session_id}", status_code=status.HTTP_200_OK,
            response_model=schema.EInvoiceWithDownloadURLResponse)
async def get_e_invoice(request: Request, charging_session_id: str,
                        dbsession: SessionLocal = Depends(create_session)):
    """
    Get e-Invoice by Charging Session ID

    :param str charging_session_id: Charging Session ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        e_invoice = None
        charging_session_bill = get_charging_session_bill_by_charging_session(dbsession, charging_session_id)
        if not charging_session_bill:
            raise HTTPException(403, "No invoice generated, cannot view e-invoice")

        if charging_session_bill:
            charging_session_bill = validate_e_invoice_status_for_mobile_display(dbsession, charging_session_bill,
                                                                                 update_data=False)
            e_invoice = charging_session_bill.e_invoice
        if e_invoice is not None:
            if e_invoice.status == schema.EInvoiceStatus.validated:
                token = jwt.encode(
                    {
                        "exp": datetime.now(tz=timezone.utc) + timedelta(days=1),
                        "charging_session_id": str(charging_session_id),
                        "membership_id": f'{membership_id}',
                    },
                    settings.JWT_REPORT_SECRET,
                    algorithm=schema.JWT_ALGORITHM,
                )

                # Encode the token as a query parameter
                query_string = urlencode({'key': token})
                base_path = f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}"
                url = f"{base_path}/api/v1/invoice/e_invoice/pdf/download/?{query_string}"

                e_invoice.action_url = url
                e_invoice.action_method = 'GET'
                e_invoice.action_data = []

            return e_invoice

        raise HTTPException(403, "No e-invoice found")
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/credit/{charging_session_id}", status_code=status.HTTP_200_OK,
            response_model=list[schema.PaymentRefundResponseWithECreditNoteInfo])
async def get_payment_refund_with_e_cn_info(request: Request, charging_session_id: str,
                                            dbsession: SessionLocal = Depends(create_session)):
    """
    Get Payment Refund and its e-Credit-Note Info by Charging Session ID

    :param str charging_session_id: Charging Session ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        # e_invoice = None
        charging_session_bill = get_charging_session_bill_by_charging_session(dbsession, charging_session_id)
        if not charging_session_bill:
            raise HTTPException(403, "No invoice generated, payment refund not available")

        db_pr = get_payment_request_by_charging_session_bill_id(dbsession, charging_session_bill.id)

        db_member_id = str(db_pr.member_id)

        if db_member_id != membership_id:
            raise HTTPException(403, "Invoice owner missmatch, rejecting.")

        db_refunds = []
        if db_pr.payment_refund is not None:
            for refund in db_pr.payment_refund:
                if refund is None:
                    continue

                if refund.refund_status != 'Success':
                    continue

                if refund.e_credit_note is not None:
                    refund.e_credit_note = validate_e_credit_note_status_for_mobile_display(
                        dbsession, charging_session_bill, refund.e_credit_note
                    )

                db_refunds.append(schema.PaymentRefundResponseWithECreditNoteInfo.from_orm(refund))
        return db_refunds

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/credit/cn/{credit_note_id}", status_code=status.HTTP_200_OK,
            response_model=schema.ECreditNoteWithDownloadURLResponse)
async def get_e_credit_note_info(request: Request, credit_note_id: str,
                                 dbsession: SessionLocal = Depends(create_session)):
    """
    Get e-Credit-Note Info by ID

    :param str credit_note_id: Credit Note ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        # e_invoice = None

        db_credit_note = get_e_credit_note_by_id(dbsession, credit_note_id)

        if not db_credit_note:
            raise HTTPException(403, "No e-credit note available")
        db_payment_refund = get_payment_refund_by_refund_id(dbsession, db_credit_note.payment_refund_id)
        db_pr = get_payment_request(dbsession, db_payment_refund.payment_request_id)
        charging_session_bill = get_charging_session_bill(dbsession, db_pr.charging_session_bill_id)

        db_credit_note = schema.ECreditNoteWithDownloadURLResponse.from_orm(db_credit_note)

        db_credit_note = validate_e_credit_note_status_for_mobile_display(
            dbsession, charging_session_bill, db_credit_note
        )
        if db_credit_note.status == schema.ECreditNoteStatus.validated:
            token = jwt.encode(
                {
                    "exp": datetime.now(tz=timezone.utc) + timedelta(days=1),
                    "credit_note_id": str(credit_note_id),
                    "membership_id": f'{membership_id}',
                },
                settings.JWT_REPORT_SECRET,
                algorithm=schema.JWT_ALGORITHM,
            )
            # Encode the token as a query parameter
            query_string = urlencode({'key': token})
            base_path = f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}"
            url = f"{base_path}/api/v1/invoice/e_credit_note/pdf/download/?{query_string}"

            db_credit_note.action_url = url
            db_credit_note.action_method = 'GET'
            db_credit_note.action_data = []
            return db_credit_note

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/credit/request/{payment_refund_id}",
            status_code=status.HTTP_200_OK)
async def request_e_cn_submission_url(request: Request,  # pylint: disable-all
                                      payment_refund_id: str,
                                      dbsession: SessionLocal = Depends(create_session)):
    """
    Request e-Credit Note based on Credit-Note (Refund) ID

    :param str payment_refund_id: Target credit-note (refund) id
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        e_invoice = None
        payment_refund = get_payment_refund_by_refund_id(dbsession, payment_refund_id)
        e_cn = payment_refund.e_credit_note
        db_pr = get_payment_request(dbsession, payment_refund.payment_request_id)
        charging_session_bill = get_charging_session_bill(dbsession, db_pr.charging_session_bill_id)

        db_member_id = str(db_pr.member_id)

        if db_member_id != membership_id:
            raise HTTPException(403, "Invoice owner missmatch, rejecting.")

        if not charging_session_bill:
            raise HTTPException(403, "No invoice generated, cannot request for e-invoice.")

        if charging_session_bill:
            charging_session_bill = validate_e_invoice_status_for_mobile_display(dbsession, charging_session_bill,
                                                                                 update_data=False)
            e_invoice = charging_session_bill.e_invoice

        if e_invoice is None:
            raise HTTPException(403, "No e-invoice is available.")

        if e_invoice.status not in [EInvoiceStatus.validated]:
            raise HTTPException(403, "E-Invoice is not validated, cannot request e-Credit Note.")

        if e_cn is None:
            raise HTTPException(403, "No e-credit-note is available.")

        if e_cn.status == ECreditNoteStatus.not_applicable:
            raise HTTPException(403, "E-Credit-Note cannot be requested now")
        # else:
        e_invoice_base_url = settings.E_INVOICE_REQUEST_BASE_URL
        e_invoice_company_name = settings.E_INVOICE_REQUEST_COMPANY_NAME
        e_invoice_company_id = settings.E_INVOICE_COMPANY_ID
        total_amount_str = f"{float(payment_refund.refund_amount):.2f}"
        invoice_number = e_cn.e_credit_note_number

        # e_cn_date = e_cn.e_credit_note_date_time
        e_cn_date = e_cn.e_credit_note_date_time.astimezone(ZoneInfo("Asia/Kuala_Lumpur"))
        e_cn_dt = e_cn_date.strftime("%Y-%m-%dT%H:%M:%S")

        e_cn_date_only = e_cn_date.date().isoformat()

        origin = "mobile"
        buyer_internal_code = db_member_id

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(minutes=E_INVOICE_CALL_BACK_EXPIRY_MINUTES),
                "payment_refund_id": str(payment_refund.id),
            },
            settings.E_INVOICE_JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        # query_string = urlencode({'key': token})
        redirect_url = (f'{MAIN_URL_PREFIX}/api/v1/csms/e_invoice/cn/redirect/?'
                        f'key={token}')
        params = {
            "cid": e_invoice_company_id,
            "invoiceNumber": invoice_number,
            "dt": e_cn_date_only,
            "totalAmount": total_amount_str,
            "origin": origin,
            "buyerInternalCode": buyer_internal_code,
        }
        hash_parts = [
            e_invoice_company_id,
            invoice_number,
            e_cn_dt,
            total_amount_str,
            origin,
            buyer_internal_code
        ]

        if settings.E_INVOICE_ADD_REDIRECT_URL:
            hash_parts.append(redirect_url)
            params["redirectUrl"] = redirect_url

        # Generate hash
        hash_input = "@".join(hash_parts)
        params["hash"] = hashlib.sha256(hash_input.encode("utf-8")).hexdigest()
        # Final URL
        submission_url = f"{e_invoice_base_url}/{e_invoice_company_name}?{urlencode(params)}"

        action_data = []
        response = {
            "action_url": submission_url,
            "action_method": 'GET',
            "action_data": action_data
        }
        # e_cn_update = schema.ECreditNoteUpdate(
        #     status=EInvoiceStatus.requested,
        #     last_submission_date=datetime.now(timezone.utc)
        # )
        # _ = crud.update_e_credit_note(dbsession, e_cn.id, e_cn_update)
        return response

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
