import logging
from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app import settings, crud, exceptions
from app.database import create_session, SessionLocal
from app.permissions import permission
from app.utils import decode_auth_token_from_headers

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH


router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/favorite_cp",
    tags=['favorite_charge_point', ],
    dependencies=[Depends(permission)]
)


@router.post("/", status_code=status.HTTP_201_CREATED, response_model=List[UUID])
async def add_favorite_cp(request: Request,
                          charge_point_id: str,
                          dbsession: SessionLocal = Depends(create_session)):
    """
    Add a Charge Point to Member's Favorite List

    :param str charge_point_id: Charge Point ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        favorite_cp_list = crud.add_favorite_cp(dbsession, membership_id, charge_point_id)
        return favorite_cp_list
    except exceptions.ApolloIDTagError as e:
        raise HTTPException(400, e.__str__())


@router.get("/", status_code=status.HTTP_200_OK, response_model=List[UUID])
async def get_favorite_cp_list(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Get Member's Favorite Charge Points List
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        favorite_cp_list = crud.get_member_favorite_cp_list(dbsession, membership_id)
        return favorite_cp_list
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{charge_point_id}", status_code=status.HTTP_200_OK, response_model=List[UUID])
async def delete_favorite_cp(request: Request,
                             charge_point_id: str,
                             dbsession: SessionLocal = Depends(create_session)):
    """
    Remove a Charge Point From Member's Favorite List

    :param str charge_point_id: Charge Point ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        favorite_cp_list = crud.remove_favorite_cp(dbsession, membership_id, charge_point_id)
        return favorite_cp_list
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
