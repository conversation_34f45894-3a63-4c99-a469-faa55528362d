"""151

Revision ID: 0f25b5932237
Revises: aa0c192f7e1d
Create Date: 2025-02-28 02:56:19.786871

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0f25b5932237'
down_revision = 'aa0c192f7e1d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('unique_operator_subscription_plan', 'main_subscription_operator_plan', type_='unique')
    op.create_index('unique_operator_subscription_plan', 'main_subscription_operator_plan', ['operator_id', 'subscription_plan_id', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted'))

    op.drop_constraint('unique_tariff_tag_subscription_plan', 'main_subscription_tariff_plan', type_='unique')
    op.create_index('unique_tariff_tag_subscription_plan', 'main_subscription_tariff_plan', ['tariff_tag_id', 'subscription_plan_id', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('unique_tariff_tag_subscription_plan', table_name='main_subscription_tariff_plan', postgresql_where=sa.text('NOT is_deleted'))
    op.create_unique_constraint('unique_tariff_tag_subscription_plan', 'main_subscription_tariff_plan', ['tariff_tag_id', 'subscription_plan_id'])

    op.drop_index('unique_operator_subscription_plan', table_name='main_subscription_operator_plan', postgresql_where=sa.text('NOT is_deleted'))
    op.create_unique_constraint('unique_operator_subscription_plan', 'main_subscription_operator_plan', ['operator_id', 'subscription_plan_id'])
    # ### end Alembic commands ###
