"""56

Revision ID: cac92efae9ff
Revises: f33ae9111fbb
Create Date: 2023-09-13 16:10:27.262541

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'cac92efae9ff'
down_revision = 'f33ae9111fbb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_external_organization', sa.Column('name', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_external_organization', 'name')
    # ### end Alembic commands ###
