"""124

Revision ID: 155fd6c7f4a0
Revises: 188ffcea26bd
Create Date: 2024-09-30 12:46:24.569124

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '155fd6c7f4a0'
down_revision = '188ffcea26bd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_reporting_task',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('task_name', sa.String(), nullable=True),
    sa.Column('json_payload', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_reporting_task')
    # ### end Alembic commands ###
