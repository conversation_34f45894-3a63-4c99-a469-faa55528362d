<!DOCTYPE html>
<html>
  <head>
    <link
      href="https://fonts.googleapis.com/css?family=Lato"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css?family=Calibri"
      rel="stylesheet"
    />
    <style>
      body {
        font-family: "Lato";
        background-color: "#F3F3F3";
      }
      div {
        width: 75vw;
        height: 50vh;

        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;

        margin: auto;
      }

      img {
        width: 30vw;
        display: block;
        margin-left: auto;
        margin-right: auto;
      }
      p {
        font-weight: bold;
        align-items: center;
        align-content: center;
        text-align: center;
        font-size: 3vh;
      }

      div > button {
        background-color: #2fcc8b;
        border: 2px solid #2fcc8b;
        border-radius: 20px;
        padding-top: 3%;
        padding-bottom: 3%;
        width: 100%;
        color: #ffffff;
        font-weight: bold;
        font-size: 1.8vh;
      }
    </style>
  </head>
  <body>
    <div class="failure">
      <img
        src="data:image/png;base64, 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"
        alt="Failure Icon"
      />
      <p>Sorry, something went wrong</p>
      <button onclick="runFunction()">OK</button>
    </div>
  </body>
</html>

<script>
  window.onload = function () {
    window.parent.postMessage(
      { status: "success", html: document.documentElement.outerHTML },
      "*"
    );
    var viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute(
        "content",
        "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
      );
    } else {
      var meta = document.createElement("meta");
      meta.setAttribute("name", "viewport");
      meta.setAttribute(
        "content",
        "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
      );
      document.head.appendChild(meta);
    }
  };
  function runFunction() {
    <!-- Close payment website -->
    try {
      window.webkit.messageHandlers.backToIos.postMessage({ status: "ok" });
    } catch (err) {
      console.log("swift catch");
    }

    try {
      backToAndroid.onBackClicked();
    } catch (err) {
      console.log("android catch");
    }
  }
</script>
