"""95

Revision ID: 7568df9eb42c
Revises: 03005a7e4119
Create Date: 2024-05-13 13:32:08.609438

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7568df9eb42c'
down_revision = '03005a7e4119'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_membership', sa.Column('allow_marketing', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_membership', 'allow_marketing')
    # ### end Alembic commands ###
