import logging
import uuid

from fastapi import APIRouter, HTTPException, Depends, Request

from app import settings, schema, crud, exceptions, models
from app.crud import get_charging_session_bill_by_charging_session, get_payment_request_by_charging_session_bill_id
from app.database import create_session, SessionLocal
from app.e_invoice_utils import construct_and_submit_invoice_to_rmp
from app.middlewares import set_admin_as_context_user, deactivate_audit
from app.utils import decode_auth_token_from_headers, encode_token_to_base64, generate_charger_header, send_request, \
    billing_request_ocpi, decode_base64_to_token

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH
CHARGER_URL_PREFIX_V2 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/external_auth",
    tags=['external_auth', ],
)


@router.post('/signup-external-service', status_code=200)
async def signup_external_service(request: Request, data: schema.SignupRequest,
                                  dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-up endpoint for external service in Credentials OCPI module
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    user_id = auth_token_data.get('user_id')
    membership_id = auth_token_data.get('membership_id')
    db_user = crud.get_user_by_id(dbsession, user_id)
    db_member = crud.get_membership_by_id(dbsession, membership_id)
    if db_user.is_superuser or db_member.membership_type == schema.MembershipType.staff:
        try:
            return crud.register_external_service(dbsession, data)
        except (exceptions.ApolloUserDataError, exceptions.ApolloDuplicateMembershipError,
                exceptions.ApolloExternalTokenDataError) as e:
            raise HTTPException(400, e.__str__())
    raise HTTPException(403, "You are not allowed to perform this action")


@router.post('/generate-token-a', status_code=200)
async def generate_token_a(request: Request,
                           dbsession: SessionLocal = Depends(create_session)):
    """
    Generate Token A for Sender for Credentials OCPI module
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    user_id = auth_token_data.get('user_id')
    membership_id = auth_token_data.get('membership_id')
    db_user = crud.get_user_by_id(dbsession, user_id)
    db_member = crud.get_membership_by_id(dbsession, membership_id)
    if db_user.is_superuser or db_member.membership_type == schema.MembershipType.staff:
        try:

            temp_token = str(uuid.uuid4())
            return crud.create_token_a(dbsession, temp_token)
        except (exceptions.ApolloUserDataError, exceptions.ApolloDuplicateMembershipError,
                exceptions.ApolloExternalTokenDataError) as e:
            raise HTTPException(400, e.__str__())
    raise HTTPException(403, "You are not allowed to perform this action")


@router.post("/signin-external-service/{external_token}", include_in_schema=False, status_code=200)
async def signin_external_service(external_token: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-in endpoint for external service
    """
    try:
        external_service_token = crud.get_external_token(external_token, dbsession)
        db_user = dbsession.query(models.User).filter(models.User.id == external_service_token.user_id).one()
        db_membership = dbsession.query(models.Membership).filter(models.Membership.user_id == db_user.id).one()
        db_user.password = db_membership.password
        user = schema.UserAuth.from_orm(db_user)
        return user.generate_token(str(db_membership.id))
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.patch("/external-token/{external_token}", include_in_schema=False, status_code=200)
async def update_external_service_info(request: Request, external_token: str, token_data: schema.ExternalTokenUpdate,
                                       dbsession: SessionLocal = Depends(create_session)):
    """
    Update external service token information
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        user_id = auth_token_data.get('user_id')
        crud.get_user_by_id(dbsession, user_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'No Authorized.')

    data = token_data.dict()
    try:
        db_external_token = crud.update_external_token(external_token, dbsession, data)
        return db_external_token
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


# @router.post("/generate-new-auth-token/{external_token}", include_in_schema=False, status_code=200)
@router.post("/generate-new-auth-token/{external_token}", status_code=200)
async def generate_new_token_for_external_service(request: Request, external_token: str,
                                                  token_data: schema.ExternalTokenUpdate,
                                                  dbsession: SessionLocal = Depends(create_session)):
    """
    Generate new authorization permanent token for external service
    """
    # try:
    #     auth_token_data = decode_auth_token_from_headers(request.headers)
    #     user_id = auth_token_data.get('user_id')
    #     crud.get_user_by_id(dbsession, user_id)
    # except exceptions.ApolloObjectDoesNotExist:
    #     raise HTTPException(400, 'No Authorized.')
    try:
        crud.get_token_by_temp_token(external_token, dbsession)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())

    data = token_data.dict()
    external_token_encoded = encode_token_to_base64(data['external_service_token'])
    data['external_service_token'] = encode_token_to_base64(external_token_encoded)
    data['token'] = str(uuid.uuid4())
    data['is_temporary'] = False
    try:
        external_token = crud.update_external_token_by_temp_token(external_token, dbsession, data)
        # updated_external_token = crud.get_external_token(str(external_token.external_service_token), dbsession)
        return {'token': str(external_token.token)}

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/external-token/{external_token}", include_in_schema=False, status_code=200)
async def get_external_token(request: Request, external_token: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Get external service token
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        user_id = auth_token_data.get('user_id')
        crud.get_user_by_id(dbsession, user_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'No Authorized.')

    try:
        external_token = crud.get_external_token(external_token, dbsession)
        return external_token
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/check-existing-temp-token/{temp_token}", include_in_schema=False, status_code=200)
async def get_existing_temp_token(request: Request, temp_token: uuid.UUID,
                                  dbsession: SessionLocal = Depends(create_session)):
    """
    Check if CREDENTIAL_TOKEN_A exists
    """

    try:
        temp_token = crud.get_temp_token(temp_token, dbsession)
        return temp_token
    except exceptions.ApolloObjectDoesNotExist as e:
        print(e)
        return None
        # raise HTTPException(400, e.__str__())


@router.get("/get-client-token/{token}", status_code=200)
async def get_client_token(request: Request, token: uuid.UUID,
                           dbsession: SessionLocal = Depends(create_session)):
    """
    GET OCPI Client Token with Token C
    """

    try:
        client_token = crud.get_client_token(token, dbsession)
        return {'external_service_token': decode_base64_to_token(client_token.external_service_token)}
    except exceptions.ApolloObjectDoesNotExist as e:
        print('client token not found', e)
        raise HTTPException(400, e.__str__())


# @router.post("/temp_token_auth/{temp_token}", include_in_schema=False, status_code=200)
@router.post("/temp_token_auth/{temp_token}", status_code=200)
async def temp_token_authentication(temp_token: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Authentication with CREDENTIAL_TOKEN_A
    """
    try:
        temp_token = crud.get_token_by_temp_token(temp_token, dbsession)
        # db_user = dbsession.query(models.User).filter(models.User.id == temp_token.user_id).one()
        # db_membership = dbsession.query(models.Membership).filter(models.Membership.user_id == db_user.id).one()
        # db_user.password = db_membership.password
        # user = schema.UserAuth.from_orm(db_user)
        if temp_token:
            return {'success': True, 'token': encode_token_to_base64(str(temp_token.token))}
        return {'success': False}
        # return user.generate_token(str(db_membership.id))
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/auth-token-a-or-c/{token}", status_code=200)
async def authentication_with_token_a_or_c(token: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Authentication with CREDENTIAL_TOKEN_A or CREDENTIAL_TOKEN_C
    """
    try:
        token = crud.get_token_a_or_c(token, dbsession)

        if token:
            return {'success': True, 'token': encode_token_to_base64(str(token.token))}
        return {'success': False, 'token': None}
        # return user.generate_token(str(db_membership.id))
    except exceptions.ApolloObjectDoesNotExist as e:
        print(e)
        return None


@router.get('/id-tag-token/{token}', status_code=200)
async def get_id_tag_by_id_tag_token(token: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Get idtag given token
    """
    set_admin_as_context_user(dbsession)
    token = crud.get_membership_by_ocpi_token_id(dbsession, token)

    return token


@router.post("/ocpi-auth/{token}", status_code=200)
async def authentication_with_token_c(token: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Authentication with only CREDENTIAL_TOKEN_C
    """
    try:
        token = crud.token_c_auth(token, dbsession)
        if token:
            encoded_token = encode_token_to_base64(str(token.token))
            party_id = token.external_organization.party_id
            country_code = token.external_organization.country_code
            standardized_tariff = int(token.standardized_tariff) if token.standardized_tariff else 0
            is_publish_all = int(token.is_publish_all) if token.is_publish_all else 0
            is_lta = int(token.is_lta) if token.is_lta else 0
            return {'success': True, 'token': encoded_token, 'party_id': party_id,
                    'country_code': country_code, 'standardized_tariff': str(standardized_tariff),
                    'is_publish_all': str(is_publish_all), 'is_lta': str(is_lta)}
        return {'success': False, 'token': None}
        # return user.generate_token(str(db_membership.id))
    except exceptions.ApolloObjectDoesNotExist as e:
        print(e)
        return None


@router.post("/ocpi-charger-auth/{token}", status_code=200)
async def authentication_with_token_c_to_charger(token: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Authentication with CREDENTIAL_TOKEN_C for charger
    """
    try:
        token = crud.token_c_auth(token, dbsession)
        if token:
            standardized_tariff = int(token.standardized_tariff) if token.standardized_tariff else 0
            is_publish_all = int(token.is_publish_all) if token.is_publish_all else 0
            is_lta = int(token.is_lta) if token.is_lta else 0

            return {'success': True, 'token': encode_token_to_base64(str(token.token)), 'token_object': token,
                    'standardized_tariff': str(standardized_tariff),
                    'is_publish_all': str(is_publish_all), 'is_lta': str(is_lta)}
        return {'success': False, 'token': None, 'token_object': token}
        # return user.generate_token(str(db_membership.id))
    except exceptions.ApolloObjectDoesNotExist as e:
        print(e)
        return None


@router.post('/cpo/token', response_model=schema.OCPICPOTokenResponse, status_code=200)
async def create_cpo_token_token(token: schema.OCPICPOToken, dbsession: SessionLocal = Depends(create_session)):
    """
    Get idtag given token
    """
    set_admin_as_context_user(dbsession)
    deactivate_audit()
    token = crud.create_cpo_token(dbsession, token)

    return token


@router.get('/cpo/token/{token}', response_model=schema.OCPICPOTokenResponse, status_code=200)
async def get_cpo_token_token_by_id(token: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Get idtag given token
    """
    set_admin_as_context_user(dbsession)
    token = crud.get_cpo_token(dbsession, token)

    return token


@router.patch('/cpo/token/{token}', response_model=schema.OCPICPOTokenResponse, status_code=200)
async def update_cpo_token_token_by_id(token_schema: schema.OCPICPOTokenUpdate, token: str,
                                       dbsession: SessionLocal = Depends(create_session), ):
    """
    Get idtag given token
    """
    set_admin_as_context_user(dbsession)
    token = crud.update_cpo_token(dbsession, token, token_schema)

    return token


@router.post('/cpo/cdr', response_model=schema.OCPICPOCdrResponse, status_code=200)
async def create_cpo_cdr(cdr: schema.OCPICPOCdrCreate,  # pylint: disable=too-many-locals
                         dbsession: SessionLocal = Depends(create_session)):
    """
    Get idtag given token
    """
    admin = set_admin_as_context_user(dbsession)
    deactivate_audit()
    token = crud.create_cpo_cdr_session(dbsession, cdr)

    ocpi_token_id = cdr.cdr_token.get('uid')

    db_ocpi_token = crud.OCPITokenCRUD.get(dbsession, ocpi_token_id)

    # db_member = crud.MembershipCRUD.get(dbsession, db_ocpi_token.member_id)

    headers = generate_charger_header(dbsession, db_ocpi_token.member_id)

    path = 'csms/ocpi/emsp/session'
    url = f'{CHARGER_URL_PREFIX_V2}/{path}/{cdr.country_code}/{cdr.party_id}/{cdr.session_id}'
    response = await send_request('GET', url, headers=headers)
    response_json = response.json()

    if response.status_code == 200:
        charging_session_id = response_json.get('charging_session_id')
        transaction_id = response_json.get('transaction_id')
        connector_id = response_json.get('ocpi_evse_connector_id')
        operator_id = response_json.get('ocpi_evse_connector', {}).get('ocpi_evse', {}).get('operator_id')
        # charging_session_obj = response_json
        cdr_update = schema.OCPICPOCdrUpdate(
            native_charging_session_id=charging_session_id,
            transaction_id=transaction_id,
            operator_id=operator_id
        )

        crud.update_cpo_cdr_session_by_id(dbsession, token.id, cdr_update)

    amount_incl_tax = float(cdr.total_cost.get('incl_vat'))
    amount_excl_tax = float(cdr.total_cost.get('excl_vat'))
    tax_amount = amount_incl_tax - amount_excl_tax
    try:
        tax_rate = round((tax_amount / amount_excl_tax) * 100, 1)
    except ZeroDivisionError:
        tax_rate = 0.00

    hogging_cost = 0
    if cdr.total_parking_cost:
        hogging_cost = float(cdr.total_parking_cost.get('incl_vat', 0)) - float(
            cdr.total_parking_cost.get('excl_vat', 0))

    path = 'csms/charging'
    url = f'{CHARGER_URL_PREFIX_V2}/{path}/{charging_session_id}'
    headers = generate_charger_header(dbsession, admin.id)
    response = await send_request('GET', url, headers=headers)
    response_json = response.json()
    charging_session_obj = response_json

    if cdr.credit:
        return token

    billing_request = await billing_request_ocpi(db=dbsession, id_tag=db_ocpi_token.contract_id,
                                                 amount=float(cdr.total_cost.get('incl_vat')),
                                                 session_start=cdr.start_date_time, session_end=cdr.end_date_time,
                                                 charging_session_id=charging_session_id,
                                                 transaction_id=transaction_id, billing_currency=cdr.currency,
                                                 connector_id=connector_id, tax_rate=tax_rate, tax_amount=tax_amount,
                                                 hogging_cost=hogging_cost, charging_session=charging_session_obj,
                                                 total_energy=float(cdr.total_energy),
                                                 total_time=float(cdr.total_time))

    db_csb = get_charging_session_bill_by_charging_session(dbsession, charging_session_id)
    db_pr = get_payment_request_by_charging_session_bill_id(dbsession, str(db_csb.id))

    await construct_and_submit_invoice_to_rmp(dbsession,
                                              charging_session_obj,
                                              db_csb,
                                              str(db_pr.member_id))

    if billing_request:
        path = 'charging/ocpi/charging-session-bill'
        query_params = {
            'charging_session_bill_id': billing_request.get('charging_session_bill_id')
        }
        url = f'{CHARGER_URL_PREFIX_V2}/{path}/{charging_session_id}'
        await send_request('PATCH', url, headers=headers, query_params=query_params)

    return token


@router.get('/cpo/cdr/{id}', response_model=schema.OCPICPOCdrResponse, status_code=200)
async def get_cpo_cdr_given_id(id: str, dbsession: SessionLocal = Depends(create_session)):
    """
    Get idtag given token
    """
    set_admin_as_context_user(dbsession)
    cdr = crud.get_cpo_cdr_session(dbsession, id)

    return cdr


@router.post('/cpo/{token_b}', status_code=200)
async def update_cpo(token_b: str, data: schema.CreateExternalOrganization,
                     dbsession: SessionLocal = Depends(create_session)):
    """
    Update CPO with its business details
    """
    try:
        set_admin_as_context_user(dbsession)

        encoded_token_b = encode_token_to_base64(str(token_b))
        second_layer_encoded_b = encode_token_to_base64(str(encoded_token_b))
        token = crud.get_external_token(second_layer_encoded_b, dbsession)
        external_org = crud.create_external_organization(dbsession, data)
        updated_ext_token = crud.link_external_token_with_external_organization(dbsession, token.external_service_token,
                                                                                external_org.id)
        # db_operator = crud.update_operator(dbsession, data, cpo.id)

        return updated_ext_token
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
