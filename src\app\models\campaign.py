import sqlalchemy as db
from sqlalchemy.dialects.postgresql import UUID, ARRAY, JSONB

from app.models.base import BaseModel


class Campaign(BaseModel):
    __tablename__ = 'main_campaign'

    name = db.Column(db.String, unique=True)
    description = db.Column(db.String)
    public_description = db.Column(db.String)
    organization_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_organization.id', ondelete='CASCADE'))
    organization = db.orm.relationship('Organization')

    # either fixed or percentage
    discount_category = db.Column(db.String, nullable=False)  # Enums: CampaignDiscountCategory
    discount_amount = db.Column(db.Numeric(scale=2), nullable=False)
    discount_amount_cap = db.Column(db.Numeric(scale=2))
    currency = db.Column(db.String, nullable=False)  # Enums: CampaignDiscountCurrency

    # minimum amount to apply promotion
    min_amount = db.Column(db.Numeric(scale=2))
    is_reusable = db.Column(db.Boolean, default=True)

    # unique / non-unique (each with own Tab at UI)
    promo_code_type = db.Column(db.String, nullable=False)  # Enums: CampaignPromoCodeType

    start_at = db.Column(db.DateTime(timezone=True), nullable=False)
    expire_at = db.Column(db.DateTime(timezone=True), nullable=False)
    is_active = db.Column(db.Boolean, default=True)

    only_exclusive_locations = db.Column(db.Boolean, default=False)
    exclusive_location_ids = db.Column(ARRAY(UUID(as_uuid=True)), default=[])
    ocpi_usable = db.Column(db.Boolean, default=False)

    per_user_usage_limit = db.Column(db.Integer)
    # Enums: New (no session), Recent (1-5 sessions), Premium (more than X sessions)
    valid_membership_segments_only = db.Column(ARRAY(db.String))
    is_stackable = db.Column(db.Boolean, default=False)
    valid_period = db.Column(JSONB)


class CampaignPromoCode(BaseModel):
    __tablename__ = 'main_campaign_promo_code'

    code = db.Column(db.String, unique=True, nullable=False)
    limit = db.Column(db.Integer, default=1)

    campaign_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_campaign.id', ondelete='CASCADE'))
    campaign = db.orm.relationship('Campaign')
    promo_code_usage = db.orm.relationship('CampaignPromoCodeUsage', back_populates='campaign_promo_code')


class CampaignPromoCodeUsage(BaseModel):
    __tablename__ = 'main_campaign_promo_code_usage'

    # booked or/and used
    booked = db.Column(db.Boolean, default=False)
    membership_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_membership.id', ondelete='CASCADE'))
    used = db.Column(db.Boolean, default=False)
    used_at = db.Column(db.DateTime(timezone=True))
    charging_session_id = db.Column(db.String)

    campaign_promo_code_id = db.Column(
        UUID(as_uuid=True),
        db.ForeignKey('main_campaign_promo_code.id', ondelete='CASCADE'),
    )
    transaction_id = db.Column(db.String, default=None)
    campaign_promo_code = db.orm.relationship('CampaignPromoCode', back_populates='promo_code_usage')
    membership = db.orm.relationship('Membership')
