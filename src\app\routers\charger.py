# pylint:disable=too-many-lines
import json
import logging
import re
import uuid
from datetime import datetime, timezone
from functools import partial
import pytz
from botocore.exceptions import ClientError
from fastapi import APIRouter, Depends, Request, HTTPException, UploadFile, File, BackgroundTasks, \
    status, Response
from fastapi.responses import JSONResponse, FileResponse

from fastapi_pagination import Params

from starlette.datastructures import QueryParams
from sqlalchemy import cast, String
from sqlalchemy.orm import Session
from app import schema, settings, models, crud, exceptions
from app.crud.reporting_task import create_reporting_task_record
from app.middlewares import set_admin_as_context_user
from app.crud import get_operator_by_operator_name, get_chargev_lhn_np, TaxRateCRUD, \
    get_operator_list_by_organization_id, UserCRUD
from app.permissions import permission
from app.database import create_session, SessionLocal
from app.s3_utils import delete_image, upload_image_with_presigned_url, generate_presigned_url_for_view_image, \
    S3_BUCKET
from app.schema import ReportingTask<PERSON><PERSON>
from app.settings import S3_REGION
from app.utils import (
    decode_auth_token_from_headers, PreChargingFlow,
    send_request, generate_charger_header, charger_merge_user_details,
    operator_charge_points, GenerateReport, push_update,
    get_charging_history_data, get_energy_dispenses_data,
    GenerateEnergyGraph,
    send_cp_breakdown_email,
    stringify_dict_value, generate_cp_qrcode, generate_connector_qrcode, generate_location_qrcode,
    generate_upload_lta_static_excel,
    read_excel_s3_temp, process_adhoc_excel, get_all_child_organizations, is_disallowed_image,
)
# moved to celery, uncomment this if celery not working
# from app.celery import app
from app.schema.auth import MessageReason
from app.schema.v2.charger import ChargingSessionFiltersParams
from app.lta_tasks import (generate_lta_static_monthly_task, send_charging_history_via_email_sync,
                           send_cpo_cdr_via_email_sync, send_ocpp_log_via_email)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix=f"/{settings.MAIN_ROOT_PATH}/api/v1/csms/charger",
    tags=['charger', ],
    dependencies=[Depends(permission)],
)
CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'
S3_DEFAULT_URL = f'https://s3-{S3_REGION}.amazonaws.com/{S3_BUCKET}'


async def log_activity(user_id: str, table_name: str, data: dict,
                       log_type: schema.ActivityLogType, dbsession: SessionLocal = Depends(create_session)):
    db_activity = models.ActivityLog(user_id=user_id, table_name=table_name, data=data, type=log_type)
    dbsession.add(db_activity)
    dbsession.commit()


def write_audit_trail(dbsession: SessionLocal, table_name: str, user_id: str, membership_id: str,
                      log_type: schema.AuditLogType, api: str, group_id: str,
                      data_before: dict = None, data_after: dict = None,
                      remarks: dict = None, object_id: str = None, **kwargs):
    data_before = stringify_dict_value(data_before)
    data_after = stringify_dict_value(data_after)

    module = str(table_name)
    module = " ".join(word.capitalize() for word in module.split("_"))
    if not isinstance(object_id, (dict, list)):
        object_id = (object_id,)

    db_activity = models.UserAuditLog(user_id=user_id, membership_id=membership_id,
                                      table_name=table_name, action=log_type,
                                      data_before=data_before, data_after=data_after,
                                      api=api, group_id=group_id, module=module,
                                      object_id=object_id, remarks=remarks)
    dbsession.add(db_activity)
    dbsession.commit()


async def audit_log_charger(dbsession, response, user_id, membership_id, request, is_celery=False):
    if is_celery:
        write_audit_trail(dbsession=dbsession, user_id=user_id, membership_id=membership_id,
                          group_id=str(uuid.uuid4()), **response)
    else:
        resp_header = getattr(response, 'headers', {})
        if 'X-Audit' in resp_header:
            x_audit_resp = response.headers.get('X-Audit')
            x_audit = json.loads(x_audit_resp)
            group_id = request.headers.get('X-group-id')
            if not group_id:
                group_id = uuid.uuid4()
            if x_audit.get('audit') and x_audit.get('table_name'):
                write_audit_trail(dbsession=dbsession, user_id=user_id,
                                  membership_id=membership_id,
                                  group_id=str(group_id), **x_audit)


# function to handle audit log sent from celery task
async def audit_log_entry(dbsession, data):
    if data.get('id_tag'):
        id_tag = data.get('id_tag')
        data.pop('id_tag')
        membership = crud.get_membership_by_id_tag(dbsession, id_tag)
        await audit_log_charger(dbsession, data, membership.user_id, membership.id, None, True)


def map_charging_filters(filters: ChargingSessionFiltersParams) -> dict:
    mapping = {
        'user_rfid': 'username',
        'id_tag': 'idtag',
        'total_price': 'usage_amount'
    }
    mapped_filters = {}
    for key, value in filters.dict().items():
        backend_key = mapping.get(key, key)
        mapped_filters[backend_key] = value

    return mapped_filters


async def log_filters(
        serial_number: str = None,
        from_: datetime = None,
        to: datetime = None,
        message_type: str = None,
) -> dict:
    filter_dict = {}
    if from_:
        filter_dict['from_'] = from_
    if to:
        filter_dict['to'] = to
    if message_type:
        filter_dict['message_type'] = message_type
    if serial_number:
        filter_dict['serial_number'] = serial_number
    return filter_dict


async def energy_consumed_filters(
        date_start: datetime,
        date_end: datetime = None,
        connector_id: int = None,
        is_download: bool = None,
) -> dict:
    filter_dict = {}
    if date_start:
        filter_dict['date_start'] = date_start
    if date_end:
        filter_dict['date_end'] = date_end
    if connector_id:
        filter_dict['connector_id'] = connector_id
    if is_download:
        filter_dict['is_download'] = is_download
    return filter_dict


def evse_filters(id: str = None, ocpi_partner_evse_id: str = None, party_id: str = None, status: str = None,
                 location_country_code: str = None, location_name: str = None, location_address: str = None) -> dict:
    filter_dict = {}
    if id:
        filter_dict['id'] = id
    if ocpi_partner_evse_id:
        filter_dict['ocpi_partner_evse_id'] = ocpi_partner_evse_id
    if party_id:
        filter_dict['vendor'] = party_id
    if status:
        filter_dict['status'] = status
    if location_country_code:
        filter_dict['location_country_code'] = location_country_code
    if location_name:
        filter_dict['location_name'] = location_name
    if location_address:
        filter_dict['location_address'] = location_address
    return filter_dict


def emsp_cs_filters(id: str = None, ocpi_partner_evse_id: str = None, ocpi_partner_evse_uid: str = None,  # noqa: MC0001
                    party_id: str = None, transaction_id: str = None, country_code: str = None, usage: str = None,
                    status: str = None) -> dict:
    filter_dict = {}
    if id:
        filter_dict['id'] = id
    if ocpi_partner_evse_id:
        filter_dict['OCPIEVSE_ocpi_partner_evse_id'] = ocpi_partner_evse_id
    if ocpi_partner_evse_uid:
        filter_dict['OCPIEVSE_ocpi_partner_evse_uid'] = ocpi_partner_evse_uid
    if party_id:
        filter_dict['party_id'] = party_id
    if status:
        filter_dict['status'] = status
    if country_code:
        filter_dict['country_code'] = country_code
    if usage:
        try:
            filter_dict['kwh'] = float(usage)
        except ValueError:
            filter_dict['kwh'] = None
    if transaction_id:
        try:
            filter_dict['transaction_id'] = int(transaction_id)
        except ValueError:
            filter_dict['transaction_id'] = None
    return filter_dict


def location_filters(name: str = None, country: str = None, address: str = None, state: str = None):
    return {'name': name, 'country': country, 'address': address, 'state': state}


def ocpi_location_filters(name: str = None, country: str = None, address: str = None, country_code: str = None,
                          party_id: str = None, operator: str = None, published: bool = None, id: str = None):
    return {'name': name, 'country': country, 'address': address, 'country_code': country_code,
            'party_id': party_id, 'publish': published, 'id': id, 'operator': operator}


def cpo_cdr_filters(date_from: datetime = None, date_to: datetime = None, id: str = None, cdr_id: str = None,
                    transaction_id: str = None, cpo_transaction_id: str = None, token: str = None,
                    country_code: str = None, currency: str = None, party_id: str = None,
                    evse_id: str = None, partner_name: str = None, charging_session_id: str = None):
    return {'date_from': date_from, 'date_to': date_to, 'id': id, 'cdr_id': cdr_id, 'transaction_id': transaction_id,
            'cpo_transaction_id': cpo_transaction_id, 'country_code': country_code, 'cdr_token': token,
            'currency': currency, 'party_id': party_id, 'evse_id': evse_id, 'partner_name': partner_name,
            'charging_session_id': charging_session_id}


# deleted too many lines ( linting )


@router.get("/generate/monthly_lta_static")
async def generate_lta_static_excel(request: Request, date: datetime, background_tasks: BackgroundTasks,
                                    task: bool = True):
    date_str = date.strftime('%Y-%m')

    # filename = generate_upload_lta_static_excel(str(date_str))
    # if filename is not None:
    #     return FileResponse(filename,
    #                         media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    #                         filename=filename)

    async def run(log_date):
        if task:
            generate_lta_static_monthly_task.delay(log_date)
        else:
            generate_upload_lta_static_excel(str(log_date))

    background_tasks.add_task(run, date_str)
    return {'message': f'The LTA Static Report for Date: {str(date_str)} will be sent through email.'}


@router.get("/monthly_lta_static")
async def get_lta_static_excel(request: Request, date: datetime = None):
    if date is None:
        tz = pytz.timezone('Asia/Singapore')
        date = datetime.now(tz)
    date_str = date.strftime('%Y-%m')
    filename = f'Static Data Submission {date_str}.xlsx'
    key = f'{settings.LTA_S3_PREFIX}/monthly/{filename}'
    temp_file = read_excel_s3_temp(settings.S3_BUCKET, key)
    if temp_file:
        return FileResponse(temp_file,
                            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            filename=filename)
    return {"error": "Failed to retrieve Excel file from S3"}


@router.get("/lta_adhoc")
async def get_lta_adhoc_excel(request: Request, start_date: datetime, end_date: datetime,
                              dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    url = f'{CHARGER_URL_PREFIX}/log/generate/lta_static_unique_user'
    response = await send_request('GET', url, headers=headers, query_params=request.query_params)
    data = response.json()
    temp_file = process_adhoc_excel(data, start_date, end_date, dbsession)
    filename = 'Static Data Submission ADHOC.xlsx'
    if temp_file:
        return FileResponse(temp_file,
                            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            filename=filename)
    return {"error": "Failed to retrieve Excel file from S3"}


@router.post('/start-charging/{connector_id}')
async def start_charging(request: Request, connector_id: str,
                         dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    data = await request.body()
    query_params = request.query_params

    pre_charging_flow = PreChargingFlow(request_headers=headers, connector_id=connector_id)
    await pre_charging_flow.start_flow(dbsession, membership_id)

    # start charging
    path = f'connector/{connector_id}/remote-start'
    url = f'{CHARGER_URL_PREFIX}/{path}'
    response = await send_request('POST', url, data=data, headers=headers, query_params=query_params)
    user_id = auth_token_data.get('user_id')
    await audit_log_charger(dbsession, response, user_id, membership_id, request)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/charge_point/{charge_point_id}/image')
async def create_cp_image_s3(request: Request, charge_point_id: str, file: UploadFile = File(None),
                             dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    query_params = request.query_params

    if file.content_type not in ['image/png', 'image/jpeg', 'image/jpg']:
        raise HTTPException(400, 'The uploaded file is not in PNG, JPEG, or JPG format .')

    file_bytes = await file.read()
    is_svg = is_disallowed_image(file_bytes, file.filename)
    if is_svg:
        raise HTTPException(400, 'The uploaded file is not in PNG, JPEG, or JPG format .')
    key = f'resources/{charge_point_id}'
    content_type = file.content_type.split('/')[1]

    path = f'charge_point/image/{charge_point_id}/{content_type}'
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method
    response = await send_request(method, url, headers=headers, query_params=query_params)
    if response.status_code == 201:
        await upload_image_with_presigned_url(file_bytes, key)
    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)
    await audit_log_charger(dbsession, response, user_id, membership_id, request)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.get("/location/view-qrcode/")
async def view_location_image(image_key: str, filename: str):
    try:
        filename = f'qrcode_{filename}'
        image_url = await generate_presigned_url_for_view_image(image_key, filename)
        return {"image_url": image_url}
    except Exception as e:  # pylint: disable=broad-except
        logger.error('Error viewing image: %s', e)
        raise HTTPException(status_code=500, detail="Failed to view image")


@router.get('/location/gallery/{location_id}')
async def get_location_images(request: Request, location_id: str, dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    path = f'location/gallery/{location_id}'
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method

    response = await send_request(method, url, headers=headers)

    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/location/{location_id}/image')
async def upload_location_image_s3(request: Request, location_id: str, file: UploadFile = File(None),
                                   dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    query_params = request.query_params
    if file.content_type not in ['image/png', 'image/jpeg', 'image/jpg']:
        raise HTTPException(400, 'The uploaded file is not in PNG, JPEG, or JPG format .')

    file_bytes = await file.read()
    is_svg = is_disallowed_image(file_bytes, file.filename)
    if is_svg:
        raise HTTPException(400, 'The uploaded file is not in PNG, JPEG, or JPG format .')
    random_key = str(uuid.uuid4())
    key = f'resources/{location_id}/{random_key}'
    img_url = await upload_image_with_presigned_url(file_bytes, key)
    content_type = file.content_type.split('/')[1]
    path = f'location/image/{location_id}'
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = 'POST'

    image_object = {
        'url': img_url,
        'key': key,
        'id': random_key,
        'category': 'LOCATION',
        'type': content_type,
        'thumbnail': img_url
    }

    response = await send_request(method, url, headers=headers, query_params=query_params,
                                  data=json.dumps(image_object))
    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)
    await audit_log_charger(dbsession, response, user_id, membership_id, request)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.delete('/location/{location_id}/image/{image_key}')
async def delete_location_image_s3(request: Request, location_id: str, image_key: str,
                                   dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    query_params = request.query_params
    path = f'location/image/{location_id}/{image_key}'
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method

    response = await send_request(method, url, headers=headers, query_params=query_params)
    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)

    await audit_log_charger(dbsession, response, user_id, membership_id, request)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/location/{location_id}/campaign_logo')
async def upload_campaign_logo_to_s3(request: Request, location_id: str, file: UploadFile = File(None),
                                     dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    query_params = request.query_params
    if file.content_type not in ['image/png', 'image/jpeg', 'image/jpg']:
        raise HTTPException(400, 'The uploaded file is not in PNG, JPEG, or JPG format .')

    file_bytes = await file.read()
    is_svg = is_disallowed_image(file_bytes, file.filename)
    if is_svg:
        raise HTTPException(400, 'The uploaded file is not in PNG, JPEG, or JPG format .')
    random_key = str(uuid.uuid4())
    key = f'resources/{location_id}/{random_key}'
    img_url = await upload_image_with_presigned_url(file_bytes, key)
    content_type = file.content_type.split('/')[1]
    path = f'location/campaign_logo/{location_id}'
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = 'POST'

    image_object = {
        'url': img_url,
        'key': key,
        'id': random_key,
        'category': 'LOCATION',
        'type': content_type,
        'thumbnail': img_url
    }

    response = await send_request(method, url, headers=headers, query_params=query_params,
                                  data=json.dumps(image_object))
    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)
    await audit_log_charger(dbsession, response, user_id, membership_id, request)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.delete('/location/{location_id}/campaign_logo/{image_key}')
async def delete_campaign_logo_s3(request: Request, location_id: str, image_key: str,
                                  dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    query_params = request.query_params
    path = f'location/campaign_logo/{location_id}/{image_key}'
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method

    response = await send_request(method, url, headers=headers, query_params=query_params)
    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)

    await audit_log_charger(dbsession, response, user_id, membership_id, request)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.get('/charge_point/{charge_point_id}/image')
async def get_cp_image_s3(request: Request, charge_point_id: str,
                          dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    path = f'charge_point/image/{charge_point_id}'
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method

    response = await send_request(method, url, headers=headers)

    table_name = re.match(r'^[^/]*', path).group(0)
    response_json = {
        'image': str(response.content),
        'content_type': response.headers.get('content-type')
    }
    await log_activity(user_id, table_name, response_json, schema.ActivityLogType.create, dbsession)
    # return Response(content=response.content, media_type=response.headers['Content-Type'])
    key = f'resources/{charge_point_id}'
    content_type = response_json['content_type'].split('/')[1]
    if content_type in ['png', 'jpg', 'jpeg']:
        url = S3_DEFAULT_URL + f'/{key}'
        return {'url': url, 'image_format': content_type}
    return None


@router.delete('/charge_point/{charge_point_id}/image')
async def delete_cp_image_s3(request: Request, charge_point_id: str,
                             dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    query_params = request.query_params
    path = f'charge_point/image/{charge_point_id}'
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method

    response = await send_request(method, url, headers=headers, query_params=query_params)
    try:
        key = f'resources/{charge_point_id}'
        await delete_image(key)
    except ClientError as e:
        logger.error("Error deleting file '%s' from S3: %s", key, e)

    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)

    await audit_log_charger(dbsession, response, user_id, membership_id, request)

    return JSONResponse(response.json(), status_code=response.status_code)


# deleted too many lines ( linting )

# @router.patch('/charge_point/{charge_point_id}/image')
# async def update_cp_image(request: Request, charge_point_id: str, file: UploadFile = File(None),
#                           dbsession: SessionLocal = Depends(create_session)):
#     auth_token_data = decode_auth_token_from_headers(request.headers)
#     membership_id = auth_token_data.get('membership_id')
#     user_id = auth_token_data.get('user_id')
#     headers = generate_charger_header(dbsession, membership_id)
#     logger.debug('header for request to charger service: %s', headers)

#     if not membership_id:
#         raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

#     query_params = request.query_params
#     file_bytes = await file.read()
#     files = {
#         'file': (file.filename, file_bytes, file.content_type)
#     }
#     path = f'charge_point/image/{charge_point_id}'
#     url = f'{CHARGER_URL_PREFIX}/{path}'
#     method = request.method

#     response = await send_request(method, url, files=files, headers=headers, query_params=query_params)
#     table_name = re.match(r'^[^/]*', path).group(0)
#     await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)

#     return JSONResponse(response.json(), status_code=response.status_code)


# @router.get('/charge_point/{charge_point_id}/image')
# async def get_cp_image(request: Request, charge_point_id: str,
#                        dbsession: SessionLocal = Depends(create_session)):
#     auth_token_data = decode_auth_token_from_headers(request.headers)
#     membership_id = auth_token_data.get('membership_id')
#     user_id = auth_token_data.get('user_id')
#     headers = generate_charger_header(dbsession, membership_id)
#     logger.debug('header for request to charger service: %s', headers)

#     if not membership_id:
#         raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

#     path = f'charge_point/image/{charge_point_id}'
#     url = f'{CHARGER_URL_PREFIX}/{path}'
#     method = request.method

#     response = await send_request(method, url, headers=headers)

#     table_name = re.match(r'^[^/]*', path).group(0)
#     response_json = {
#         'image': str(response.content),
#         'content_type': response.headers.get('content-type')
#     }
#     await log_activity(user_id, table_name, response_json, schema.ActivityLogType.create, dbsession)
#     return Response(content=response.content, media_type=response.headers['Content-Type'])


# @router.delete('/charge_point/{charge_point_id}/image')
# async def delete_cp_image(request: Request, charge_point_id: str,
#                           dbsession: SessionLocal = Depends(create_session)):
#     auth_token_data = decode_auth_token_from_headers(request.headers)
#     membership_id = auth_token_data.get('membership_id')
#     user_id = auth_token_data.get('user_id')
#     headers = generate_charger_header(dbsession, membership_id)
#     logger.debug('header for request to charger service: %s', headers)

#     if not membership_id:
#         raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

#     query_params = request.query_params
#     path = f'charge_point/image/{charge_point_id}'
#     url = f'{CHARGER_URL_PREFIX}/{path}'
#     method = request.method

#     response = await send_request(method, url, headers=headers, query_params=query_params)
#     table_name = re.match(r'^[^/]*', path).group(0)
#     await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)

#     return JSONResponse(response.json(), status_code=response.status_code)


@router.get('/charging-invoices/')
async def get_charging_with_invoice_information(request: Request,  # noqa: MC0001
                                                dbsession: SessionLocal = Depends(create_session),  # noqa: MC0001
                                                params: Params = Depends(),  # noqa: MC0001
                                                filters: ChargingSessionFiltersParams = Depends()):  # noqa: MC0001
    """
    Is Charging History Page on CSMS
    """
    filters = map_charging_filters(filters)
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    login_user = UserCRUD.get(dbsession, user_id)
    headers = generate_charger_header(dbsession, membership_id)
    # result = []
    # user_id_tag = []
    id_tags = []
    url = f'{CHARGER_URL_PREFIX}/charging/'
    query_params = request.query_params
    query_params = dict(query_params)
    if settings.USE_CSB_INSTEAD_OF_PR_FOR_INVOICES:
        query_params["query_with_deleted"] = True
    if (filters['email'] or filters['phone_number'] or (
            filters['username'] is not None and len(filters['username'].strip()) >= 3)):
        # for _, value in invoice_dict.items():
        #     for nested_key, nested_value in value.items():
        #         if nested_key == 'member':
        #             user_id_tag.append(str(nested_value.user_id_tag))
        if filters['email']:
            query_params.pop('email')
        if filters['phone_number']:
            query_params.pop('phone_number')
        mem_filter = {'name': filters['username'], 'user_id_tag': None, 'organization': None}
        user_filter = {'email': filters['email'], 'phone_number': filters['phone_number']}
        members = crud.organization.get_organization_membership_list(dbsession, organization_id=None,
                                                                     mem_filter=mem_filter, user_filter=user_filter,
                                                                     check_permission=False).all()
        rfids_filter = {'name': None, 'type': None, 'is_active': None, 'from_date': None,
                        'to_date': None, 'username': filters['username']}
        id_fitlers = {'email': filters['email'], 'phone_number': filters['phone_number']}
        rfids = crud.organization.get_all_id_tags_with_filters(dbsession, rfids_filter, id_fitlers,
                                                               check_permission=False).all()
        rfid = [r.id_tag for r in rfids]
        id_tags = [member.user_id_tag for member in members]
        ruby_user_ids = [member.ruby_user_id for member in members if member.ruby_user_id]
        if ruby_user_ids:
            query_params['ruby_user_ids'] = ruby_user_ids
        id_tags = id_tags + rfid
        if len(id_tags) <= 0 and not filters['idtag']:
            return {"items": [], "total": 0, "page": int(query_params['page']), "size": int(query_params['size'])}

    if filters['idtag']:
        query_params['idtag'] = filters['idtag']

    query_params['id_tags'] = list(set(id_tags))
    ext_org_list = dbsession.query(models.ExternalOrganization)
    if filters['partner_name']:
        p_name = filters['partner_name']
        ext_org_list_filter = ext_org_list.filter(models.ExternalOrganization.friendly_name.ilike(f'%{p_name}%'))
        ext_org_query = [str(ext.id) for ext in ext_org_list_filter.all()]
        ext_party_id = [ext.party_id for ext in ext_org_list_filter.all()]
        query_params['evse_partner_id'] = ext_org_query
        query_params['party_ids'] = ext_party_id
        if not ext_org_query:
            return {'items': [], 'total': 0, 'size': params.size, 'page': params.page}
    if filters['operator']:
        operator_id_list = [id.strip() for id in filters['operator'].split(',')]
        query_params['operator'] = operator_id_list
    ext_org = {str(ext.id): ext.friendly_name for ext in ext_org_list.all()}
    ext_org_party = {ext.party_id: ext.friendly_name for ext in ext_org_list}
    mem_org_id = crud.MembershipCRUD.membership().organization_id
    mem_org = dbsession.query(models.Organization).filter(models.Organization.id == mem_org_id).first()
    parent_org = mem_org.parent
    if parent_org and parent_org.parent:
        query_params['allow_ocpi'] = False
    else:
        query_params['allow_ocpi'] = True
    # query_params['allow_cpo'] = False
    all_child_org = get_all_child_organizations(dbsession, mem_org_id)
    all_child_org = [str(child) for child in all_child_org]

    if filters['outlier_status']:
        outlier_filter = {'status': filters['outlier_status']}
        outlier_cs_ids = crud.get_outlier_list(dbsession, outlier_filter, params)
        if outlier_cs_ids.total <= 0:
            return {"items": [], "total": 0, "page": int(query_params['page']), "size": int(query_params['size'])}
        outlier_cs = outlier_cs_ids.items
        outlier_cs_ids = [str(item.charging_session_id) for item in outlier_cs]
        query_params['ids'] = outlier_cs_ids

    response = await send_request('GET', url=url, headers=headers, query_params=query_params)
    data = response.json()
    if len(data['items']) <= 0:
        return data
    cs_ids = [cs['id'] for cs in data['items']]
    inv_filters = {'cs_ids': cs_ids, 'operator': filters['operator'], 'is_charging_history': True}
    ###
    # This share same function between invoice and charging history, here filter_ocpi is False because history require
    # OCPI charging invoice also (to display price and etc)
    ###
    invoice_dict = await crud.get_charging_session_invoice_list(dbsession, filter_params=inv_filters,
                                                                operator_filter=True, filter_ocpi=False)
    outlier_filter = {'charging_session_id_list': cs_ids}
    outlier_list = crud.get_outlier_list(dbsession, outlier_filter)
    outlier_map = {str(item.charging_session_id): item for item in outlier_list}
    # if filters['username'] is not None:
    #     for _, value in invoice_dict.items():
    #         for nested_key, nested_value in value.items():
    #             if nested_key == 'member':
    #                 user_id_tag.append(str(nested_value.user_id_tag))
    # pylint:disable=too-many-nested-blocks
    for d in data['items']:
        cs_mem_org_id = ''
        d['amount'] = 0
        d['discount_amount'] = 0
        d['user_name'] = None
        d['meter_stop'] = d['meter_stop'] / 1000
        d['meter_start'] = d['meter_start'] / 1000
        d['usage_kwh'] = d['meter_stop'] - d['meter_start']
        d['partner_name'] = None
        d['total_amount'] = 0
        d['tax_rate'] = 0
        d['tax_amount'] = 0
        inv = invoice_dict.get(d['id'], None)
        outlier_obj = outlier_map.get(d['id'], None)
        d['outlier_obj'] = outlier_obj

        if d['charge_point_connector']:
            if 'charge_point' in d['charge_point_connector']:
                operator_id = d['charge_point_connector']['charge_point']['operator_id']
                operator = dbsession.query(models.Operator).filter(models.Operator.id == operator_id) \
                    .with_entities(models.Operator.name).first()
                if operator is not None:
                    d['operator_name'] = operator.name
                else:
                    d['operator_name'] = None
        else:
            d['operator_name'] = None
        if inv:
            if inv.get('discount'):
                if isinstance(inv.get('discount'), dict):
                    subscription_discount = inv.get('discount').get('subscription_discount')
                    campaign_discount = inv.get('discount').get('campaign_promo_code_discount', 0)
                    d['discount_amount'] = subscription_discount + campaign_discount
                elif isinstance(inv.get('discount'), float):
                    d['discount_amount'] = float(inv['discount'])
            d['amount'] = inv['usage_amount'] if inv['usage_amount'] else 0.00
            d['hogging_fee'] = inv['hogging_fee'] if inv['hogging_fee'] else 0.00
            d['tax_amount'] = inv['tax_amount'] if inv['tax_amount'] else 0.00
            d['tax_rate'] = inv['tax_rate'] if inv['tax_rate'] else 0.00
            d['total_amount'] = float(d['amount']) - float(d['discount_amount']) + float(d['tax_amount']) + float(
                d['hogging_fee'])
            d['user_name'] = inv['user_name'] if 'user_name' in inv else None
            try:
                d['meta']['member'] = schema.ShallowMembershipResponse.from_orm(
                    inv['member']) if 'member' in inv else None
            except Exception:  # pylint: disable=broad-except
                d['meta']['member'] = None
        if d['ocpi_evse_connector']:
            partner_id = d['ocpi_evse_connector']['ocpi_evse']['operator_id']
            partner_name = ext_org.get(partner_id)
            d['partner_name'] = partner_name
        elif 'cdr_data' in d['meta'] and d['meta']['cdr_data']:
            party_id = d['meta']['cdr_data']['party_id'] if 'party_id' in d['meta']['cdr_data'] else None
            d['partner_name'] = ext_org_party.get(party_id)
        if not d['user_name']:
            try:
                mem_detail = crud.get_member_by_id_tag(dbsession, d['id_tag'])
                if mem_detail:
                    d['user_name'] = f"{mem_detail.first_name} {mem_detail.last_name}"
                    _ = mem_detail.user.__dict__
                    d['meta']['member'] = schema.ShallowMembershipResponse(**mem_detail.__dict__)
            except exceptions.ApolloObjectDoesNotExist:
                continue
        if login_user.is_superuser is False:
            if d['status'] in ["Completed", "Charging", "Incomplete", "Cancelled"]:
                if not settings.CAN_VIEW_SUB_ORG:
                    try:
                        cs_mem_org_id = d['meta']['member']['organization_id']
                    except Exception:  # pylint: disable=broad-except
                        cs_mem_org_id = d['meta']['member'].organization_id
                    if str(cs_mem_org_id) != str(mem_org_id):
                        if cs_mem_org_id not in all_child_org:
                            d['meta']['member'].user.email = '*****'
                            d['meta']['member'].user.phone_number = '*****'
                            d['user_name'] = '*****'
    #         result.append(d)
    # if filters['username'] is not None:
    #     return result
    return data


# pylint:disable=too-many-statements  # noqa
@router.get('/charging-invoices/download/')  # noqa
async def get_charging_with_invoice_for_csv(request: Request,  # pylint: disable=too-many-locals  # noqa
                                            filename: str = None, send_to: str = None,
                                            dbsession: SessionLocal = Depends(create_session),
                                            filters: ChargingSessionFiltersParams = Depends()):  # noqa

    """
    Is Charging History Page on CSMS
    """

    headers = ['ORDER', 'USER/RFID', 'PHONE NUMBER', 'EMAIL', 'ID TAG', 'LOCATION', 'OPERATOR', 'CHARGE POINT ID',
               'CONNECTOR', 'CURRENCY', 'TOTAL PRICE', 'IDLE PRICE', 'DISCOUNTED', 'USAGE(KWH)', 'DURATION',
               'START TIME', 'END TIME', 'STATUS', 'IDLE STATUS', 'TYPE', 'PARTNER NAME', 'OPERATOR', 'COUNTRY']

    columns = ['invoice_number', 'user_name', 'user_phone', 'user_email', 'meta.id_tag', 'meta.location.name',
               'meta.operator.name', 'cp_serial_number', 'meta.connector_number', 'meta.billing_info.billing_currency',
               'amount', 'hogging_fee', 'discount_amount', 'usage_kwh', 'duration', 'session_start', 'session_end',
               'status', 'hogging_status', 'type', 'partner_name', 'operator_name', 'meta.location.country']

    if not filename:
        tz = pytz.timezone('Asia/Kuala_Lumpur')
        current_datetime = datetime.now(tz)
        format_datetime = current_datetime.strftime("%d.%m.%Y_(%H:%M)")
        filename = 'charging_history_' + format_datetime

    filters = map_charging_filters(filters)
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    req_headers = generate_charger_header(dbsession, membership_id)
    url = f'{CHARGER_URL_PREFIX}/charging/'
    query_params = request.query_params
    query_params = dict(query_params)
    id_tags = []
    if filters['username'] is not None and len(filters['username'].strip()) >= 3:
        mem_filter = {'name': filters['username'], 'user_id_tag': None, 'organization': None}
        members = crud.organization.get_organization_membership_list(dbsession, organization_id=None,
                                                                     mem_filter=mem_filter,
                                                                     check_permission=False).all()
        rfids_filter = {'name': None, 'type': None, 'is_active': None, 'from_date': None,
                        'to_date': None, 'username': filters['username']}
        rfids = crud.organization.get_all_id_tags_with_filters(dbsession, rfids_filter,
                                                               check_permission=False).all()
        rfid = [r.id_tag for r in rfids]
        id_tags = [member.user_id_tag for member in members]
        ruby_user_ids = [member.ruby_user_id for member in members if member.ruby_user_id]
        if ruby_user_ids:
            query_params['ruby_user_ids'] = ruby_user_ids
        id_tags = id_tags + rfid
        if len(id_tags) <= 0 and not filters['idtag']:
            return {"items": [], "total": 0, "page": int(query_params['page']), "size": int(query_params['size'])}

    if filters['idtag']:
        query_params['idtag'] = filters['idtag']

    query_params['id_tags'] = list(set(id_tags))
    ext_org_list = dbsession.query(models.ExternalOrganization)
    if filters['partner_name']:
        p_name = filters['partner_name']
        ext_org_list_filter = ext_org_list.filter(models.ExternalOrganization.friendly_name.ilike(f'%{p_name}%'))
        ext_org_query = [str(ext.id) for ext in ext_org_list_filter.all()]
        ext_party_id = [ext.party_id for ext in ext_org_list_filter.all()]
        query_params['evse_partner_id'] = ext_org_query
        query_params['party_ids'] = ext_party_id
        if not ext_org_query:
            return
    if filters['operator']:
        operator_id_list = [id.strip() for id in filters['operator'].split(',')]
        query_params['operator'] = operator_id_list
    else:
        query_params['operator'] = []
    ext_org = {str(ext.id): ext.friendly_name for ext in ext_org_list.all()}
    ext_org_party = {ext.party_id: ext.friendly_name for ext in ext_org_list}
    mem_org_id = crud.MembershipCRUD.membership().organization_id
    mem_org = dbsession.query(models.Organization).filter(models.Organization.id == mem_org_id).first()
    parent_org = mem_org.parent
    if parent_org and parent_org.parent:
        query_params['allow_ocpi'] = False
    else:
        query_params['allow_ocpi'] = True
    # query_params['allow_cpo'] = False

    ###
    # This share same function between invoice and charging history, here filter_ocpi is False because history require
    # OCPI charging invoice also (to display price and etc)
    ###
    get_data = partial(get_charging_history_data, dbsession=dbsession, url=url, ext_org=ext_org,
                       ext_org_party=ext_org_party, headers=req_headers, query_params=query_params, filters=filters,
                       operator_filter=True)
    report = GenerateReport('charging_history', headers, columns, function=get_data)

    if 'from_date' in filters and isinstance(filters['from_date'], datetime):
        filters['from_date'] = filters['from_date'].isoformat()  # Convert to string
    if 'to_date' in filters and isinstance(filters['to_date'], datetime):
        filters['to_date'] = filters['to_date'].isoformat()  # Convert to string

    if send_to:
        message = {
            'filters': filters,
            'query_params': query_params,
            'headers': headers,
            'filename': filename,
            'columns': columns,
            'email': send_to,
            'req_headers': req_headers,
            'membership_id': membership_id,
            'ext_org': ext_org,
            'ext_org_party': ext_org_party,
        }

        task_data = ReportingTaskCreate(
            task_name="charging_history",
            json_payload=message,
            retry_count=0
        )

        reporting_task = create_reporting_task_record(dbsession, task_data)
        task_uid = reporting_task.id
        message['task_uid'] = task_uid
        send_charging_history_via_email_sync.delay(message=message)

        # moved to celery, use this function if celery not working
        # app.send_task(f'{settings.MAIN_SERVICE_TASKS_PREFIX}.send_charging_history_via_email',
        #               kwargs={'message': message},
        #               queue=settings.MAIN_SERVICE_WORKER_QUEUE,
        #               routing_key=settings.MAIN_SERVICE_WORKER_ROUTE)

        return {'message': 'The report will be ready in 15 minutes.'}

    await report.generate_dataframe()
    await report.datetime_reformat('session_start')
    await report.datetime_reformat('session_end')
    await report.nan_handling('hogging_fee', 0.00, None)
    return await report.generate_report()


@router.get('/ocpi/evse/download/')
async def download_evse_cp(request: Request, filters: dict = Depends(evse_filters),
                           dbsession: SessionLocal = Depends(create_session)):
    headers = ['EVSE ID', 'PARTY ID', 'COUNTRY CODE', 'LOCATION NAME', 'LOCATION ADDRESS', 'STATUS']

    columns = ['ocpi_partner_evse_id', 'location.party_id', 'location.country_code',
               'location.name', 'location.address', 'status']

    get_data = partial(get_evse_cp, request=request, report=True, dbsession=dbsession, filters=filters)
    report = GenerateReport('ocpi_evse', headers, columns, function=get_data)
    await report.generate_dataframe()
    # await report.datetime_reformat('start_date_time')
    # await report.datetime_reformat('end_date_time')
    res = await report.generate_report()
    return res


@router.api_route('/ocpi/evse/', methods=['GET'])
async def get_evse_cp(request: Request,
                      filters: dict = Depends(evse_filters), params: Params = Depends(),  # noqa
                      dbsession: SessionLocal = Depends(create_session), report: bool = False):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    headers = generate_charger_header(dbsession, membership_id)

    query_params = request.query_params
    dict_params = dict(query_params)

    url = f'{CHARGER_URL_PREFIX}/ocpi/evse/'

    if params:
        params = dict(params)
        dict_params['size'] = params['size']
        dict_params['page'] = params['page']
        dict_params['filters'] = filters

    query_params = QueryParams(dict_params)

    response = await send_request('GET', url=url, headers=headers, query_params=query_params)
    data = response.json()
    ops_id = set(d['operator_id'] for d in data['items'])
    ops_list = dbsession.query(models.ExternalOrganization).filter(models.ExternalOrganization.id.in_(ops_id)).all()
    ops = {op.id: op for op in ops_list}
    for d in data['items']:
        d['operator'] = ops.get(d['operator_id'], None)

    if report:
        return response.json()
    return JSONResponse(response.json(), status_code=response.status_code)


@router.get('/ocpi/charging-session/emsp/download/')
async def download_emsp_charging_session(request: Request,
                                         filters: dict = Depends(emsp_cs_filters),
                                         dbsession: SessionLocal = Depends(create_session)):
    headers = ['PARTNER SESSION UID', 'TOKEN ID', 'EVSE ID', 'CONNECTOR', 'PARTY ID', 'COUNTRY CODE', 'CURRENCY',
               'TOTAL AMOUNT', 'USAGE',
               'START TIME', 'END TIME', 'STATUS', 'DURATION']

    columns = ['partner_session_uid', 'cdr_token.uid', 'ocpi_evse.ocpi_partner_evse_id', 'ocpi_evse_connector.number',
               'party_id',
               'country_code', 'currency', 'total_cost.incl_vat', 'kwh', 'start_date_time', 'end_date_time',
               'status']

    get_data = partial(get_emsp_charging_session, request=request, report=True, dbsession=dbsession)
    report = GenerateReport('ocpi_charging_history', headers, columns, function=get_data)
    await report.generate_dataframe()
    await report.get_duration_given_rows('duration', 'start_date_time', 'end_date_time')
    await report.datetime_reformat('start_date_time')
    await report.datetime_reformat('end_date_time')
    res = await report.generate_report()
    return res


@router.get('/ocpi/charging-session/emsp/')
async def get_emsp_charging_session(
        request: Request,
        filters: dict = Depends(emsp_cs_filters),
        params: Params = Depends(),  # noqa
        report: bool = False,
        dbsession: Session = Depends(create_session),
):
    query_params = dict(request.query_params)
    url = f'{CHARGER_URL_PREFIX}/ocpi/charging-session/emsp/'

    # To skip query if organization is 2nd level and below
    # They are not allowed to see any eMSP charging session
    mem_org_id = crud.MembershipCRUD.membership().organization_id
    mem_org = dbsession.query(models.Organization).filter(models.Organization.id == mem_org_id).first()
    parent_org = mem_org.parent
    if parent_org and parent_org.parent:
        return {'items': [], 'total': 0, 'size': params.size, 'page': params.page}

    if params:
        params = dict(params)
        query_params['size'] = params['size']
        query_params['page'] = params['page']
        query_params['pagination'] = True

    # Get list of valid external organization ids (`accessible_ocpi_cps`) by generating headers
    # To filter charging sessions by their locations' ext. org. id
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    response = await send_request('GET', url=url, query_params=query_params, headers=headers)
    if report:
        return response.json()
    return JSONResponse(response.json(), status_code=response.status_code)


@router.api_route('/ocpi/cpo-cdr', methods=['GET'])
async def get_cpo_cdr(request: Request,  # noqa
                      filters: dict = Depends(cpo_cdr_filters), report: bool = False,  # noqa
                      params: Params = Depends(), dbsession: SessionLocal = Depends(create_session)):  # noqa
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    headers = generate_charger_header(dbsession, membership_id)

    query_params = request.query_params

    if params:
        query_params = dict(query_params)
        query_params.update(dict(params))
    ext_org_list = dbsession.query(models.ExternalOrganization)
    ext_org_party = {ext.party_id: ext.friendly_name for ext in ext_org_list}
    if 'partner_name' in query_params and query_params['partner_name']:
        partner_name = query_params['partner_name']
        ext_org_filter = ext_org_list.filter(models.ExternalOrganization.friendly_name
                                             .ilike(f"%{partner_name}%"))
        ext_party_id = [ext.party_id for ext in ext_org_filter]
        if ext_party_id:
            query_params['party_ids'] = ext_party_id
        else:
            return {'items': [], 'total': 0, 'size': params.size, 'page': params.page}
    if 'cdr_id' in query_params and query_params['cdr_id']:
        cdr_id = query_params['cdr_id']

        trans_ids = (
            dbsession.query(models.ChargingSessionBill.invoice_number)
            .filter(cast(models.ChargingSessionBill.id, String).ilike(f'%{cdr_id}%'))
            .all()
        )
        t_ids = [x[0].split('-')[-1] for x in trans_ids if x[0] is not None]

        if t_ids:
            query_params['transaction_id'] = t_ids
            filters['transaction_id'] = t_ids

    url = f'{CHARGER_URL_PREFIX}/ocpi/cpo-cdr'

    response = await send_request('GET', url=url, query_params=query_params, headers=headers)
    data = response.json()
    cs_ids = set(d['charging_session']['id'] for d in data['items'])
    bills = crud.get_charging_session_bill_ids(dbsession, cs_ids)
    bill_dict = {str(bill.charging_session_id): {'bill': schema.ChargingSessionBillResponse.from_orm(bill).dict()} for
                 bill in bills}

    for d in data['items']:
        d['usage_amount'] = bill_dict.get(d['charging_session']['id'])
        party_id = d['charging_session']['meta']['cdr_data']['party_id'] if 'charging_session' in d else None
        d['partner_name'] = ext_org_party.get(party_id)
        if report:
            d['duration'] = 0
            if d['start_date_time']:
                d['start_date_time'] = datetime.fromisoformat(d['start_date_time'])
            if d['end_date_time']:
                d['end_date_time'] = datetime.fromisoformat(d['end_date_time'])
            if d['start_date_time'] and d['end_date_time']:
                duration = d['end_date_time'] - d['start_date_time']
                total_hours = duration.days * 24 + duration.seconds // 3600
                minutes = (duration.seconds % 3600) // 60
                d['duration'] = f"{total_hours:02d}:{minutes:02d}:{duration.seconds % 60:02d}"
            try:
                if d['usage_amount']['bill']['total_amount'] is None:
                    d['usage_amount']['bill']['total_amount'] = d['usage_amount']['bill']['usage_amount']
            except TypeError:
                no_invoice_obj = {
                    "bill": {
                        "id": None,
                        "total_amount": None
                    }
                }
                d['usage_amount'] = no_invoice_obj

    return data


@router.api_route('/ocpi/cpo-cdr/download/', methods=['GET'])
async def download_cpo_cdr(request: Request,
                           filters: dict = Depends(cpo_cdr_filters), params: Params = Depends(),  # noqa
                           dbsession: SessionLocal = Depends(create_session),
                           filename: str = None, send_to: str = None):
    headers = ['INVOICE NUMBER', 'SESSION ID', 'CDR ID', 'EVSE ID', 'CURRENCY', 'PARTY ID', 'COUNTRY CODE', 'TOKEN ID',
               'TOKEN CONTRACT ID',
               'TOTAL AMOUNT', 'USAGE', 'DURATION', 'START TIME', 'END TIME']

    columns = ['cpo_transaction_id', 'id', 'usage_amount.bill.id', 'charging_session.meta.charger_serial_number',
               'currency',
               'party_id', 'country_code', 'cdr_token.uid', 'cdr_token.contract_id', 'usage_amount.bill.total_amount',
               'kwh',
               'duration', 'start_date_time', 'end_date_time']

    if 'date_from' in filters and isinstance(filters['date_from'], datetime):
        filters['date_from'] = filters['date_from'].isoformat()  # Convert to string
    if 'date_to' in filters and isinstance(filters['date_to'], datetime):
        filters['date_to'] = filters['date_to'].isoformat()  # Convert to string

    if send_to:
        # moved to celery, use this function if celery not working
        # background_tasks.add_task(run_get_invoice_list, send_to)
        request_data = {
            "headers": dict(request.headers),
            "query_params": dict(request.query_params)
        }

        message = {
            'filters': filters,
            'request_data': request_data,
            'headers': headers,
            'columns': columns,
            'filename': filename,
            'email': send_to,
        }

        task_data = ReportingTaskCreate(
            task_name="cpo_cdr",
            json_payload=message,
            retry_count=0
        )

        reporting_task = create_reporting_task_record(dbsession, task_data)
        task_uid = reporting_task.id
        message['task_uid'] = task_uid
        send_cpo_cdr_via_email_sync.delay(message=message)

        return {'message': f'The invoice mailed to {send_to}. Will be ready in 15 minutes.'}

    get_data = partial(get_cpo_cdr, request=request, report=True, dbsession=dbsession)
    report = GenerateReport('cpo_cdr', headers, columns, function=get_data)
    await report.generate_dataframe()
    await report.datetime_reformat('start_date_time')
    await report.datetime_reformat('end_date_time')
    res = await report.generate_report()
    return res


@router.get('/ocpi/ocpi-location/download/')  # noqa
async def download_ocpi_location(request: Request,  # noqa
                                 filters: dict = Depends(ocpi_location_filters),
                                 dbsession: SessionLocal = Depends(create_session)):
    headers = ['NAME', 'ADDRESS', 'COUNTRY', 'PARTY_ID', 'COUNTRY_CODE', 'OPERATOR',
               'PUBLISHED', 'IS_24/7']

    columns = ['name', 'address', 'country', 'party_id', 'country_code',
               'operator', 'publish', 'opening_times.twentyfourseven']

    get_data = partial(ocpi_location, request=request, dbsession=dbsession, filters=filters)

    report = GenerateReport('ocpi_locations', headers, columns, function=get_data)
    await report.generate_dataframe()
    # await report.datetime_reformat('created_at')
    res = await report.generate_report()

    return res


@router.get('/ocpi/ocpi-location/')  # noqa
async def ocpi_location(request: Request, params: Params = Depends(),  # noqa
                        filters: dict = Depends(ocpi_location_filters),
                        dbsession: SessionLocal = Depends(create_session)):
    # query_params = request.query_params
    # query_params = dict(query_params)

    ext_org_query = dbsession.query(models.ExternalOrganization).filter(
        models.ExternalOrganization.is_deleted.is_(False))
    query_params = request.query_params
    dict_params = dict(query_params)

    if dict_params.get('operator') is not None:
        ext_org_query = ext_org_query.filter(models.ExternalOrganization.friendly_name
                                             .ilike(f'%{dict_params.get("operator")}%'))
        query_params.pop('operator')
        dict_params.pop('operator')
        ext_org = dbsession.execute(ext_org_query).scalars().unique().all()
        ext_list = [str(ex.id) for ex in ext_org]
        if ext_list:
            dict_params['operator_ids'] = ext_list
        else:
            return {'items': [], "total": 0, "page": params.page, "size": params.size}

    if params:
        params = dict(params)
        dict_params['size'] = params['size']
        dict_params['page'] = params['page']
        dict_params['filters'] = filters
    ext_org = dbsession.execute(ext_org_query).scalars().unique().all()
    ext_dict = {str(ex.id): ex.friendly_name for ex in ext_org}
    url = f'{CHARGER_URL_PREFIX}/ocpi/ocpi-location/'
    response = await send_request('GET', url=url, query_params=dict_params)
    data = response.json()

    for d in data['items']:
        try:
            operator_id = d['ocpi_evses'][0]['operator_id'] if 'ocpi_evses' in d else None
            d['operator'] = ext_dict.get(operator_id)
        except IndexError:
            d['operator'] = None
    return data


@router.api_route('/ocpi/{path:path}', methods=['GET', 'POST', 'PUT', 'PATCH', 'DELETE'], include_in_schema=False)
async def proxy_ocpi_request(request: Request, path: str,
                             dbsession: SessionLocal = Depends(create_session)):
    set_admin_as_context_user(dbsession)
    auth_token_data = decode_auth_token_from_headers(request.headers)

    membership_id = auth_token_data.get('membership_id')
    db_member = crud.get_membership_by_id(dbsession, membership_id)

    cpo = crud.get_operator_by_organization_id(dbsession, db_member.organization_id)
    membership = crud.MembershipCRUD.membership()
    if membership.user.is_superuser:
        cpo_id = None
    else:
        cpo_id = cpo.id
    headers = {
        'operator_id': str(cpo_id)
    }
    url = f'{CHARGER_URL_PREFIX}/ocpi/{path}'
    method = request.method
    query_params = request.query_params
    data = await request.body()

    if method == 'DELETE':
        _ = await send_request('GET', url, data=data, headers=headers, query_params=query_params)

    response = await send_request(method, url, data=data, headers=headers, query_params=query_params)
    response_data = response.json()

    user_id = auth_token_data.get('user_id')
    await audit_log_charger(dbsession, response, user_id, membership_id, request)

    return response_data


@router.api_route('/charge_point/', methods=['GET'])
async def get_charge_points(request: Request, operator: str = None,
                            dbsession: SessionLocal = Depends(create_session),
                            report: bool = False, params: Params = Depends()):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    headers = generate_charger_header(dbsession, membership_id)

    query_params = request.query_params
    dict_params = dict(query_params)
    dict_params = {**dict_params, **dict(params)}
    if dict_params.get("operator") is not None:
        filtered_cps = get_operator_by_operator_name(dbsession, dict_params.get("operator"))
        operator_cps = operator_charge_points(filtered_cps)
        headers['operator_list'] = json.dumps(operator_cps)
        dict_params['operator_filter'] = True

    if report:
        dict_params['no_update'] = True
    query_params = QueryParams(dict_params)
    url = f'{CHARGER_URL_PREFIX}/charge_point/'

    response = await send_request('GET', url=url, headers=headers, query_params=query_params)
    if report:
        data = response.json()
        operator_ids = [d['operator_id'] for d in data['items']]
        operators = dbsession.query(models.Operator.id, models.Operator.name).filter(
            models.Operator.id.in_(operator_ids)
        ).all()
        operator_dict = {str(k): v for k, v in operators}
        for d in data['items']:
            d['operator_name'] = operator_dict.get(d['operator_id'], None)
        return data
    return JSONResponse(response.json(), status_code=response.status_code)


# route to increase gst to 9%
@router.api_route('/charge_point/update_gst', methods=['GET'])
async def update_sg_gst(request: Request, dbsession: SessionLocal = Depends(create_session),
                        params: Params = Depends()):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    membership = crud.MembershipCRUD.membership()
    if not membership.user.is_superuser:
        raise HTTPException(status_code=401, detail='Only superuser can trigger this')

    headers = generate_charger_header(dbsession, membership_id)

    utc_now = datetime.now(timezone.utc)
    target_date = datetime(2023, 12, 31, 16, tzinfo=timezone.utc)
    if utc_now < target_date:
        tax_rate = dbsession.query(models.TaxRate).filter(
            models.TaxRate.country == 'Singapore').first()
        TaxRateCRUD.update(dbsession, tax_rate.id, {'tax_rate': 8.00})
    else:
        tax_rate = dbsession.query(models.TaxRate).filter(
            models.TaxRate.country == 'Singapore').first()
        TaxRateCRUD.update(dbsession, tax_rate.id, {'tax_rate': 9.00})

    query_params = request.query_params
    dict_params = dict(query_params)
    dict_params = {**dict_params, **dict(params)}
    filtered_cps = get_chargev_lhn_np(dbsession)
    operator_cps = operator_charge_points(filtered_cps)
    headers['operator_list'] = json.dumps(operator_cps)
    dict_params['operator_filter'] = True

    query_params = QueryParams(dict_params)
    url = f'{CHARGER_URL_PREFIX}/charge_point/update_gst'

    response = await send_request('GET', url=url, headers=headers, query_params=query_params)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.api_route('/charge_point/download/', methods=['GET'])
async def download_charge_points(request: Request, operator_name: str = None,
                                 dbsession: SessionLocal = Depends(create_session)):
    headers = ['CHARGE_POINT_ID', 'SERIAL NUMBER', 'VENDOR', 'MODEL', 'OPERATOR', 'LOCATION',
               'PROTOCOL', 'ROAMING', 'STATUS', 'CONNECTED', 'PLATFORM', 'PRIVATE', 'CREATED_AT',
               'KILOWATT']

    columns = ['charge_box_serial_number', 'serial_number', 'charge_point_vendor', 'charge_point_model',
               'operator_name', 'location.name', 'provider', 'location.ocpi_enabled', 'status',
               'is_connected', 'cp_platform', 'is_private', 'created_at', 'meta.kilowatt']

    get_data = partial(get_charge_points, request=request,
                       dbsession=dbsession, report=True)

    report = GenerateReport('charge_point', headers, columns, function=get_data)
    await report.generate_dataframe()
    await report.datetime_reformat('created_at')
    res = await report.generate_report()
    return res


@router.api_route('/charge_point/get_pnc_chargepoints/download/', methods=['GET'])
async def download_evse_certificates(request: Request,
                                     dbsession: SessionLocal = Depends(create_session)):
    headers = ['CHARGE POINT ID', 'LOCATION NAME', 'COMMON NAME', 'SERIAL NUMBER', 'ISSUER NAME HASH',
               'ISSUER KEY HASH', 'EXPIRY DATE', 'CERT STATUS']

    columns = ['charge_box_serial_number', 'location.name', 'pnc_common_name', 'serial_number',
               'issuer_name_hash', 'issuer_key_hash', 'expiry', 'status']
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    charger_headers = generate_charger_header(dbsession, membership_id)
    url = f'{CHARGER_URL_PREFIX}/charge_point/get_pnc_chargepoints'

    report = GenerateReport('evse_certificates', headers, columns)
    query_params = request.query_params
    query_params = dict(query_params)
    await report.generate_dataframe_with_charger_endpoint(query_params, url, charger_headers,
                                                          certificates=True)
    await report.datetime_reformat('expiry')
    return await report.generate_report()


@router.get('/location/download')  # noqa
async def download_locations(request: Request,
                             filters: dict = Depends(location_filters),  # noqa
                             dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    charger_headers = generate_charger_header(dbsession, membership_id)
    url = f'{CHARGER_URL_PREFIX}/location/'

    headers = ['NAME', 'ADDRESS', 'COUNTRY', 'OPERATING TIME (24*7)']
    columns = ['name', 'address', 'country', 'is_24']

    report = GenerateReport('locations', headers, columns)
    query_params = request.query_params
    query_params = dict(query_params)
    await report.generate_dataframe_with_charger_endpoint(query_params, url, charger_headers)
    return await report.generate_report()


@router.get('/ocpp/download')  # noqa
async def download_ocpp(request: Request, background_tasks: BackgroundTasks, send_to: str = None,
                        filters: dict = Depends(log_filters),  # noqa
                        dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    charger_headers = generate_charger_header(dbsession, membership_id)
    url = f'{CHARGER_URL_PREFIX}/log/ocpp'

    headers = ['Transaction Time', 'Charge Point ID', 'OCPP', 'Request', 'Response']
    reorder_columns = ['created_at', 'charge_point.charge_box_serial_number', 'ocpp_type',
                       'request_', 'response_']

    tz = pytz.timezone('Asia/Kuala_Lumpur')
    current_datetime = datetime.now(tz)
    format_datetime = current_datetime.strftime("%d.%m.%Y_(%H:%M)")
    filename = 'ocpp_log_' + format_datetime

    report = GenerateReport(filename, headers, reorder_columns=reorder_columns)
    query_params = request.query_params
    query_params = dict(query_params)

    async def run_get_ocpp_list(email):
        await report.generate_dataframe_with_charger_endpoint(query_params, url, charger_headers, ocpp=True)
        await report.datetime_reformat('created_at')
        await report.send_report_via_email('OCPP Log Report', filename, email)
        logger.info("Background task completed")

    if send_to:
        background_tasks.add_task(run_get_ocpp_list, send_to)
        return {'message': f'The report mailed to {send_to}. Will be ready in 15 minutes.'}
    await report.generate_dataframe_with_charger_endpoint(query_params, url, charger_headers, ocpp=True)
    await report.datetime_reformat('created_at')
    return await report.generate_report()


@router.get('/ocpp_v2/download')  # noqa
async def download_ocpp_using_ls(request: Request, background_tasks: BackgroundTasks, send_to: str = None,
                                 filters: dict = Depends(log_filters),  # noqa
                                 dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    charger_headers = generate_charger_header(dbsession, membership_id)
    url = f'{CHARGER_URL_PREFIX}/log/ocpp_2'

    headers = ['Transaction Time', 'Charge Point ID', 'Message Id', 'OCPP', 'Message Type', 'Response']
    reorder_columns = ['created_at', 'serial_number', 'message_id', 'ocpp_type', 'message_type', 'response']

    tz = pytz.timezone('Asia/Kuala_Lumpur')
    current_datetime = datetime.now(tz)
    format_datetime = current_datetime.strftime("%d.%m.%Y_(%H:%M)")
    filename = 'ocpp_log_' + format_datetime

    report = GenerateReport(filename, headers, reorder_columns=reorder_columns)
    query_params = request.query_params
    query_params = dict(query_params)

    # async def run_get_ocpp_list(email):
    #     await report.generate_dataframe_with_charger_endpoint(query_params, url, charger_headers, ocpp=True, ls=True)
    #     await report.datetime_reformat('created_at')
    #     await report.send_report_via_email('OCPP Log Report', filename, email)
    #     logger.info("Background task completed")

    if send_to:
        message = {
            "headers": headers,
            "reorder_columns": reorder_columns,
            "query_params": query_params,
            "url": url,
            "charger_headers": charger_headers,
            "filename": filename,
            "email": send_to
        }
        task_data = ReportingTaskCreate(
            task_name="send_ocpp_log_via_email",
            json_payload=message,
            retry_count=0
        )
        reporting_task = create_reporting_task_record(dbsession, task_data)
        task_uid = reporting_task.id
        message['task_uid'] = task_uid
        # message['task_uid'] = task_uid
        send_ocpp_log_via_email.delay(message=message)
        return {'message': f'The report mailed to {send_to}. Will be ready in 15 minutes.'}
    await report.generate_dataframe_with_charger_endpoint(query_params, url, charger_headers, ocpp=True, ls=True)
    await report.datetime_reformat('created_at')
    return await report.generate_report()


@router.get('/charge_point/energy-consumed/{charge_point_id}/download')  # noqa
async def download_charger_energy_consumed(request: Request, charge_point_id: str,
                                           filters: dict = Depends(energy_consumed_filters),
                                           dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    charger_headers = generate_charger_header(dbsession, membership_id)
    url = f'{CHARGER_URL_PREFIX}/charge_point/report/get-charger-energy-consumed/{charge_point_id}'
    query_params = request.query_params
    query_params = dict(query_params)
    filters['is_download'] = True
    headers = ['Time Interval (30 minutes)', 'Energy Consumed (kWh)']
    columns = ['hour', 'energy_spent']

    tz = pytz.timezone('Asia/Kuala_Lumpur')
    current_datetime = datetime.now(tz)
    format_datetime = current_datetime.strftime("%d.%m.%Y")
    filename_prefix = str(charge_point_id)

    if 'connector_id' in filters and filters['connector_id'] is not None:
        filename_prefix += str(filters['connector_id'])

    filename = 'energy_dispenses_graph_' + filename_prefix + '_' + format_datetime

    get_data = partial(get_energy_dispenses_data, url=url, headers=charger_headers,
                       filters=filters)
    report = GenerateEnergyGraph(filename, headers, columns, function=get_data)

    return await report.generate_report()


@router.post('charge_point/{charge_point_id}/install_root_certificate')
async def install_cp_root_certificate(request: Request, charge_point_id: str,
                                      dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    path = f"charge_point/{charge_point_id}/install_root_certificate"
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method

    response = await send_request(method, url, headers=headers, timeout=30)

    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/charge_point/{charge_point_id}/trigger_sign_certificate')
async def trigger_cp_sign_certificate(request: Request, charge_point_id: str,
                                      dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    path = f"charge_point/{charge_point_id}/ocpp"
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method
    ocpp_message_data = {
        'reason': MessageReason.start_ocpp_operation,
        'body': {
            'message_type': 'DataTransfer',
            'parameters': {
                'message_id': 'TriggerMessage',
                'data': '',
                'vendor_id': 'org.openchargealliance.iso15118pnc'
            }
        }
    }

    response = await send_request(method, url, headers=headers, data=json.dumps(ocpp_message_data), timeout=30)

    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/charge_point/{charge_point_id}/get_installed_certificate_ids')
async def get_cp_installed_certificate_ids(request: Request, charge_point_id: str,
                                           dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    path = f"charge_point/{charge_point_id}/ocpp"
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method
    ocpp_message_data = {
        'reason': MessageReason.start_ocpp_operation,
        'body': {
            'message_type': 'DataTransfer',
            'parameters': {
                'message_id': 'GetInstalledCertificateIds',
                'data': '',
                'vendor_id': 'org.openchargealliance.iso15118pnc'
            }
        }
    }

    response = await send_request(method, url, headers=headers, data=json.dumps(ocpp_message_data), timeout=30)

    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('charge_point/{charge_point_id}/delete_certificate/{serial_number}')
async def delete_cp_certificate(request: Request, charge_point_id: str, serial_number: str,
                                dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    path = f"charge_point/{charge_point_id}/delete_certificate/{serial_number}"
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method

    response = await send_request(method, url, headers=headers, timeout=30)

    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.get("/charge_point/view-qrcode/")
async def view_cp_image(image_key: str, filename: str):
    try:
        filename = f'qrcode_{filename}'
        image_url = await generate_presigned_url_for_view_image(image_key, filename)
        return {"image_url": image_url}
    except Exception as e:  # pylint: disable=broad-except
        logger.error('Error viewing image: %s', e)
        raise HTTPException(status_code=500, detail="Failed to view image")


@router.post('/charge_point/qrcode')
async def create_cp_qrcode_s3(request: Request,
                              dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    data = await request.json()
    serial_number = data.get('serial_number')
    charge_point_id = data.get('charge_point_id')

    qrcode_object = await generate_cp_qrcode(charge_point_id, serial_number)

    path = f'charge_point/qrcode/{charge_point_id}'
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method

    query_params = request.query_params
    response = await send_request(method, url, headers=headers,
                                  query_params=query_params, data=json.dumps(qrcode_object))

    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)
    await audit_log_charger(dbsession, response, user_id, membership_id, request)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/location/qrcode')
async def create_location_qrcode_s3(request: Request,
                                    dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    data = await request.json()

    location_id = data.get('location_id')

    qrcode_object = await generate_location_qrcode(location_id)

    path = f'location/qrcode/{location_id}'
    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = 'POST'

    response = await send_request(method, url, headers=headers, data=json.dumps(qrcode_object))

    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/connector/qrcode')
async def create_connector_qrcode_s3(request: Request,
                                     dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    if not membership_id:
        raise HTTPException(status_code=401, detail='Authorization token is missing or invalid')

    data = await request.json()
    for connector in data['connectors']:
        qrcode_object = await generate_connector_qrcode(connector.get('id'), data.get('serial_number'),
                                                        connector.get('number'))
        path = f"connector/qrcode/{connector.get('id')}"
        url = f'{CHARGER_URL_PREFIX}/{path}'
        method = request.method
        query_params = request.query_params
        response = await send_request(method, url, headers=headers,
                                      query_params=query_params, data=json.dumps(qrcode_object))

    table_name = re.match(r'^[^/]*', path).group(0)
    await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)
    await audit_log_charger(dbsession, response, user_id, membership_id, request)

    return JSONResponse(response.json(), status_code=response.status_code)


@router.post('/charge_point/')
async def create_cp(request: Request,  # noqa: MC0001
                    dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)
    query_params = request.query_params

    data = await request.json()
    serial_number = data.get('serial_number')
    # charge_point_id = data.get('charge_box_serial_number')
    #
    # try:
    #     qrcode_object = await generate_cp_qrcode(charge_point_id, serial_number)
    #     data['qrcode'] = qrcode_object
    # except Exception:  # pylint: disable=broad-except
    #     _ = ''
    path = 'charge_point/'
    url = f'{CHARGER_URL_PREFIX}/{path}'

    response = await send_request('POST', url, data=json.dumps(data), headers=headers, query_params=query_params)
    if response.status_code in [200, 201]:
        try:
            response_json = response.json()
            charge_point_id = response_json['id']
            qrcode_object = await generate_cp_qrcode(charge_point_id, serial_number)

            path = f'charge_point/qrcode/{charge_point_id}'
            url = f'{CHARGER_URL_PREFIX}/{path}'
            method = request.method

            _ = await send_request(method, url, headers=headers, data=json.dumps(qrcode_object))
        except Exception as e:  # pylint: disable=broad-except
            logger.error("Error creating QR Code as error as: %s", str(e))

    await audit_log_charger(dbsession, response, user_id, membership_id, request)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.api_route('/{path:path}', methods=['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
                  include_in_schema=False)
async def proxy_request(request: Request, path: str,  # noqa: MC0001
                        background_tasks: BackgroundTasks,
                        dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    login_user = UserCRUD.get(dbsession, user_id)
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    url = f'{CHARGER_URL_PREFIX}/{path}'
    method = request.method
    query_params = request.query_params
    data = await request.body()
    # Get object data that is being deleted in order to log delete activity in charger service
    if method == 'DELETE':
        response_delete = await send_request('GET', url, data=data, headers=headers, query_params=query_params)

    if 'csms/charging/current' in url and method == 'GET':
        query_params = dict(query_params)
        mem_org_id = crud.MembershipCRUD.membership().organization_id
        mem_org = dbsession.query(models.Organization).filter(models.Organization.id == mem_org_id).first()
        parent_org = mem_org.parent
        if parent_org and parent_org.parent:
            query_params['allow_ocpi'] = False
        else:
            query_params['allow_ocpi'] = True

        username = query_params['user_rfid'] if 'user_rfid' in query_params else None
        phone_number = query_params['phone_number'] if 'phone_number' in query_params else None
        email = query_params['email'] if 'email' in query_params else None

        if email or phone_number or (username is not None and len(username.strip()) >= 3):
            if email:
                query_params.pop('email')
            if phone_number:
                query_params.pop('phone_number')
            mem_filter = {
                'name': username,
                'user_id_tag': None,
                'organization': None,
            }
            user_filter = {'email': email, 'phone_number': phone_number}
            user_org = crud.organization.get_organization_from_user_id(dbsession, user_id)[0]['id']
            members = crud.organization.get_organization_membership_list(dbsession, organization_id=user_org,
                                                                         include_child=True,
                                                                         mem_filter=mem_filter,
                                                                         user_filter=user_filter, ).all()
            rfids_filter = {'name': None, 'type': None, 'is_active': None, 'from_date': None,
                            'to_date': None, 'username': username}
            id_fitlers = {'email': email, 'phone_number': phone_number}

            rfids = crud.organization.get_all_id_tags_with_filters(dbsession, rfids_filter, id_fitlers,
                                                                   check_permission=False).all()
            rfid = [r.id_tag for r in rfids]
            id_tags = [member.user_id_tag for member in members]
            id_tags = id_tags + rfid
            query_params['id_tags'] = id_tags
    response = await send_request(method, url, data=data, headers=headers, query_params=query_params)

    if 'api/v1/csms/charge_point/email/cp-breakdown-email' in url and method == 'GET' and response.status_code == 200:
        await send_cp_breakdown_email(response.json())

    if 'api/v1/csms/location/' in url and method == 'PATCH' and response.status_code == 200:
        location_id = response.json()['id']
        location_url = f'{CHARGER_URL_PREFIX}/location/{location_id}'
        location_response = await send_request('GET', location_url, data=data, headers=headers,
                                               query_params=query_params)
        # await push_update(dbsession, 'location', location_response.json())
        background_tasks.add_task(push_update, dbsession, 'location', location_response.json())

    if 'api/v1/csms/charge_point/' in url and method == 'PATCH' and response.status_code == 200:
        location_id = response.json()['location']['id']
        location_url = f'{CHARGER_URL_PREFIX}/location/{location_id}'
        location_response = await send_request('GET', location_url, data=data, headers=headers,
                                               query_params=query_params)
        # await push_update(dbsession, 'location', location_response.json())
        background_tasks.add_task(push_update, dbsession, 'location', location_response.json())
        try:
            response_json = response.json()
            charge_point_id = response_json['id']
            qrcode_object = await generate_cp_qrcode(charge_point_id, response_json['serial_number'])
            path = f'charge_point/qrcode/{charge_point_id}'
            url = f'{CHARGER_URL_PREFIX}/{path}'

            _ = await send_request('POST', url, headers=headers, data=json.dumps(qrcode_object))
        except Exception as e:  # pylint: disable=broad-except
            logger.error("Error creating QR Code as error as: %s", str(e))
    if 'api/v1/csms/location/' in url and method == 'POST' and response.status_code == 201:
        location_id = response.json()['id']
        # try:
        #     qrcode_object = await generate_location_qrcode(location_id)
        #
        #     path = f'location/qrcode/{location_id}'
        #     url = f'{CHARGER_URL_PREFIX}/{path}'
        #     method = 'POST'
        #     background_tasks.add_task(send_request, method, url, headers=headers, data=json.dumps(qrcode_object))
        # except Exception:  # pylint: disable=broad-except
        #     _ = ''

    if 'api/v1/csms/connector/list_connector' in url and method == 'POST' and response.status_code == 201:
        data = await request.json()
        try:

            cp_id = data[0].get('charge_point_id')
            cp_url = f'{CHARGER_URL_PREFIX}/charge_point/{cp_id}'
            cp_response = await send_request('GET', cp_url, headers=headers)
            cp_response_json = cp_response.json()
            for connector in cp_response_json.get('connectors'):
                try:
                    qrcode_object = await generate_connector_qrcode(connector.get('id'),
                                                                    cp_response_json.get('serial_number'),
                                                                    connector.get('number'))
                    path = f"connector/qrcode/{connector.get('id')}"
                    url = f'{CHARGER_URL_PREFIX}/{path}'
                    method = request.method
                    query_params = request.query_params
                    background_tasks.add_task(send_request, 'POST', url, headers=headers,
                                              data=json.dumps(qrcode_object))
                except Exception:  # pylint: disable=broad-except
                    _ = ''

        except Exception as e:  # pylint: disable=broad-except
            print(e)

    if 'api/v1/csms/connector' in url and method == 'PATCH' and response.status_code == 200:
        connector_id = response.json()['id']
        connector_url = f'{CHARGER_URL_PREFIX}/connector/{connector_id}'
        connector_response = await send_request('GET', connector_url, data=data, headers=headers,
                                                query_params=query_params)
        # await push_update(dbsession, 'connector', connector_response.json())
        background_tasks.add_task(push_update, dbsession, 'connector', connector_response.json())
        try:
            connector_response_json = connector_response.json()
            try:
                qrcode_object = await generate_connector_qrcode(connector_id,
                                                                connector_response_json.get('charge_point').get(
                                                                    'serial_number'),
                                                                connector_response_json.get('number'))
                path = f"connector/qrcode/{connector_id}"
                url = f'{CHARGER_URL_PREFIX}/{path}'
                background_tasks.add_task(send_request, 'POST', url, headers=headers, data=json.dumps(qrcode_object))
            except Exception:  # pylint: disable=broad-except
                _ = ''
        except Exception as e:  # pylint: disable=broad-except
            print(e)

        try:
            location_id = connector_response.json()['charge_point']['location']['id']
            location_url = f'{CHARGER_URL_PREFIX}/location/{location_id}'
            location_response = await send_request('GET', location_url, data=data, headers=headers,
                                                   query_params=query_params)
            # await push_update(dbsession, 'location', location_response.json())
            background_tasks.add_task(push_update, dbsession, 'location', location_response.json())

        except Exception as e:  # pylint: disable=broad-except
            print(e)

    if 'api/v1/csms/connector' in url and method == 'POST' and response.status_code == 201:
        connector_id = response.json().get('id')
        if connector_id is not None:
            connector_id = response.json()['id']
            connector_url = f'{CHARGER_URL_PREFIX}/connector/{connector_id}'
            connector_response = await send_request('GET', connector_url, data=data, headers=headers,
                                                    query_params=query_params)
            # await push_update(dbsession, 'connector', connector_response.json())
            background_tasks.add_task(push_update, dbsession, 'connector', connector_response.json())
            try:
                connector_response_json = connector_response.json()
                qrcode_object = await generate_connector_qrcode(connector_id,
                                                                connector_response_json.get('charge_point').get(
                                                                    'serial_number'),
                                                                connector_response_json.get('number'))
                path = f"connector/qrcode/{connector_id}"
                url = f'{CHARGER_URL_PREFIX}/{path}'
                background_tasks.add_task(send_request, 'POST', url, headers=headers, data=json.dumps(qrcode_object))
            except Exception as e:  # pylint: disable=broad-except
                print(e)

            try:
                location_id = connector_response.json()['charge_point']['location']['id']
                location_url = f'{CHARGER_URL_PREFIX}/location/{location_id}'
                location_response = await send_request('GET', location_url, data=data, headers=headers,
                                                       query_params=query_params)
                # await push_update(dbsession, 'location', location_response.json())
                background_tasks.add_task(push_update, dbsession, 'location', location_response.json())
            except Exception as e:  # pylint: disable=broad-except
                print(e)

    # response_data = response.json()
    # Log activity
    table_name = re.match(r'^[^/]*', path).group(0)
    if response.status_code < 300 and method == 'PATCH':
        await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.update, dbsession)
        # await push_update(dbsession, table_name, response_data)
    elif response.status_code < 300 and method == 'POST':
        await log_activity(user_id, table_name, response.json(), schema.ActivityLogType.create, dbsession)
        # await push_update(dbsession, table_name, response_data)
    elif response.status_code < 300 and method == 'DELETE':
        await log_activity(user_id, table_name, response_delete.json(), schema.ActivityLogType.delete, dbsession)

    # x_audit_resp = response.headers.get('X-Audit')
    await audit_log_charger(dbsession, response, user_id, membership_id, request)
    # AP-487 - Merge user details from charger service to user service
    # pylint:disable=too-many-nested-blocks
    if 'current' in url and method == 'GET':
        proxy_response_body = response.json()

        try:
            if len(proxy_response_body['items']) > 0:
                id_tags = [d['id_tag'] if d['id_tag'] else '' for d in proxy_response_body['items']]
                members = crud.get_member_by_id_tags(dbsession, id_tags)
                members = [schema.ShallowMembershipResponse.from_orm(member) for member in members]
                proxy_response_data = charger_merge_user_details(proxy_response_body['items'], members)
                mem_org_id = login_user.organization_id
                all_child_org = get_operator_list_by_organization_id(dbsession, mem_org_id)
                all_child_org = [str(child.id) for child in all_child_org]
                for cs in proxy_response_data:
                    meta = cs.get('meta', {})
                    member = meta.get('member')
                    if member:
                        cs_mem_org_id = None
                        cp = cs.get('charge_point_connector', {})
                        if cp:
                            cs_mem_org_id = cp.get('charge_point', {}).get('operator_id')
                        if cs_mem_org_id:
                            # cs_mem_org_id = cs['meta']['member']['organization_id']
                            if not login_user.is_superuser:
                                if not settings.CAN_VIEW_SUB_ORG:
                                    if str(cs_mem_org_id) not in all_child_org:
                                        cs['meta']['member'].user.email = '*****'
                                        cs['meta']['member'].first_name = '*****'
                                        cs['meta']['member'].last_name = '*****'
                if 'csms' in url:
                    proxy_response_body['items'] = proxy_response_data
                    return proxy_response_body
                return proxy_response_data
            return JSONResponse(response.json(), status_code=response.status_code)
        except KeyError:
            pass
    if 'api/v1/csms/load_balancing' in url and method == 'DELETE' and response.status_code == 204:
        return Response(status_code=status.HTTP_204_NO_CONTENT)
    if method == 'DELETE':
        return JSONResponse(response_delete.json(), status_code=response.status_code)
    return JSONResponse(response.json(), status_code=response.status_code)
