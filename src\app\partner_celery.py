import datetime
import ssl
from pathlib import Path
import pytz
from kombu import Queue
from celery import Celery
from celery.signals import heartbeat_sent, worker_ready, worker_shutdown
# from celery.schedules import crontab

from app.settings import CELERY_BROKER_URL, PARTNER_WORKER_EXCHANGE, WORKER_EXCHANGE_TYPE, RABBITMQ_IS_SECURE

# ESS_CELERY_INTERVAL

PARTNER_HEARTBEAT_FILE = Path("/tmp/partner_worker_heartbeat")  # nosec
PARTNER_READINESS_FILE = Path("/tmp/partner_worker_ready")  # nosec


# Liveness prob for celery/beat workers
@heartbeat_sent.connect
def heartbeat(**_):
    PARTNER_HEARTBEAT_FILE.touch()


@worker_ready.connect
def worker_ready(**_):
    PARTNER_READINESS_FILE.touch()


@worker_shutdown.connect
def worker_shutdown(**_):
    for f in (PARTNER_HEARTBEAT_FILE, PARTNER_READINESS_FILE):
        f.unlink()


if RABBITMQ_IS_SECURE:
    app = Celery('apollo-partner-charger', broker=CELERY_BROKER_URL, imports='app.partner_tasks', broker_use_ssl={
        'ssl_version': ssl.PROTOCOL_TLS_CLIENT,
    })
else:
    app = Celery('apollo-partner-charger', broker=CELERY_BROKER_URL, imports='app.partner_tasks')

app.conf.task_default_queue = 'default'
app.conf.task_queues = (
    Queue('default', routing_key='default'),
    Queue('main_partner_worker', routing_key='main_partner_worker'),
)
app.conf.task_always_eager = False
app.conf.task_default_exchange = PARTNER_WORKER_EXCHANGE
app.conf.task_default_exchange_type = WORKER_EXCHANGE_TYPE
app.conf.task_default_queue = 'default'
app.conf.task_default_routing_key = 'default'


def now_func():
    return datetime.datetime.now(pytz.timezone('Asia/Kuala_Lumpur'))


app.conf.beat_schedule = {
}
app.conf.timezone = 'UTC'
