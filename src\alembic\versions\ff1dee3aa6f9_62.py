"""62

Revision ID: ff1dee3aa6f9
Revises: e07d27a634df
Create Date: 2023-10-27 00:01:01.145969

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ff1dee3aa6f9'
down_revision = 'e07d27a634df'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_auth_external_token', sa.Column('name', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_auth_external_token', 'name')
    # ### end Alembic commands ###
