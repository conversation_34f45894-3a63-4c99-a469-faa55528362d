# pylint: disable=too-many-lines, too-many-statements, unused-variable
import json
import logging

import jwt
from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.exc import NoResultFound
from app.celery import app

from app import settings, crud, cybersource_payment_utils as cyber_utils, exceptions, schema
from app.crud import (get_member_primary_cc, get_payment_request,
                      get_cybersource_payment_enrollment_by_payment_form_id)
from app.cybersource_payment_utils import (
    create_jwt_token, setup_completion_with_flex_transient_token,
    check_enrollment, validate_authentication_results, create_tms_token_with_pa, decode_session_jwt_token,
    decode_base64_to_dict, get_transient_token_payload_from_jwt_transient_payload
)
# from app.celery import app
from app.database import create_session, SessionLocal
from app.exceptions import ApolloBlacklistedCreditCard
from app.middlewares import set_member_as_context_user, deactivate_audit
# set_admin_as_context_user
from app.schema.payment import (
    CyberSourceCreateToken, CyberSourceCreateDefaultPaymentCard, CyberSourceTokenResponse, PaymentRequestReason
)
from app.utils import get_cc_html_template, update_pr_status_on_failed, update_pr_status_on_success

templates = Jinja2Templates(directory="app/defaults")
PAYMENT_GATEWAY_TYPE = schema.CreditCardPaymentGateway.cybersource
logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH
MAIN_URL_PREFIX = f'{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}'
router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/cybersource",
    tags=['cybersource'],
)


def enroll_validate_auth(dbsession, transaction_id, token_type, db_cybersource_form, transient_token_payload,
                         template_type):
    db_payment_request = get_payment_request(dbsession, db_cybersource_form.payment_request_id)
    reference_id = str(db_payment_request.invoice_number)
    auth_status, request_body = validate_authentication_results(
        token_type=token_type,
        transaction_id=transaction_id,
        reference_id=str(reference_id),
        transient_token_payload=transient_token_payload,
        amount=db_cybersource_form.amount,
        currency=db_cybersource_form.currency
    )
    if not auth_status:
        content = get_cc_html_template(False, template_type,
                                       payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content

    auth_response = json.loads(auth_status)
    if auth_response:
        auth_schema = schema.CyberSourceAuthenticationStepCreate(
            payment_form_id=db_cybersource_form.id,
            transaction_id=transaction_id,
            response_body=auth_response,
            request_body=json.loads(request_body),
        )
        if auth_response["status"] == "AUTHENTICATION_SUCCESSFUL":
            auth_schema.status = 'Authorized'
            crud.create_cybersource_payment_authentication_step(dbsession, auth_schema)
            return True
        content = get_cc_html_template(False, template_type,
                                       payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content
    content = get_cc_html_template(False, template_type,
                                   payment_gateway_type=PAYMENT_GATEWAY_TYPE)
    return content


def validate_member(dbsession, membership_id):
    try:
        db_member = crud.get_membership_by_id(dbsession, membership_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Membership not found.')
    return db_member


def validate_session(session_token):
    session_data = decode_session_jwt_token(session_token)
    if not session_data:
        raise HTTPException(401, 'Session not found, not permitted to entry')
    return session_data


def validate_session_against_member(dbsession, membership_id, session_token):
    try:
        session_data = validate_session(session_token)
        db_member = validate_member(dbsession, membership_id)
        db_cybersource_form = crud.get_cybersource_payment_form(dbsession, session_data['entry_id'])
        if str(db_member.id) != str(db_cybersource_form.member_id):
            raise HTTPException(401, 'User mismatch, not permitted to entry')
    except HTTPException as exc:
        content = get_cc_html_template(False, schema.PaymentRequestReason.subscription,
                                       payment_gateway_type=PAYMENT_GATEWAY_TYPE, reason_code=exc.detail)
        raise HTTPException(status_code=3001, detail=content)
    return db_member, session_data, db_cybersource_form


def get_payment_token(dbsession, session_data, membership_id, db_cybersource_form):
    if db_cybersource_form.save_credit_card:
        return crud.get_cybersource_payment_token_step_by_payment_form_id(dbsession, session_data['entry_id'])
    return get_member_primary_cc(dbsession, membership_id)


def get_token_info(dbsession, db_cybersource_form, session_data, membership_id, use_transient: bool = False):
    # If it is for saving card, require to use the temporary token (transient token)
    if db_cybersource_form.save_credit_card or use_transient:
        db_cybersource_payment_token = crud.get_cybersource_payment_token_step_by_payment_form_id(dbsession,
                                                                                                  session_data[
                                                                                                      'entry_id'])
        token_type = schema.CyberSourceTokenType.transient_token
        transient_token_payload = db_cybersource_payment_token.transient_token
    # Else, use the customer token (user token)
    else:
        db_cybersource_payment_token = get_member_primary_cc(dbsession, membership_id)
        token_type = schema.CyberSourceTokenType.tms_token
        transient_token_payload = db_cybersource_payment_token.user_token
    return db_cybersource_payment_token, token_type, transient_token_payload


@router.post('/create-token', response_model=CyberSourceTokenResponse)
async def create_token_cybersource(request: Request, user_data: CyberSourceCreateToken,
                                   dbsession: SessionLocal = Depends(create_session)):
    deactivate_audit()
    db_member = crud.get_membership_by_user_id(dbsession, user_data.id)
    db_user = crud.get_user_by_id(dbsession, user_data.id)
    customer_response = json.loads(cyber_utils.create_customer(db_user, user_data.reference_no))
    instrument_identifier_response = json.loads(cyber_utils.create_instrument_identifier(user_data.cc_number))

    customer_token = customer_response.get('id')
    instrument_identifier_token = instrument_identifier_response.get('id')

    return CyberSourceTokenResponse(
        member_id=db_member.id,
        user_id=db_user.id,
        customer_token=customer_token,
        instrument_identifier_token=instrument_identifier_token
    )


@router.post('/default-card')
async def create_default_card_cybersource(request: Request, user_data: CyberSourceCreateDefaultPaymentCard,
                                          dbsession: SessionLocal = Depends(create_session)):
    deactivate_audit()
    default_card_response = json.loads(cyber_utils.create_customer_default_payment_instrument_card(user_data))
    return default_card_response


@router.get('/pre-auth', name="pre_auth_cybersource")
async def pre_auth_cybersource(request: Request, membership_id: str,
                               payment_request_id: str,
                               dbsession: SessionLocal = Depends(create_session)):
    deactivate_audit()
    # membership_id = "05f0f41d-d304-4b77-9b2d-fde573c374a7"
    set_member_as_context_user(dbsession, str(membership_id))

    try:
        db_member = crud.get_membership_by_id(dbsession, membership_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Membership not found.')

    _ = crud.get_user_by_id(dbsession, db_member.user_id)
    db_payment_request = get_payment_request(dbsession, payment_request_id)
    db_cc = get_member_primary_cc(dbsession, db_member.id)

    jwt_token, request_obj = cyber_utils.generate_capture_context(currency=db_payment_request.currency)
    if not jwt_token:
        reason_code = 'CAPTURE-NOT-AVAILABLE'
        content = get_cc_html_template(False, schema.PaymentRequestReason.subscription,
                                       payment_gateway_type=PAYMENT_GATEWAY_TYPE, reason_code=reason_code)
        return HTMLResponse(content=content, status_code=200)

    payment_form_data = schema.CyberSourcePaymentFormCreate(
        member_id=membership_id,
        save_credit_card=False,
        currency=db_payment_request.currency,
        amount=db_payment_request.amount,
        description="Pre-authorization fee",
        capture_context=jwt_token,
        payment_request_id=payment_request_id,
        request_body=json.loads(request_obj),
    )
    db_cybersource_form = crud.create_cybersource_payment_form(dbsession, payment_form_data)

    db_payment_request = get_payment_request(dbsession, db_cybersource_form.payment_request_id)
    reference_id = str(db_payment_request.invoice_number)

    payment_response, request_obj = setup_completion_with_flex_transient_token(
        token_type=schema.CyberSourceTokenType.tms_token,
        transient_token_payload=db_cc.token,
        reference_id=str(reference_id),
        db_cc=db_cc,
        currency=db_payment_request.currency,
    )
    if not payment_response:
        content = get_cc_html_template(False, PaymentRequestReason.pre_auth, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return HTMLResponse(content=content, status_code=200)

    setup_response = json.loads(payment_response)
    session_data = {"entry_id": str(db_cybersource_form.id)}
    session_token = create_jwt_token(session_data)

    error = setup_response.get('errorInformation', None)
    if error is not None:
        error = error.get('reason', None)
        content = get_cc_html_template(False, PaymentRequestReason.pre_auth, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                       reason_code=error)
        token_step_data = schema.CyberSourceTokenStepCreate(
            payment_form_id=db_cybersource_form.id,
            transient_token=db_cc.user_token,
            access_token=None,
            device_data_collection_url=None,
            response_body=setup_response,
            request_body=json.loads(request_obj),
        )
        crud.create_cybersource_payment_token_step(dbsession, token_step_data)
        return HTMLResponse(content=content, status_code=200)

    access_token = setup_response["consumerAuthenticationInformation"]["accessToken"]
    device_data_collection_url = setup_response["consumerAuthenticationInformation"]["deviceDataCollectionUrl"]

    token_step_data = schema.CyberSourceTokenStepCreate(
        payment_form_id=db_cybersource_form.id,
        transient_token=db_cc.user_token,
        access_token=access_token,
        device_data_collection_url=device_data_collection_url,
        response_body=setup_response,
        request_body=json.loads(request_obj),
    )
    crud.create_cybersource_payment_token_step(dbsession, token_step_data)

    return templates.TemplateResponse("cybersource_device_collection.html", {
        "request": request,
        "session_token": session_token,
        "access_token": access_token,
        "device_data_collection_url": device_data_collection_url,
        "member_id": membership_id,
    })


@router.get("/cc", response_class=HTMLResponse, name="serve_cybersource_cc_html")
async def serve_cybersource_cc_html(request: Request, membership_id: str, payment_request_id: str,
                                    dbsession: SessionLocal = Depends(create_session)):
    deactivate_audit()
    set_member_as_context_user(dbsession, str(membership_id))
    try:
        db_member = crud.get_membership_by_id(dbsession, membership_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Membership not found.')
    payment_request = get_payment_request(dbsession, payment_request_id)

    jwt_token, request_obj = cyber_utils.generate_capture_context(currency=payment_request.currency)
    if not jwt_token:
        return HTMLResponse(content="Failed to generate capture context", status_code=500)

    db_user = crud.get_user_by_id(dbsession, db_member.user_id)

    user_information = {
        'first_name': db_member.first_name,
        'last_name': db_member.last_name,
        'member_id': db_member.id,
        'email': db_user.email,
        'phone_number': db_user.phone_number
    }

    payload = jwt.decode(jwt_token, options={"verify_signature": False})
    client_library_url = payload["ctx"][0]["data"]["clientLibrary"]
    client_library_integrity = payload["ctx"][0]["data"].get("clientLibraryIntegrity", None)

    save_cc = True
    if payment_request.reason == schema.PaymentRequestReason.subscription:
        save_cc = False
    payment_form_data = schema.CyberSourcePaymentFormCreate(
        member_id=membership_id,
        save_credit_card=save_cc,
        currency=payment_request.currency,
        amount=float(payment_request.amount),
        description=payment_request.billing_description,
        capture_context=jwt_token,
        client_library_url=client_library_url,
        client_library_integrity=client_library_integrity,
        response_body=payload,
        request_body=json.loads(request_obj),
        payment_request_id=payment_request_id,
    )
    db_payment_form = crud.create_cybersource_payment_form(dbsession, payment_form_data)
    session_data = {"entry_id": str(db_payment_form.id)}
    session_token = create_jwt_token(session_data)

    return templates.TemplateResponse("cybersource_add_card.html", {
        "request": request,
        "capture_context": jwt_token,
        "client_library_url": client_library_url,
        "client_library_integrity": client_library_integrity,
        "user_information": user_information,
        "session_token": session_token,
    })


@router.post("/save-cc", response_class=HTMLResponse)
async def save_card_from_form(request: Request, dbsession: SessionLocal = Depends(create_session)):
    deactivate_audit()
    form_data = await request.form()
    membership_id = form_data.get('member_id')
    session_token = form_data.get('session_token')
    flex_response = form_data.get('flexresponse')
    set_member_as_context_user(dbsession, membership_id)

    # session_data = decode_jwt_token(session_token)
    try:
        _, session_data, db_cybersource_form = validate_session_against_member(dbsession, membership_id,
                                                                               session_token)
    except HTTPException as exc:
        if exc.status_code == 3001:
            return HTMLResponse(content=exc.detail, status_code=200)
        content = get_cc_html_template(False, PaymentRequestReason.subscription,
                                       payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content
    session_token = create_jwt_token(session_data)
    is_subscription = db_cybersource_form.payment_request.reason in [schema.PaymentRequestReason.subscription,
                                                                     schema.PaymentRequestReason.renewal]
    save_card = db_cybersource_form.save_credit_card
    if is_subscription:
        template_type = PaymentRequestReason.subscription
    else:
        if save_card:
            template_type = PaymentRequestReason.payment
        else:
            template_type = PaymentRequestReason.pre_auth

    db_payment_request = get_payment_request(dbsession, db_cybersource_form.payment_request_id)
    reference_id = str(db_payment_request.invoice_number)

    setup_response, request_obj = setup_completion_with_flex_transient_token(
        token_type=schema.CyberSourceTokenType.transient_token,
        transient_token_payload=flex_response,
        reference_id=str(reference_id),
        currency=db_payment_request.currency,
    )
    if not setup_response:
        content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content

    setup_response = json.loads(setup_response)

    token_step_data = schema.CyberSourceTokenStepCreate(
        payment_form_id=db_cybersource_form.id,
        transient_token=flex_response,
        response_body=setup_response,
        request_body=json.loads(request_obj),
    )

    error = setup_response.get('errorInformation', None)
    if error is not None:
        error = error.get('reason', None)
        content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                       reason_code=error)
        crud.create_cybersource_payment_token_step(dbsession, token_step_data)
        return content

    access_token = setup_response["consumerAuthenticationInformation"]["accessToken"]
    device_data_collection_url = setup_response["consumerAuthenticationInformation"]["deviceDataCollectionUrl"]

    token_step_data.access_token = access_token
    token_step_data.device_data_collection_url = device_data_collection_url
    crud.create_cybersource_payment_token_step(dbsession, token_step_data)

    return templates.TemplateResponse("cybersource_device_collection.html", {
        "request": request,
        "session_token": session_token,
        "access_token": access_token,
        "device_data_collection_url": device_data_collection_url,
        "member_id": membership_id,
    })
    # else:


@router.post("/enroll", response_class=HTMLResponse)
# pylint:disable=too-many-statements, # pylint:disable=too-many-branches, # noqa: MC0001
async def enroll_3ds(request: Request, dbsession: SessionLocal = Depends(create_session)):  # noqa: MC0001
    client_host = request.client.host

    deactivate_audit()
    form_data = await request.json()
    cardinal_reference_id = form_data.get("referenceId")
    session_token = form_data.get("session_token")
    membership_id = form_data.get('member_id')
    screen_width = form_data.get('screen_width')
    screen_height = form_data.get('screen_height')

    set_member_as_context_user(dbsession, membership_id)
    try:
        db_member, session_data, db_cybersource_form = validate_session_against_member(dbsession, membership_id,
                                                                                       session_token)
    except HTTPException as exc:
        if exc.status_code == 3001:
            return HTMLResponse(content=exc.detail, status_code=200)
        content = get_cc_html_template(False, PaymentRequestReason.subscription,
                                       payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content
    db_user = crud.get_user_by_id(dbsession, db_member.user_id)

    is_subscription = db_cybersource_form.payment_request.reason in [schema.PaymentRequestReason.subscription,
                                                                     schema.PaymentRequestReason.renewal]
    save_card = db_cybersource_form.save_credit_card

    if is_subscription:
        template_type = PaymentRequestReason.subscription
    else:
        if save_card:
            template_type = PaymentRequestReason.payment
        else:
            template_type = PaymentRequestReason.pre_auth

    db_cybersource_payment_token, token_type, transient_token_payload = get_token_info(dbsession, db_cybersource_form,
                                                                                       session_data, membership_id,
                                                                                       is_subscription)

    return_url = f'{MAIN_URL_PREFIX}/api/v1/csms/cybersource/auth-check'
    session_token = create_jwt_token(session_data)

    db_payment_request = get_payment_request(dbsession, db_cybersource_form.payment_request_id)
    reference_id = str(db_payment_request.invoice_number)

    enroll_response, request_obj = check_enrollment(
        db_member=db_member,
        db_user=db_user,
        token_type=token_type,
        reference_id=cardinal_reference_id,
        return_url=return_url,
        transient_token_payload=transient_token_payload,
        session_id=str(reference_id),
        amount=db_cybersource_form.amount,
        currency=db_cybersource_form.currency,
        screen_width=screen_width,
        screen_height=screen_height,
        ip_address=client_host,
    )

    if not enroll_response:
        card_data = get_transient_token_payload_from_jwt_transient_payload(transient_token_payload, 'data')
        if card_data is not None:
            card_type = card_data.get('type', None)
            if card_type not in ['001', '002']:
                content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                               reason_code='CARD-NOT-SUPPORTED')
                return content

        content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content

    enroll_response = json.loads(enroll_response)

    enrollment_step = schema.CyberSourceEnrollmentStepCreate(
        payment_form_id=db_cybersource_form.id,
        reference_id=cardinal_reference_id,
        response_body=enroll_response,
        screen_height=screen_height,
        screen_width=screen_width,
        request_body=json.loads(request_obj),
    )
    if enroll_response["status"] == "PENDING_AUTHENTICATION":
        step_up_url = enroll_response["consumerAuthenticationInformation"]["stepUpUrl"]
        jwt_token = enroll_response["consumerAuthenticationInformation"]["accessToken"]
        challenge_window_size = enroll_response["consumerAuthenticationInformation"]["pareq"]
        challenge_decoded = decode_base64_to_dict(challenge_window_size)
        challenge_decoded = challenge_decoded.get('challengeWindowSize')
        window_size_map = {
            "01": (250, 400),
            "02": (390, 400),
            "03": (500, 600),
            "04": (600, 400),
            "05": ("100%", "100%")
        }
        height, width = window_size_map.get(challenge_decoded, (390, 400))
        enrollment_step.step_up_url = step_up_url
        enrollment_step.access_token = jwt_token
        enrollment_step.return_url = return_url

        crud.create_cybersource_payment_enrollment_step(dbsession, enrollment_step)

        return templates.TemplateResponse("cybersource_step_up_authentication.html", {
            "request": request,
            "step_up_url": step_up_url,
            "jwt_token": jwt_token,
            "session_token": session_token,
            "member_id": membership_id,
            "height": height,
            "width": width
        })

    crud.create_cybersource_payment_enrollment_step(dbsession, enrollment_step)
    status = enroll_response.get('status')
    enrolled = enroll_response.get('consumerAuthenticationInformation', {}).get('veresEnrolled')
    pares_status = enroll_response.get('consumerAuthenticationInformation', {}).get('paresStatus')

    bypass_consumer_auth_check = False
    if status == 'AUTHENTICATION_SUCCESSFUL':
        if enrolled == 'Y':
            content = None
            if pares_status == 'U':
                reason_code = 'NOT-AVAILABLE-AT-THIS-MOMENT-PARES-U'
                content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                               reason_code=reason_code)

            elif pares_status == 'C':
                reason_code = 'NOT-SUPPORTING-3DS-RES-A'
                content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                               reason_code=reason_code)

            elif pares_status == 'Y':
                bypass_consumer_auth_check = True
            elif pares_status == 'A':
                bypass_consumer_auth_check = True

            if content:
                return content

        elif enrolled == 'U':
            reason_code = 'TIMEOUT-ENROLL-U'
            content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                           reason_code=reason_code)
            return content
        elif enrolled == 'B':
            bypass_consumer_auth_check = True

    if status == 'AUTHENTICATION_FAILED' and enrolled == 'Y':
        content = None
        if pares_status == 'N':
            reason_code = 'AUTHENTICATION-FAILED-RES-N'
            content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                           reason_code=reason_code)
        elif pares_status == 'R':
            reason_code = 'AUTHENTICATION-REJECTED-RES-R'
            content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                           reason_code=reason_code)
        if content:
            return content

    if db_cybersource_form.save_credit_card or is_subscription:
        transaction_id = enroll_response["consumerAuthenticationInformation"]["authenticationTransactionId"]
        try:
            tokenized, error_code = create_tms_token_with_pa(
                reference_id=str(reference_id),
                transient_token_payload=db_cybersource_payment_token.transient_token,
                db_member=db_member,
                db_user=db_user,
                amount=db_cybersource_form.amount,
                currency=db_cybersource_form.currency,
                transaction_id=transaction_id,
                save_cc=not is_subscription,
                bypass_consumer_auth_check=bypass_consumer_auth_check,
                payment_form_id=str(db_cybersource_form.id),
                ip_address=client_host,
            )
        except ApolloBlacklistedCreditCard:
            return HTMLResponse(content=get_cc_html_template(False, template_type, reason='Blacklisted'),
                                status_code=200)
        # admin = set_admin_as_context_user(dbsession)  # noqa
        if tokenized:
            await update_pr_status_on_success(dbsession, db_cybersource_form.payment_request,
                                              {}, is_sync=True)
            content = get_cc_html_template(True, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        else:
            await update_pr_status_on_failed(dbsession, db_cybersource_form.payment_request, {})
            content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                           reason_code=error_code)
        return HTMLResponse(content=content, status_code=200)

    transaction_id = enroll_response["consumerAuthenticationInformation"]["authenticationTransactionId"]
    db_payment_request = get_payment_request(dbsession, db_cybersource_form.payment_request_id)
    reference_id = str(db_payment_request.invoice_number)
    payment_response = json.loads(cyber_utils.create_payment(
        customer_token=db_cybersource_payment_token.user_token,
        payment_instrument_token=db_cybersource_payment_token.token,
        amount=db_cybersource_form.amount,
        currency=db_cybersource_form.currency,
        auth_indicator=1, is_3ds=True,
        bypass_consumer_auth_check=True,
        transaction_id=transaction_id,
        reference_id=str(reference_id),
    ))
    if payment_response['status'] == 'AUTHORIZED':
        content = get_cc_html_template(True, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        try:
            db_pre_auth = crud.get_pre_auth_payment_by_pr_id(dbsession,
                                                             db_cybersource_form.payment_request_id)
            pre_auth_payment_update = schema.PreAuthPaymentResponse(
                is_successful=True,
                response=payment_response,
                transaction_id=payment_response['id']
            )
            crud.update_pre_auth_payment(dbsession, db_pre_auth.id, pre_auth_payment_update)
            message = {
                'message_type': 'SUCCESS',
                'pre_auth_id': db_pre_auth.id
            }
            app.send_task(f'{settings.MAIN_SERVICE_TASKS_PREFIX}.notify_pre_auth',
                          kwargs={'message': message},
                          queue=settings.MAIN_SERVICE_WORKER_QUEUE,
                          routing_key=settings.MAIN_SERVICE_WORKER_ROUTE)
            return content
        except NoResultFound:
            content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
    else:
        content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
    return HTMLResponse(content=content, status_code=200)


@router.post("/auth-check")
async def auth_check_3ds_and_process_payment(request: Request,  # pylint: disable=too-many-locals  # noqa: MC0001
                                             dbsession: SessionLocal = Depends(create_session)):
    client_host = request.client.host

    deactivate_audit()
    form_data = await request.form()
    transaction_id = form_data.get("TransactionId")
    _ = form_data.get("Response")
    md = form_data.get("MD")

    parts = md.split('|')
    membership_id, session_token = parts

    set_member_as_context_user(dbsession, membership_id)
    try:
        db_member, session_data, db_cybersource_form = validate_session_against_member(dbsession, membership_id,
                                                                                       session_token)
    except HTTPException as exc:
        if exc.status_code == 3001:
            return HTMLResponse(content=exc.detail, status_code=200)
        content = get_cc_html_template(False, PaymentRequestReason.subscription,
                                       payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content
    db_user = crud.get_user_by_id(dbsession, db_member.user_id)

    is_subscription = db_cybersource_form.payment_request.reason in [schema.PaymentRequestReason.subscription,
                                                                     schema.PaymentRequestReason.renewal]
    save_card = db_cybersource_form.save_credit_card
    if is_subscription:
        template_type = PaymentRequestReason.subscription
    else:
        if save_card:
            template_type = PaymentRequestReason.payment
        else:
            template_type = PaymentRequestReason.pre_auth
    db_cybersource_payment_token, token_type, transient_token_payload = get_token_info(dbsession, db_cybersource_form,
                                                                                       session_data, membership_id,
                                                                                       is_subscription)

    result = enroll_validate_auth(dbsession, transaction_id, token_type, db_cybersource_form, transient_token_payload,
                                  template_type)
    db_payment_request = get_payment_request(dbsession, db_cybersource_form.payment_request_id)
    reference_id = str(db_payment_request.invoice_number)
    if not isinstance(result, bool):
        return HTMLResponse(content=result, status_code=200)
    if db_cybersource_form.save_credit_card or is_subscription:
        db_enrollment = get_cybersource_payment_enrollment_by_payment_form_id(dbsession, db_cybersource_form.id)
        try:
            tokenized, error_code = create_tms_token_with_pa(
                transaction_id=transaction_id,
                reference_id=str(reference_id),
                transient_token_payload=db_cybersource_payment_token.transient_token,
                db_member=db_member,
                db_user=db_user,
                amount=db_cybersource_form.amount,
                currency=db_cybersource_form.currency,
                screen_width=db_enrollment.screen_width,
                screen_height=db_enrollment.screen_height,
                save_cc=not is_subscription,
                payment_form_id=str(db_cybersource_form.id),
                ip_address=client_host,
            )
        except ApolloBlacklistedCreditCard:
            return HTMLResponse(content=get_cc_html_template(False, template_type, reason='Blacklisted'),
                                status_code=200)
        # admin = set_admin_as_context_user(dbsession)  # noqa
        if tokenized:
            crud.payment_request_done(dbsession, db_cybersource_form.payment_request_id)
            await update_pr_status_on_success(dbsession, db_cybersource_form.payment_request,
                                              {}, is_sync=True)

            content = get_cc_html_template(True, template_type,
                                           payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        else:
            # await update_pr_status_on_failed(dbsession, db_cybersource_form.payment_request_id, {})
            await update_pr_status_on_failed(dbsession,
                                             db_cybersource_form.payment_request,
                                             {})

            content = get_cc_html_template(False, template_type,
                                           payment_gateway_type=PAYMENT_GATEWAY_TYPE, reason_code=error_code)

        return HTMLResponse(content=content, status_code=200)

    # If not save-cc
    payment_response = json.loads(cyber_utils.create_payment(
        customer_token=db_cybersource_payment_token.user_token,
        payment_instrument_token=db_cybersource_payment_token.token,
        amount=db_cybersource_form.amount,
        currency=db_cybersource_form.currency,
        auth_indicator=1,
        is_3ds=True,
        transaction_id=transaction_id,
        reference_id=str(reference_id),
    ))
    if payment_response["status"] == "AUTHORIZED":
        try:
            db_pre_auth = crud.get_pre_auth_payment_by_pr_id(dbsession,
                                                             db_cybersource_form.payment_request_id)
            pre_auth_payment_update = schema.PreAuthPaymentResponse(
                is_successful=True,
                response=payment_response,
                transaction_id=payment_response['id']
            )
            crud.update_pre_auth_payment(dbsession, db_pre_auth.id, pre_auth_payment_update)
            content = get_cc_html_template(True, template_type,
                                           payment_gateway_type=PAYMENT_GATEWAY_TYPE)
            message = {
                'message_type': 'SUCCESS',
                'pre_auth_id': db_pre_auth.id
            }
            app.send_task(f'{settings.MAIN_SERVICE_TASKS_PREFIX}.notify_pre_auth',
                          kwargs={'message': message},
                          queue=settings.MAIN_SERVICE_WORKER_QUEUE,
                          routing_key=settings.MAIN_SERVICE_WORKER_ROUTE)
        except NoResultFound:
            content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
    else:
        content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
    return HTMLResponse(content=content, status_code=200)


@router.get("/guest/pre-auth-get-cc", response_class=HTMLResponse, name="guest_serve_cybersource_cc_html")
async def guest_serve_cybersource_cc_html(request: Request, membership_id: str, payment_request_id: str,
                                          dbsession: SessionLocal = Depends(create_session)):
    deactivate_audit()
    set_member_as_context_user(dbsession, str(membership_id))
    try:
        db_member = crud.get_membership_by_id(dbsession, membership_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Membership not found.')
    payment_request = get_payment_request(dbsession, payment_request_id)

    jwt_token, request_obj = cyber_utils.generate_capture_context(currency=payment_request.currency)
    if not jwt_token:
        return HTMLResponse(content="Failed to generate capture context", status_code=500)

    # db_user = crud.get_user_by_id(dbsession, db_member.user_id)

    user_information = {
        'first_name': '',
        'last_name': '',
        'member_id': str(db_member.id),
        'email': '',
        'phone_number': ''
    }

    payload = jwt.decode(jwt_token, options={"verify_signature": False})
    client_library_url = payload["ctx"][0]["data"]["clientLibrary"]
    client_library_integrity = payload["ctx"][0]["data"].get("clientLibraryIntegrity", None)

    payment_form_data = schema.CyberSourcePaymentFormCreate(
        member_id=membership_id,
        save_credit_card=False,
        currency=payment_request.currency,
        amount=float(payment_request.amount),
        description=payment_request.billing_description,
        capture_context=jwt_token,
        client_library_url=client_library_url,
        client_library_integrity=client_library_integrity,
        response_body=payload,
        request_body=json.loads(request_obj),
        payment_request_id=payment_request_id,
    )
    db_payment_form = crud.create_cybersource_payment_form(dbsession, payment_form_data)
    session_data = {"entry_id": str(db_payment_form.id)}
    session_token = create_jwt_token(session_data)

    return templates.TemplateResponse("cybersource_guest_pre_auth_card.html", {
        "request": request,
        "capture_context": jwt_token,
        "client_library_url": client_library_url,
        "client_library_integrity": client_library_integrity,
        "user_information": user_information,
        "session_token": session_token,
    })


@router.post("/guest/pre-auth-cc", response_class=HTMLResponse)
async def guest_device_collection_form(request: Request, dbsession: SessionLocal = Depends(create_session)):
    deactivate_audit()
    form_data = await request.form()
    membership_id = form_data.get('member_id')
    session_token = form_data.get('session_token')
    flex_response = form_data.get('flexresponse')

    guest_info = {
        "first_name": form_data.get("firstName") or None,
        "last_name": form_data.get("lastName") or None,
        "email": form_data.get("email") or None,
        "phone_number": form_data.get("phoneNumber") or None,
    }
    guest_info_json = json.dumps(guest_info)

    set_member_as_context_user(dbsession, membership_id)

    # session_data = decode_jwt_token(session_token)
    try:
        _, session_data, db_cybersource_form = validate_session_against_member(dbsession, membership_id,
                                                                               session_token)
    except HTTPException as exc:
        if exc.status_code == 3001:
            return HTMLResponse(content=exc.detail, status_code=200)
        content = get_cc_html_template(False, PaymentRequestReason.subscription,
                                       payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content
    session_token = create_jwt_token(session_data)

    template_type = PaymentRequestReason.pre_auth

    db_payment_request = get_payment_request(dbsession, db_cybersource_form.payment_request_id)
    reference_id = str(db_payment_request.invoice_number)

    setup_response, request_obj = setup_completion_with_flex_transient_token(
        token_type=schema.CyberSourceTokenType.transient_token,
        transient_token_payload=flex_response,
        reference_id=str(reference_id),
        currency=db_payment_request.currency,
    )
    if not setup_response:
        content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content

    setup_response = json.loads(setup_response)

    token_step_data = schema.CyberSourceTokenStepCreate(
        payment_form_id=db_cybersource_form.id,
        transient_token=flex_response,
        response_body=setup_response,
        request_body=json.loads(request_obj),
    )

    error = setup_response.get('errorInformation', None)
    if error is not None:
        error = error.get('reason', None)
        content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                       reason_code=error)
        crud.create_cybersource_payment_token_step(dbsession, token_step_data)
        return content

    access_token = setup_response["consumerAuthenticationInformation"]["accessToken"]
    device_data_collection_url = setup_response["consumerAuthenticationInformation"]["deviceDataCollectionUrl"]

    token_step_data.access_token = access_token
    token_step_data.device_data_collection_url = device_data_collection_url
    crud.create_cybersource_payment_token_step(dbsession, token_step_data)

    return templates.TemplateResponse("cybersource_guest_device_collection.html", {
        "request": request,
        "session_token": session_token,
        "access_token": access_token,
        "device_data_collection_url": device_data_collection_url,
        "member_id": membership_id,
        "guest_info": guest_info_json,
    })


@router.post("/guest/enroll", response_class=HTMLResponse)
# pylint:disable=too-many-statements, # pylint:disable=too-many-branches, # noqa: MC0001
async def guest_enroll_3ds(request: Request, dbsession: SessionLocal = Depends(create_session)):  # noqa: MC0001
    client_host = request.client.host

    deactivate_audit()
    form_data = await request.json()
    cardinal_reference_id = form_data.get("referenceId")
    session_token = form_data.get("session_token")
    membership_id = form_data.get('member_id')
    screen_width = form_data.get('screen_width')
    screen_height = form_data.get('screen_height')
    guest_info = form_data.get('guest_info')
    guest_info_json = json.loads(guest_info)

    set_member_as_context_user(dbsession, membership_id)
    try:
        db_member, session_data, db_cybersource_form = validate_session_against_member(dbsession, membership_id,
                                                                                       session_token)
    except HTTPException as exc:
        if exc.status_code == 3001:
            return HTMLResponse(content=exc.detail, status_code=200)
        content = get_cc_html_template(False, PaymentRequestReason.subscription,
                                       payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content
    db_user = crud.get_user_by_id(dbsession, db_member.user_id)

    template_type = PaymentRequestReason.pre_auth

    db_cybersource_payment_token, token_type, transient_token_payload = get_token_info(dbsession, db_cybersource_form,
                                                                                       session_data, membership_id,
                                                                                       use_transient=True)

    return_url = f'{MAIN_URL_PREFIX}/api/v1/csms/cybersource/guest/auth-check'
    session_token = create_jwt_token(session_data)

    db_payment_request = get_payment_request(dbsession, db_cybersource_form.payment_request_id)
    reference_id = str(db_payment_request.invoice_number)

    enroll_response, request_obj = check_enrollment(
        db_member=guest_info_json,
        db_user=db_user,
        token_type=token_type,
        reference_id=cardinal_reference_id,
        return_url=return_url,
        transient_token_payload=transient_token_payload,
        session_id=str(reference_id),
        amount=db_cybersource_form.amount,
        currency=db_cybersource_form.currency,
        screen_width=screen_width,
        screen_height=screen_height,
        ip_address=client_host,
    )

    if not enroll_response:
        card_data = get_transient_token_payload_from_jwt_transient_payload(transient_token_payload, 'data')
        if card_data is not None:
            card_type = card_data.get('type', None)
            if card_type not in ['001', '002']:
                content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                               reason_code='CARD-NOT-SUPPORTED')
                return content

        content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content

    enroll_response = json.loads(enroll_response)

    enrollment_step = schema.CyberSourceEnrollmentStepCreate(
        payment_form_id=db_cybersource_form.id,
        reference_id=cardinal_reference_id,
        response_body=enroll_response,
        screen_height=screen_height,
        screen_width=screen_width,
        request_body=json.loads(request_obj),
    )
    if enroll_response["status"] == "PENDING_AUTHENTICATION":
        step_up_url = enroll_response["consumerAuthenticationInformation"]["stepUpUrl"]
        jwt_token = enroll_response["consumerAuthenticationInformation"]["accessToken"]
        challenge_window_size = enroll_response["consumerAuthenticationInformation"]["pareq"]
        challenge_decoded = decode_base64_to_dict(challenge_window_size)
        challenge_decoded = challenge_decoded.get('challengeWindowSize')
        window_size_map = {
            "01": (250, 400),
            "02": (390, 400),
            "03": (500, 600),
            "04": (600, 400),
            "05": ("100%", "100%")
        }
        height, width = window_size_map.get(challenge_decoded, (390, 400))
        enrollment_step.step_up_url = step_up_url
        enrollment_step.access_token = jwt_token
        enrollment_step.return_url = return_url

        crud.create_cybersource_payment_enrollment_step(dbsession, enrollment_step)

        return templates.TemplateResponse("cybersource_guest_step_up_authentication.html", {
            "request": request,
            "step_up_url": step_up_url,
            "jwt_token": jwt_token,
            "session_token": session_token,
            "member_id": membership_id,
            "height": height,
            "width": width,
            "guest_info": guest_info
        })

    crud.create_cybersource_payment_enrollment_step(dbsession, enrollment_step)
    status = enroll_response.get('status')
    enrolled = enroll_response.get('consumerAuthenticationInformation', {}).get('veresEnrolled')
    pares_status = enroll_response.get('consumerAuthenticationInformation', {}).get('paresStatus')

    bypass_consumer_auth_check = False
    if status == 'AUTHENTICATION_SUCCESSFUL':
        if enrolled == 'Y':
            content = None
            if pares_status == 'U':
                reason_code = 'NOT-AVAILABLE-AT-THIS-MOMENT-PARES-U'
                content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                               reason_code=reason_code)

            elif pares_status == 'C':
                reason_code = 'NOT-SUPPORTING-3DS-RES-A'
                content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                               reason_code=reason_code)

            elif pares_status == 'Y':
                bypass_consumer_auth_check = True
            elif pares_status == 'A':
                bypass_consumer_auth_check = True

            if content:
                return content

        elif enrolled == 'U':
            reason_code = 'TIMEOUT-ENROLL-U'
            content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                           reason_code=reason_code)
            return content
        elif enrolled == 'B':
            bypass_consumer_auth_check = True

    if status == 'AUTHENTICATION_FAILED' and enrolled == 'Y':
        content = None
        if pares_status == 'N':
            reason_code = 'AUTHENTICATION-FAILED-RES-N'
            content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                           reason_code=reason_code)
        elif pares_status == 'R':
            reason_code = 'AUTHENTICATION-REJECTED-RES-R'
            content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE,
                                           reason_code=reason_code)
        if content:
            return content

    transaction_id = enroll_response["consumerAuthenticationInformation"]["authenticationTransactionId"]
    db_payment_request = get_payment_request(dbsession, db_cybersource_form.payment_request_id)
    reference_id = str(db_payment_request.invoice_number)
    payment_response = json.loads(cyber_utils.create_payment_guest(
        guest_info=guest_info_json,
        transient_token_payload=db_cybersource_payment_token.transient_token,
        amount=db_cybersource_form.amount,
        currency=db_cybersource_form.currency,
        is_3ds=True,
        transaction_id=transaction_id,
        reference_id=str(reference_id),
        bypass_consumer_auth_check=bypass_consumer_auth_check,
    ))

    if payment_response['status'] == 'AUTHORIZED':
        content = get_cc_html_template(True, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        try:
            db_pre_auth = crud.get_pre_auth_payment_by_pr_id(dbsession,
                                                             db_cybersource_form.payment_request_id)
            pre_auth_payment_update = schema.PreAuthPaymentResponse(
                is_successful=True,
                response=payment_response,
                transaction_id=payment_response['id']
            )
            crud.update_pre_auth_payment(dbsession, db_pre_auth.id, pre_auth_payment_update)
            message = {
                'message_type': 'SUCCESS',
                'pre_auth_id': db_pre_auth.id
            }
            app.send_task(f'{settings.MAIN_SERVICE_TASKS_PREFIX}.notify_pre_auth',
                          kwargs={'message': message},
                          queue=settings.MAIN_SERVICE_WORKER_QUEUE,
                          routing_key=settings.MAIN_SERVICE_WORKER_ROUTE)
            return content
        except NoResultFound:
            content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
    else:
        content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
    return HTMLResponse(content=content, status_code=200)


@router.post("/guest/auth-check")
async def guest_auth_check_3ds_and_process_payment(request: Request,  # pylint: disable=too-many-locals  # noqa: MC0001
                                                   dbsession: SessionLocal = Depends(create_session)):
    # client_host = request.client.host

    deactivate_audit()
    form_data = await request.form()
    transaction_id = form_data.get("TransactionId")
    _ = form_data.get("Response")
    md = form_data.get("MD")

    parts = md.split('|')
    membership_id, session_token, guest_info = parts
    guest_info_json = json.loads(guest_info)

    set_member_as_context_user(dbsession, membership_id)
    try:
        db_member, session_data, db_cybersource_form = validate_session_against_member(dbsession, membership_id,
                                                                                       session_token)
    except HTTPException as exc:
        if exc.status_code == 3001:
            return HTMLResponse(content=exc.detail, status_code=200)
        content = get_cc_html_template(False, PaymentRequestReason.subscription,
                                       payment_gateway_type=PAYMENT_GATEWAY_TYPE)
        return content
    template_type = PaymentRequestReason.pre_auth
    db_cybersource_payment_token, token_type, transient_token_payload = get_token_info(dbsession, db_cybersource_form,
                                                                                       session_data, membership_id,
                                                                                       use_transient=True)

    result = enroll_validate_auth(dbsession, transaction_id, token_type, db_cybersource_form, transient_token_payload,
                                  template_type)

    db_payment_request = get_payment_request(dbsession, db_cybersource_form.payment_request_id)
    reference_id = str(db_payment_request.invoice_number)
    if not isinstance(result, bool):
        return HTMLResponse(content=result, status_code=200)

    # If not save-cc
    payment_response = json.loads(cyber_utils.create_payment_guest(
        guest_info=guest_info_json,
        transient_token_payload=db_cybersource_payment_token.transient_token,
        amount=db_cybersource_form.amount,
        currency=db_cybersource_form.currency,
        is_3ds=True,
        transaction_id=transaction_id,
        reference_id=str(reference_id),
    ))
    if payment_response["status"] == "AUTHORIZED":
        try:
            db_pre_auth = crud.get_pre_auth_payment_by_pr_id(dbsession,
                                                             db_cybersource_form.payment_request_id)
            pre_auth_payment_update = schema.PreAuthPaymentResponse(
                is_successful=True,
                response=payment_response,
                transaction_id=payment_response['id']
            )
            crud.update_pre_auth_payment(dbsession, db_pre_auth.id, pre_auth_payment_update)
            content = get_cc_html_template(True, template_type,
                                           payment_gateway_type=PAYMENT_GATEWAY_TYPE)
            message = {
                'message_type': 'SUCCESS',
                'pre_auth_id': db_pre_auth.id
            }
            app.send_task(f'{settings.MAIN_SERVICE_TASKS_PREFIX}.notify_pre_auth',
                          kwargs={'message': message},
                          queue=settings.MAIN_SERVICE_WORKER_QUEUE,
                          routing_key=settings.MAIN_SERVICE_WORKER_ROUTE)
        except NoResultFound:
            content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
    else:
        content = get_cc_html_template(False, template_type, payment_gateway_type=PAYMENT_GATEWAY_TYPE)
    return HTMLResponse(content=content, status_code=200)
