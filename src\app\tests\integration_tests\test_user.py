from contextlib import contextmanager
from datetime import datetime, timedelta, timezone
import jwt
import pytest
import uuid

from fastapi.testclient import TestClient
from unittest.mock import patch, Mock

from app import schema, settings
from app.main import app, ROOT_PATH
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory, CreditCardFactory, IDTagFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory, )
from app.database import SessionLocal, create_session, Base, engine


client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        IDTagFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session
USER_BASE_URL = f'{ROOT_PATH}/api/v1/csms/user'


def test_get_user(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{USER_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(user.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{USER_BASE_URL}/{user.id}'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 200
        assert response.json()['id'] == str(user.id)


def test_get_member(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(member.id)

        create_res_server_and_roles(db, member, str(organization.id), fr'{USER_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(user.id),
                "membership_id": f'{member.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{USER_BASE_URL}/member/{membership_id}'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 200
        assert response.json()['id'] == membership_id


def test_get_user_not_found(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{USER_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{USER_BASE_URL}/{uuid.uuid4()}'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 400
        assert response.json() == {'detail': 'User object does not exist.'}


def test_get_member_not_found(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, member, str(organization.id), fr'{USER_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': f'{member.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{USER_BASE_URL}/member/{uuid.uuid4()}'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 400
        assert response.json() == {'detail': 'Membership object does not exist.'}


# error from code when get membership for targer_member
# because cls_membership in MembershipCRUD was member1 not member2
def test_send_manual_link_verification_email_error(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user1 = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id,
            is_migrated=True,
            migration_status=False
        )
        db.commit()

        member1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user1.id}'
        )
        db.commit()

        user2 = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        member2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user2.id}'
        )
        db.commit()

        create_res_server_and_roles(db, member1, str(organization.id), fr'{USER_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user1.id),
                'membership_id': str(member1.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        data = {
            'target_member_id': str(member2.id)
        }
        url = f'{USER_BASE_URL}/member/{str(member1.id)}/send-manual-link-email'
        response = client.post(url, headers={'authorization': token}, json=data)
        print('<<< response', response.json())
        assert response.status_code == 400
        assert response.json()['detail'] == 'Membership object does not exist.'


def test_send_manual_link_verification_email_staff(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id,
        )
        db.commit()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff
        )
        db.commit()

        create_res_server_and_roles(db, member, str(organization.id), fr'{USER_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(member.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        data = {
            'target_member_id': str(uuid.uuid4())
        }
        url = f'{USER_BASE_URL}/member/{str(member.id)}/send-manual-link-email'
        response = client.post(url, headers={'authorization': token}, json=data)
        print('<<< response', response.json())
        assert response.status_code == 404
        assert response.json()['detail'] == 'Manual Linking only available for regular user.'


def test_send_manual_link_verification_email_not_migrated(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id,
        )
        db.commit()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}'
        )
        db.commit()

        create_res_server_and_roles(db, member, str(organization.id), fr'{USER_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(member.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        data = {
            'target_member_id': str(uuid.uuid4())
        }
        url = f'{USER_BASE_URL}/member/{str(member.id)}/send-manual-link-email'
        response = client.post(url, headers={'authorization': token}, json=data)
        print('<<< response', response.json())
        assert response.status_code == 404
        assert response.json()['detail'] == 'Selected Migrated User is not available for manual linking.'


def test_send_manual_link_verification_email_same_user(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, member, str(organization.id), fr'{USER_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': str(member.id)
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        data = {
            'target_member_id': str(member.id)
        }
        url = f'{USER_BASE_URL}/member/{str(member.id)}/send-manual-link-email'
        response = client.post(url, headers={'authorization': token}, json=data)
        assert response.status_code == 404
        assert response.json()['detail'] == 'Cannot manual link to the same user.'
