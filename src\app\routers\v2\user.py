import logging

from fastapi import APIRouter, HTTPException, Depends, Request
# import httpx

from app import settings, schema, crud, exceptions
# from app.constants import TOKEN_API_URL
from app.crud.auth import MembershipCRUD
from app.database import create_session, SessionLocal
from app.permissions import permission, x_api_key
from app.utils import (
    # calculate_sha256_hash,
    decode_auth_token_from_headers,
    RouteErrorHandler,
    calculate_and_update_member_dunning,
    # get_dunning_bills
)

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/user",
    tags=['v2 user', ],
    dependencies=[Depends(permission), Depends(x_api_key)],
    route_class=RouteErrorHandler,
)


@router.get('/get-dunning-status', response_model=schema.MembershipDunningResponse)
async def get_user_dunning_status(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Get and calculate dunning for user.
    """

    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        member_id = auth_token_data.get('membership_id')
        db_dunning, dunning_bills = calculate_and_update_member_dunning(dbsession, member_id)
        if not isinstance(db_dunning, dict):
            db_dunning = db_dunning.__dict__
        combined_data = {**db_dunning, **dunning_bills}
        res = schema.MembershipDunningResponse(**combined_data)
        return res
    except (exceptions.ApolloObjectDoesNotExist, exceptions.UpdateDunningError) as e:
        raise HTTPException(400, e.__str__())


@router.get('/get-privacy-acceptance', response_model=schema.MembershipPrivacyAcceptanceResponse)
async def get_user_privacy_acceptance(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Get the PDPA and Marketing acceptance status and date of user
    """

    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        member_id = auth_token_data.get('membership_id')
        db_member = crud.get_membership_by_id(dbsession, member_id)
        return db_member
    except (exceptions.ApolloObjectDoesNotExist) as e:
        raise HTTPException(400, e.__str__())


@router.patch("/update-privacy-acceptance", response_model=schema.MembershipPrivacyAcceptanceResponse)
async def update_privacy_acceptance(request: Request, data: schema.MembershipPrivacyUpdate,
                                    dbsession: SessionLocal = Depends(create_session)):
    """
    Update membership privacy acceptance
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    member_id = auth_token_data.get('membership_id')

    try:
        privacy_update_dict = data.dict()
        db_membership = MembershipCRUD.update(dbsession, member_id, privacy_update_dict, check_permission=False)
        return db_membership
    except (exceptions.ApolloObjectDoesNotExist) as e:
        raise HTTPException(400, e.__str__())


@router.delete('/terminate_account', response_model=schema.BasicMessage)
async def terminate_user_account(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Terminate User Account
    """
    success_msg = 'Sad to see you go.'

    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        member_id = auth_token_data.get('membership_id')
        db_cc = crud.get_member_cc_list(dbsession, member_id)
        if db_cc:
            for result in db_cc:
                try:
                    # db_mem = result.member
                    # billing_name = f'{db_mem.first_name} {db_mem.last_name}'
                    # signature = calculate_sha256_hash(
                    #     f'DELETE_TOKEN{db_mem.user.email}{db_mem.user.phone_number}{billing_name}'
                    #     f'{settings.MERCHANT_ID}{result.token}',
                    #     settings.SECRET_KEY
                    # )
                    # params = {
                    #     'action': 'DELETE_TOKEN',
                    #     'token': f'{result.token}',
                    #     'billing_name': billing_name,
                    #     'billing_mobile': f'{db_mem.user.phone_number}',
                    #     'billing_email': f'{db_mem.user.email}',
                    #     'merchantID': settings.MERCHANT_ID,
                    #     'signature': signature
                    # }
                    # async with httpx.AsyncClient() as client:
                    #     response = await client.post(TOKEN_API_URL, data=params)
                    # if response.status_code >= 400:
                    #     logger.info('Credit card delete error description: %s', response.json()["error_desc"])
                    # else:

                    crud.delete_cc(dbsession, result.id)
                except exceptions.ApolloObjectDoesNotExist:
                    continue
        crud.delete_organization_membership(dbsession, member_id)
        return schema.BasicMessage(detail=success_msg)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
