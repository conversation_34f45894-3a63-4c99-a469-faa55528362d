import sqlalchemy as db
from sqlalchemy.dialects.postgresql import UUID, JSONB

from app.models.base import BaseModel


class CreditCard(BaseModel):
    __tablename__ = 'main_credit_card'

    payment_gateway = db.Column(db.String)

    brand = db.Column(db.String)
    currency = db.Column(db.String, default='MYR')
    token = db.Column(db.String)
    last_four_digit = db.Column(db.String)
    type = db.Column(db.String)
    primary = db.Column(db.Boolean)
    status = db.Column(db.String, default='Active')
    member = db.orm.relationship('Membership', backref='credit_cards')

    member_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_membership.id', ondelete='CASCADE'))

    bill_name = db.Column(db.String)
    bill_first_name = db.Column(db.String)
    bill_last_name = db.Column(db.String)
    bill_email = db.Column(db.String)
    bill_mobile = db.Column(db.String)
    user_token = db.Column(db.String)
    expiry_month = db.Column(db.String)
    expiry_year = db.Column(db.String)
    card_type = db.Column(db.String)
    network_transaction_id = db.Column(db.String)
    response = db.Column(JSONB)


db.Index('unique_credit_card', CreditCard.member_id, CreditCard.last_four_digit, CreditCard.type, CreditCard.currency,
         CreditCard.brand, CreditCard.is_deleted,
         unique=True, postgresql_where=~CreditCard.is_deleted)

db.Index('only_one_primary_credit_card', CreditCard.primary, CreditCard.member_id, CreditCard.currency,
         CreditCard.brand, unique=True, postgresql_where=db.and_(~CreditCard.is_deleted, CreditCard.primary))


class BlacklistCreditCard(BaseModel):
    __tablename__ = 'main_blacklist_credit_card'

    currency = db.Column(db.String, default='MYR')
    last_four_digit = db.Column(db.String)
    status = db.Column(db.String, default='Blacklist')

    phone_number = db.Column(db.String)
    email = db.Column(db.String)

    member = db.orm.relationship('Membership', backref='blacklisted_credit_card')
    member_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_membership.id', ondelete='SET NULL'))
