# pylint:disable=too-many-lines
import asyncio
import json
import logging
import os
from contextlib import contextmanager
from datetime import datetime, timedelta
from functools import partial
from urllib.parse import urlencode

import httpx
import pydantic
from jose import jwt
from kombu.exceptions import KombuError
from sqlalchemy.orm import joinedload

from app import settings, crud, models, schema, lta_adaptor
from app.lta_celery import app
from app.database import create_session
from app.s3_utils import upload_csv_with_presigned_url
from app.settings import APOLLO_MAIN_DOMAIN, MAIN_ROOT_PATH
from app.utils import generate_upload_lta_static_excel, GenerateReport, get_charging_history_data, \
    decode_auth_token_from_headers, get_invoice_list, update_task_retry_count, get_all_emsp_cdr, \
    get_cpo_cdr, convert_meta_to_string, update_task_successfully, decode_base64_to_token, lta_send_request
from app.middlewares import set_admin_as_context_user, _request_user_ctx_var, set_member_as_context_user

main_path = f"{APOLLO_MAIN_DOMAIN}/{MAIN_ROOT_PATH}"
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter(settings.LOG_FORMAT))
logger.addHandler(handler)

# publisher = ChargerServicePublisher()
CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'
S3_BUCKET_REPORT = f'{settings.S3_BUCKET_REPORT}'
EXPIRY_DAYS = int(f'{settings.REPORT_DOWNLOAD_EXPIRY_DAYS}')


@app.task(name='apollo.main.main_reporting_task.generate_lta_static_excel',
          autoretry_for=(KombuError,), retry_backoff=3,
          routing_key='main_reporting_worker', queue='main_reporting_worker')
def generate_lta_static_monthly_task(set_date: datetime = None):
    if set_date is None:
        date = datetime.now() - timedelta(days=1)
        date = date.strftime('%Y-%m')
    else:
        date = set_date
    with contextmanager(create_session)() as dbsession:
        set_admin_as_context_user(dbsession)
        generate_upload_lta_static_excel(date)


@app.task(name='apollo.main.lta_main_tasks.sending_lta_data', bind=True, retry_backoff=3,
          retry_kwargs={'max_retries': settings.LTA_DYNAMIC_RETRY},
          routing_key='main_lta_worker', queue='main_lta_worker')
def send_lta_dynamic_data(self, message: dict):
    lta_data = lta_adaptor.map_lta_data(
        message['body']['parameters']['lta_data'])
    with contextmanager(create_session)() as dbsession:
        external_service_token = None
        url_paths = []
        if pydantic.parse_obj_as(bool, settings.GET_LTA_URL_FROM_ENV):
            url_paths.append(settings.DYNAMIC_LTA_URL)
        else:
            for lta_token in dbsession.query(models.ExternalToken).filter(  # pylint: disable=unreachable
                    models.ExternalToken.is_lta.is_(True),
                    models.ExternalToken.is_deleted.is_(False),
                    models.ExternalToken.external_organization_id is not None).all():

                if lta_token is None:
                    raise ValueError("No valid LTA external token found.")

                external_auths = schema.ExternalToken.from_orm(lta_token).dict()
                end_points = json.loads(external_auths['endpoints'])['endpoints']
                # if endpoint found, url change according to external auth
                for endpoint in end_points:
                    if endpoint['identifier'] == 'locations' and endpoint['role'] == 'RECEIVER':
                        url_paths.append(endpoint['url'])
                external_service_token = decode_base64_to_token(external_auths['external_service_token'])
                external_service_token = f'Token {external_service_token}'
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))
        if len(lta_data) >= 1:
            try:
                for path in url_paths:
                    for lta_object in lta_data:
                        loop.run_until_complete(lta_send_request(path, lta_object['id'], lta_object,
                                                                 self.request.retries + 1,
                                                                 self.retry_kwargs['max_retries'],
                                                                 external_service_token))
            except httpx.HTTPStatusError:
                raise self.retry(countdown=1.5 * (self.request.retries + 1), max_retries=self.max_retries)


@app.task(name='apollo.main.main_reporting_task.send_charging_history_via_email',
          autoretry_for=(KombuError,), retry_backoff=3,
          routing_key='main_reporting_worker', queue='main_reporting_worker')
def send_charging_history_via_email_sync(
        message: dict):  # noqa: MC0001, # pylint:disable=too-many-statements, # pylint: disable=inconsistent-return-statements
    task_uid = message['task_uid']
    if task_uid:
        with contextmanager(create_session)() as dbsession:
            should_stop_retry = update_task_retry_count(task_uid, dbsession)
            if should_stop_retry:
                return True

    def send_charging_history():
        async def send_charging_history_coroutine():
            filters = message['filters']
            query_params = message['query_params']
            headers = message['headers']
            filename = message['filename']
            columns = message['columns']
            email = message['email']
            req_headers = message['req_headers']
            membership_id = message['membership_id']
            ext_org = message['ext_org']
            ext_org_party = message['ext_org_party']
            url = f'{CHARGER_URL_PREFIX}/charging/'
            bucket_name = S3_BUCKET_REPORT
            expiry_day = EXPIRY_DAYS
            #
            # # Throw exception to test retry
            # raise Exception("Test exception to trigger retry")

            if filters['operator']:
                operator_id_list = [id.strip() for id in filters['operator'].split(',')]
                query_params['operator'] = operator_id_list
            else:
                query_params['operator'] = []
            with contextmanager(create_session)() as dbsession:
                membership = crud.MembershipCRUD.query(dbsession, check_permission=False) \
                    .filter(models.Membership.id == membership_id).options(joinedload(models.Membership.user),
                                                                           joinedload(models.Membership.operators),
                                                                           joinedload(models.Membership.roles)).first()
                membership = schema.MembershipResponse(**membership.__dict__)
                _request_user_ctx_var.set(membership)
                use_task = True
                get_data = partial(get_charging_history_data, dbsession=dbsession, url=url, ext_org=ext_org,
                                   ext_org_party=ext_org_party, headers=req_headers,
                                   query_params=query_params, filters=filters, operator_filter=True, use_task=use_task)

                # This is to make it write to file concurrently while getting report to reduce OOM issue.
                report = GenerateReport('charging_history', headers, columns, function=get_data)
                # await report.datetime_reformat('session_start')
                # await report.datetime_reformat('session_end')
                await report.set_send_via_email(filename, True, ['session_start', 'session_end'])
                await report.generate_dataframe(send_via_email=True)
                # file_path = os.path.abspath(report.file_name)
                # await upload_csv_with_presigned_url(file_path, report.file_name)
                await upload_csv_with_presigned_url(report.temp_file_path, report.file_name)
                token = jwt.encode(
                    {
                        "exp": datetime.utcnow() + timedelta(days=expiry_day),
                        "bucket_name": f'{bucket_name}',
                        "file_name": f'{report.file_name}'
                    },
                    settings.JWT_EMAIL_REPORT_SECRET,
                    algorithm=schema.JWT_ALGORITHM,
                )

                # Encode the token as a query parameter
                query_string = urlencode({'key': token})
                url = (f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/charging-history/download/?"
                       f"{query_string}")
                await report.send_report_url_via_email('Your Charging History Report', email, url, expiry_day)
                update_task_successfully(task_uid, dbsession)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        # loop.run_until_complete(send_charging_history_coroutine())
        try:
            loop.run_until_complete(send_charging_history_coroutine())
        except Exception as e:
            # Trigger Celery retry on exception
            raise send_charging_history_via_email_sync.retry(exc=e)
        # asyncio.run(send_charging_history_coroutine())

    send_charging_history()


@app.task(name='apollo.main.main_reporting_task.send_invoice_list_via_email',
          autoretry_for=(KombuError,), retry_backoff=3,
          routing_key='main_reporting_worker', queue='main_reporting_worker')
def send_invoice_list_via_email_sync(message: dict):  # pylint: disable=inconsistent-return-statements
    task_uid = message['task_uid']
    if task_uid:
        with contextmanager(create_session)() as dbsession:
            should_stop_retry = update_task_retry_count(task_uid, dbsession)
            if should_stop_retry:
                return True

    def send_invoice_list():
        async def send_invoice_list_coroutine():
            request_data = message['request_data']
            filter_params = message['filter_params']
            user_filters = message['user_filters']
            headers = message['headers']
            filename = message['filename']
            columns = message['columns']
            email = message['email']
            bucket_name = S3_BUCKET_REPORT
            expiry_day = EXPIRY_DAYS
            with contextmanager(create_session)() as dbsession:
                auth_token_data = decode_auth_token_from_headers(request_data['headers'])
                membership_id = auth_token_data.get('membership_id')
                set_member_as_context_user(dbsession, membership_id)
                use_task = True
                get_data = partial(get_invoice_list, request=request_data,
                                   dbsession=dbsession, filter_params=filter_params, user_filters=user_filters,
                                   use_task=use_task)

                # This is to make it write to file concurrently while getting report to reduce OOM issue.
                report = GenerateReport('invoice_list', headers, columns, function=get_data)
                await report.set_send_via_email(filename, True, ['start_time', 'end_time'])
                await report.generate_dataframe(send_via_email=True)
                # file_path = os.path.abspath(report.file_name)
                # await upload_csv_with_presigned_url(file_path, report.file_name)
                await upload_csv_with_presigned_url(report.temp_file_path, report.file_name)

                token = jwt.encode(
                    {
                        "exp": datetime.utcnow() + timedelta(days=expiry_day),
                        "bucket_name": f'{bucket_name}',
                        "file_name": f'{report.file_name}'
                    },
                    settings.JWT_EMAIL_REPORT_SECRET,
                    algorithm=schema.JWT_ALGORITHM,
                )

                # Encode the token as a query parameter
                query_string = urlencode({'key': token})
                url = (f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/report/download/?"
                       f"{query_string}")

                await report.send_report_url_via_email('Invoice List', email, url, expiry_day)
                update_task_successfully(task_uid, dbsession)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        try:
            loop.run_until_complete(send_invoice_list_coroutine())
        except Exception as e:
            # Trigger Celery retry on exception
            raise send_invoice_list_via_email_sync.retry(exc=e)
        # asyncio.run(send_invoice_list_coroutine())

    send_invoice_list()


@app.task(name='apollo.main.main_reporting_task.send_subscriber_list_via_email_sync',
          autoretry_for=(KombuError,), retry_backoff=3,
          routing_key='main_reporting_worker', queue='main_reporting_worker')
def send_subscriber_list_via_email_sync(message: dict):  # pylint: disable-all
    task_uid = message['task_uid']
    if task_uid:
        with contextmanager(create_session)() as dbsession:
            should_stop_retry = update_task_retry_count(task_uid, dbsession)
            if should_stop_retry:
                return True

    def send_subscriber_list():
        async def send_subscriber_list_coroutine():
            subscription_plan_id = message['subscription_plan_id']
            date_filters = message['date_filters']
            subscription_filters = message['subscription_filters']
            user_filters = message['user_filters']
            member_id = message['member_id']
            request_data = message['request_data']
            headers = message['headers']
            subscriber_id = message['subscriber_id']
            reorder_columns = message['reorder_columns']
            columns = message['columns']
            filename = message['filename']
            email = message['email']
            bucket_name = S3_BUCKET_REPORT
            expiry_day = EXPIRY_DAYS
            with contextmanager(create_session)() as dbsession:
                auth_token_data = decode_auth_token_from_headers(request_data['headers'])
                membership_id = auth_token_data.get('membership_id')
                set_member_as_context_user(dbsession, membership_id)
                query = crud.get_subscription_list(dbsession, subscription_plan_id, date_filters,
                                                   subscriber_id, member_id, user_filters, subscription_filters,
                                                   membership_id)
                extra_query = crud.get_subscription_order_list(dbsession)

                report = GenerateReport("subscribers", header=headers, columns=columns,
                                        reorder_columns=reorder_columns, query=query)
                await report.set_send_via_email(filename, True)

                await report.generate_dataframe_with_query(schema=schema.SubscriptionResponse,
                                                           multiple_join=['member', 'subscription_plan'])

                await report.extra_data_with_query(query=extra_query, schema=schema.SubscriptionOrderDownload,
                                                   left_on='id', right_on='subscription_id', ignore_key_error=True)
                await report.drop_duplicate('id')
                await report.nan_handling('is_default', 'Active', 'True')
                await report.nan_handling('is_default', 'In-active', 'False')
                await report.operation('full_name', lambda x, y: f'{x} {y}', 'member.first_name', 'member.last_name')
                await report.operation('payable_fees', convert_meta_to_string, 'payable_fees', 'payable_fees',
                                       ignore_key_error=True)
                await report.datetime_reformat('created_at')
                file_path = await report.handle_send_via_email()
                # file_path = os.path.abspath(report.file_name)
                await upload_csv_with_presigned_url(file_path, report.file_name)

                token = jwt.encode(
                    {
                        "exp": datetime.utcnow() + timedelta(days=expiry_day),
                        "bucket_name": f'{bucket_name}',
                        "file_name": f'{report.file_name}'
                    },
                    settings.JWT_EMAIL_REPORT_SECRET,
                    algorithm=schema.JWT_ALGORITHM,
                )

                # Encode the token as a query parameter
                query_string = urlencode({'key': token})
                url = (f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/report/download/?"
                       f"{query_string}")

                await report.send_report_url_via_email('Subscriber List', email, url, expiry_day)
                update_task_successfully(task_uid, dbsession)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        try:
            loop.run_until_complete(send_subscriber_list_coroutine())
        except Exception as e:
            # Trigger Celery retry on exception
            raise send_subscriber_list_via_email_sync.retry(exc=e)
        # asyncio.run(send_invoice_list_coroutine())

    send_subscriber_list()


@app.task(name='apollo.main.main_reporting_task.send_customer_list_via_email_sync',
          autoretry_for=(KombuError,), retry_backoff=3,
          routing_key='main_reporting_worker', queue='main_reporting_worker')
def send_customer_list_via_email_sync(message: dict):  # pylint: disable=inconsistent-return-statements
    task_uid = message['task_uid']
    if task_uid:
        with contextmanager(create_session)() as dbsession:
            should_stop_retry = update_task_retry_count(task_uid, dbsession)
            if should_stop_retry:
                return True

    def send_customer_list():
        async def send_customer_list_coroutine():
            user_filter = message['user_filter']
            mem_filter = message['mem_filter']
            include_child_orgs = message['include_child_orgs']
            organization_id = message['organization_id']
            membership_type = message['membership_type']
            request_data = message['request_data']
            headers = message['headers']
            columns = message['columns']
            reorder_columns = message['reorder_columns']
            filename = message['filename']
            email = message['email']
            bucket_name = S3_BUCKET_REPORT
            expiry_day = EXPIRY_DAYS
            with contextmanager(create_session)() as dbsession:
                auth_token_data = decode_auth_token_from_headers(request_data['headers'])
                membership_id = auth_token_data.get('membership_id')
                set_member_as_context_user(dbsession, membership_id)
                query = crud.get_organization_membership_list(dbsession, organization_id, membership_type,
                                                              include_child_orgs, user_filter, mem_filter)

                report = GenerateReport('customer_list', headers, columns, reorder_columns, query=query)
                await report.set_send_via_email(filename, True, ['created_at'])
                await report.generate_dataframe_with_query(schema.MembershipDownloadResponse,
                                                           multiple_join=['organization', 'user'])
                await report.operation('full_name', lambda x, y: f'{x} {y}', 'first_name', 'last_name')
                file_path = await report.handle_send_via_email()
                # file_path = os.path.abspath(report.file_name)
                await upload_csv_with_presigned_url(file_path, report.file_name)

                token = jwt.encode(
                    {
                        "exp": datetime.utcnow() + timedelta(days=expiry_day),
                        "bucket_name": f'{bucket_name}',
                        "file_name": f'{report.file_name}'
                    },
                    settings.JWT_EMAIL_REPORT_SECRET,
                    algorithm=schema.JWT_ALGORITHM,
                )

                # Encode the token as a query parameter
                query_string = urlencode({'key': token})
                url = (f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/report/download/?"
                       f"{query_string}")

                await report.send_report_url_via_email('Customer List', email, url, expiry_day)
                update_task_successfully(task_uid, dbsession)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        try:
            loop.run_until_complete(send_customer_list_coroutine())
        except Exception as e:
            # Trigger Celery retry on exception
            raise send_customer_list_via_email_sync.retry(exc=e)
        # asyncio.run(send_invoice_list_coroutine())

    send_customer_list()


@app.task(name='apollo.main.main_reporting_task.send_wallet_list_via_email_sync',
          autoretry_for=(KombuError,), retry_backoff=3,
          routing_key='main_reporting_worker', queue='main_reporting_worker')
def send_wallet_list_via_email_sync(message: dict):  # pylint: disable=inconsistent-return-statements
    task_uid = message['task_uid']
    if task_uid:
        with contextmanager(create_session)() as dbsession:
            should_stop_retry = update_task_retry_count(task_uid, dbsession)
            if should_stop_retry:
                return True

    def send_wallet_list():
        async def send_wallet_list_coroutine():
            filter_wallets = message['filter_wallets']
            request_data = message['request_data']
            headers = message['headers']
            columns = message['columns']
            reorder_columns = message['reorder_columns']
            filename = message['filename']
            email = message['email']
            bucket_name = S3_BUCKET_REPORT
            expiry_day = EXPIRY_DAYS
            with contextmanager(create_session)() as dbsession:
                auth_token_data = decode_auth_token_from_headers(request_data['headers'])
                membership_id = auth_token_data.get('membership_id')
                set_member_as_context_user(dbsession, membership_id)

                query = crud.get_wallet_list_with_filter(dbsession, filter_wallets, query_only=True)
                report = GenerateReport("Wallets", header=headers, columns=columns, reorder_columns=reorder_columns,
                                        query=query)
                await report.set_send_via_email(filename, True)
                await report.generate_dataframe_with_query(schema=schema.WalletResponse, join='member')
                await report.operation('full_name', lambda x, y: f'{x} {y}', 'member.first_name', 'member.last_name')
                file_path = await report.handle_send_via_email()
                # file_path = os.path.abspath(report.file_name)
                await upload_csv_with_presigned_url(file_path, report.file_name)

                token = jwt.encode(
                    {
                        "exp": datetime.utcnow() + timedelta(days=expiry_day),
                        "bucket_name": f'{bucket_name}',
                        "file_name": f'{report.file_name}'
                    },
                    settings.JWT_EMAIL_REPORT_SECRET,
                    algorithm=schema.JWT_ALGORITHM,
                )

                # Encode the token as a query parameter
                query_string = urlencode({'key': token})
                url = (f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/report/download/?"
                       f"{query_string}")

                await report.send_report_url_via_email('Wallet List', email, url, expiry_day)
                update_task_successfully(task_uid, dbsession)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        try:
            loop.run_until_complete(send_wallet_list_coroutine())
        except Exception as e:
            # Trigger Celery retry on exception
            raise send_wallet_list_via_email_sync.retry(exc=e)
        # asyncio.run(send_invoice_list_coroutine())

    send_wallet_list()


@app.task(name='apollo.main.main_reporting_task.send_wallet_transaction_list_via_email_sync',
          autoretry_for=(KombuError,), retry_backoff=3,
          routing_key='main_reporting_worker', queue='main_reporting_worker')
def send_wallet_transaction_list_via_email_sync(message: dict):  # pylint: disable=inconsistent-return-statements
    task_uid = message['task_uid']
    if task_uid:
        with contextmanager(create_session)() as dbsession:
            should_stop_retry = update_task_retry_count(task_uid, dbsession)
            if should_stop_retry:
                return True

    def send_wallet_transaction_list():
        async def send_wallet_transaction_list_coroutine():
            wallet_transactions_filter_parameters = message['wallet_transactions_filter_parameters']
            request_data = message['request_data']
            headers = message['headers']
            columns = message['columns']
            reorder_columns = message['reorder_columns']
            filename = message['filename']
            email = message['email']
            bucket_name = S3_BUCKET_REPORT
            expiry_day = EXPIRY_DAYS
            with contextmanager(create_session)() as dbsession:
                auth_token_data = decode_auth_token_from_headers(request_data['headers'])
                membership_id = auth_token_data.get('membership_id')
                set_member_as_context_user(dbsession, membership_id)

                query = crud.get_wallet_transaction_with_filter(dbsession,
                                                                wallet_transactions_filter_parameters, query_only=True)
                report = GenerateReport("Wallet Transactions", header=headers, columns=columns,
                                        reorder_columns=reorder_columns, query=query)
                await report.set_send_via_email(filename, True)
                await report.generate_dataframe_with_query(schema=schema.WalletTransactionWithMemberResponse,
                                                           join='wallet')
                await report.operation('full_name', lambda x, y: f'{x} {y}', 'wallet.member.first_name',
                                       'wallet.member.last_name')
                file_path = await report.handle_send_via_email()
                # file_path = os.path.abspath(report.file_name)
                await upload_csv_with_presigned_url(file_path, report.file_name)

                token = jwt.encode(
                    {
                        "exp": datetime.utcnow() + timedelta(days=expiry_day),
                        "bucket_name": f'{bucket_name}',
                        "file_name": f'{report.file_name}'
                    },
                    settings.JWT_EMAIL_REPORT_SECRET,
                    algorithm=schema.JWT_ALGORITHM,
                )

                # Encode the token as a query parameter
                query_string = urlencode({'key': token})
                url = (f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/report/download/?"
                       f"{query_string}")

                await report.send_report_url_via_email('Wallet Transaction List', email, url, expiry_day)
                update_task_successfully(task_uid, dbsession)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        try:
            loop.run_until_complete(send_wallet_transaction_list_coroutine())
        except Exception as e:
            # Trigger Celery retry on exception
            raise send_wallet_transaction_list_via_email_sync.retry(exc=e)
        # asyncio.run(send_invoice_list_coroutine())

    send_wallet_transaction_list()


@app.task(name='apollo.main.main_reporting_task.send_credit_card_list_via_email_sync',
          autoretry_for=(KombuError,), retry_backoff=3,
          routing_key='main_reporting_worker', queue='main_reporting_worker')
def send_credit_card_list_via_email_sync(message: dict):  # pylint: disable=inconsistent-return-statements
    task_uid = message['task_uid']
    if task_uid:
        with contextmanager(create_session)() as dbsession:
            should_stop_retry = update_task_retry_count(task_uid, dbsession)
            if should_stop_retry:
                return True

    def send_credit_card_list():
        async def send_credit_card_list_coroutine():
            filters = message['filters']
            request_data = message['request_data']
            headers = message['headers']
            columns = message['columns']
            reorder_columns = message['reorder_columns']
            filename = message['filename']
            email = message['email']
            bucket_name = S3_BUCKET_REPORT
            expiry_day = EXPIRY_DAYS
            with contextmanager(create_session)() as dbsession:
                auth_token_data = decode_auth_token_from_headers(request_data['headers'])
                membership_id = auth_token_data.get('membership_id')
                set_member_as_context_user(dbsession, membership_id)

                query = crud.get_cc_list(dbsession, filters)
                report = GenerateReport("credit_card", header=headers, columns=columns,
                                        reorder_columns=reorder_columns, query=query)
                await report.set_send_via_email(filename, True)
                await report.generate_dataframe_with_query(schema=schema.CreditCardResponse, join='member')
                report.dataframe['created_at'] = report.dataframe['created_at'].dt.tz_convert('Asia/Kuala_Lumpur')
                await report.operation('cc_number', lambda x, _: '**** **** **** ' + x, 'last_four_digit',
                                       'last_four_digit')
                file_path = await report.handle_send_via_email()
                # file_path = os.path.abspath(report.file_name)
                await upload_csv_with_presigned_url(file_path, report.file_name)

                token = jwt.encode(
                    {
                        "exp": datetime.utcnow() + timedelta(days=expiry_day),
                        "bucket_name": f'{bucket_name}',
                        "file_name": f'{report.file_name}'
                    },
                    settings.JWT_EMAIL_REPORT_SECRET,
                    algorithm=schema.JWT_ALGORITHM,
                )

                # Encode the token as a query parameter
                query_string = urlencode({'key': token})
                url = (f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/report/download/?"
                       f"{query_string}")

                await report.send_report_url_via_email('Credit Card', email, url, expiry_day)
                update_task_successfully(task_uid, dbsession)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        try:
            loop.run_until_complete(send_credit_card_list_coroutine())
        except Exception as e:
            # Trigger Celery retry on exception
            raise send_credit_card_list_via_email_sync.retry(exc=e)
        # asyncio.run(send_credit_card_list_coroutine())

    send_credit_card_list()


@app.task(name='apollo.main.main_reporting_task.send_emsp_cdr_via_email_sync',
          autoretry_for=(KombuError,), retry_backoff=3,
          routing_key='main_reporting_worker', queue='main_reporting_worker')
def send_emsp_cdr_via_email_sync(message: dict):  # pylint: disable=inconsistent-return-statements
    task_uid = message['task_uid']
    if task_uid:
        with contextmanager(create_session)() as dbsession:
            should_stop_retry = update_task_retry_count(task_uid, dbsession)
            if should_stop_retry:
                return True

    def send_emsp_cdr():
        async def send_emsp_cdr_coroutine():
            headers = message['headers']
            filename = message['filename']
            columns = message['columns']
            email = message['email']
            filters = message['filters']
            request_data = message['request_data']
            bucket_name = S3_BUCKET_REPORT
            expiry_day = EXPIRY_DAYS
            with contextmanager(create_session)() as dbsession:
                auth_token_data = decode_auth_token_from_headers(request_data['headers'])
                membership_id = auth_token_data.get('membership_id')
                set_member_as_context_user(dbsession, membership_id)

                get_data = partial(get_all_emsp_cdr, request=request_data, dbsession=dbsession,
                                   filters=filters,
                                   report=True)
                # This is to make it write to file concurrently while getting report to reduce OOM issue.
                report = GenerateReport('emsp_cdr', headers, columns, function=get_data)
                await report.generate_dataframe()
                await report.datetime_reformat('start_date_time')
                await report.datetime_reformat('end_date_time')
                await report.set_send_via_email(file_name=filename, send_via_email=True)
                file_path = await report.handle_send_via_email()
                # file_path = os.path.abspath(report.file_name)
                await upload_csv_with_presigned_url(file_path, report.file_name)

                token = jwt.encode(
                    {
                        "exp": datetime.utcnow() + timedelta(days=expiry_day),
                        "bucket_name": f'{bucket_name}',
                        "file_name": f'{report.file_name}'
                    },
                    settings.JWT_EMAIL_REPORT_SECRET,
                    algorithm=schema.JWT_ALGORITHM,
                )

                # Encode the token as a query parameter
                query_string = urlencode({'key': token})
                url = (f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/report/download/?"
                       f"{query_string}")

                await report.send_report_url_via_email('eMSP CDR', email, url, expiry_day)
                update_task_successfully(task_uid, dbsession)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        try:
            loop.run_until_complete(send_emsp_cdr_coroutine())
        except Exception as e:
            # Trigger Celery retry on exception
            raise send_emsp_cdr_via_email_sync.retry(exc=e)
        # asyncio.run(send_invoice_list_coroutine())

    send_emsp_cdr()


@app.task(name='apollo.main.main_reporting_task.send_cpo_cdr_via_email_sync',
          autoretry_for=(KombuError,), retry_backoff=3,
          routing_key='main_reporting_worker', queue='main_reporting_worker')
def send_cpo_cdr_via_email_sync(message: dict):  # pylint: disable=inconsistent-return-statements
    task_uid = message['task_uid']
    if task_uid:
        with contextmanager(create_session)() as dbsession:
            should_stop_retry = update_task_retry_count(task_uid, dbsession)
            if should_stop_retry:
                return True

    def send_cpo_cdr():
        async def send_cpo_cdr_coroutine():
            filters = message['filters']
            request_data = message['request_data']
            headers = message['headers']
            columns = message['columns']
            filename = message['filename']
            email = message['email']
            bucket_name = S3_BUCKET_REPORT
            expiry_day = EXPIRY_DAYS
            with contextmanager(create_session)() as dbsession:
                auth_token_data = decode_auth_token_from_headers(request_data['headers'])
                membership_id = auth_token_data.get('membership_id')
                set_member_as_context_user(dbsession, membership_id)

                get_data = partial(get_cpo_cdr, request=request_data, dbsession=dbsession, filters=filters, report=True)
                report = GenerateReport('cpo_cdr', headers, columns, function=get_data)
                await report.set_send_via_email(filename, True, ['start_date_time', 'end_date_time'])
                await report.generate_dataframe(send_via_email=True)
                # file_path = os.path.abspath(report.file_name)
                await upload_csv_with_presigned_url(report.temp_file_path, report.file_name)

                token = jwt.encode(
                    {
                        "exp": datetime.utcnow() + timedelta(days=expiry_day),
                        "bucket_name": f'{bucket_name}',
                        "file_name": f'{report.file_name}'
                    },
                    settings.JWT_EMAIL_REPORT_SECRET,
                    algorithm=schema.JWT_ALGORITHM,
                )

                # Encode the token as a query parameter
                query_string = urlencode({'key': token})
                url = (f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/report/download/?"
                       f"{query_string}")

                await report.send_report_url_via_email('CPO CDR', email, url, expiry_day)
                update_task_successfully(task_uid, dbsession)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        try:
            loop.run_until_complete(send_cpo_cdr_coroutine())
        except Exception as e:
            # Trigger Celery retry on exception
            raise send_cpo_cdr_via_email_sync.retry(exc=e)
        # asyncio.run(send_invoice_list_coroutine())

    send_cpo_cdr()


@app.task(name='apollo.main.main_reporting_task.send_ocpp_log_via_email', autoretry_for=(KombuError,),
          retry_backoff=3, routing_key='main_reporting_worker', queue='main_reporting_worker')
def send_ocpp_log_via_email(message: dict):
    with contextmanager(create_session)() as dbsession:
        task_uid = message['task_uid']
        headers = message['headers']
        reorder_columns = message['reorder_columns']
        query_params = message['query_params']
        url = message['url']
        charger_headers = message['charger_headers']
        filename = message['filename']
        email = message['email']

        report = GenerateReport(filename, headers, reorder_columns=reorder_columns)

        async def run_get_ocpp_list(email):
            await report.generate_dataframe_with_charger_endpoint(query_params, url, charger_headers, ocpp=True,
                                                                  ls=True)
            await report.datetime_reformat('created_at')
            await report.send_report_via_email('OCPP Log Report', filename, email)
            logger.info("Celery task completed")
            update_task_successfully(task_uid, dbsession)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))
        loop.run_until_complete(run_get_ocpp_list(email))
