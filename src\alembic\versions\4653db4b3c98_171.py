"""171

Revision ID: 4653db4b3c98
Revises: c9069eb2fc2d
Create Date: 2025-05-29 20:49:01.268756

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4653db4b3c98'
down_revision = 'c9069eb2fc2d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_e_credit_note', sa.Column('e_invoice_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('submission_payload', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('submission_response', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_e_credit_note', sa.Column('submission_callback', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.create_foreign_key('fk_main_e_credit_note_e_invoice_id', 'main_e_credit_note', 'main_e_invoice', ['e_invoice_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_main_e_credit_note_e_invoice_id', 'main_e_credit_note', type_='foreignkey')
    op.drop_column('main_e_credit_note', 'submission_callback')
    op.drop_column('main_e_credit_note', 'submission_response')
    op.drop_column('main_e_credit_note', 'submission_payload')
    op.drop_column('main_e_credit_note', 'e_invoice_id')
    # ### end Alembic commands ###
