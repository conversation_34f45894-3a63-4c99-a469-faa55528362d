from contextlib import contextmanager
from datetime import datetime, timedelta

import jwt
import pytest

from fastapi.testclient import TestClient

from app import schema, settings
from app.main import app, ROOT_PATH
from app.database import SessionLocal, create_session, Base, engine
from app.tests.factories import (UserFactory, MembershipFactory, OrganizationFactory, ResourceServerFactory,
                                 RoleFactory, ResourceFactory,
                                 OperatorFactory, IDTagFactory, CreditCardFactory)

client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        IDTagFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session
MEMBER_USER_URL = f'{ROOT_PATH}/api/v1/csms/organization'


# insert organization
def test_list_all_organization_members_with_both_params_empty(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(is_verified=True, verification_method='email', organization_id=organization_id)
        db.commit()

        member = MembershipFactory(organization_id=organization_id, user_id=f'{user.id}',
                                   membership_type=schema.MembershipType.regular_user)
        db.commit()

        create_res_server_and_roles(db, member, str(organization.id), fr'{MEMBER_USER_URL}/.*',
                                    'get', 'apollo-main')

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": str(user.id),
            "membership_id": f'{member.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ROOT_PATH}/api/v1/csms/organization/memberships'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 200


def test_list_all_organization_members_with_both_params(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(is_verified=True, verification_method='email', organization_id=organization_id)
        db.commit()

        member = MembershipFactory(organization_id=organization_id, user_id=f'{user.id}',
                                   membership_type=schema.MembershipType.regular_user)
        db.commit()

        if member.membership_type == schema.MembershipType.regular_user:
            membership_type = 'regular_user'
        elif member.membership_type == schema.MembershipType.staff:
            membership_type = 'staff'
        elif member.membership_type == schema.MembershipType.sub_staff:
            membership_type = 'sub_staff'

        create_res_server_and_roles(db, member, str(organization.id), fr'{MEMBER_USER_URL}/.*',
                                    'get', 'apollo-main')

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": str(user.id),
            "membership_id": f'{member.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ROOT_PATH}/api/v1/csms/organization/memberships'
        response = client.get(url, headers={'authorization': token},
                              params={"organization_id": organization_id, "membership_type": membership_type})
        assert response.status_code == 200


def test_list_all_organization_members_with_organization_param(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(is_verified=True, verification_method='email', organization_id=organization_id)
        db.commit()

        member = MembershipFactory(organization_id=organization_id, user_id=f'{user.id}',
                                   membership_type=schema.MembershipType.regular_user)
        db.commit()

        create_res_server_and_roles(db, member, str(organization.id), fr'{MEMBER_USER_URL}/.*',
                                    'get', 'apollo-main')

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": str(user.id),
            "membership_id": f'{member.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ROOT_PATH}/api/v1/csms/organization/memberships'
        response = client.get(url, headers={'authorization': token}, params={"organization_id": organization_id})
        assert response.status_code == 200


def test_list_all_organization_members_with_membership_type(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(is_verified=True, verification_method='email', organization_id=organization_id)
        db.commit()

        member = MembershipFactory(organization_id=organization_id, user_id=f'{user.id}',
                                   membership_type=schema.MembershipType.regular_user)
        db.commit()
        if member.membership_type == schema.MembershipType.regular_user:
            membership_type = 'regular_user'
        elif member.membership_type == schema.MembershipType.staff:
            membership_type = 'staff'
        elif member.membership_type == schema.MembershipType.sub_staff:
            membership_type = 'sub_staff'

        create_res_server_and_roles(db, member, str(organization.id), fr'{MEMBER_USER_URL}/.*',
                                    'get', 'apollo-main')

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": str(user.id),
            "membership_id": f'{member.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ROOT_PATH}/api/v1/csms/organization/memberships'
        response = client.get(url, headers={'authorization': token}, params={"membership_type": membership_type})
        assert response.status_code == 200


def test_list_all_organization_members_permission_denied(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(is_verified=True, verification_method='email', organization_id=organization_id)
        db.commit()

        member = MembershipFactory(organization_id=organization_id, user_id=f'{user.id}',
                                   membership_type=schema.MembershipType.regular_user)
        db.commit()

        create_res_server_and_roles(db, member, str(organization.id), fr'{MEMBER_USER_URL}/.*',
                                    'post', 'apollo-main')

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": str(user.id),
            "membership_id": f'{member.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ROOT_PATH}/api/v1/csms/organization/memberships'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 403


def test_list_all_organization_members_with_invalid_organization_id(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(is_verified=True, verification_method='email', organization_id=organization_id)
        db.commit()

        member = MembershipFactory(organization_id=organization_id, user_id=f'{user.id}',
                                   membership_type=schema.MembershipType.regular_user)
        db.commit()
        create_res_server_and_roles(db, member, str(organization.id), fr'{MEMBER_USER_URL}/.*',
                                    'get', 'apollo-main')

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": str(user.id),
            "membership_id": f'{member.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ROOT_PATH}/api/v1/csms/organization/memberships'
        response = client.get(url, headers={'authorization': token},
                              params={"organization_id": 'aa3f2b98-26d3-495a-9cf3-389bd35c5db6'})
        assert response.json()['items'] == []


def test_list_all_organization_members_with_invalid_membership_type(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        user = UserFactory(is_verified=True, verification_method='email', organization_id=organization_id)
        db.commit()

        member = MembershipFactory(organization_id=organization_id, user_id=f'{user.id}',
                                   membership_type=schema.MembershipType.regular_user)
        db.commit()
        create_res_server_and_roles(db, member, str(organization.id), fr'{MEMBER_USER_URL}/.*',
                                    'get', 'apollo-main')

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": str(user.id),
            "membership_id": f'{member.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{ROOT_PATH}/api/v1/csms/organization/memberships'
        response = client.get(url, headers={'authorization': token},
                              params={"organization_id": organization_id, "membership_type": 'regular_admin'})
        assert response.status_code == 400
