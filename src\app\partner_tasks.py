import asyncio
import logging
from contextlib import contextmanager
from kombu.exceptions import KombuError
from celery.exceptions import MaxRetriesExceededError  # noqa

from app import settings, crud  # pylint: disable=unused-import
from app.partner_celery import app
from app.database import create_session
from app.settings import APOL<PERSON>O_MAIN_DOMAIN, MAIN_ROOT_PATH
from app.utils import push_update

main_path = f"{APOLLO_MAIN_DOMAIN}/{MAIN_ROOT_PATH}"
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter(settings.LOG_FORMAT))
logger.addHandler(handler)

# publisher = ChargerServicePublisher()
CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'
OCPI_URL = f'{settings.MAIN_OCPI_DOMAIN}/{settings.OCPI_PREFIX}'


@app.task(name='apollo.main.partner_main.tasks.push_emsp', autoretry_for=(KombuError,), retry_backoff=3,
          routing_key='main_partner_worker', queue='main_partner_worker')
def push_to_emsp(message: dict):

    logger.info('push_emsp')
    with contextmanager(create_session)() as dbsession:
        data = message['body']['parameters']
        type = message['body']['message_type']
        # loop = asyncio.get_event_loop()
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))
        if type == 'OCPI Call-Back':
            loop.run_until_complete(push_update(dbsession, 'location', data))
        else:
            loop.run_until_complete(push_update(dbsession, 'charging_session', data))
