name: apollo-main-pr

on:
  pull_request:
    types: [opened, reopened, synchronize]
  
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          persist-credentials: false
      - name: preparing files
        run: |
          cp ./docker/.ci.env .env
      - name: Build and start docker-compose 
        run: |
          docker compose up -d --build
          sleep 20
      - name: Linting
        run: |
          docker compose exec -T apollo-main pipenv run prospector
      - name: test security
        run: |
          docker compose exec -T apollo-main pipenv run bandit -r . -x tests,citests
      - name: Run tests
        run: |
          docker compose exec -T apollo-main pipenv run pytest -v --log-cli-level=warning
          ret_val=$?
          if [ $ret_val -ne 0 ];then
            exit 1
          fi

  secret-scan:
    runs-on: ubuntu-latest
    permissions:
      contents: read # Required for actions/checkout
      actions: write
      pull-requests: write
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
    # - name: Scan Secrets using TruffleHog Secret
    #   uses: trufflesecurity/trufflehog@v3.74.0
    #   with: 
    #     extra_args: --debug
    - name: Scan Secrets using Gitleaks
      uses: gitleaks/gitleaks-action@v2.3.7
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}