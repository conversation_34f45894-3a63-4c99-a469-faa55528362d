from decimal import Decimal
import logging
from datetime import datetime
from functools import partial
import pytz

from fastapi import APIRouter, Depends, Request, HTTPException, status, BackgroundTasks
from fastapi.responses import HTMLResponse
from fastapi_pagination import Params, Page
from fastapi_pagination.ext.sqlalchemy import paginate
# from sqlalchemy import or_

from app import settings, schema, crud, exceptions, models
from app.crud import UserCRUD
from app.crud.reporting_task import create_reporting_task_record
from app.database import create_session, SessionLocal
# from app.middlewares import set_admin_as_context_user
from app.permissions import permission
from app.mail import RecipientsSchema
from app.schema import ReportingTaskCreate
from app.utils import (create_payment_refund, decode_auth_token_from_headers, send_credit_note_mail, send_invoice_mail,
                       generate_charger_header, user_filters, send_request, GenerateInvoice,
                       get_invoice_html, get_invoice_template_name, get_all_child_organizations)
from app.lta_tasks import send_invoice_list_via_email_sync

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/invoice",
    tags=['invoice', ],
    dependencies=[Depends(permission)],
)
CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'


async def invoice_list_filter_parameters(date_from: datetime = None, date_to: datetime = None, status: str = None,
                                         invoice_number: str = None, operator: str = None, charge_point_id: str = None,
                                         payment_type: str = None, currency: str = None, reference_number: str = None,
                                         connector_label: str = None, refund_status: str = None):
    return {'date_from': date_from, 'date_to': date_to, 'status': status,
            'invoice_number': invoice_number, 'operator': operator, 'charge_point_id': charge_point_id,
            'connector_label': connector_label,
            'payment_type': payment_type, 'currency': currency, 'reference_number': reference_number,
            'refund_status': refund_status}


@router.get("/", status_code=status.HTTP_200_OK, response_model=Page[schema.InvoiceResponse])
async def get_invoice_list(request: Request,  # noqa: MC0001, # pylint: disable=too-many-locals
                           params: Params = Depends(),
                           filter_params: dict = Depends(invoice_list_filter_parameters),
                           user_filters: dict = Depends(user_filters),
                           dbsession: SessionLocal = Depends(create_session)) -> Page[schema.InvoiceResponse]:
    """
    Get Member Invoice List
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    login_user = UserCRUD.get(dbsession, user_id)
    headers = generate_charger_header(dbsession, membership_id)
    cp_id = filter_params['charge_point_id'] if 'charge_point_id' in filter_params else None
    connector_label = filter_params['connector_label'] if 'connector_label' in filter_params else None
    operator = filter_params['operator'] if 'operator' in filter_params else None
    evse_url = f'{CHARGER_URL_PREFIX}/ocpi/evse/'
    if cp_id or connector_label:
        query_params = {'serial_number': cp_id, 'connector_label': connector_label}
        url = f'{CHARGER_URL_PREFIX}/charge_point/'
        response = await send_request('GET', url=url, headers=headers, query_params=query_params)
        filter_params['charge_point_id'] = [cp['id'] for cp in response.json()['items']]

        evse_query_params = {'ocpi_partner_evse_id': cp_id, 'return_conn_ids': True,
                             'physical_reference': connector_label}
        evse_response = await send_request('GET', url=evse_url, headers=headers, query_params=evse_query_params)
        evse_connectors_ids = evse_response.json()
        filter_params['evse_connectors_ids'] = list(evse_connectors_ids)

    if operator:
        operator_id_list = [id.strip() for id in filter_params['operator'].split(',')]
        ext_org = dbsession.query(models.ExternalOrganization).filter(
            models.ExternalOrganization.id.in_(operator_id_list)).all()
        if ext_org:
            ext_org_ids = [str(ex.id) for ex in ext_org]
            evse_query_params = {'op_id': ext_org_ids, 'return_conn_ids': True}
            evse_response = await send_request('GET', url=evse_url, headers=headers, query_params=evse_query_params)
            evse_connectors_ids_op = evse_response.json()
            filter_params['evse_connectors_ops'] = list(evse_connectors_ids_op)

    mem_org_id = crud.MembershipCRUD.membership().organization_id
    all_child_org = get_all_child_organizations(dbsession, mem_org_id)
    all_child_org = [str(child) for child in all_child_org]

    try:
        evse_url = f'{CHARGER_URL_PREFIX}/ocpi/evse/full/'
        evse_response = await send_request('GET', url=evse_url, headers=headers)
        evse_connectors_ids = evse_response.json()
        filter_params['allowed_ocpi_connectors'] = list(evse_connectors_ids)
    except Exception as e:  # pylint: disable=broad-except
        logger.error("Error getting full evse list, something is wrong, error as: %s", str(e))

    # pylint:disable=too-many-nested-blocks
    try:
        # invoice_list = await crud.get_member_invoice_list(dbsession, headers, filter_params)
        invoice_list = await crud.get_member_invoice_list_optimized(dbsession, filter_params, request,
                                                                    user_filters, params, headers)
        for item in invoice_list['items']:
            if item.organization:
                cs_mem_org_id = item.organization.id
                if not login_user.is_superuser and not settings.CAN_VIEW_SUB_ORG:
                    if str(mem_org_id) != str(cs_mem_org_id):
                        if cs_mem_org_id not in all_child_org:
                            item.member.user.email = '*****'
                            item.member.user.phone_number = '*****'
                            item.user.email = '*****'
                            item.user.phone_number = '*****'
        return invoice_list
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/download/")
async def download_invoice_list(request: Request, background_tasks: BackgroundTasks,
                                filename: str = None, send_to: str = None,
                                filter_params: dict = Depends(invoice_list_filter_parameters),
                                user_filters: dict = Depends(user_filters),
                                dbsession: SessionLocal = Depends(create_session)):
    headers = ['INVOICE NUMBER', 'REFERENCE_NUMBER', 'ID TAG', 'EMAIL', 'PHONE NUMBER', 'CHARGE POINT ID', 'OPERATOR',
               'LOCATION NAME', 'CONNECTOR KIND',
               'AMOUNT BEFORE TAX', 'IDLE PRICE', 'DISCOUNTED', 'PROMO DISCOUNTED', 'PROMO APPLIED',
               'TAXED AMOUNT', 'TAX RATE', 'AMOUNT AFTER TAX', 'PAYMENT TYPE', 'WALLET PAID AMOUNT',
               'CARD PAID AMOUNT', 'STATUS', 'PLAN NAME', 'PLAN CATEGORY', 'PLAN AMOUNT',
               'CUSTOM PLAN FIXED AMOUNT', 'START TIME', 'END TIME', 'ENERGY DISPENSED (KWH)',
               'CURRENCY', 'REFUND STATUS', 'TOTAL WALLET REFUNDED AMOUNT', 'TOTAL NON WALLET REFUNDED AMOUNT']
    columns = ['invoice_number', 'reference_number', 'id_tag', 'email', 'phone_number', 'serial_number', 'operator',
               'location_name', 'connector_kind',
               'amount', 'hogging_fee', 'subscription_discount', 'campaign_promo_code_discount',
               'campaign_promo_code_usage', 'tax_amount', 'tax_rate',
               'total_amount', 'payment_type', 'wallet_deduct_amount',
               'non_wallet_deduct_amount', 'status', 'plan_name', 'plan_category', 'plan_amount',
               'custom_plan_fixed_amount', 'start_time', 'end_time', 'energy_dispensed',
               'currency', 'refund_status', 'total_wallet_refunded_amount', 'total_non_wallet_refunded_amount']

    get_data = partial(get_invoice_list, request=request,
                       dbsession=dbsession, filter_params=filter_params, user_filters=user_filters)

    report = GenerateInvoice('invoice_list', headers, columns, function=get_data)
    if not filename:
        tz = pytz.timezone('Asia/Kuala_Lumpur')
        current_datetime = datetime.now(tz)
        format_datetime = current_datetime.strftime("%d.%m.%Y_(%H:%M)")
        filename = 'invoice_list_' + format_datetime

    # moved to celery, use this function if celery not working
    # async def run_get_invoice_list(email):
    #     await report.send_report_via_email('Your Invoice Report', filename, email)
    #     logger.info("Background task completed")

    if 'date_from' in filter_params and isinstance(filter_params['date_from'], datetime):
        filter_params['date_from'] = filter_params['date_from'].isoformat()  # Convert to string
    if 'date_to' in filter_params and isinstance(filter_params['date_to'], datetime):
        filter_params['date_to'] = filter_params['date_to'].isoformat()  # Convert to string

    if send_to:
        # moved to celery, use this function if celery not working
        # background_tasks.add_task(run_get_invoice_list, send_to)
        request_data = {
            "headers": dict(request.headers)
        }

        message = {
            'request_data': request_data,
            'filter_params': filter_params,
            'user_filters': user_filters,
            'headers': headers,
            'filename': filename,
            'columns': columns,
            'email': send_to,
        }

        task_data = ReportingTaskCreate(
            task_name="invoice_list",
            json_payload=message,
            retry_count=0
        )

        reporting_task = create_reporting_task_record(dbsession, task_data)
        task_uid = reporting_task.id
        message['task_uid'] = task_uid
        send_invoice_list_via_email_sync.delay(message=message)

        return {'message': f'The invoice mailed to {send_to}. Will be ready in 15 minutes.'}
    return await report.generate_report()


@router.get("/{charging_session_id}", status_code=status.HTTP_200_OK, response_model=schema.InvoiceResponse)
async def get_invoice(request: Request,  # noqa: MC0001,# pylint: disable=too-many-nested-blocks
                      charging_session_id: str, html_content: bool = False,
                      dbsession: SessionLocal = Depends(create_session)) -> schema.InvoiceResponse:
    """
    Get a Invoice

    :param str charging_session_id: Target Invoice ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    login_user = UserCRUD.get(dbsession, user_id)
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    try:
        invoice = await crud.get_invoice(dbsession, charging_session_id, headers, login_user)
        if html_content:
            try:
                template_name = get_invoice_template_name(invoice, None)  # Pass appropriate reference if needed
                # Determine organization-specific templates
                db_member = crud.get_membership_by_id(dbsession, str(invoice.member.id))
                if db_member.organization.name.lower() == settings.YGT_ORGANIZATION_NAME.lower():
                    if "_sg" in template_name:
                        template_name = template_name.replace("_sg", "_sg_ygt")
                    else:
                        template_name = template_name.replace(".html", "_ygt.html")
                html = get_invoice_html(invoice.dict(), template_name)
            except Exception:  # pylint: disable=broad-except
                # A simplified exception fallback
                if invoice.connector.billing_currency == 'SGD':
                    html = get_invoice_html(invoice.dict(), 'invoice_v2_sg.html')
                elif invoice.connector.billing_currency == 'KHR':
                    html = get_invoice_html(invoice.dict(), 'invoice_v2_kh.html')
                elif invoice.connector.billing_currency == 'BND':
                    html = get_invoice_html(invoice.dict(), 'invoice_v2_bn.html')
                elif invoice.connector.billing_currency == 'IDR':
                    html = get_invoice_html(invoice.dict(), 'invoice_v2_idr.html')
                else:
                    html = get_invoice_html(invoice.dict(), 'invoice_v2.html')

            return HTMLResponse(content=html, status_code=200)

        return invoice

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/{charging_session_id}",
             status_code=status.HTTP_200_OK)
async def send_invoice_email(request: Request, charging_session_id: str, recipients: RecipientsSchema,
                             dbsession: SessionLocal = Depends(create_session)) -> schema.Invoice:
    """
    Send Invoice Email

    :param str charging_session_id: Target Invoice ID
    :param RecipientsSchema recipients: Invoice Email Recipients
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    try:
        invoice = await crud.get_invoice(dbsession, charging_session_id, headers)
        try:
            db_member = crud.get_membership_by_id(dbsession, str(invoice.member.id))
            if db_member.organization.name.lower() == settings.YGT_ORGANIZATION_NAME.lower():
                send_invoice_mail(invoice, invoice_ref='YGT', recipients=recipients)
            else:
                send_invoice_mail(invoice, recipients=recipients)
        except Exception:  # pylint: disable=broad-except
            send_invoice_mail(invoice, recipients=recipients)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/refund/{invoice_number}",  # noqa: MC0001
             status_code=status.HTTP_201_CREATED)
async def refund_invoice(request: Request, invoice_number: str,  # noqa: MC0001
                         refund_data: schema.PaymentRefund,
                         dbsession: SessionLocal = Depends(create_session)):

    """
    Refund Invoice

    :param str invoice_number: Target Invoice Number
    """
    # auth_token_data = decode_auth_token_from_headers(request.headers)
    # membership_id = auth_token_data.get('membership_id')
    membership = crud.MembershipCRUD.membership()
    if membership.membership_type not in [schema.MembershipType.staff, schema.MembershipType.sub_staff]:
        raise HTTPException(status_code=400, detail='Only administrator can refund invoice')

    try:
        refund_data = refund_data.dict()
        db_payment_request = crud.get_payment_request_by_invoice_number(dbsession, invoice_number)
        refund_amount = Decimal(str(refund_data.get('refund_amount')))

        refund_details = crud.get_refund_details(db_payment_request, refund_data)
        if refund_details:
            refund_type = refund_details['refund_type']
            paid_amount = refund_details['paid_amount']

        total_refunded_amount = crud.get_total_payment_refund_by_payment_request_id(dbsession,
                                                                                    db_payment_request.id,
                                                                                    refund_data.get('refund_type'))
        if refund_amount > Decimal(str(paid_amount - total_refunded_amount)):
            raise HTTPException(status_code=400, detail='Refund amount cannot be greater than paid amount')

        refund_result = create_payment_refund(dbsession, db_payment_request, refund_data, refund_type)
        return refund_result

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/refund/{invoice_number}",  # noqa: MC0001
             status_code=status.HTTP_200_OK,
             response_model=Page[schema.PaymentRefundResponse])
async def get_refund_invoice(request: Request,
                             invoice_number: str,  # noqa: MC0001
                             params: Params = Depends(),
                             dbsession: SessionLocal = Depends(create_session)):

    membership = crud.MembershipCRUD.membership()
    if membership.membership_type not in [schema.MembershipType.staff, schema.MembershipType.sub_staff]:
        raise HTTPException(status_code=400, detail='Only administrator can view refund invoice')

    db_payment_request = crud.get_all_payment_refund_by_invoice_number(dbsession, invoice_number)
    return paginate(db_payment_request, params)


@router.get("/view_refund/{charging_session_id}", status_code=status.HTTP_200_OK,
            response_model=schema.InvoiceResponse)
async def view_refund(request: Request,  # noqa: MC0001,# pylint: disable=too-many-nested-blocks
                      charging_session_id: str, html_content: bool = False,
                      dbsession: SessionLocal = Depends(create_session)) -> schema.InvoiceResponse:
    """
    View Refund receipt

    :param str charging_session_id: Target Invoice ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    login_user = UserCRUD.get(dbsession, user_id)
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    membership = crud.MembershipCRUD.membership()
    if membership.membership_type not in [schema.MembershipType.staff, schema.MembershipType.sub_staff]:
        raise HTTPException(status_code=400, detail='Only administrator can refund invoice')
    try:
        creidt_note = await crud.get_invoice(dbsession, charging_session_id, headers,
                                             login_user, True)
        if html_content:
            try:
                template_name = 'credit_note.html'
                html = get_invoice_html(creidt_note.dict(), template_name)
            except Exception:  # pylint: disable=broad-except
                html = get_invoice_html(creidt_note.dict(), 'credit_note.html')
            return HTMLResponse(content=html, status_code=200)
        return creidt_note

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/send_refund/{charging_session_id}",
             status_code=status.HTTP_200_OK)
async def send_refund_mail(request: Request, charging_session_id: str, recipients: RecipientsSchema,
                           dbsession: SessionLocal = Depends(create_session)) -> schema.Invoice:
    """
    Send Refund Email

    :param str charging_session_id: Target Invoice ID
    :param RecipientsSchema recipients: Refund Email Recipients
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    membership = crud.MembershipCRUD.membership()
    if membership.membership_type not in [schema.MembershipType.staff, schema.MembershipType.sub_staff]:
        raise HTTPException(status_code=400, detail='Only administrator can refund invoice')
    try:
        creidt_note = await crud.get_invoice(dbsession, charging_session_id,
                                             headers, is_credit_note=True)
        try:
            # db_member = crud.get_membership_by_id(dbsession, str(invoice.member.id))
            send_credit_note_mail(creidt_note, recipients=recipients)
        except Exception:  # pylint: disable=broad-except
            send_credit_note_mail(creidt_note, recipients=recipients)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/view_single_refund/{refund_id}", status_code=status.HTTP_200_OK,
            )
async def view_single_refund(request: Request,  # noqa: MC0001,# pylint: disable=too-many-nested-blocks
                             refund_id: str, html_content: bool = False,
                             dbsession: SessionLocal = Depends(create_session)) -> schema.InvoiceResponse:
    """
    View a Refund

    :param str charging_session_id: Target Invoice ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    login_user = UserCRUD.get(dbsession, user_id)
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    membership = crud.MembershipCRUD.membership()
    if membership.membership_type not in [schema.MembershipType.staff, schema.MembershipType.sub_staff]:
        raise HTTPException(status_code=400, detail='Only administrator can refund invoice')
    try:
        creidt_note = await crud.get_single_credit_note(dbsession, refund_id, headers,
                                                        login_user)
        if html_content:
            try:
                template_name = 'credit_note.html'
                html = get_invoice_html(creidt_note.dict(), template_name)
            except Exception:  # pylint: disable=broad-except
                html = get_invoice_html(creidt_note.dict(), 'credit_note.html')
            return HTMLResponse(content=html, status_code=200)
        return creidt_note

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/send_single_refund/{refund_id}",
             status_code=status.HTTP_200_OK)
async def send_single_refund_mail(request: Request, refund_id: str, recipients: RecipientsSchema,
                                  dbsession: SessionLocal = Depends(create_session)) -> schema.Invoice:
    """
    Send a Refund Email

    :param str charging_session_id: Target Invoice ID
    :param RecipientsSchema recipients: Refund Email Recipients
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    membership = crud.MembershipCRUD.membership()
    if membership.membership_type not in [schema.MembershipType.staff, schema.MembershipType.sub_staff]:
        raise HTTPException(status_code=400, detail='Only administrator can refund invoice')
    try:
        creidt_note = await crud.get_single_credit_note(dbsession, refund_id, headers)
        try:
            # db_member = crud.get_membership_by_id(dbsession, str(invoice.member.id))
            send_credit_note_mail(creidt_note, recipients=recipients)
        except Exception:  # pylint: disable=broad-except
            send_credit_note_mail(creidt_note, recipients=recipients)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
