# pylint:disable=too-many-lines
import re
import typing
import uuid
from datetime import datetime, timedelta
from secrets import token_urlsafe
import random
import string
import bcrypt

from sqlalchemy import or_, and_, func
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, NoResultFound

from app import models, schema, settings, exceptions, constants

from .wallet import create_wallet
from .base import BaseCRUD
from ..settings import SG_PRE_AUTH_DEFAULT_AMOUNT, \
    MY_PRE_AUTH_DEFAULT_AMOUNT, KH_PRE_AUTH_DEFAULT_AMOUNT, BN_PRE_AUTH_DEFAULT_AMOUNT, \
    MY_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT, BN_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT, KH_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT, \
    SG_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT, ENABLE_RESTRICT_OTP_FAILURE


class UserCRUD(BaseCRUD):
    model = models.User

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        if not check_permission:
            return super().query(dbsession, *columns)
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            return super().query(dbsession, *columns)
        return super().query(dbsession, *columns).filter(cls.model.id == membership.user.id)

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        if membership.user.id == object_id:
            return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_delete(cls, dbsession: Session, object_id, *args, **kwargs):
        superusers = super().query(dbsession).filter(
            cls.model.id == object_id, cls.model.is_superuser).first()
        if superusers:
            raise exceptions.ApolloRootUserDeletion
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        raise exceptions.ApolloPermissionError()


class MembershipCRUD(BaseCRUD):
    model = models.Membership

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        if not check_permission:
            return super().query(dbsession, *columns)
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession, *columns).filter(
                models.Membership.organization_id.in_(allowed_orgs)
            )
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     return super().query(dbsession, *columns).filter(
        #         models.Membership.organization_id == membership.organization_id
        #     )
        if membership.membership_type == schema.MembershipType.regular_user:
            return super().query(dbsession, *columns).filter(cls.model.id == membership.id)
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_create(cls, dbsession: Session, data, *args, **kwargs):
        if data['membership_type'] == schema.MembershipType.regular_user:
            return True

        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        cls.check_root_organization(dbsession, data['organization_id'])
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.custom):
            child_orgs = cls.child_organizations(dbsession, membership)
            if data['membership_type'] in [schema.MembershipType.sub_staff, schema.MembershipType.custom]:
                allowed_orgs = child_orgs + [membership.organization_id]
                if data['organization_id'] in allowed_orgs:
                    return True
            if data['membership_type'] == schema.MembershipType.staff:
                if data['organization_id'] in child_orgs:
                    return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        db_member = dbsession.query(cls.model).get(object_id)
        if not db_member:
            return True
        cls.check_root_organization(dbsession, db_member.organization_id)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.custom):
            child_orgs = cls.child_organizations(dbsession, membership)
            if db_member.membership_type in [schema.MembershipType.sub_staff,
                                             schema.MembershipType.regular_user,
                                             schema.MembershipType.custom]:
                allowed_orgs = child_orgs + [membership.organization_id]
                if db_member.organization_id in allowed_orgs:
                    return True
            if db_member.membership_type == schema.MembershipType.staff:
                if db_member.organization_id in child_orgs:
                    return True
        if db_member.id == membership.id:
            return True
        raise exceptions.ApolloPermissionError()


class MemberPreAuthInfoCRUD(BaseCRUD):
    model = models.MemberPreAuthInfo


class ResetPasswordTokenCRUD(BaseCRUD):
    model = models.ResetPasswordToken


class VerificationTokenCRUD(BaseCRUD):
    model = models.VerificationToken


class ManualLinkingTokenCRUD(BaseCRUD):
    model = models.ManualLinkingToken


class InviteCRUD(BaseCRUD):
    model = models.Invite


class MembershipExtendedCRUD(BaseCRUD):
    model = models.MembershipExtended


class MembershipDunningCRUD(BaseCRUD):
    model = models.MembershipDunning


class ExternalTokenCRUD(BaseCRUD):
    model = models.ExternalToken


class OCPICPOTokenCRUD(BaseCRUD):
    model = models.OCPICPOToken


# becareful, there is two place using class due to circular, and the naming is wrong
class OCPIEMSPTokenCRUD(BaseCRUD):
    model = models.OCPIToken


# Auth


# pylint:disable=unsubscriptable-object
def get_user_by_email(db: Session, email: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(models.User.email == email,
                                                                    models.User.is_guest.is_(False)).one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


# pylint:disable=unsubscriptable-object
def get_user_by_email_and_organization_id(db: Session, email: str, organization_id: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            models.User.email == email,
            models.User.organization_id == organization_id,
            models.User.is_guest.is_(False),
        ).one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


# pylint:disable=unsubscriptable-object
def get_user_by_email_and_organization_id_v2(db: Session, email: str, organization_id: str,
                                             user_type: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            models.User.email == email,
            models.User.organization_id == organization_id,
            models.User.is_guest.is_(False),
            models.User.user_type == user_type,
        ).one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_id_and_type(db: Session, user_id: str, user_type: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            models.User.id == user_id,
            models.User.is_guest.is_(False),
            models.User.user_type == user_type,
        ).one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


# pylint:disable=unsubscriptable-object
def get_all_user_by_email(db: Session, email: str) -> list[models.User]:
    db_user = UserCRUD.query(db, check_permission=False).filter(models.User.email == email).all()
    if db_user:
        return db_user
    raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_id(db: Session, user_id: str) -> models.User:
    try:
        db_user = UserCRUD.get(db, user_id)
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_phone_number(db: Session, phone_number: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(models.User.phone_number == phone_number).one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_phone_number_v2(db: Session, phone_number: str, user_type: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            models.User.phone_number == phone_number,
            models.User.user_type == user_type).one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_phone_or_email(db: Session, phone_number: str, email: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            or_(
                func.lower(models.User.email) == str(email).lower(),
                models.User.phone_number == phone_number,
            ),
            models.User.is_guest.is_(False),
        ).one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_phone_or_email_v2(db: Session, phone_number: str, email: str, user_type: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            or_(
                func.lower(models.User.email) == str(email).lower(),
                models.User.phone_number == phone_number
            ),
            models.User.is_guest.is_(False),
            models.User.user_type == user_type
        ).first()

        if not db_user:
            raise NoResultFound
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_phone_number_normal_user(db: Session, phone_number: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(models.User.phone_number == phone_number,
                                                                    models.User.is_guest.is_(False)).join(
            models.Membership).filter(models.Membership.membership_type == schema.MembershipType.regular_user).one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_phone_number_normal_user_v2(db: Session, phone_number: str) -> models.User:
    try:
        db_user = (
            UserCRUD.query(db, check_permission=False)
            .filter(models.User.phone_number == phone_number,
                    models.User.is_guest.is_(False),
                    models.User.user_type == schema.UserType.regular)
            .join(models.Membership).filter(models.Membership.membership_type == schema.MembershipType.regular_user)
            .one())
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_normal_user_by_phone_or_email(db: Session, phone_number: str, email: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            or_(
                func.lower(models.User.email) == str(email).lower(),
                models.User.phone_number == phone_number
            ),
            models.User.is_guest.is_(False)).join(
            models.Membership).filter(
            models.Membership.membership_type == schema.MembershipType.regular_user) \
            .one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_normal_user_by_phone_or_email_v2(db: Session, phone_number: str, email: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            or_(
                func.lower(models.User.email) == str(email).lower(),
                models.User.phone_number == phone_number
            ),
            models.User.user_type == schema.UserType.regular,
            models.User.is_guest.is_(False)).join(
            models.Membership).filter(
            models.Membership.membership_type == schema.MembershipType.regular_user) \
            .first()
        if not db_user:
            raise NoResultFound
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_phone_number_and_organization_id(db: Session, phone_number: str,
                                                 organization_id: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            models.User.phone_number == phone_number,
            models.User.organization_id == organization_id,
            models.User.is_guest.is_(False),
        ).one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_phone_number_and_organization_id_v2(db: Session, phone_number: str,
                                                    organization_id: str,
                                                    user_type: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            models.User.phone_number == phone_number,
            models.User.organization_id == organization_id,
            models.User.is_guest.is_(False),
            models.User.user_type == user_type,
        ).one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_guest_app_id_and_organization_id(db: Session, guest_app_id: str,
                                                 organization_id: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            models.User.guest_app_id == guest_app_id,
            models.User.is_guest.is_(True),
            models.User.organization_id == organization_id
        ).one()
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_email_or_phone_number(db: Session, email: str,
                                      phone_number: str, organization_id: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            or_(
                models.User.email == email,
                models.User.phone_number == phone_number
            ),
            models.User.is_guest.is_(False),
            models.User.organization_id == organization_id
        ).first()
        if db_user is None:
            raise NoResultFound
        if db_user.is_superuser:
            raise exceptions.ApolloRootUserCreation
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_by_email_or_phone_number_v2(db: Session, email: str,
                                         phone_number: str, organization_id: str,
                                         user_type: str) -> models.User:
    try:
        db_user = UserCRUD.query(db, check_permission=False).filter(
            or_(
                models.User.email == email,
                models.User.phone_number == phone_number
            ),
            models.User.is_guest.is_(False),
            models.User.organization_id == organization_id,
            models.User.user_type == user_type
        ).first()
        if db_user is None:
            raise NoResultFound
        if db_user.is_superuser:
            raise exceptions.ApolloRootUserCreation
        return db_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_all_users_by_email_or_phone_number(db: Session, email: str,
                                           phone_number: str) -> models.User:
    try:
        db_users = UserCRUD.query(db, check_permission=False).filter(
            or_(
                models.User.email == email,
                models.User.phone_number == phone_number,
                models.User.is_guest.is_(False),
            )
        ).all()
        for db_user in db_users:
            if db_user.is_superuser:
                raise exceptions.ApolloRootUserCreation
        return db_users
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def update_user_phone_number(db: Session, user_id: str, phone_number: str) -> str:
    try:
        UserCRUD.update(db, user_id, {'phone_number': phone_number})
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_user_roles(db: Session, user_id: str) -> typing.Set[str]:
    db_user = get_user_by_id(db, user_id)
    roles = db_user.roles
    if db_user.is_superuser:
        roles.append(constants.DefaultRoles.IS_SUPERUSER)
    return set(roles)


def update_user_password(db: Session, new_password: str, membership_id: str) -> str:
    try:
        # Dont use MembershipCRUD.update as this action will be prevented
        db_member = MembershipCRUD.get(db, membership_id, check_permission=False)
        db_member.password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt(11, b'2a')).decode('utf-8')
        db.commit()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_user_membership_by_organization_id(
        db: Session,
        user_id: str,
        organization_id: str
) -> models.User:
    try:
        db_membership = MembershipCRUD.query(db, check_permission=False).filter(
            models.Membership.organization_id == organization_id,
            models.Membership.user_id == user_id,
        ).one()
        return db_membership
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_user_staff_membership_by_organization_id(
        db: Session,
        user_id: str,
        organization_id: str
) -> models.Membership:
    try:
        db_membership = MembershipCRUD.query(db, check_permission=False).filter(
            models.Membership.organization_id == organization_id,
            models.Membership.user_id == user_id,
            models.Membership.membership_type.in_([
                schema.MembershipType.staff,
                schema.MembershipType.sub_staff,
                schema.MembershipType.custom,
            ])
        ).one()
        return db_membership
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def get_membership_type_by_membership_id(db: Session, membership_id: str) -> int:
    try:
        db_membership = MembershipCRUD.query(db, check_permission=False).filter(
            models.Membership.id == membership_id
        ).one()
        return dict(
            first_name=db_membership.first_name,
            last_name=db_membership.last_name,
            membership_type=db_membership.membership_type,
            roles=db_membership.roles
        )
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def create_user_membership(db: Session, membership: schema.Membership) -> models.Membership:
    """
    Creates a user membership
    """
    try:
        # Create Membership
        db_membership = MembershipCRUD.add(db, membership.dict())

        # currencies = [schema.Currency.myr, schema.Currency.sgd, schema.Currency.bnd, schema.Currency.khr]
        # Filter valid currencies from WALLET_CURRENCY_LIST
        currencies = []
        for currency in settings.WALLET_CURRENCY_LIST:
            try:
                currencies.append(schema.Currency(currency.upper()))
            except ValueError:
                continue

        for currency in currencies:
            wallet = schema.Wallet(currency=currency)
            create_wallet(db, wallet, str(db_membership.id))

        return db_membership

    except IntegrityError as e:
        db.rollback()
        if "unique_membership_id_tag" in str(e):
            raise exceptions.ApolloDuplicateUserIdTagError() from e
        raise exceptions.ApolloDuplicateMembershipError() from e


def create_guest_membership(db: Session, membership: schema.GuestMembershipEnroll) -> models.Membership:
    """
    Creates a guest membership
    """
    try:
        # Create Membership
        db_membership = MembershipCRUD.add(db, membership.dict())

        # Create user wallet
        currencies = []
        for currency in settings.WALLET_CURRENCY_LIST:
            try:
                currencies.append(schema.Currency(currency.upper()))
            except ValueError:
                continue

        for currency in currencies:
            wallet = schema.Wallet(currency=currency)
            create_wallet(db, wallet, str(db_membership.id))

        return db_membership
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloDuplicateMembershipError()


def create_user(db: Session, data: dict) -> models.User:  # noqa: MC0001
    invite_token = data.get('invite_token')

    # Remove data that are not used for User model
    keys_to_remove = ['password_confirmation', 'invite_token']
    for key in keys_to_remove:
        try:
            if data[key] or key in data:
                del data[key]
        # Guest user do not have these info
        except KeyError:
            pass

    # use invite token
    if invite_token:
        try:
            db_invite = InviteCRUD.query(db).filter(models.Invite.token == invite_token).first()
            InviteCRUD.update(db, str(db_invite.id),
                              {'accepted_invite_count': db_invite.accepted_invite_count + 1})
        except NoResultFound:
            raise exceptions.ApolloObjectDoesNotExist(model_name='Invite')

    try:
        db_user = UserCRUD.add(db, data)
        return db_user
    except IntegrityError as e:
        print(e)
        # User already exists (User might have a membership for another Organization)
        db.rollback()

    try:
        db_user = get_user_by_email_or_phone_number(db, data['email'], data['phone_number'], data['organization_id'])
        # For migrated user, if email is different but phone is matching, update phone to none
        if db_user.is_migrated and data['email'] != db_user.email:
            UserCRUD.update(db, db_user.id, {'phone_number': None}, check_permission=False)
            db_user = UserCRUD.add(db, data)
    except NoResultFound:
        raise exceptions.ApolloDuplicateMembershipError
    # KeyError happen for Guest Charging ONLY
    except KeyError:
        raise exceptions.ApolloDuplicateMembershipError

    return db_user


def create_user_v2(db: Session, data: dict) -> models.User:  # noqa: MC0001
    invite_token = data.get('invite_token')

    # Remove data that are not used for User model
    keys_to_remove = ['password_confirmation', 'invite_token']
    for key in keys_to_remove:
        try:
            if data[key] or key in data:
                del data[key]
        # Guest user do not have these info
        except KeyError:
            pass

    # use invite token
    if invite_token:
        try:
            db_invite = InviteCRUD.query(db).filter(models.Invite.token == invite_token).first()
            InviteCRUD.update(db, str(db_invite.id),
                              {'accepted_invite_count': db_invite.accepted_invite_count + 1})
        except NoResultFound:
            raise exceptions.ApolloObjectDoesNotExist(model_name='Invite')

    try:
        db_user = UserCRUD.add(db, data)
        return db_user
    except IntegrityError as e:
        print(e)
        # User already exists (User might have a membership for another Organization)
        db.rollback()

    try:
        db_user = get_user_by_email_or_phone_number_v2(db, data['email'], data['phone_number'],
                                                       data['organization_id'], data['user_type'])
        # For migrated user, if email is different but phone is matching, update phone to none
        if db_user.is_migrated and data['email'] != db_user.email:
            UserCRUD.update(db, db_user.id, {'phone_number': None}, check_permission=False)
            db_user = UserCRUD.add(db, data)
    except NoResultFound:
        raise exceptions.ApolloDuplicateMembershipError
    # KeyError happen for Guest Charging ONLY
    except KeyError:
        raise exceptions.ApolloDuplicateMembershipError

    return db_user


def generate_verification_token(db: Session, user_id: str, is_used: bool = False) -> str:
    # Delete existing tokens first
    VerificationTokenCRUD.query(db).filter(
        models.VerificationToken.is_used is False,
        models.VerificationToken.user_id == user_id
    ).delete(synchronize_session='fetch')
    token = schema.VerificationToken(**{
        'token': re.sub(r'\W+', '', token_urlsafe(settings.VERIFICATION['TOKEN_BYTES_LENGTH'])),
        'expiration': datetime.utcnow() + timedelta(minutes=settings.VERIFICATION['TOKEN_DURATION_MINUTES']),
        'user_id': str(user_id),
        'is_used': is_used,
    })

    VerificationTokenCRUD.add(db, token.dict())
    return token.token


def get_verification_token(db: Session, data: schema.SubmittedVerificationToken,
                           user_id: str) -> models.VerificationToken:
    try:
        data_dict = data.dict()
        db_token = VerificationTokenCRUD.query(db).filter(
            models.VerificationToken.token == data_dict['token'],
            models.VerificationToken.user_id == user_id,
        ).one()
        return db_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='VerificationToken')


def get_verification_token_by_id(db: Session, token_id: str) -> models.VerificationToken:
    try:
        db_token = VerificationTokenCRUD.get(db, token_id)
        return db_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='VerificationToken')


def update_verification_token_used_status(db: Session, token_id: str, is_used: bool) -> bool:
    VerificationTokenCRUD.update(db, token_id, {'is_used': is_used})
    return is_used


def update_user_verification_status(db: Session, data: schema.VerificationMethodUpdate) -> str:
    data = data.dict()
    try:
        # Dont use UserCRUD.update as this action will be prevented
        db_user = UserCRUD.get(db, data['user_id'], check_permission=False)
        db_user.verification_method = data['verification_method']
        db_user.is_verified = data['is_verified']
        if data['migration_status']:
            db_user.migration_status = data['migration_status']
        if data['created_at']:
            db_user.created_at = data['created_at']
        if data['link_date']:
            db_user.link_date = data['link_date']
        db.commit()
        return data['is_verified']
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def check_reset_password_limit(db: Session, user_id: str, limit: int) -> bool:
    today_date = datetime.today().date()
    today_token = ResetPasswordTokenCRUD.query(db).filter(
        models.ResetPasswordToken.user_id == user_id,
        func.date(models.ResetPasswordToken.created_at) == today_date
    ).count()
    if today_token >= limit:
        return True
    return False


def generate_reset_password_token(db: Session, user_id: str, is_used: bool = False) -> str:
    # Delete existing tokens first
    VerificationTokenCRUD.query(db).filter(
        models.VerificationToken.is_used is False,
        models.VerificationToken.user_id == user_id
    ).delete(synchronize_session='fetch')
    token = schema.ResetPasswordToken(**{
        'token': re.sub(r'\W+', '', token_urlsafe(settings.RESET_PASSWORD['TOKEN_BYTES_LENGTH'])),
        'expiration': datetime.utcnow() + timedelta(minutes=settings.RESET_PASSWORD['TOKEN_DURATION_MINUTES']),
        'user_id': str(user_id),
        'is_used': is_used,
    })
    try:
        ResetPasswordTokenCRUD.add(db, token.dict())
        return token.token
    except IntegrityError:
        db.rollback()


def generate_verification_token_return_model(db: Session, user_id: str, is_used: bool = False) \
        -> models.VerificationToken:
    # Delete existing tokens first
    VerificationTokenCRUD.query(db).filter(
        models.VerificationToken.is_used is False
    ).delete(synchronize_session='fetch')
    token_schema = schema.VerificationToken(**{
        'token': re.sub(r'\W+', '', token_urlsafe(settings.VERIFICATION['TOKEN_BYTES_LENGTH'])),
        'expiration': datetime.utcnow() + timedelta(minutes=settings.VERIFICATION['TOKEN_DURATION_MINUTES']),
        'user_id': str(user_id),
        'is_used': is_used,
    })

    token = VerificationTokenCRUD.add(db, token_schema.dict())
    return token


def generate_email_six_digit_token(db: Session, user_id: str, is_used: bool = False) \
        -> models.VerificationToken:
    # Delete existing tokens first
    VerificationTokenCRUD.query(db).filter(
        models.VerificationToken.is_used is False
    ).delete(synchronize_session='fetch')
    six_digit_otp = ''.join(random.SystemRandom().choice(string.digits) for _ in range(6))
    token_schema = schema.VerificationToken(**{
        'token': six_digit_otp,
        'expiration': datetime.utcnow() + timedelta(minutes=settings.VERIFICATION['TOKEN_DURATION_MINUTES']),
        'user_id': str(user_id),
        'is_used': is_used,
    })

    token = VerificationTokenCRUD.add(db, token_schema.dict())
    return token


def get_reset_password_token(db: Session, data: schema.ResetPasswordConfirm, user_id: str) -> models.ResetPasswordToken:
    try:
        data_dict = data.dict()
        db_token = ResetPasswordTokenCRUD.query(db).filter(
            models.ResetPasswordToken.token == data_dict['token'],
            models.ResetPasswordToken.user_id == user_id,
        ).one()
        return db_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ResetPasswordToken')


def get_user_membership_extended_by_membership_id(
        db: Session,
        membership_id: str
) -> models.MembershipExtended:
    try:
        db_membership = MembershipExtendedCRUD.query(db).filter(
            models.MembershipExtended.membership_id == membership_id,
        ).one()
        return db_membership
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')


def create_user_membership_extended(db: Session, membership_extended: schema.MembershipExtended) -> models.Membership:
    """
    Creates extended necessary data for user membership
    """
    try:
        # Create extended membership
        db_membership = MembershipExtendedCRUD.add(db, membership_extended.dict())

        return db_membership
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloDuplicateMembershipError()


def get_user_manual_linking_token_by_membership_id(
        db: Session,
        migrated_member_id: str,
        target_member_id: str = None,
        is_used: bool = False
) -> models.ManualLinkingToken:
    try:
        membership_query = ManualLinkingTokenCRUD.query(db).filter(
            models.ManualLinkingToken.migrated_member_id == migrated_member_id,
            # models.ManualLinkingToken.target_member_id == target_member_id,
            models.ManualLinkingToken.is_used == is_used)
        if target_member_id:
            membership_query = membership_query.filter(
                models.ManualLinkingToken.target_member_id == target_member_id
            )
        db_membership = membership_query.one()
        return db_membership
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ManualLinkingToken')


def create_manual_linking_token(db: Session, manual_linking_token: schema.ManualLinkingToken) -> models.Membership:
    """
    Create token for manual linking
    """
    # Create token for manual linking
    try:
        ManualLinkingTokenCRUD.query(db).filter(
            models.ManualLinkingToken.is_used.is_(False),
            models.ManualLinkingToken.migrated_member_id == manual_linking_token.migrated_member_id,
            models.ManualLinkingToken.target_member_id == manual_linking_token.target_member_id
        ).delete(synchronize_session='fetch')
        db_manual_link_token = ManualLinkingTokenCRUD.add(db, manual_linking_token.dict())

        return db_manual_link_token
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloDuplicateMembershipError()


def create_dummy_verification_and_update_membership_verification(db: Session, user_id: str, membership_extended_id: str,
                                                                 is_used: bool = False) -> str:
    """
    Updates verification token for membership extended
    """
    # Update verification token
    token_schema = schema.VerificationToken(**{
        'token': re.sub(r'\W+', '', token_urlsafe(settings.VERIFICATION['TOKEN_BYTES_LENGTH'])),
        'expiration': datetime.utcnow() + timedelta(minutes=settings.VERIFICATION['TOKEN_DURATION_MINUTES']),
        'user_id': str(user_id),
        'is_used': is_used,
    })
    try:
        token = VerificationTokenCRUD.add(db, token_schema.dict())

        MembershipExtendedCRUD.update(db, membership_extended_id, {'verification_token_id': str(token.id)})
        return token_schema.token
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloDuplicateMembershipError()


def update_failed_otp_count_on_extended_membership(db: Session,
                                                   membership_extended_id: str,
                                                   reset: bool = False,
                                                   plus: int = 0) -> models.MembershipExtended:
    """
    Updates failed otp count for membership extended
    """
    try:
        if not ENABLE_RESTRICT_OTP_FAILURE:
            return None
        membership_extended = MembershipExtendedCRUD.query(db).filter(
            models.MembershipExtended.id == membership_extended_id,
        ).one()

        if reset:
            membership_extended.failed_otp_count = 0
        else:
            membership_extended.failed_otp_count = (membership_extended.failed_otp_count or 0) + plus
        db.commit()
        db.refresh(membership_extended)
        return membership_extended
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloDuplicateMembershipError()


def update_verification_token_on_extended_membership(db: Session, verification_token_id: str,
                                                     membership_extended_id: str) -> bool:
    """
    Updates verification token for membership extended
    """
    try:
        MembershipExtendedCRUD.update(db, membership_extended_id, {'verification_token_id': verification_token_id})
        return verification_token_id
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloDuplicateMembershipError()


def create_dummy_reset_password_and_update_membership_reset_password(db: Session, user_id: str,
                                                                     membership_extended_id: str,
                                                                     is_used: bool = False) -> str:
    token_schema = schema.ResetPasswordToken(**{
        'token': re.sub(r'\W+', '', token_urlsafe(settings.RESET_PASSWORD['TOKEN_BYTES_LENGTH'])),
        'expiration': datetime.utcnow() + timedelta(minutes=settings.RESET_PASSWORD['TOKEN_DURATION_MINUTES']),
        'user_id': str(user_id),
        'is_used': is_used,
    })
    try:
        token = ResetPasswordTokenCRUD.add(db, token_schema.dict())
        MembershipExtendedCRUD.update(db, membership_extended_id, {'reset_password_token_id': str(token.id)})

        return token_schema.token
    except IntegrityError:
        db.rollback()


def update_membership_extended_verification(db: Session, data: schema.MembershipExtendedVerificationMethodUpdate):
    """
    Updates verification token for membership extended
    """
    membership_extended_id = data['id']
    del data['id']
    try:
        MembershipExtendedCRUD.update(db, membership_extended_id, data)
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloDuplicateMembershipError()


# External Service for Credentials OCPI module


def register_external_service(db: Session, data: schema.SignupRequest) -> str:
    user_data = data.dict()
    password = user_data.pop('password')
    organization_id = user_data.pop('organization_id')
    first_name = user_data.pop('first_name')
    last_name = user_data.pop('last_name')
    user_id_tag = user_data.pop('user_id_tag')
    user_data['is_verified'] = True

    # Remove data that are not used for User model
    keys_to_remove = ['password_confirmation', 'invite_token']
    for key in keys_to_remove:
        del user_data[key]

    # Create external service user
    try:
        db_user = UserCRUD.add(db, user_data, check_permission=False)
        user_id = str(db_user.id)
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloUserDataError()

    # Create external token attached to external service
    try:
        external_token_data = schema.ExternalToken(
            token=str(uuid.uuid4()),
            user_id=user_id,
            is_temporary=True,
            organization_id=organization_id
        )
        external_token = ExternalTokenCRUD.add(db, external_token_data.dict())
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloExternalTokenDataError()

    # Create external service membership
    try:
        membership_data = schema.Membership(
            first_name=first_name,
            last_name=last_name,
            organization_id=organization_id,
            user_id=user_id,
            user_id_tag=user_id_tag,
            password=password
        )
        MembershipCRUD.add(db, membership_data.dict(), check_permission=False)
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloDuplicateMembershipError()
    return external_token.token


def create_token_a(db: Session, temp_token: str) -> str:
    # Create temporary token A for OCPI Credentials Module
    try:
        external_token_data = schema.ExternalToken(
            token=temp_token,
            is_temporary=True,
        )
        ExternalTokenCRUD.add(db, external_token_data.dict())
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloExternalTokenDataError()
    return temp_token


def get_external_token(external_service_token: str, db: Session) -> models.ExternalToken:
    try:
        external_token = ExternalTokenCRUD.query(db).filter(
            models.ExternalToken.external_service_token == external_service_token
        ).one()
        return external_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ExternalToken')


def get_temp_token(token: str, db: Session) -> models.ExternalToken:
    try:
        temp_token = ExternalTokenCRUD.query(db).filter(
            models.ExternalToken.token == token,
            models.ExternalToken.is_temporary.is_(False)
        ).one()
        return temp_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ExternalToken')


def get_client_token(token: str, db: Session) -> models.ExternalToken:
    try:
        client_token = ExternalTokenCRUD.query(db).filter(
            models.ExternalToken.token == token,
            models.ExternalToken.is_temporary.is_(False)
        ).one()
        return client_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ExternalToken')


def update_external_token(external_service_token: str, db: Session, data: dict):
    external_token = get_external_token(external_service_token, db)
    external_token = ExternalTokenCRUD.update(db, external_token.id, data)
    return external_token


def get_token_by_temp_token(token: str, db: Session) -> models.ExternalToken:
    try:
        external_token = ExternalTokenCRUD.query(db).filter(
            models.ExternalToken.token == token,
            models.ExternalToken.is_temporary.is_(True)
        ).one()
        return external_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ExternalToken')


def get_token_a_or_c(token: str, db: Session) -> models.ExternalToken:
    try:
        external_token = ExternalTokenCRUD.query(db).filter(
            or_(
                and_(
                    models.ExternalToken.token == token,
                    models.ExternalToken.is_temporary.is_(True)
                ),
                and_(
                    models.ExternalToken.token == token,
                    models.ExternalToken.is_temporary.is_(False)
                )
            )
        ).one()
        return external_token
    except NoResultFound:
        return None


def token_c_auth(token: str, db: Session) -> models.ExternalToken:
    try:
        token_c = ExternalTokenCRUD.query(db).filter(
            models.ExternalToken.token == token,
            models.ExternalToken.is_temporary.is_(False)
        ).one()
        return token_c
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ExternalToken')


def update_external_token_by_temp_token(token: str, db: Session, data: dict) -> models.ExternalToken:
    try:
        external_token = get_token_by_temp_token(token, db)
        external_token = ExternalTokenCRUD.update(db, external_token.id, data)
        return external_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ExternalToken')


def link_external_token_with_external_organization(db: Session, token: str, ext_org_id: str) -> models.ExternalToken:
    try:
        external_token = get_external_token(token, db)
        external_token = ExternalTokenCRUD.update(db, external_token.id, {'external_organization_id': ext_org_id})
        return external_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ExternalToken')


def create_cpo_token(db: Session, token: schema.OCPICPOToken) -> models.OCPICPOToken:
    try:
        ocpi_cpo_token = OCPICPOTokenCRUD.add(db, token.dict())
        return ocpi_cpo_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICPOToken')


def update_cpo_token(db: Session, token_id, token: schema.OCPICPOTokenUpdate) -> models.OCPICPOToken:
    try:
        db_ocpi_cpo_token = OCPICPOTokenCRUD.query(db) \
            .filter(models.OCPICPOToken.partner_ocpi_cpo_token_id == token_id).one()

        ocpi_cpo_token = OCPICPOTokenCRUD.update(db, db_ocpi_cpo_token.id, token.dict())
        return ocpi_cpo_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICPOToken')


def update_cpo_token_by_token_uuid(db: Session, token_id, token: schema.OCPICPOTokenUpdate) -> models.OCPICPOToken:
    try:
        ocpi_cpo_token = OCPICPOTokenCRUD.update(db, token_id, token.dict())
        return ocpi_cpo_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICPOToken')


def get_cpo_token(db: Session, token: str) -> models.OCPICPOToken:
    try:
        ocpi_cpo_token = OCPICPOTokenCRUD.query(db).filter(models.OCPICPOToken.partner_ocpi_cpo_token_id == token).one()
        return ocpi_cpo_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICPOToken')


def get_all_cpo_token(db: Session):
    try:
        ocpi_cpo_token = OCPICPOTokenCRUD.query(db)
        return ocpi_cpo_token
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICPOToken')


def get_all_emsp_token(db: Session):
    ocpi_cpo_token = OCPIEMSPTokenCRUD.query(db)
    return ocpi_cpo_token


def get_cpo_token_by_partner_id(db: Session, party_id: str, country_code: str, token_id: str):
    try:
        query = OCPICPOTokenCRUD.query(db)
        query = query.filter(models.OCPICPOToken.partner_ocpi_cpo_token_id == token_id,
                             models.OCPICPOToken.party_id == party_id,
                             models.OCPICPOToken.country_code == country_code)

        return query
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICPOToken')


def get_or_create_pre_auth_info_cc(db: Session, member_id: str, currency: schema.Currency) -> models.MemberPreAuthInfo:
    try:
        db_pre_auth = MemberPreAuthInfoCRUD.query(db).filter(
            models.MemberPreAuthInfo.member_id == member_id,
            models.MemberPreAuthInfo.pre_auth_currency == currency,
            models.MemberPreAuthInfo.pre_auth_type == 'Credit-Card',
        ).one()
        return db_pre_auth
    except NoResultFound:
        pre_auth_info = schema.MemberPreAuthInfo(pre_auth_currency=currency)
        pre_auth_info.pre_auth_currency = currency
        pre_auth_info.pre_auth_type = 'Credit-Card'
        if currency == 'MYR':
            pre_auth_info.pre_auth_amount = MY_PRE_AUTH_DEFAULT_AMOUNT
        elif currency == 'BND':
            pre_auth_info.pre_auth_amount = BN_PRE_AUTH_DEFAULT_AMOUNT
        elif currency == 'KHR':
            pre_auth_info.pre_auth_amount = KH_PRE_AUTH_DEFAULT_AMOUNT
        else:
            pre_auth_info.pre_auth_amount = SG_PRE_AUTH_DEFAULT_AMOUNT

        db_pre_auth_info = create_pre_auth_info(db, pre_auth_info, str(member_id))
        return db_pre_auth_info


def get_or_create_partial_pre_auth_info_cc(db: Session, member_id: str, currency: schema.Currency):
    pre_auth_info = schema.MemberPreAuthInfo(pre_auth_currency=currency)
    pre_auth_info.pre_auth_currency = currency
    pre_auth_info.pre_auth_type = 'Credit-Card'
    if currency == 'MYR':
        pre_auth_info.pre_auth_amount = MY_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT
    elif currency == 'BND':
        pre_auth_info.pre_auth_amount = BN_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT
    elif currency == 'KHR':
        pre_auth_info.pre_auth_amount = KH_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT
    else:
        pre_auth_info.pre_auth_amount = SG_PARTIAL_PRE_AUTH_DEFAULT_AMOUNT

    return pre_auth_info


def create_pre_auth_info(db: Session, pre_auth_info: schema.MemberPreAuthInfo, member_id: str) -> models.Wallet:
    try:
        db_pre_auth_info = MemberPreAuthInfoCRUD.add(db, dict(pre_auth_info.dict(), member_id=member_id))
        return db_pre_auth_info
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloWalletError()


# def get_migrated_email(db: Session, email: str, organization_id: str):
def get_migrated_user_by_email(db: Session, email: str):
    try:
        migrated_user = UserCRUD.query(db, check_permission=False).filter(
            func.lower(models.User.email) == email.lower(),
            models.User.is_migrated.is_(True),
            # models.User.organization_id == organization_id,
            models.User.is_guest.is_(False),
            models.User.migration_status.is_(False),
        ).one()
        return migrated_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def get_migrated_user_by_email_v2(db: Session, email: str):
    try:
        migrated_user = UserCRUD.query(db, check_permission=False).filter(
            func.lower(models.User.email) == email.lower(),
            models.User.is_migrated.is_(True),
            # models.User.organization_id == organization_id,
            models.User.is_guest.is_(False),
            models.User.migration_status.is_(False),
            models.User.user_type == schema.UserType.regular
        ).one()
        return migrated_user
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')


def update_user_login_attempts(db: Session, user_id: str, login_attempts: typing.List[str]):
    try:
        # Dont use MembershipCRUD.update as this action will be prevented
        db_member = UserCRUD.get(db, user_id, check_permission=False)
        db_member.login_attempts = login_attempts
        db.commit()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='User')
