import logging

from fastapi import APIRouter, Depends, Request
from fastapi_pagination import Params
import httpx
from app import settings, schema
from app.database import create_session, SessionLocal
from app.permissions import permission
from app.utils import decode_auth_token_from_headers, get_notification_info

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/notification",
    tags=['notification', ],
    dependencies=[Depends(permission)]
)


@router.get('/')
async def get_all_notification(status: str = None, params: Params = Depends(),
                               dbsession: SessionLocal = Depends(create_session)):

    status_mapping = {
        "sent": {"sent": True, "archived": False},
        "deleted": {"sent": False, "archived": True},
        "scheduled": {"sent": False, "archived": False},
        "unknown": {"sent": True, "archived": True},
    }

    sent = None
    archived = None
    if status in status_mapping:
        sent = status_mapping[status]["sent"]
        archived = status_mapping[status]["archived"]

    request_body = {
        'pageNum': params.page,
        'pageSize': params.size,
        'sent': sent,
        'archived': archived
    }

    notification_info = get_notification_info(dbsession)

    headers = {
        'x-api-key': notification_info.api_key,
        'flavor': notification_info.flavor
    }

    async with httpx.AsyncClient() as client:
        request = client.build_request(
            method="GET",
            url=f'{notification_info.domain}/chargeev-notification/schedule',
            headers=headers,
            json=request_body
        )
        response = await client.send(request)

    return response.json()


@router.get('/{notification_id}')
async def get_notification_by_id(notification_id: str,
                                 dbsession: SessionLocal = Depends(create_session)):
    notification_info = get_notification_info(dbsession)

    headers = {
        'x-api-key': notification_info.api_key,
        'flavor': notification_info.flavor
    }

    async with httpx.AsyncClient() as client:
        request = client.build_request(
            method="GET",
            url=f'{notification_info.domain}/chargeev-notification/schedule/{notification_id}',
            headers=headers,
        )
        response = await client.send(request)
    return response.json()


@router.post('/')
async def create_notification(request: Request,
                              notification_data: schema.CreateNotificationService,
                              dbsession: SessionLocal = Depends(create_session)):
    notification_info = get_notification_info(dbsession)
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    request_body = {
        'senderId': membership_id,
        'sendNow': notification_data.send_now,
        'toIos': notification_data.to_ios,
        'toAndroid': notification_data.to_android,
        'memberIds': [],  # currently hardcoded to empty list
        'title': notification_data.title,
        'description': notification_data.description,
        'remarks': notification_data.remarks
    }

    if not notification_data.send_now:
        request_body['scheduledTime'] = notification_data.scheduled_time

    headers = {
        'x-api-key': notification_info.api_key,
        'flavor': notification_info.flavor
    }

    async with httpx.AsyncClient() as client:
        request = client.build_request(
            method="POST",
            url=f'{notification_info.domain}/chargeev-notification/schedule',
            headers=headers,
            json=request_body
        )
        response = await client.send(request)
    return response.json()


@router.patch('/{notification_id}')
async def update_notification_by_id(request: Request, notification_id: str,
                                    notification_data: schema.UpdateNotificationService,
                                    dbsession: SessionLocal = Depends(create_session)):
    notification_info = get_notification_info(dbsession)
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    request_body = {
        'senderId': membership_id,
        'sendNow': notification_data.send_now,
        'toIos': notification_data.to_ios,
        'toAndroid': notification_data.to_android,
        'memberIds': [],  # currently hardcoded to empty list
        'title': notification_data.title,
        'description': notification_data.description
    }

    if not notification_data.send_now:
        request_body['scheduledTime'] = notification_data.scheduled_time

    headers = {
        'x-api-key': notification_info.api_key,
        'flavor': notification_info.flavor
    }

    async with httpx.AsyncClient() as client:
        request = client.build_request(
            method="PUT",
            url=f'{notification_info.domain}/chargeev-notification/schedule/{notification_id}',
            headers=headers,
            json=request_body
        )
        response = await client.send(request)
    return response.json()


@router.delete('/{notification_id}')
async def delete_notification_by_id(notification_id: str,
                                    dbsession: SessionLocal = Depends(create_session)):

    notification_info = get_notification_info(dbsession)
    headers = {
        'x-api-key': notification_info.api_key,
        'flavor': notification_info.flavor
    }

    async with httpx.AsyncClient() as client:
        request = client.build_request(
            method="DELETE",
            url=f'{notification_info.domain}/chargeev-notification/schedule/{notification_id}',
            headers=headers,
        )
        response = await client.send(request)
    return response.json()
