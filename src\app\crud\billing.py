from typing import List

from sqlalchemy import cast, Numeric
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, NoResultFound

from app import models, schema, exceptions, settings

from .base import BaseCRUD


class BillingCRUD(BaseCRUD):
    model = models.ChargingSessionBill


def get_charging_session_bill(db: Session, charging_session_bill_id: str) -> models.ChargingSessionBill:
    try:
        return BillingCRUD.get(db, charging_session_bill_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')


def get_charging_session_bill_ids(db: Session,
                                  charging_session_bill_ids: List[str] = None) -> models.ChargingSessionBill:
    try:
        query = BillingCRUD.query(db).filter(
            models.ChargingSessionBill.charging_session_id.in_(charging_session_bill_ids))
        return query.all()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')


def get_charging_session_bill_by_charging_session(db: Session,
                                                  charging_session_id: str) -> models.ChargingSessionBill:
    query = BillingCRUD.query(db).filter(
        models.ChargingSessionBill.charging_session_id == charging_session_id,
    )
    return query.first()


def get_charging_session_bill_list(db: Session) -> List[models.ChargingSessionBill]:
    db_charging_session_bill_list = BillingCRUD.query(db).all()
    return db_charging_session_bill_list


def get_grouped_dunning_list(db: Session, member_id):
    dunning_start_date = settings.DUNNING_START_DATE

    query = (
        db.query(
            models.PaymentRequest.currency.label("currency"),
            models.ChargingSessionBill.id.label("bill_id"),
            models.ChargingSessionBill.total_amount.label("total_amount")
        )
        .join(models.ChargingSessionBill,
              models.PaymentRequest.charging_session_bill_id == models.ChargingSessionBill.id)
        .filter(
            models.PaymentRequest.member_id == member_id,
            models.PaymentRequest.type != 'Wallet',
            models.PaymentRequest.pay_via_preauth_and_recurring.is_(False),
            models.ChargingSessionBill.status.in_([
                # schema.ChargingSessionBillStatus.pending,
                schema.ChargingSessionBillStatus.failed,
                schema.ChargingSessionBillStatus.rejected
            ]),
            models.ChargingSessionBill.total_amount >= 1.01,
            models.ChargingSessionBill.created_at >= dunning_start_date
        )
    )
    result = query.all()
    summed_data = {"total_outstanding_amount": 0, "all_outstanding_bill_ids": []}
    grouped_data = {}

    for row in result:
        # master list of dunning
        summed_data["all_outstanding_bill_ids"].append(str(row.bill_id))
        summed_data["total_outstanding_amount"] += float(row.total_amount)
        # group into currency
        currency = row.currency
        if currency not in grouped_data:
            grouped_data[currency] = {"currency": currency, "outstanding_bill_ids": [], "outstanding_amount": 0}
        grouped_data[currency]["outstanding_bill_ids"].append(str(row.bill_id))
        grouped_data[currency]["outstanding_amount"] += float(row.total_amount)

    # query for failed outstanding pre auth + recurring
    query_outstanding = (
        db.query(
            models.PaymentRequest.currency.label("currency"),
            models.ChargingSessionBill.id.label("bill_id"),
            models.PaymentRequest.pre_auth_outstanding_amount.label("total_amount")
        )
        .join(models.ChargingSessionBill,
              models.PaymentRequest.charging_session_bill_id == models.ChargingSessionBill.id)
        .filter(
            models.PaymentRequest.member_id == member_id,
            models.PaymentRequest.type != 'Wallet',
            models.PaymentRequest.pay_via_preauth_and_recurring.is_(True),
            models.PaymentRequest.pre_auth_outstanding_capture_status.in_([
                schema.PaymentRequestStatus.failed,
                schema.PaymentRequestStatus.rejected
            ]),
            cast(models.PaymentRequest.pre_auth_outstanding_amount, Numeric) >= 1.01,
            models.ChargingSessionBill.created_at >= dunning_start_date
        )
    )

    result_outstanding = query_outstanding.all()

    for row in result_outstanding:
        summed_data["all_outstanding_bill_ids"].append(str(row.bill_id))
        summed_data["total_outstanding_amount"] += float(row.total_amount)
        currency = row.currency
        if currency not in grouped_data:
            grouped_data[currency] = {"currency": currency, "outstanding_bill_ids": [], "outstanding_amount": 0}
        grouped_data[currency]["outstanding_bill_ids"].append(str(row.bill_id))
        grouped_data[currency]["outstanding_amount"] += float(row.total_amount)

    grouped_list = list(grouped_data.values())

    summed_data["dunnings"] = grouped_list
    return summed_data


def get_user_outstanding_bills(db: Session, member_id):
    dunning_start_date = settings.DUNNING_START_DATE

    outstanding_bills = (
        db.query(models.ChargingSessionBill.charging_session_id,
                 models.ChargingSessionBill.status
                 )
        .join(models.PaymentRequest)
        .filter(
            models.PaymentRequest.member_id == member_id,
            models.PaymentRequest.type != 'Wallet',
            models.ChargingSessionBill.status.in_([
                # schema.ChargingSessionBillStatus.pending,
                schema.ChargingSessionBillStatus.failed,
                schema.ChargingSessionBillStatus.rejected
            ]),
            models.ChargingSessionBill.total_amount >= 1.01,
            models.ChargingSessionBill.created_at >= dunning_start_date
        ).all()
    )
    return outstanding_bills


def create_charging_session_bill(db: Session,
                                 charging_session_bill_data: schema.ChargingSessionBill
                                 ) -> models.ChargingSessionBill:
    try:
        db_charging_session_bill = BillingCRUD.add(db, charging_session_bill_data.dict())
        return db_charging_session_bill
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloChargingBillError()


def charging_bill_done(db: Session, charging_session_bill_id: str) -> models.ChargingSessionBill:
    try:
        db_charging_session_bill = db.query(models.ChargingSessionBill).filter(
            models.ChargingSessionBill.id == charging_session_bill_id).one()
        db_charging_session_bill.status = schema.ChargingSessionBillStatus.paid
        db.commit()
        return db_charging_session_bill
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')


def update_charging_bill_outstanding(db: Session, charging_session_bill_id: str,
                                     outstanding_balance: float) -> models.ChargingSessionBill:
    try:
        db_charging_session_bill = db.query(models.ChargingSessionBill).filter(
            models.ChargingSessionBill.id == charging_session_bill_id).one()
        db_charging_session_bill.outstanding_balance = outstanding_balance
        db.commit()
        return db_charging_session_bill
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')


def charging_bill_free(db: Session, charging_session_bill_id: str) -> models.ChargingSessionBill:
    try:
        db_charging_session_bill = db.query(models.ChargingSessionBill).filter(
            models.ChargingSessionBill.id == charging_session_bill_id).one()
        db_charging_session_bill.status = schema.ChargingSessionBillStatus.free
        db.commit()
        return db_charging_session_bill
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')


def charging_bill_failed(db: Session, charging_session_bill_id: str) -> models.ChargingSessionBill:
    try:
        db_charging_session_bill = db.query(models.ChargingSessionBill).filter(
            models.ChargingSessionBill.id == charging_session_bill_id).one()
        db_charging_session_bill.status = schema.ChargingSessionBillStatus.failed
        db.commit()
        return db_charging_session_bill
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')


def charging_bill_rejected(db: Session, charging_session_bill_id: str) -> models.ChargingSessionBill:
    try:
        db_charging_session_bill = db.query(models.ChargingSessionBill).filter(
            models.ChargingSessionBill.id == charging_session_bill_id).one()
        db_charging_session_bill.status = schema.ChargingSessionBillStatus.rejected
        db.commit()
        return db_charging_session_bill
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')


def charging_bill_pending(db: Session, charging_session_bill_id: str) -> models.ChargingSessionBill:
    try:
        db_charging_session_bill = db.query(models.ChargingSessionBill).filter(
            models.ChargingSessionBill.id == charging_session_bill_id).one()
        db_charging_session_bill.status = schema.ChargingSessionBillStatus.pending
        db.commit()
        return db_charging_session_bill
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')


def delete_charging_session_bill(db: Session, charging_session_bill_id: str) -> models.ChargingSessionBill:
    try:
        BillingCRUD.delete(db, charging_session_bill_id)
        return charging_session_bill_id
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')


def update_charging_session_bill(db: Session,
                                 charging_session_bill_data: dict,
                                 charging_session_bill_id: str) -> models.ChargingSessionBill:
    try:
        db_charging_session_bill = BillingCRUD.update(
            db,
            charging_session_bill_id,
            charging_session_bill_data.dict(exclude_unset=True, exclude_defaults=True)
        )
        return db_charging_session_bill
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')
