"""177

Revision ID: ebdcc4c5175b
Revises: ba3896e5bf21
Create Date: 2025-06-05 14:29:07.227312

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ebdcc4c5175b'
down_revision = 'ba3896e5bf21'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_e_credit_note', sa.Column('e_cn_running_number', sa.Integer(), nullable=True))
    op.create_unique_constraint('e_cn_unique_rn', 'main_e_credit_note', ['e_cn_running_number'])
    op.execute(sa.schema.CreateSequence(sa.schema.Sequence('e_cn_running_number_sequence', start=10000, increment=1)))
    op.alter_column("main_e_credit_note", "e_cn_running_number",
                    server_default=sa.text("nextval('e_cn_running_number_sequence'::regclass)"))

    op.add_column('main_e_invoice', sa.Column('e_invoice_running_number', sa.Integer(), nullable=True))
    op.create_unique_constraint('e_invoice_unique_rn', 'main_e_invoice', ['e_invoice_running_number'])
    op.execute(sa.schema.CreateSequence(sa.schema.Sequence('e_invoice_running_number_sequence', start=10000, increment=1)))
    op.alter_column("main_e_invoice", "e_invoice_running_number",
                    server_default=sa.text("nextval('e_invoice_running_number_sequence'::regclass)"))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('e_invoice_unique_rn', 'main_e_invoice', type_='unique')
    op.drop_column('main_e_invoice', 'e_invoice_running_number')
    op.execute(sa.schema.DropSequence(sa.schema.Sequence('e_invoice_running_number_sequence')))

    op.drop_constraint('e_cn_unique_rn', 'main_e_credit_note', type_='unique')
    op.drop_column('main_e_credit_note', 'e_cn_running_number')
    op.execute(sa.schema.DropSequence(sa.schema.Sequence('e_cn_running_number_sequence')))
    # ### end Alembic commands ###
