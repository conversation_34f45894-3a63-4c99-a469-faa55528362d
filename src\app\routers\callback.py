import logging
from typing import List

from fastapi import APIRouter, Depends, status

from app import settings, schema, crud
from app.database import create_session, SessionLocal
from app.permissions import permission

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH


router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/callback",
    tags=['callback', ],
    dependencies=[Depends(permission)],
)


@router.get("/", response_model=List[schema.CallbackResponse],
            status_code=status.HTTP_200_OK)
async def get_callback_list(dbsession: SessionLocal = Depends(create_session)):
    """
    Get Callbacks List
    """
    db_callback_list = crud.get_callback_list(dbsession)
    return db_callback_list
