"""43

Revision ID: 9c520e23fb39
Revises: 0a3e1e9837f7
Create Date: 2023-07-06 14:24:00.066563

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '9c520e23fb39'
down_revision = '0a3e1e9837f7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_tax_rate',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>an(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('country', sa.String(), nullable=True),
    sa.Column('tax_rate', sa.Numeric(scale=2), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('main_charging_session_bill', sa.Column('tax_amount', sa.Numeric(scale=2), nullable=True))
    op.add_column('main_charging_session_bill', sa.Column('tax_rate', sa.Numeric(scale=2), nullable=True))
    op.add_column('main_operator', sa.Column('is_taxable', sa.Boolean(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_operator', 'is_taxable')
    op.drop_column('main_charging_session_bill', 'tax_rate')
    op.drop_column('main_charging_session_bill', 'tax_amount')
    op.drop_table('main_tax_rate')
    # ### end Alembic commands ###
