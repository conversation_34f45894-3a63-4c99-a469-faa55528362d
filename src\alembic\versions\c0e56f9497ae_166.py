"""166

Revision ID: c0e56f9497ae
Revises: 19c7098f8dd6
Create Date: 2025-05-15 23:12:35.604168

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c0e56f9497ae'
down_revision = '19c7098f8dd6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_e_invoice',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('charging_session_id', sa.String(), nullable=True),
    sa.Column('charging_session_bill_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('last_submission_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_rejection_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status_timeline', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['charging_session_bill_id'], ['main_charging_session_bill.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('charging_session_bill_id')
    )
    op.create_table('main_e_invoice_reqeust',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('e_invoice_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['e_invoice_id'], ['main_e_invoice.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_e_credit_note',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('payment_refund_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('last_submission_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_rejection_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status_timeline', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['payment_refund_id'], ['main_payment_refund.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('payment_refund_id')
    )
    op.create_table('main_e_credit_note_request',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('e_credit_note_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['e_credit_note_id'], ['main_e_credit_note.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_e_credit_note_request')
    op.drop_table('main_e_credit_note')
    op.drop_table('main_e_invoice_reqeust')
    op.drop_table('main_e_invoice')
    # ### end Alembic commands ###
