"""48

Revision ID: 9ef8e81d00ca
Revises: bb76dac721ab
Create Date: 2023-08-04 10:50:38.531383

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '9ef8e81d00ca'
down_revision = 'bb76dac721ab'
branch_labels = None
depends_on = None



def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_wallet_package',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('feature_description', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('price', sa.Numeric(scale=2), nullable=True),
    sa.Column('credit_amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('is_redeemable', sa.Boolean(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_private', sa.Boolean(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['main_organization.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_wallet_package_invitation',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('linked_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('discount_amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('wallet_package_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('wallet_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['wallet_id'], ['main_wallet.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['wallet_package_id'], ['main_wallet_package.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('main_wallet_package_order',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('discount_amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('credit_amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('order_id', sa.Integer(), nullable=False),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('wallet_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('wallet_package_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('package_meta', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['wallet_id'], ['main_wallet.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['wallet_package_id'], ['main_wallet_package.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('order_id')
    )
    op.create_table('main_wallet_transaction',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('remarks', sa.String(), nullable=True),
    sa.Column('charging_session_id', sa.String(), nullable=True),
    sa.Column('transaction_type', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('package_meta', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('wallet_package_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('wallet_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('transaction_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['wallet_id'], ['main_wallet.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['wallet_package_id'], ['main_wallet_package.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.execute(sa.schema.CreateSequence(sa.schema.Sequence('wallet_package_order_sequence_id', start=10000, increment=1)))
    op.alter_column("main_wallet_package_order", "order_id", nullable=False,
                    server_default=sa.text("nextval('wallet_package_order_sequence_id'::regclass)"))

    op.execute(sa.schema.CreateSequence(sa.schema.Sequence('wallet_transaction_id_sequence_id', start=10000, increment=1)))
    op.alter_column("main_wallet_transaction", "transaction_id", nullable=False,
                    server_default=sa.text("nextval('wallet_transaction_id_sequence_id'::regclass)"))
    op.add_column('main_wallet_transaction', sa.Column('description', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_wallet_transaction')
    op.drop_table('main_wallet_package_order')
    op.drop_table('main_wallet_package_invitation')
    op.drop_table('main_wallet_package')
    # ### end Alembic commands ###