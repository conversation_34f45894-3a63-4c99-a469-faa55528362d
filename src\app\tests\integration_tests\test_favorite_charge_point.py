import uuid
import urllib
from datetime import datetime, timedelta
from contextlib import contextmanager

import jwt
import pytest
from fastapi.testclient import TestClient

from app import settings, schema
from app.main import app, ROOT_PATH
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory, CreditCardFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory,
                                 IDTagFactory)
from app.database import SessionLocal, create_session, Base, engine


client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        IDTagFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


FAVORITE_CP_BASE_URL = f'{ROOT_PATH}/api/v1/csms/favorite_cp'
CHARGE_POINT_ID_1 = str(uuid.uuid4())
CHARGE_POINT_ID_2 = str(uuid.uuid4())


def test_add_favorite_cp(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{FAVORITE_CP_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        params = {
            'charge_point_id': CHARGE_POINT_ID_1
        }
        url = f'{FAVORITE_CP_BASE_URL}/?' + urllib.parse.urlencode(params)

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 201
        assert response.json() == [CHARGE_POINT_ID_1]


def test_get_favorite_cp_list(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            favorite_charge_points=[CHARGE_POINT_ID_1, CHARGE_POINT_ID_2]
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{FAVORITE_CP_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{FAVORITE_CP_BASE_URL}/'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 200
        assert response.json() == [CHARGE_POINT_ID_1, CHARGE_POINT_ID_2]


def test_remove_favorite_cp(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            favorite_charge_points=[CHARGE_POINT_ID_1, CHARGE_POINT_ID_2]
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{FAVORITE_CP_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        url = f'{FAVORITE_CP_BASE_URL}/{CHARGE_POINT_ID_1}'

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        response = client.delete(url, headers={'authorization': token})
        assert response.status_code == 200
        assert response.json() == [CHARGE_POINT_ID_2]
