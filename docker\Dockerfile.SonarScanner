# Install required tools
FROM tiangolo/uvicorn-gunicorn-fastapi:python3.9

RUN pip3 install pipenv==2024.2.0

# Install Java 17 (ensure that it is Java 17 being installed)
RUN apt-get update -qq \
    && apt-get install --no-install-recommends -yq openjdk-17-jdk \
    && update-alternatives --install /usr/bin/java java /usr/lib/jvm/java-17-openjdk-amd64/bin/java 1 \
    && update-alternatives --set java /usr/lib/jvm/java-17-openjdk-amd64/bin/java \
    && java -version

ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
ENV PATH="${JAVA_HOME}/bin:${PATH}"

# Download and install SonarScanner
RUN apt-get install -y wget unzip \
    && wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-6.2.1.4610-linux-x64.zip -O /tmp/sonar-scanner.zip \
    && unzip /tmp/sonar-scanner.zip -d /opt/sonar-scanner \
    && rm /tmp/sonar-scanner.zip

# Ensure the sonar-scanner binary is executable
RUN chmod +x /opt/sonar-scanner/sonar-scanner-6.2.1.4610-linux-x64/bin/sonar-scanner

# Set SonarScanner environment variables
ENV SONAR_SCANNER_HOME=/opt/sonar-scanner/sonar-scanner-6.2.1.4610-linux-x64
ENV PATH=$SONAR_SCANNER_HOME/bin:$PATH

# Verify installation
RUN sonar-scanner --version

# Add a new non-root user 
RUN useradd -u 1000 apollo

# Set the ownership of the working directory to the non-root user
RUN chown -R apollo:apollo /app
RUN chown -R apollo:apollo /home

# -- Adding Pipfiles
COPY ./src/Pipfile Pipfile
COPY ./src/Pipfile.lock Pipfile.lock
RUN touch sonar-project.properties && touch .coveragerc
# -- Install dependencies:
RUN set -ex && pipenv install --system --dev --ignore-pipfile

# Copy the custom Gunicorn configuration
COPY ./src/custom_gunicorn_conf.py /gunicorn_conf.py
COPY ./src/custom_start.sh /start.sh

RUN chmod +x /start.sh

COPY ./src /app
RUN chown -R apollo:apollo /app

# Change to non-root privilege
USER apollo
