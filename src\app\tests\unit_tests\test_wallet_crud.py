import json # noqa
import uuid
from unittest.mock import patch
from datetime import datetime, timedelta
from contextlib import contextmanager

import pytest

from app import schema, crud
from app.database import SessionLocal, Base, engine
from app.tests.factories import (PromoCodeFactory, UserFactory, MembershipFactory, OrganizationFactory,
                                 CreditCardFactory, IDTagFactory, WalletFactory, PaymentRequestFactory,
                                 OrganizationPromoCodeFactory)


CHARGE_POINT_ID = str(uuid.uuid4())
CONNECTOR_ID = str(uuid.uuid4())


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    # Base.metadata.drop_all(bind=engine)


def create_db_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        IDTagFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        WalletFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PaymentRequestFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PromoCodeFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationPromoCodeFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


@patch('app.crud.base.BaseCRUD.membership')
def test_wallet_balance_deposit_only(membership_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        PaymentRequestFactory(
            amount='10.50',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 30.5


@patch('app.crud.base.BaseCRUD.membership')
def test_wallet_balance_deposit_and_payment(membership_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        PaymentRequestFactory(
            amount='10.50',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        PaymentRequestFactory(
            amount='9.00',
            type=schema.PaymentRequestType.wallet,
            billing_description='Charging Payment',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            status=schema.PaymentRequestStatus.done,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 21.5


@patch('app.crud.base.BaseCRUD.membership')
def test_wallet_balance_deposit_and_payment_and_promo(membership_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        PaymentRequestFactory(
            amount='10.50',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        promo_code = PromoCodeFactory(
            start_at=str(datetime.now() - timedelta(days=1)),
            expire_at=str(datetime.now() + timedelta(days=1)),
            type=schema.PromoType.amount,
            amount=5.0,
        )
        db.commit()

        OrganizationPromoCodeFactory(
            organization_id=organization_id,
            promo_code_id=str(promo_code.id)
        )
        db.commit()

        promo_usage = crud.create_promo_usage(db, promo_code.code, membership_id,
                                              CHARGE_POINT_ID, '5.00')
        PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.wallet,
            billing_description='Charging Payment',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            promo_usage_id=promo_usage.id,
            status=schema.PaymentRequestStatus.done,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 25.5
