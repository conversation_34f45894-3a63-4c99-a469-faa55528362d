import sqlalchemy as db
from sqlalchemy.dialects.postgresql import UUI<PERSON>, JSONB

from app.models.base import BaseModel

from app.schema import VerificationMethodEnum

metadata = BaseModel.metadata

membership_role_association_table = db.Table(
    'main_user_vehicle',
    BaseModel.metadata,
    db.Column('main_auth_user_id', db.<PERSON><PERSON>('main_auth_user.id', ondelete='CASCADE')),
    db.Column('main_vehicle_id', db.<PERSON>ey('main_vehicle.id', ondelete='CASCADE')),
)


class User(BaseModel):
    __tablename__ = 'main_auth_user'

    email = db.Column(db.String)
    phone_number = db.Column(db.String)

    is_superuser = db.Column(db.Boolean, default=False)

    is_verified = db.Column(db.Boolean, default=False)
    verification_method = db.Column(db.String, default=VerificationMethodEnum.phone)

    tokens = db.orm.relationship('VerificationToken')

    organization_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('main_organization.id', ondelete='CASCADE'))

    is_guest = db.Column(db.Boolean, default=False)
    guest_app_id = db.Column(db.String)

    is_migrated = db.Column(db.Boolean, default=False)
    migration_status = db.Column(db.Boolean, default=False)
    # external_token = db.orm.relationship('ExternalToken')
    login_attempts = db.Column(JSONB, default=[])
    migrated_date = db.Column(db.DateTime(timezone=True))
    link_date = db.Column(db.DateTime(timezone=True))
    user_type = db.Column(db.String)

    mfa_enabled = db.Column(db.Boolean, default=False)
    mfa_secret = db.Column(db.String, nullable=True)  # Permanent TOTP secret
    temp_mfa_secret = db.Column(db.String, nullable=True)  # Temporary secret used during setup
    temp_mfa_secret_created_at = db.Column(db.DateTime, nullable=True)


db.Index('unique_phone_number_organization_id_guest_app_id', User.phone_number, User.organization_id, User.user_type,
         User.is_deleted, unique=True, postgresql_where=(~User.is_deleted & ~User.is_guest))
db.Index('unique_email_organization_id_guest_app_id', User.email, User.organization_id, User.user_type,
         User.is_deleted, unique=True, postgresql_where=(~User.is_deleted & ~User.is_guest))
db.Index('unique_guest_app_id', User.guest_app_id, User.organization_id,
         User.is_deleted, unique=True,
         postgresql_where=~User.is_deleted)


# db.Index('unique_user_email', User.email, User.is_deleted,
#          unique=True, postgresql_where=~User.is_deleted)


class VerificationToken(BaseModel):
    __tablename__ = 'main_auth_token'
    token = db.Column(db.String)
    expiration = db.Column(db.DateTime(timezone=True))
    is_used = db.Column(db.Boolean, default=False)

    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_auth_user.id'))


class ExternalToken(BaseModel):
    __tablename__ = 'main_auth_external_token'

    token = db.Column(db.String, nullable=False)
    is_temporary = db.Column(db.Boolean, default=True)
    version = db.Column(db.String)
    endpoints = db.Column(JSONB, default={})
    url = db.Column(db.String)
    external_service_token = db.Column(db.String)
    push_notification = db.Column(db.Boolean, default=True)
    is_publish_all = db.Column(db.Boolean, default=False)
    standardized_tariff = db.Column(db.Boolean, default=True)
    pnc_friendly = db.Column(db.Boolean, default=False)

    external_organization_id = db.Column(UUID(as_uuid=True),
                                         db.ForeignKey('main_external_organization.id', ondelete='CASCADE'))
    external_organization = db.orm.relationship('ExternalOrganization', passive_deletes=True)

    name = db.Column(db.String, nullable=True)

    is_lta = db.Column(db.Boolean, default=False)


db.Index('unique_external_service_token', ExternalToken.external_service_token, ExternalToken.is_deleted,
         unique=True, postgresql_where=~ExternalToken.is_deleted)


class ResetPasswordToken(BaseModel):
    __tablename__ = 'main_reset_password_token'
    token = db.Column(db.String)
    expiration = db.Column(db.DateTime(timezone=True))
    is_used = db.Column(db.Boolean, default=False)

    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_auth_user.id'))


class Invite(BaseModel):
    __tablename__ = 'main_invite'

    token = db.Column(db.String)

    # token creator
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_auth_user.id'))

    accepted_invite_count = db.Column(db.Integer, default=0)
    accepted_invites = db.orm.relationship('AcceptedInvite', back_populates='invite')


db.Index('unique_invite_token', Invite.token, Invite.is_deleted,
         unique=True, postgresql_where=~Invite.is_deleted)
db.Index('unique_invite_user_id', Invite.user_id, Invite.is_deleted,
         unique=True, postgresql_where=~Invite.is_deleted)


class AcceptedInvite(BaseModel):
    __tablename__ = 'main_accepted_invite'

    # user who accepted the invite
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_auth_user.id'))

    invite_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_invite.id'))
    invite = db.orm.relationship('Invite', back_populates='accepted_invites')


class Label(BaseModel):
    __tablename__ = 'main_label'

    name = db.Column(db.String)


class IDTag(BaseModel):
    __tablename__ = 'main_id_tag'

    id_tag = db.Column(db.String, nullable=False)
    parent_id_tag = db.orm.relationship('IDTag')
    name = db.Column(db.String)
    type = db.Column(db.String)
    is_external = db.Column(db.Boolean, default=False)
    expiration = db.Column(db.DateTime(timezone=True))
    is_active = db.Column(db.Boolean, default=True)
    member = db.orm.relationship('Membership', backref='id_tags')
    organization_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_organization.id'))
    description = db.Column(db.Text)
    meta = db.Column(JSONB, default={})

    member_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_membership.id', ondelete='CASCADE'))
    parent_id_tag_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_id_tag.id', ondelete='CASCADE'))

    emaid = db.orm.relationship('Emaid', back_populates='id_tag')
    emaid_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_emaid.id', ondelete='CASCADE'))
    autocharge = db.orm.relationship('Autocharge', back_populates='id_tag')
    autocharge_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_autocharge.id', ondelete='CASCADE'))


db.Index('unique_id_tag_id_tag', db.func.lower(IDTag.id_tag), IDTag.is_deleted,
         unique=True, postgresql_where=~IDTag.is_deleted)


# db.Index('', db.func.lower(IDTag.id_tag), IDTag.is_deleted,
#          unique=True, postgresql_where=~IDTag.is_deleted)


class OCPICdr(BaseModel):
    __tablename__ = 'main_ocpi_cdr'

    country_code = db.Column(db.String)
    party_id = db.Column(db.String)
    uid = db.Column(db.String)

    type = db.Column(db.String)
    contract_id = db.Column(db.String)


class OCPIToken(BaseModel):
    __tablename__ = 'main_ocpi_token'

    country_code = db.Column(db.String)
    party_id = db.Column(db.String)
    type = db.Column(db.String)
    contract_id = db.Column(db.String)
    visual_number = db.Column(db.String)
    issuer = db.Column(db.String)
    group_id = db.Column(db.String)
    valid = db.Column(db.Boolean, default=True)
    whitelist = db.Column(db.String, default='ALWAYS')
    language = db.Column(db.String)
    default_profile_type = db.Column(db.String)
    energy_contract = db.Column(JSONB, default={})

    member = db.orm.relationship('Membership', backref='ocpi_token')
    description = db.Column(db.Text)
    meta = db.Column(JSONB, default={})

    member_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_membership.id', ondelete='CASCADE'))


class OCPICPOToken(BaseModel):
    __tablename__ = 'main_ocpi_cpo_token'

    partner_ocpi_cpo_token_id = db.Column(db.String)
    country_code = db.Column(db.String)
    party_id = db.Column(db.String)
    type = db.Column(db.String)
    contract_id = db.Column(db.String)
    visual_number = db.Column(db.String)
    issuer = db.Column(db.String)
    group_id = db.Column(db.String)
    valid = db.Column(db.Boolean, default=True)
    whitelist = db.Column(db.String, default='ALWAYS')
    language = db.Column(db.String)
    default_profile_type = db.Column(db.String)
    energy_contract = db.Column(JSONB, default={})

    description = db.Column(db.Text)
    meta = db.Column(JSONB, default={})


class IDTagAuthorizationHistories(BaseModel):
    __tablename__ = 'main_id_tag_auth_histories'

    id_tag = db.Column(db.String)
    result = db.Column(db.String)
    result_reason = db.Column(db.String)
    connector_id = db.Column(db.String)
    request_type = db.Column(db.String)
