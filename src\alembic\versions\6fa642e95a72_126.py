"""126

Revision ID: 6fa642e95a72
Revises: 9ba2895a8646
Create Date: 2024-10-16 21:21:34.583146

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6fa642e95a72'
down_revision = '9ba2895a8646'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_autocharge',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>olean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('mac_address', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('expiry', sa.DateTime(timezone=True), nullable=True),
    sa.Column('vehicle_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['vehicle_id'], ['main_vehicle.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_autocharge')
    # ### end Alembic commands ###
