"""105

Revision ID: a15fd74d0dd2
Revises: 9362049066ef
Create Date: 2024-06-06 20:56:14.138748

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a15fd74d0dd2'
down_revision = '9362049066ef'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('_unique_operator_user', 'main_user_access_operator', type_='unique')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('_unique_operator_user', 'main_user_access_operator', ['membership_id', 'operator_id'])
    # ### end Alembic commands ###
