"""66

Revision ID: 96f7e00a6fbd
Revises: 9fd7e129b137
Create Date: 2023-12-29 14:35:40.888771

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '96f7e00a6fbd'
down_revision = '9fd7e129b137'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_operator_power_cable',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('power_cable_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('operator_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['operator_id'], ['main_operator.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('power_cable_id', 'operator_id', name='_unique_operator_power_cable')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_operator_power_cable')
    # ### end Alembic commands ###
