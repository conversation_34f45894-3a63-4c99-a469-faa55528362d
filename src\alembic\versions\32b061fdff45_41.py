"""41

Revision ID: 32b061fdff45
Revises: ff97c29d313f
Create Date: 2023-06-15 22:28:51.521895

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '32b061fdff45'
down_revision = 'ff97c29d313f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_external_organization',
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('party_id', sa.String(length=3), nullable=False),
    sa.Column('country_code', sa.String(length=2), nullable=False),
    sa.Column('images', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('website', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('meta', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('whatsapp_number', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_ocpi_cdr',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('party_id', sa.String(), nullable=True),
    sa.Column('uid', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('contract_id', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_ocpi_cpo_cdr',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('party_id', sa.String(), nullable=True),
    sa.Column('partner_ocpi_cpo_cdr_id', sa.String(), nullable=True),
    sa.Column('start_date_time', sa.DateTime(), nullable=True),
    sa.Column('end_date_time', sa.DateTime(), nullable=True),
    sa.Column('session_id', sa.String(), nullable=True),
    sa.Column('cdr_token', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('auth_method', sa.String(), nullable=True),
    sa.Column('authorization_reference', sa.String(), nullable=True),
    sa.Column('cdr_location', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('meter_id', sa.String(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('tariffs', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('charging_periods', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('signed_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('total_cost', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('total_fixed_cost', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('total_energy', sa.String(), nullable=True),
    sa.Column('total_energy_cost', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('total_time', sa.String(), nullable=True),
    sa.Column('total_time_cost', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('total_parking_time', sa.String(), nullable=True),
    sa.Column('total_parking_cost', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('total_reservation_cost', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('remark', sa.String(), nullable=True),
    sa.Column('invoice_reference_id', sa.String(), nullable=True),
    sa.Column('credit', sa.Boolean(), nullable=True),
    sa.Column('credit_reference_id', sa.String(), nullable=True),
    sa.Column('home_charging_compensation', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_ocpi_cpo_token',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('partner_ocpi_cpo_token_id', sa.String(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('party_id', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('contract_id', sa.String(), nullable=True),
    sa.Column('visual_number', sa.String(), nullable=True),
    sa.Column('issuer', sa.String(), nullable=True),
    sa.Column('group_id', sa.String(), nullable=True),
    sa.Column('valid', sa.Boolean(), nullable=True),
    sa.Column('whitelist', sa.String(), nullable=True),
    sa.Column('language', sa.String(), nullable=True),
    sa.Column('default_profile_type', sa.String(), nullable=True),
    sa.Column('energy_contract', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('meta', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_ocpi_token',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('party_id', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('contract_id', sa.String(), nullable=True),
    sa.Column('visual_number', sa.String(), nullable=True),
    sa.Column('issuer', sa.String(), nullable=True),
    sa.Column('group_id', sa.String(), nullable=True),
    sa.Column('valid', sa.Boolean(), nullable=True),
    sa.Column('whitelist', sa.String(), nullable=True),
    sa.Column('language', sa.String(), nullable=True),
    sa.Column('default_profile_type', sa.String(), nullable=True),
    sa.Column('energy_contract', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('meta', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('main_auth_external_token', sa.Column('external_organization_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.drop_constraint('main_auth_external_token_organization_id_fkey', 'main_auth_external_token', type_='foreignkey')
    op.drop_constraint('main_auth_external_token_user_id_fkey', 'main_auth_external_token', type_='foreignkey')
    op.create_foreign_key(None, 'main_auth_external_token', 'main_external_organization', ['external_organization_id'], ['id'], ondelete='CASCADE')
    op.drop_column('main_auth_external_token', 'organization_id')
    op.drop_column('main_auth_external_token', 'user_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_auth_external_token', sa.Column('user_id', postgresql.UUID(), autoincrement=False, nullable=True))
    op.add_column('main_auth_external_token', sa.Column('organization_id', postgresql.UUID(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'main_auth_external_token', type_='foreignkey')
    op.create_foreign_key('main_auth_external_token_user_id_fkey', 'main_auth_external_token', 'main_auth_user', ['user_id'], ['id'])
    op.create_foreign_key('main_auth_external_token_organization_id_fkey', 'main_auth_external_token', 'main_organization', ['organization_id'], ['id'], ondelete='CASCADE')
    op.drop_column('main_auth_external_token', 'external_organization_id')
    op.drop_table('main_ocpi_token')
    op.drop_table('main_ocpi_cpo_token')
    op.drop_table('main_ocpi_cpo_cdr')
    op.drop_table('main_ocpi_cdr')
    op.drop_table('main_external_organization')
    # ### end Alembic commands ###
