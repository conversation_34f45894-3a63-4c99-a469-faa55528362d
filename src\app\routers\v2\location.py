import logging
from typing import Union

from fastapi.responses import J<PERSON>NResponse
from fastapi.security.api_key import API<PERSON>eyHeader
from fastapi import APIRouter, Depends, Request, status, Security
from app import settings, crud, schema
from app.database import create_session, SessionLocal
from app.middlewares import set_admin_as_context_user
from app.permissions import permission, x_api_key
from app.utils import decode_auth_token_from_headers, \
    generate_charger_header_unauthorized, send_request, RouteErrorHandler, location_merge_operator_details, safe_get, \
    generate_charger_header, decode_token_type_from_headers, generate_superuser_headers, \
    apply_subscription_discount_connector, calculate_tax_amount, append_specific_operator_to_headers

logger = logging.getLogger(__name__)

API_KEY_NAME = "x-api-key"
SID_KEY_NAME = "x-api-sid"

api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)
api_sid_header = APIKeyHeader(name=SID_KEY_NAME, auto_error=False)

ROOT_PATH = settings.MAIN_ROOT_PATH

CHARGER_URL_V2_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/location",
    tags=['v2 location', ],
    dependencies=[Depends(permission), Depends(x_api_key)],
    route_class=RouteErrorHandler
)


@router.get("/", status_code=status.HTTP_200_OK)
async def get_native_location_by_distance(request: Request, longitude: float,
                                          latitude: float, distance: float,
                                          sort_by: Union[str, None] = 'distance',  # noqa
                                          api_sid_header: str = Security(api_sid_header),
                                          dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    if membership_id:
        headers = generate_charger_header(dbsession, membership_id)
    else:
        headers = generate_charger_header_unauthorized(dbsession, api_sid_header)
    request_data = dict(request.query_params)
    discounted = request_data.get('discounted')

    if discounted:
        connector = crud.get_discounted_connector(dbsession, membership_id)
        discount_type = connector[0].get('discount_type')
        connector_id = connector[0].get('connector')

        # this code is for checking custom plan, if custom plan exist then will add the connector id to param
        if len(connector_id) > 0:
            connector_id_list = "".join([str("connector_id=" + str(item) + "&") for item in connector_id])
            path = f'location/distance/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}&' \
                   f'distance={distance}&{connector_id_list}&discount_type={discount_type}&{request.query_params}'
        else:
            path = f'location/distance/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}&' \
                   f'distance={distance}&discount_type={discount_type}&{request.query_params}'

    else:
        path = f'location/distance/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}&distance={distance}&' \
               f'{request.query_params}'

    url = f'{CHARGER_URL_V2_PREFIX}/{path}'
    response = await send_request('GET', url, headers=headers)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.get("/marker/", status_code=status.HTTP_200_OK)
async def get_native_location_simplified(request: Request, longitude: float,
                                         latitude: float, distance: float,
                                         sort_by: Union[str, None] = 'distance',  # noqa
                                         api_sid_header: str = Security(api_sid_header),
                                         dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    if membership_id:
        headers = generate_charger_header(dbsession, membership_id)
    else:
        headers = generate_charger_header_unauthorized(dbsession, api_sid_header)

    request_data = dict(request.query_params)
    discounted = request_data.get('discounted')

    if discounted:
        connector = crud.get_discounted_connector(dbsession, membership_id)
        discount_type = connector[0].get('discount_type')
        connector_id = connector[0].get('connector')

        # this code is for checking custom plan, if custom plan exist then will add the connector id to param
        if len(connector_id) > 0:
            connector_id_list = "".join([str("connector_id=" + str(item) + "&") for item in connector_id])
            path = f'location/marker/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}&' \
                   f'distance={distance}&{connector_id_list}&discount_type={discount_type}&{request.query_params}'
        else:
            path = f'location/marker/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}&' \
                   f'distance={distance}&discount_type={discount_type}&{request.query_params}'

    else:
        path = f'location/marker/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}&distance={distance}&' \
               f'{request.query_params}'

    url = f'{CHARGER_URL_V2_PREFIX}/{path}'
    response = await send_request('GET', url, headers=headers)
    return JSONResponse(response.json(), status_code=response.status_code)


@router.get("/ocpi", status_code=status.HTTP_200_OK)
async def get_all_location_by_distance(request: Request,  # pylint: disable-all, # noqa:MC0001
                                       longitude: float,
                                       latitude: float, distance: float,
                                       sort_by: Union[str, None] = 'distance',  # noqa
                                       api_sid_header: str = Security(api_sid_header),
                                       dbsession: SessionLocal = Depends(create_session)):
    set_admin_as_context_user(dbsession)
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    is_webhook = decode_token_type_from_headers(request.headers)
    if membership_id:
        headers = generate_charger_header(dbsession, membership_id)
    else:
        headers = generate_charger_header_unauthorized(dbsession, api_sid_header)

    # Special handling for smartEV App Handling
    if is_webhook is not None and is_webhook == 'webhook-event':
        try:
            headers = append_specific_operator_to_headers(dbsession, headers, operator_name='YGT Trial')
        except Exception as e:  # pylint: disable=broad-except
            logger.error("Failing to update specific operator to headers %s", str(e))

    request_data = dict(request.query_params)
    discounted = request_data.get('discounted')

    if discounted:
        connector = crud.get_discounted_connector(dbsession, membership_id)
        discount_type = connector[0].get('discount_type')
        connector_id = connector[0].get('connector')

        # this code is for checking custom plan, if custom plan exist then will add the connector id to param
        if len(connector_id) > 0:
            connector_id_list = "".join([str("connector_id=" + str(item) + "&") for item in connector_id])
            path = f'location/ocpi/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}&' \
                   f'distance={distance}&{connector_id_list}&discount_type={discount_type}&{request.query_params}'
        else:
            path = f'location/ocpi/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}&' \
                   f'distance={distance}&discount_type={discount_type}&{request.query_params}'

    else:
        path = f'location/ocpi/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}&distance={distance}&' \
               f'{request.query_params}'

    url = f'{CHARGER_URL_V2_PREFIX}/{path}'
    response = await send_request('GET', url, headers=headers)
    response_json = response.json()
    try:
        if len(response_json['items']) > 0:
            # Mapping for external parties
            operators = []
            operators_by_name = []
            for item in response_json['items']:
                operator_id = safe_get(item, 'charge_points', 0, 'operator_id')
                operator_name = safe_get(item, 'ocpi_partner_operator', 'name')
                if operator_name is not None:
                    operators_by_name.append(operator_name)
                if operator_id is not None:
                    operators.append(operator_id)

            operators = crud.get_external_organization_by_operator_list(dbsession, operators)
            operators = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in operators]
            operators_dict = {}
            for l2 in operators:
                operator_details = l2.dict()
                operators_dict[str(operator_details['id'])] = {k: v for k, v in operator_details.items() if k != 'id'}
            if len(operators_by_name) > 0:
                operators_by_name = crud.get_external_organization_by_operator_name(dbsession,
                                                                                    operators_by_name)
                operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                                     operators_by_name]
                for l2 in operators_by_name:
                    operator_details = l2.dict()
                    operators_dict[str(operator_details['original_name'])] = {k: v for k, v in
                                                                              operator_details.items()
                                                                              if k != 'original_name'}

            response_json_merged = location_merge_operator_details(response_json['items'], operators_dict)
            response_json['items'] = response_json_merged
            return response_json
    except KeyError:
        pass
    return JSONResponse(response.json(), status_code=response.status_code)


@router.get("/ocpi/marker", status_code=status.HTTP_200_OK)
async def get_location_simplified(request: Request, longitude: float,  # pylint: disable=all, # noqa: MC0001
                                  latitude: float, distance: float,
                                  sort_by: Union[str, None] = 'distance',  # noqa
                                  api_sid_header: str = Security(api_sid_header),
                                  dbsession: SessionLocal = Depends(create_session)):
    set_admin_as_context_user(dbsession)
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    if membership_id:
        headers = generate_charger_header(dbsession, membership_id)
    else:
        headers = generate_charger_header_unauthorized(dbsession, api_sid_header)

    request_data = dict(request.query_params)
    discounted = request_data.get('discounted')

    if discounted:
        connector = crud.get_discounted_connector(dbsession, membership_id)
        discount_type = connector[0].get('discount_type')
        connector_id = connector[0].get('connector')

        # this code is for checking custom plan, if custom plan exist then will add the connector id to param
        if len(connector_id) > 0:
            connector_id_list = "".join([str("connector_id=" + str(item) + "&") for item in connector_id])
            path = f'location/ocpi/marker/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}&' \
                   f'distance={distance}&{connector_id_list}&discount_type={discount_type}&{request.query_params}'
        else:
            path = f'location/ocpi/marker/?sort_by={sort_by}&longitude={longitude}&latitude={latitude}&' \
                   f'distance={distance}&discount_type={discount_type}&{request.query_params}'

    else:
        path = f'location/ocpi/marker/?sort_by={sort_by}&longitude={longitude}' \
               f'&latitude={latitude}&distance={distance}&' \
               f'{request.query_params}'

    url = f'{CHARGER_URL_V2_PREFIX}/{path}'
    response = await send_request('GET', url, headers=headers)
    response_json = response.json()
    try:
        if len(response_json) > 0:
            operators = []
            operators_by_name = []
            for item in response_json:
                operator_id = safe_get(item, 'charge_points', 0, 'operator_id')
                operator_name = safe_get(item, 'ocpi_partner_operator', 'name')
                if operator_name is not None:
                    operators_by_name.append(operator_name)
                if operator_id is not None:
                    operators.append(operator_id)

            operators = crud.get_external_organization_by_operator_list(dbsession, operators)
            operators = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in operators]
            operators_dict = {}
            for l2 in operators:
                operator_details = l2.dict()
                operators_dict[str(operator_details['id'])] = {k: v for k, v in operator_details.items() if k != 'id'}

            if len(operators_by_name) > 0:
                operators_by_name = crud.get_external_organization_by_operator_name(dbsession,
                                                                                    operators_by_name)
                operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                                     operators_by_name]
                for l2 in operators_by_name:
                    operator_details = l2.dict()
                    operators_dict[str(operator_details['original_name'])] = {k: v for k, v in
                                                                              operator_details.items()
                                                                              if k != 'original_name'}

            response_json_merged = location_merge_operator_details(response_json, operators_dict)

            return response_json_merged
    except KeyError:
        pass
    return JSONResponse(response.json(), status_code=response.status_code)


@router.get("/ocpi/{location_id}", status_code=status.HTTP_200_OK)
async def get_ocpi_location(request: Request,  # pylint: disable=too-many-branches
                            location_id: str,
                            api_sid_header: str = Security(api_sid_header),
                            dbsession: SessionLocal = Depends(create_session)):
    set_admin_as_context_user(dbsession)
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    if membership_id:
        headers = generate_charger_header(dbsession, membership_id)
    else:
        headers = generate_charger_header_unauthorized(dbsession, api_sid_header)

    path = f'location/ocpi/{location_id}'

    url = f'{CHARGER_URL_V2_PREFIX}/{path}'
    response = await send_request('GET', url, headers=headers)
    response_json = response.json()
    try:
        if len(response_json) > 0:
            response_json = [response_json]

            operators = []
            operators_by_name = []
            for item in response_json:
                operator_id = safe_get(item, 'charge_points', 0, 'operator_id')
                operator_name = safe_get(item, 'ocpi_partner_operator', 'name')
                if operator_name is not None:
                    operators_by_name.append(operator_name)
                if operator_id is not None:
                    operators.append(operator_id)

            operators = crud.get_external_organization_by_operator_list(dbsession, operators)
            operators = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in operators]

            operators_dict = {}
            for l2 in operators:
                operator_details = l2.dict()
                operators_dict[str(operator_details['id'])] = {k: v for k, v in operator_details.items() if k != 'id'}

            if len(operators_by_name) > 0:
                operators_by_name = crud.get_external_organization_by_operator_name(dbsession,
                                                                                    operators_by_name)
                operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                                     operators_by_name]
                for l2 in operators_by_name:
                    operator_details = l2.dict()
                    operators_dict[str(operator_details['original_name'])] = {k: v for k, v in
                                                                              operator_details.items()
                                                                              if k != 'original_name'}
            response_json_merged = location_merge_operator_details(response_json, operators_dict)
            response_json_merged = response_json_merged[0]

            return response_json_merged
    except KeyError:
        pass
    return JSONResponse(response.json(), status_code=response.status_code)


@router.get('/ocpi/serial/{serial_number}', status_code=status.HTTP_200_OK)
async def get_location_by_serial_number(request: Request,
                                        serial_number: str,  # noqa: MC0001
                                        api_sid_header: str = Security(api_sid_header),
                                        dbsession: SessionLocal = Depends(create_session)):
    set_admin_as_context_user(dbsession)
    auth_token_data = decode_auth_token_from_headers(request.headers)
    is_webhook = decode_token_type_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    if membership_id:
        headers = generate_charger_header(dbsession, membership_id)
    else:
        headers = generate_charger_header_unauthorized(dbsession, api_sid_header)

    if is_webhook is not None and is_webhook == 'webhook-event':
        headers = generate_superuser_headers()

    path = f'charge_point/ocpi/serial/{serial_number}'

    url = f'{CHARGER_URL_V2_PREFIX}/{path}'
    response = await send_request('GET', url, headers=headers)
    response_json = response.json()
    rounding_number = settings.DECIMAL_TO_ROUND_IN_DISCOUNTED_UNIT_FEE

    try:
        if len(response_json) > 0:
            response_json = [response_json]
            try:
                operators = []
                operators_by_name = []
                for item in response_json:
                    operator_id = safe_get(item, 'charge_points', 0, 'operator_id')
                    operator_name = safe_get(item, 'ocpi_partner_operator', 'name')
                    # connectors = safe_get(item, 'charge_points', 0, 'connectors', default=[])
                    if operator_name is not None:
                        operators_by_name.append(operator_name)
                    if operator_id is not None:
                        operators.append(operator_id)

                    # for connector in connectors:
                    #     cpc_id = connector.get('id')
                    #     billing_price = connector.get('billing_unit_fee')
                    #     billing_currency = connector.get('billing_currency')
                    #     if membership_id:
                    #         discounted_amount = apply_subscription_discount_connector(dbsession, membership_id, cpc_id,
                    #                                                       billing_price, is_ocpi=True)
                    #     else:
                    #         discounted_amount = billing_price
                    #
                    #     discounted_amount_after_vat = calculate_tax_amount(dbsession, cpc_id, discounted_amount,
                    #                                            billing_currency, always_tax=True,
                    #                                            rounding_number=rounding_number)
                    #
                    #     connector['discounted_billing_unit_fee'] = discounted_amount
                    #     connector['discounted_billing_unit_fee_after_vat'] = discounted_amount_after_vat['total_amount']

                operators = crud.get_external_organization_by_operator_list(dbsession, operators)
                operators = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in operators]
                operators_dict = {}
                for l2 in operators:
                    operator_details = l2.dict()
                    operators_dict[str(operator_details['id'])] = {k: v for k, v in operator_details.items() if
                                                                   k != 'id'}

                if len(operators_by_name) > 0:
                    operators_by_name = crud.get_external_organization_by_operator_name(dbsession,
                                                                                        operators_by_name)
                    operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                                         operators_by_name]
                    for l2 in operators_by_name:
                        operator_details = l2.dict()
                        operators_dict[str(operator_details['original_name'])] = {k: v for k, v in
                                                                                  operator_details.items()
                                                                                  if k != 'original_name'}
                response_json_merged = location_merge_operator_details(response_json, operators_dict)
                response_json_merged = response_json_merged[0]
                return response_json_merged
            except IndexError:
                return response_json[0]

    except KeyError:
        pass
    return JSONResponse(response.json(), status_code=response.status_code)
