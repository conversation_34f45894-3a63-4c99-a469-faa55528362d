"""4

Revision ID: 6dc8449c5c4f
Revises: e8b5e1fcf69e
Create Date: 2022-07-29 15:13:45.866441

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '6dc8449c5c4f'
down_revision = 'e8b5e1fcf69e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_vehicle', 'connector_type_id',
                    existing_type=postgresql.UUID(),
                    type_=postgresql.UUID(as_uuid=True),
                    existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_vehicle', 'connector_type_id',
                    existing_type=postgresql.UUID(as_uuid=True),
                    type_=postgresql.UUID(),
                    existing_nullable=True)
    # ### end Alembic commands ###
