"""2

Revision ID: f26c2975ec43
Revises: 0b2520c5fe6c
Create Date: 2022-07-06 11:41:13.697454

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f26c2975ec43'
down_revision = '0b2520c5fe6c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_callback',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('payload', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('response', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('event', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_shared_operator',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('operator_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['operator_id'], ['main_operator.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['organization_id'], ['main_organization.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('organization_id', 'operator_id', name='_unique_operator_organization')
    )
    op.drop_column('main_auth_user', 'first_name')
    op.drop_column('main_auth_user', 'last_name')
    op.add_column('main_membership', sa.Column('first_name', sa.String(), nullable=True))
    op.add_column('main_membership', sa.Column('last_name', sa.String(), nullable=True))
    op.alter_column('main_id_tag', 'id_tag',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.create_index('main_id_tag_id_tag_index', 'main_id_tag',
                    [sa.text('lower(id_tag)')], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('main_id_tag_id_tag_index', table_name='main_id_tag')
    op.alter_column('main_id_tag', 'id_tag',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.drop_column('main_membership', 'last_name')
    op.drop_column('main_membership', 'first_name')
    op.add_column('main_auth_user', sa.Column('last_name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('main_auth_user', sa.Column('first_name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_table('main_shared_operator')
    op.drop_table('main_callback')
    # ### end Alembic commands ###
