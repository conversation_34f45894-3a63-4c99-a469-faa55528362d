import sqlalchemy as db
from sqlalchemy.dialects.postgresql import UUID

from app.models.base import BaseModel


class CyberSourcePaymentForm(BaseModel):
    __tablename__ = 'main_cybersource_form'

    member_id = db.Column(db.String, index=True)
    save_credit_card = db.Column(db.Bo<PERSON>, default=False)
    amount = db.Column(db.Float, default=1.01)
    currency = db.Column(db.String, default='SGD')
    description = db.Column(db.String)
    capture_context = db.Column(db.String)
    client_library_url = db.Column(db.String)
    client_library_integrity = db.Column(db.String)
    response_body = db.Column(db.JSON)
    request_body = db.Column(db.JSON)
    payment_request_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_payment_request.id'))
    payment_request = db.orm.relationship('PaymentRequest', backref='cybersource_payment_form')


class CyberSourceTokenStep(BaseModel):
    __tablename__ = 'main_cybersource_token_step'

    payment_form_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_cybersource_form.id'), index=True)
    transient_token = db.Column(db.String)
    access_token = db.Column(db.String)
    device_data_collection_url = db.Column(db.String)
    response_body = db.Column(db.JSON)
    request_body = db.Column(db.JSON)


class CyberSourceEnrollmentStep(BaseModel):
    __tablename__ = 'main_cybersource_enroll_step'

    payment_form_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_cybersource_form.id'), index=True)
    # Refer to CyberSource's Cardinal Session ID
    reference_id = db.Column(db.String, index=True)
    step_up_url = db.Column(db.String)
    return_url = db.Column(db.String)
    access_token = db.Column(db.String)
    response_body = db.Column(db.JSON)
    request_body = db.Column(db.JSON)

    screen_width = db.Column(db.String)
    screen_height = db.Column(db.String)


class CyberSourceAuthenticationStep(BaseModel):
    __tablename__ = 'main_cybersource_authentication_step'

    payment_form_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_cybersource_form.id'), index=True)
    transaction_id = db.Column(db.String, index=True)
    status = db.Column(db.String)
    response_body = db.Column(db.JSON)
    request_body = db.Column(db.JSON)


class CyberSourceTMSStep(BaseModel):
    __tablename__ = 'main_cybersource_tms_step'

    payment_form_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_cybersource_form.id'), index=True)
    transaction_id = db.Column(db.String, index=True)
    billing_info = db.Column(db.JSON)
    order_info = db.Column(db.JSON)
    response_body = db.Column(db.JSON)
    request_body = db.Column(db.JSON)
