import logging

from typing import List
from uuid import UUID
from fastapi import APIRouter, HTTPException, Depends, Request

from app import settings, schema, crud, exceptions
from app.database import create_session, SessionLocal
from app.permissions import permission

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH


router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/resources",
    tags=['resources', ],
    dependencies=[Depends(permission)],
)


@router.get("/resource", response_model=List[schema.ResourceResponse])
async def list_resource(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Lists all Resource objects
    """
    resources = crud.get_resources(dbsession)
    return resources


@router.post("/resource", response_model=schema.ResourceResponse, status_code=201)
async def create_resource(data: schema.Resource, dbsession: SessionLocal = Depends(create_session)):
    """
    Creates a Resource object
    """
    res = crud.create_resource(dbsession, data)
    return res


@router.get("/resource/{resource_id}", response_model=schema.ResourceResponse)
async def get_resource(request: Request, resource_id: UUID, dbsession: SessionLocal = Depends(create_session)):
    """
    Fetches a single Resource object
    """
    try:
        res = crud.get_resource(dbsession, resource_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
    return res


@router.patch("/resource/{resource_id}", response_model=schema.ResourceResponse)
async def update_resource(data: schema.ResourceUpdate, resource_id: UUID,
                          dbsession: SessionLocal = Depends(create_session)):
    """
    Partially updates a Resource object
    """
    try:
        res = crud.update_resource(dbsession, data, resource_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
    return res


@router.delete("/resource/{resource_id}", status_code=204)
async def delete_resource(request: Request, resource_id: UUID,
                          dbsession: SessionLocal = Depends(create_session)):
    """
    Deletes a Resource object
    """
    try:
        crud.delete_resource(dbsession, resource_id)
        return None
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/resource_server", response_model=List[schema.ResourceServerResponse])
async def list_resource_server(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Lists all ResourceServer objects
    """
    servers = crud.get_resource_servers(dbsession)
    return servers


@router.post("/resource_server", response_model=schema.ResourceServerResponse, status_code=201)
async def create_resource_server(data: schema.ResourceServer, dbsession: SessionLocal = Depends(create_session)):
    """
    Creates a ResourceServer object
    """
    res_server = crud.create_resource_server(dbsession, data)
    return res_server


@router.get("/resource_server/{resource_server_id}", response_model=schema.ResourceServerResponse)
async def get_resource_server(request: Request, resource_server_id: UUID,
                              dbsession: SessionLocal = Depends(create_session)):
    """
    Fetches a single ResourceServer object
    """
    try:
        res_server = crud.get_resource_server(dbsession, resource_server_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
    return res_server


@router.patch("/resource_server/{resource_server_id}", response_model=schema.ResourceServerResponse)
async def update_resource_server(data: schema.ResourceServerUpdate, resource_server_id: UUID,
                                 dbsession: SessionLocal = Depends(create_session)):
    """
    Partially updates a ResourceServer object
    """
    try:
        res_server = crud.update_resource_server(dbsession, data, resource_server_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
    return res_server


@router.delete("/resource_server/{resource_server_id}", status_code=204)
async def delete_resource_server(request: Request, resource_server_id: UUID,
                                 dbsession: SessionLocal = Depends(create_session)):
    """
    Deletes a ResourceServer object
    """
    try:
        crud.delete_resource_server(dbsession, resource_server_id)
        return None
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
