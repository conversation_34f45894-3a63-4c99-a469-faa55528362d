# pylint:disable=too-many-lines
# import asyncio
# import aiohttp
import asyncio
import json
import logging
# import uuid
from contextlib import contextmanager
from functools import partial
from datetime import datetime, timezone, timedelta
from dateutil import parser
from zoneinfo import ZoneInfo

import pydantic
import macaddress
import requests
# from requests import ConnectTimeout, ReadTimeout, Timeout
import httpx
from httpx import ConnectTimeout
from kombu.exceptions import KombuError
from sqlalchemy import func
from sqlalchemy.orm import joinedload, Session
from sqlalchemy.exc import IntegrityError, NoResultFound
from celery.exceptions import MaxRetriesExceededError  # noqa

from app import lta_adaptor, schema, models, settings, crud, exceptions
from app.celery import app
from app.connection import ChargerServicePublisher
from app.crud import get_pre_auth_payment_by_charging_session_id, get_pre_auth_payment_by_id, \
    get_billing_info_given_cc_info, get_billing_info_given_user_info, get_or_create_ocpi_app_token, \
    get_membership_by_ocpi_token_id, get_or_create_pre_auth_info_cc, \
    get_non_binded_pre_auth_by_member_id_with_payment_type, get_charging_session_bill_by_charging_session, \
    get_payment_request_by_charging_session_bill_id
from app.database import create_session
from app.routers.charger import audit_log_entry
from app.schema import PaymentRequestStatus, PaymentRequestType
from app.schema.v2 import AutochargeStatus, PreferredPaymentFlowEnums, PreAuthPaymentMethodEnums
from app.settings import APOLLO_MAIN_DOMAIN, MAIN_ROOT_PATH, DURATION_MINUTE_TILL_PRE_AUTH_EXPIRY, \
    APPLY_FREE_CHARGING, PRE_AUTH_INSTANT_VOID, PRE_AUTH_VOID_AFTER_MINUTES, REFUND_PRE_AUTH_WALLET_UPON_INCOMPLETE
from app.utils import calculate_charging_usage, calculate_charging_cost, calculate_md5_hash, calculate_tax_amount, pay_for_charging, \
    apply_subscription_discount, send_request, get_discount_type_by_id_tag, get_charging_history_data, \
    GenerateReport, push_update, get_charge_history_record, get_discounted_billing_unit_fee_by_id_tag, \
    has_primary_credit_card_by_id_tag, send_cp_breakdown_email, send_hung_session_email, send_ess_breakdown_email, \
    apply_subscription_discount_connector, process_razerpay_reconcilation, \
    lta_send_request, generate_charger_header, apply_campaign_promo_code_discount, \
    auto_cut_ocpi_send_to_partner, perform_pre_auth_refund, calculate_hogging_fee, \
    map_command_message_to_friendly_message, update_wallet_balance, get_wallet_by_member_id_currency, \
    handle_wallet_pre_auth, handle_credit_card_pre_auth, get_preferred_payment_flow_by_id_tag, \
    get_default_subscription_and_plans, get_connector, get_preferred_payment_method_by_id_tag, \
    check_if_low_wallet_balance, decode_base64_to_token, update_task_successfully, \
    check_user_dunning_status, get_default_subscription_and_plans, reset_or_carry_over_prepaid_wallet_subscription, \
    check_session_is_outlier
from app.e_invoice_utils import construct_and_submit_invoice_to_rmp
from app.middlewares import _request_user_ctx_var, set_admin_as_context_user

main_path = f"{APOLLO_MAIN_DOMAIN}/{MAIN_ROOT_PATH}"
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter(settings.LOG_FORMAT))
logger.addHandler(handler)

# publisher = ChargerServicePublisher()
CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'
OCPI_URL = f'{settings.MAIN_OCPI_DOMAIN}/{settings.OCPI_PREFIX}'


@app.task(name='apollo.main.tasks.request_billing', autoretry_for=(ConnectTimeout,),
          retry_backoff=3)
def request_billing(message: dict):  # noqa: MC0001
    """Request a billing
    """
    logger.info('request_billing')
    publisher = ChargerServicePublisher()
    hash = message['body']['parameters']['hash']
    charge_point_id = message['body']['parameters']['charge_point_id']
    charging_session = message['body']['parameters']['charging_session']
    try:
        cpo_cs = message['body']['parameters']['cpo_cs']
    except KeyError:
        cpo_cs = None
    try:
        partner_cs = message['body']['parameters']['partner_cs']
    except KeyError:
        partner_cs = None
    try:
        has_breakdown = message['body']['parameters']['has_breakdown']
    except KeyError:
        has_breakdown = False

    is_external_charging = False

    partner_id = None
    cpo_id = None
    if partner_cs:
        partner_id = partner_cs.get('id')
    if cpo_cs:
        cpo_id = cpo_cs.get('id')

    hogging_fee = 0
    hogging_tariff = charging_session.get('meta', {}).get('billing_info', {}).get('hogging_tariff')
    promo_code_usage = charging_session.get('meta', {}).get('billing_info', {}).get('promo_code_usage')

    if hogging_tariff:
        if hogging_tariff is not None:
            if len(hogging_tariff) > 0:
                if charging_session['hogging_start'] and charging_session['hogging_end']:
                    hogging_fee = calculate_hogging_fee(hogging_tariff,
                                                        charging_session['hogging_start'],
                                                        charging_session['hogging_end'],
                                                        has_breakdown)
    hogging_fee = round(hogging_fee, 2)
    # OCPI Charging - We as CPO && Partner Charging (eg: Payment Terminal)
    if charging_session['id_tag'].startswith('EMSP-') or charging_session['id_tag'].startswith('PTNR-'):
        subscription_discount = {'subscription_discount': float(0)}
        organization_name = 'MGT'
        if str(settings.OCPI_PARTY_ID).upper() == 'CDG':
            organization_name = 'CDG'
        elif str(settings.OCPI_PARTY_ID).upper() == 'HGM':
            organization_name = 'DCH'

        with contextmanager(create_session)() as dbsession:
            # To complete
            if charging_session['id_tag'].startswith('EMSP-'):
                cdr_data = charging_session['meta']['cdr_data']
                cdr_uid = cdr_data['uid']
                db_cpo_token = dbsession.query(models.OCPICPOToken).filter(  # noqaa
                    models.OCPICPOToken.partner_ocpi_cpo_token_id == cdr_uid  # nosec
                ).first()

            if charging_session['meta']['billing_info']['billing_type'] == schema.BillingType.kwh:
                usage = calculate_charging_usage(
                    charging_session['meter_start'],
                    charging_session['meter_stop'],
                    charging_session['meta']['billing_info']['billing_type'],
                    charging_session['meta']['billing_info']['billing_cycle']
                )
            else:
                usage = calculate_charging_usage(
                    charging_session['session_start'],
                    charging_session['session_end'],
                    charging_session['meta']['billing_info']['billing_type'],
                    charging_session['meta']['billing_info']['billing_cycle']
                )
            charging_usage = float(charging_session['meter_stop']) - float(charging_session['meter_start'])

            # duration = datetime.strptime(charging_session['session_end'], "%Y-%m-%dT%H:%M:%SZ") - datetime.strptime(
            #     charging_session['session_start'], "%Y-%m-%dT%H:%M:%SZ")

            # changed to isoparse as it can automatically parse iso8601 timestamps
            duration = parser.isoparse(charging_session['session_end']) - parser.isoparse(
                charging_session['session_start'])

            amount = calculate_charging_cost(
                unit_price=charging_session['meta']['billing_info']['billing_unit_fee'],
                usage=usage,
                constant=charging_session['meta']['billing_info']['connection_fee']
            )
            amount = round(float(amount), 2)
            # tax_info = calculate_tax_amount(dbsession,
            #                                 charge_point_id,
            #                                 amount,
            #                                 charging_session['meta']['billing_info']['billing_currency'])

            # amount = tax_info['total_amount']

            # No discount for EXT
            meta_data = {
                'transaction_id': str(charging_session['transaction_id']),
                'organization_name': organization_name
            }
            invoice_number = str(organization_name) + '-' + str(charging_session['transaction_id'])

            total_charge_amount = round(float(amount) - float(subscription_discount['subscription_discount']), 2)
            total_charge_amount = round(total_charge_amount + hogging_fee, 2)
            tax_info = calculate_tax_amount(dbsession,
                                            charge_point_id,
                                            total_charge_amount,
                                            charging_session['meta']['billing_info']['billing_currency'])
            total_charge_amount = tax_info['total_amount']
            # total_charge_amount = tax_info['total_amount'] + hogging_fee

            charging_session_bill = schema.ChargingSessionBill(
                charging_session_id=charging_session['id'],
                usage_amount=amount,
                usage_type=charging_session['meta']['billing_info']['billing_type'],
                discount=subscription_discount,
                meta=meta_data,
                invoice_number=invoice_number,
                tax_amount=tax_info['tax_amount'],
                tax_rate=tax_info['tax_rate'],
                total_amount=total_charge_amount,
                charging_session_type=charging_session['charging_session_type'],
                charge_point_id=charge_point_id,
                charging_status=charging_session['status'],
                partner_id=partner_id,
                cpo_id=cpo_id,
                id_tag=charging_session['id_tag'],
                hogging_fee=hogging_fee,
                preferred_payment_flow=charging_session['meta']['billing_info'].get('preferred_payment_flow', None),
                preferred_payment_method=charging_session['meta']['billing_info'].get('preferred_payment_method', None),
                is_low_wallet_balance=charging_session['meta']['billing_info'].get('is_low_wallet_balance', None)
            )

            # if APPLY_FREE_CHARGING is true, set to free charging within 60 seconds or less than 5wh
            if APPLY_FREE_CHARGING:
                if duration.total_seconds() <= 60 or charging_usage <= 5:
                    # amount = 0
                    charging_session_bill.usage_amount = 0
                    charging_session_bill.discount = {"subscription_discount": 0.0}
                    # subscription_discount = {"subscription_discount": 0.0}

                    # We modify the charge_amount to be charging hogging only as we assume <= 60 seconds
                    # or <= 5wh charging to be free.
                    if hogging_fee > 0:
                        total_charge_amount = hogging_fee
                        charging_session_bill.total_amount = hogging_fee
                        hogging_tax_info = calculate_tax_amount(dbsession,
                                                                charge_point_id,
                                                                hogging_fee,
                                                                charging_session['meta']['billing_info']['billing_currency'])
                        charging_session_bill.tax_amount = hogging_tax_info['tax_amount']
                    else:
                        total_charge_amount = 0
                        charging_session_bill.total_amount = 0
                        charging_session_bill.tax_amount = 0

            try:
                db_charging_session_bill = dbsession.query(models.ChargingSessionBill).filter(
                    models.ChargingSessionBill.charging_session_id == charging_session['id'],
                ).first()
                if not db_charging_session_bill:
                    db_charging_session_bill = models.ChargingSessionBill(**charging_session_bill.dict())
                    dbsession.add(db_charging_session_bill)
                    dbsession.commit()
            except IntegrityError as e:
                logger.error(e)
                return None

            charging_session_bill_dict = schema.ChargingSessionBillResponse.from_orm(db_charging_session_bill).dict()
            # total_charge_amount = round(float(amount) -
            #                             float(subscription_discount['subscription_discount']), 2)
            # charging_session['total_charge_amount'] = total_charge_amount
            charging_session['total_charge_amount'] = float(charging_session_bill_dict['total_amount'])
            charging_session['charging_usage'] = charging_usage
            charging_session['duration'] = float(duration.total_seconds())
            # meta = json.loads(schema.MembershipResponse.from_orm(db_member).json())  # static membership data

            db_operator_chargepoint = dbsession.query(models.OperatorChargepoint).filter(
                models.OperatorChargepoint.charge_point_id == charge_point_id
            ).first()
            if not db_operator_chargepoint:
                logger.error('Operator charge point not found based on %s charge point', charge_point_id)

            response_message = schema.Message(
                reason=schema.MessageReason.response,
                body=schema.MessageBody(
                    message_type='ChargingBillResponse',
                    parameters={
                        'hash': hash,
                        'charging_session_bill_id': str(charging_session_bill_dict['id']),
                        'meta': {
                            # 'member': json.loads(schema.OCPICPOTokenResponse.from_orm(db_cpo_token).json()),
                            'operator': json.loads(schema.ShallowOperatorResponse.from_orm(
                                db_operator_chargepoint.operator).json())
                        }
                    }
                )
            )

            publisher.publish_message(response_message.dict(), message['reply_to'])

            logger.info('Sent message # %s', response_message.dict())

            # loop.run_until_complete(push_update(dbsession, 'charging_session', charging_session))
            # loop = asyncio.get_event_loop()
            if charging_session['id_tag'].startswith('EMSP-'):
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError as e:
                    if str(e).startswith('There is no current event loop in thread'):
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                    else:
                        logger.error('Runtime error on asyncio with error, %s', str(e))
                cs = {
                    'cpo_cs': cpo_cs,
                    'charging_session': charging_session,
                    'charging_session_bill': charging_session_bill_dict,
                }
                loop.run_until_complete(push_update(dbsession, 'invoice', cs))
            else:
                # All info related to partner
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError as e:
                    if str(e).startswith('There is no current event loop in thread'):
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                    else:
                        logger.error('Runtime error on asyncio with error, %s', str(e))
                # db_pr = crud.get_payment_reqeust_by_invoice_number(dbsession, invoice_number)
                try:
                    crud.get_payment_reqeust_by_invoice_number(dbsession, invoice_number)
                    old_pr = True
                except Exception as e:  # noqa
                    old_pr = False

                meta = json.loads(schema.ShallowOperatorResponse.from_orm(
                    db_operator_chargepoint.operator).json())

                if not old_pr:
                    payment_request = schema.PaymentRequest(
                        type=schema.PaymentRequestType.payment_terminal,
                        amount=amount,
                        currency=charging_session['meta']['billing_info']['billing_currency'],
                        billing_description=f'charging_session-{charging_session["id"]}',
                        reason=schema.PaymentRequestReason.payment,
                        update_token=False,
                        meta=meta,
                        connector_id=charge_point_id,
                        charging_session_bill_id=str(db_charging_session_bill.id),
                        invoice_number=invoice_number
                    )
                    _ = crud.create_payment_request_no_member(dbsession, payment_request)

                for partner in dbsession.query(models.ExternalOrganization).filter(
                        models.ExternalOrganization.organization_type == 'Partner').all():
                    ocpp_callback = partner.meta['csb_callback_url']
                    ocpp_callback += '/' + str(charging_session['id'])
                    api_key = partner.meta['api_key']
                    headers = {
                        'X-API-Key': api_key,
                        'content-type': 'application/json'
                    }
                    charging_record = get_charge_history_record(charging_session, dbsession, is_partner=True)
                    csb = {
                        'charging_session_bill': charging_record,
                        'partner_cs': partner_cs,
                    }
                    # wrap with generic response
                    data = {"success": True, "message": "", "error": None, 'data': csb}
                    loop.run_until_complete(send_request('POST', ocpp_callback, data=json.dumps(data), headers=headers))

                    if settings.ENABLE_PARTNER_E_INVOICE:
                        loop.run_until_complete(construct_and_submit_invoice_to_rmp(
                            dbsession=dbsession,
                            charging_session=charging_session,
                            csb=db_charging_session_bill
                        ))

        return {
            'hash': hash,
            'charging_session_bill_id': str(charging_session_bill_dict['id']),
            'meta': {
                # 'member': json.loads(schema.OCPICPOTokenResponse.from_orm(db_cpo_token).json()),
                'operator': json.loads(schema.ShallowOperatorResponse.from_orm(
                    db_operator_chargepoint.operator).json())
            }
        }

    if charging_session['charging_session_type'] and charging_session['charging_session_type'] == 'OCPI-EMSP':
        with contextmanager(create_session)() as dbsession:
            set_admin_as_context_user(dbsession)
            charging_session_id = charging_session['id']

            try:
                db_charging_session_bill = dbsession.query(models.ChargingSessionBill).filter(
                    models.ChargingSessionBill.charging_session_id == charging_session_id,
                ).first()
                if not db_charging_session_bill:
                    logger.error("Trying to rebill non-issued invoice by EMSP")
                    return None
            except IntegrityError as e:
                logger.error(e)
                return None
            db_pr = dbsession.query(models.PaymentRequest).filter(
                models.PaymentRequest.charging_session_bill_id == db_charging_session_bill.id).first()
            if not db_pr:
                logger.error("Trying to rebill non-issued invoice by EMSP")
                return None
            total_charge_amount = float(db_pr.amount)

            if db_pr.status not in [PaymentRequestStatus.failed, PaymentRequestStatus.rejected]:
                return False
            if db_pr.type != schema.PaymentRequestType.recurring:
                return False
            if db_charging_session_bill.status in [schema.ChargingSessionBillStatus.failed,
                                                   schema.ChargingSessionBillStatus.rejected]:
                crud.charging_bill_pending(dbsession, db_charging_session_bill.id)

            # connector_id = db_pr.connector_id
            billing_currency = db_pr.currency

            db_member = dbsession.query(models.Membership).filter(
                models.Membership.id == db_pr.member_id).first()

            pay_with_cc = True

            if total_charge_amount == 0:
                cc = schema.CreditCard(
                    currency='MYR',
                    brand='NA',
                    last_four_digit='NA',
                    type='Prepaid',
                    token='NA',  # nosec
                    primary=True,
                )
                cc_token = ''  # nosec
            else:
                cc = crud.get_member_primary_cc(dbsession, db_member.id, db_pr.currency)
                pay_with_cc = True
                if not cc:
                    cc = crud.get_member_primary_cc(dbsession, db_member.id)
                    if not cc or cc.payment_gateway != schema.CreditCardPaymentGateway.cybersource:
                        # wallet = get_wallet_by_member_id_currency(dbsession, db_member.id, db_pr.currency)
                        # if float(wallet.balance) <= total_charge_amount:
                        logger.error('member %s credit card not found, but will try to pay with wallet', db_member.id)
                        pay_with_cc = True
                        cc_token = ''  # nosec
                    else:
                        cc_token = cc.token
                    # return None
                else:
                    cc_token = cc.token

            # bill_name = f'{db_member.first_name} {db_member.last_name}'
            # bill_email = db_member.user.email
            # bill_mobile = db_member.user.phone_number
            invoice_number = db_pr.invoice_number
            meta = db_pr.meta
            # if cc.bill_name:
            #     bill_name = cc.bill_name
            #     bill_email = cc.bill_email
            #     bill_mobile = cc.bill_mobile
            if cc:
                billing_info = get_billing_info_given_cc_info(cc)
            else:
                billing_info = get_billing_info_given_user_info(db_member)

            bill_name = billing_info['bill_name']
            bill_email = billing_info['bill_email']
            bill_mobile = billing_info['bill_mobile']

            pay_for_charging(
                dbsession, str(db_member.id),
                total_charge_amount,
                charging_session['id'], charge_point_id, str(db_charging_session_bill.id), bill_name, bill_email,
                bill_mobile,
                cc_token, billing_currency, meta, invoice_number, pay_with_cc,
                db_charging_session_bill.reference_number,
                is_ocpi=True,
            )

            if settings.ENABLE_EMSP_E_INVOICE:
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError as e:
                    if str(e).startswith('There is no current event loop in thread'):
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                    else:
                        logger.error('Runtime error on asyncio with error, %s', str(e))

                loop.run_until_complete(construct_and_submit_invoice_to_rmp(
                    dbsession=dbsession,
                    charging_session=charging_session,
                    csb=db_charging_session_bill,
                    member_id=db_member.id
                ))

            return None

    with contextmanager(create_session)() as dbsession:
        set_admin_as_context_user(dbsession)
        db_id_tag = dbsession.query(models.IDTag).filter(
            func.lower(models.IDTag.id_tag) == charging_session['id_tag'].lower(),
        ).first()
        if db_id_tag:
            db_member = db_id_tag.member
            is_external_charging = db_id_tag.is_external
        else:
            db_member = dbsession.query(models.Membership).filter(
                func.lower(models.Membership.user_id_tag) == charging_session['id_tag'].lower()).first()
            if not db_member:
                logger.error('IDTag %s not found', charging_session['id_tag'])
                return None
        if db_member.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                         schema.MembershipType.custom):
            return None
        if charging_session['meta']['billing_info']['billing_type'] == schema.BillingType.kwh:
            usage = calculate_charging_usage(
                charging_session['meter_start'],
                charging_session['meter_stop'],
                charging_session['meta']['billing_info']['billing_type'],
                charging_session['meta']['billing_info']['billing_cycle']
            )
        else:
            usage = calculate_charging_usage(
                charging_session['session_start'],
                charging_session['session_end'],
                charging_session['meta']['billing_info']['billing_type'],
                charging_session['meta']['billing_info']['billing_cycle']
            )

        # duration = datetime.strptime(charging_session['session_end'], "%Y-%m-%dT%H:%M:%SZ") - datetime.strptime(
        #     charging_session['session_start'], "%Y-%m-%dT%H:%M:%SZ")

        # changed to isoparse as it can automatically parse iso8601 timestamps
        duration = parser.isoparse(charging_session['session_end']) - parser.isoparse(
            charging_session['session_start'])

        amount = calculate_charging_cost(
            unit_price=charging_session['meta']['billing_info']['billing_unit_fee'],
            usage=usage,
            constant=charging_session['meta']['billing_info']['connection_fee']
        )
        amount = round(float(amount), 2)
        # tax_info = calculate_tax_amount(dbsession,
        #                                 charge_point_id,
        #                                 amount,
        #                                 charging_session['meta']['billing_info']['billing_currency'])

        # amount = tax_info['total_amount']
        is_outlier = False

        is_outlier = check_session_is_outlier(dbsession, charging_session, amount, duration)
        
        if is_outlier:
            response_message = schema.Message(
                reason=schema.MessageReason.response,
                body=schema.MessageBody(
                    message_type='ChargingBillResponse',
                    parameters={
                        'hash': hash,
                        'charging_session_bill_id': None,
                        'is_outlier': is_outlier
                    }
                )
            )
            publisher.publish_message(response_message.dict(), message['reply_to'])

            logger.info('Sent message # %s', response_message.dict())

            return {
                'hash': hash,
                'charging_session_bill_id': None,
                'is_outlier': is_outlier
            }

        subscription_discount = apply_subscription_discount(dbsession, db_member.id,
                                                            charging_session['charge_point_connector']['id'],
                                                            usage, float(amount),
                                                            charging_session['meta']
                                                            ['billing_info']['billing_discounted_type'],
                                                            charging_session['meta']
                                                            ['billing_info']['billing_discounted_amount'])

        campaign_promo_code_discount = apply_campaign_promo_code_discount(promo_code_usage, amount)

        discount_summary = {
            **subscription_discount,
            **campaign_promo_code_discount,
        }

        # organization = db_member.organization
        # meta_data = {
        #     'transaction_id': str(charging_session['transaction_id']),
        #     'organization_name': organization.name[:3].upper() if len(
        #         organization.name) >= 3 else organization.name.upper()
        # }
        # charging_session_bill = schema.ChargingSessionBill(
        #     charging_session_id=charging_session['id'],
        #     usage_amount=float(amount),
        #     usage_type=charging_session['meta']['billing_info']['billing_type'],
        #     discount=subscription_discount,
        #     meta=meta_data
        # )
        organization = db_member.organization
        meta_data = {
            'transaction_id': str(charging_session['transaction_id']),
            'organization_name': organization.name[:3].upper() if len(
                organization.name) >= 3 else organization.name.upper()
        }
        invoice_number = (organization.name[:3].upper() if len(
            organization.name) >= 3 else organization.name.upper()) + '-' + str(charging_session['transaction_id'])

        total_charge_amount = round(
            (
                float(amount)
                - float(discount_summary['subscription_discount'])
                - float(discount_summary['campaign_promo_code_discount'])
            ), 2
        )
        total_charge_amount = max(total_charge_amount, 0)
        total_charge_amount = total_charge_amount + hogging_fee

        tax_info = calculate_tax_amount(dbsession,
                                        charge_point_id,
                                        total_charge_amount,
                                        charging_session['meta']['billing_info']['billing_currency'])

        total_charge_amount = tax_info['total_amount']
        charging_session_bill = schema.ChargingSessionBill(
            charging_session_id=charging_session['id'],
            usage_amount=float(amount),
            usage_type=charging_session['meta']['billing_info']['billing_type'],
            discount=discount_summary,
            meta=meta_data,
            invoice_number=invoice_number,
            tax_amount=tax_info['tax_amount'],
            tax_rate=tax_info['tax_rate'],
            total_amount=total_charge_amount,
            charging_session_type=charging_session['charging_session_type'],
            charge_point_id=charge_point_id,
            charging_status=charging_session['status'],
            partner_id=partner_id,
            cpo_id=cpo_id,
            id_tag=charging_session['id_tag'],
            hogging_fee=hogging_fee,
            preferred_payment_flow=charging_session['meta']['billing_info'].get('preferred_payment_flow', None),
            preferred_payment_method=charging_session['meta']['billing_info'].get('preferred_payment_method', None),
            is_low_wallet_balance=charging_session['meta']['billing_info'].get('is_low_wallet_balance', None)
        )
        charging_usage = float(charging_session['meter_stop']) - float(charging_session['meter_start'])
        # if APPLY_FREE_CHARGING is true, set to free charging within 60 seconds or less than 5wh
        if APPLY_FREE_CHARGING:
            if duration.total_seconds() <= 60 or charging_usage <= 5:
                # amount = 0
                charging_session_bill.usage_amount = 0
                charging_session_bill.discount = {"subscription_discount": 0.0}
                # subscription_discount = {"subscription_discount": 0.0}

                # We modify the charge_amount to be charging hogging only as we assume <= 60 seconds
                # or <= 5wh charging to be free.
                if hogging_fee > 0:
                    total_charge_amount = hogging_fee
                    charging_session_bill.total_amount = hogging_fee
                    hogging_tax_info = calculate_tax_amount(dbsession,
                                                            charge_point_id,
                                                            hogging_fee,
                                                            charging_session['meta']['billing_info']['billing_currency'])
                    charging_session_bill.tax_amount = hogging_tax_info['tax_amount']
                else:
                    total_charge_amount = 0
                    charging_session_bill.total_amount = 0
                    charging_session_bill.tax_amount = 0

        db_pre_auth = get_pre_auth_payment_by_charging_session_id(dbsession, charging_session['id'])
        if db_pre_auth:
            db_pre_auth_pr = crud.get_payment_request(dbsession, db_pre_auth.payment_request_id)
            charging_session_bill.reference_number = str(db_pre_auth_pr.invoice_number)

        try:
            db_charging_session_bill = dbsession.query(models.ChargingSessionBill).filter(
                models.ChargingSessionBill.charging_session_id == charging_session['id'],
            ).first()
            if not db_charging_session_bill:
                db_charging_session_bill = models.ChargingSessionBill(**charging_session_bill.dict())
                dbsession.add(db_charging_session_bill)
                dbsession.commit()
        except IntegrityError as e:
            logger.error(e)
            return None

        # total_charge_amount = float(amount) - float(subscription_discount['subscription_discount'])
        meta = json.loads(schema.MembershipResponse.from_orm(db_member).json())  # static membership data
        pay_with_cc = True
        if total_charge_amount == 0:
            cc = schema.CreditCard(
                currency='MYR',
                brand='NA',
                last_four_digit='NA',
                type='Prepaid',
                token='NA',  # nosec
                primary=True,
            )
            cc_token = ''  # nosec
        else:
            cc = crud.get_member_primary_cc(dbsession, db_member.id,
                                            charging_session['meta']['billing_info']['billing_currency'])
            pay_with_cc = True
            if not cc:
                cc = crud.get_member_primary_cc(dbsession, db_member.id)
                if not cc or cc.payment_gateway != schema.CreditCardPaymentGateway.cybersource:
                    # wallet = get_wallet_by_member_id_currency(dbsession, db_member.id, db_pr.currency)
                    # if float(wallet.balance) <= total_charge_amount:
                    logger.error('member %s credit card not found, but will try to pay with wallet', db_member.id)
                    pay_with_cc = False
                    cc_token = ''  # nosec
                else:
                    cc_token = cc.token
            else:
                cc_token = cc.token

        db_operator_chargepoint = dbsession.query(models.OperatorChargepoint).filter(
            models.OperatorChargepoint.charge_point_id == charge_point_id
        ).first()
        if not db_operator_chargepoint:
            logger.error('Operator charge point not found based on %s charge point', charge_point_id)

        if db_charging_session_bill.status in [schema.ChargingSessionBillStatus.pending,
                                               schema.ChargingSessionBillStatus.rejected,
                                               schema.ChargingSessionBillStatus.failed] and not is_external_charging:
            if db_charging_session_bill.status in [schema.ChargingSessionBillStatus.failed,
                                                   schema.ChargingSessionBillStatus.rejected]:
                crud.charging_bill_pending(dbsession, db_charging_session_bill.id)

            # bill_name = f'{db_member.first_name} {db_member.last_name}'
            # bill_email = db_member.user.email
            # bill_mobile = db_member.user.phone_number

            # if cc.bill_name:
            #     bill_name = cc.bill_name
            #     bill_email = cc.bill_email
            #     bill_mobile = cc.bill_mobile

            if cc:
                billing_info = get_billing_info_given_cc_info(cc)
            else:
                billing_info = get_billing_info_given_user_info(db_member)

            bill_name = billing_info['bill_name']
            bill_email = billing_info['bill_email']
            bill_mobile = billing_info['bill_mobile']

            pay_for_charging(
                dbsession, str(db_member.id),
                float(db_charging_session_bill.total_amount),
                charging_session['id'], charge_point_id, str(db_charging_session_bill.id), bill_name, bill_email,
                bill_mobile,
                cc_token, charging_session['meta']['billing_info']['billing_currency'], meta, invoice_number,
                pay_with_cc, db_charging_session_bill.reference_number,
            )

        response_message = schema.Message(
            reason=schema.MessageReason.response,
            body=schema.MessageBody(
                message_type='ChargingBillResponse',
                parameters={
                    'hash': hash,
                    'charging_session_bill_id': str(db_charging_session_bill.id),
                    'meta': {
                        'member': json.loads(schema.ShallowMembershipResponse.from_orm(db_member).json()),
                        'operator': json.loads(schema.ShallowOperatorResponse.from_orm(
                            db_operator_chargepoint.operator).json())
                    },
                    'is_outlier': is_outlier
                }
            )
        )

        publisher.publish_message(response_message.dict(), message['reply_to'])

        logger.info('Sent message # %s', response_message.dict())

        # loop.run_until_complete(push_update(dbsession, 'charging_session', charging_session))
        # loop.run_until_complete(push_update(dbsession, 'invoice', charging_session))
        if settings.ENABLE_NATIVE_E_INVOICE:
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError as e:
                if str(e).startswith('There is no current event loop in thread'):
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                else:
                    logger.error('Runtime error on asyncio with error, %s', str(e))

            loop.run_until_complete(construct_and_submit_invoice_to_rmp(
                dbsession=dbsession,
                charging_session=charging_session,
                csb=db_charging_session_bill,
                member_id=db_member.id
            ))

        return {
            'hash': hash,
            'charging_session_bill_id': str(db_charging_session_bill.id),
            'meta': {
                'member': json.loads(schema.ShallowMembershipResponse.from_orm(db_member).json()),
                'operator': json.loads(schema.ShallowOperatorResponse.from_orm(
                    db_operator_chargepoint.operator).json())
            }
        }


@app.task(name='apollo.main.tasks.push_emsp', autoretry_for=(KombuError,), retry_backoff=3)
def push_to_emsp(message: dict):
    logger.info('push_emsp')
    with contextmanager(create_session)() as dbsession:
        data = message['body']['parameters']
        type = message['body']['message_type']
        # loop = asyncio.get_event_loop()
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))
        if type == 'OCPI Call-Back':
            loop.run_until_complete(push_update(dbsession, 'location', data))
        else:
            loop.run_until_complete(push_update(dbsession, 'charging_session', data))


@app.task(name='apollo.main.tasks.push_partner', autoretry_for=(KombuError,), retry_backoff=3)
def push_to_partner(message: dict):
    logger.info('push_partner')
    # wrap with generic response
    data = {"success": True, "data": {}, "message": "", "error": None}
    cb_data = message['body']['parameters']['db_cs']
    data['data'] = cb_data
    try:
        partner_cs = message['body']['parameters']['partner_cs']
    except KeyError:
        partner_cs = None

    try:
        loop = asyncio.get_event_loop()
    except RuntimeError as e:
        if str(e).startswith('There is no current event loop in thread'):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        else:
            logger.error('Runtime error on asyncio with error, %s', str(e))

    with contextmanager(create_session)() as dbsession:
        for partner in dbsession.query(models.ExternalOrganization).filter(
                models.ExternalOrganization.organization_type == 'Partner').all():
            url = partner.meta['session_callback_url']
            url += '/' + str(partner_cs['partner_token_id'])
            api_key = partner.meta['api_key']
            headers = {
                'X-API-Key': api_key,
                'content-type': 'application/json'
            }
            loop.run_until_complete(send_request('POST', url, data=json.dumps(data), headers=headers))


@app.task(name='apollo.main.tasks.push_cdr', autoretry_for=(ConnectTimeout,), retry_backoff=3)
def push_cdr(message: dict):  # noqa
    logger.info('push_cdr')
    hash = message['body']['parameters']['hash']
    charge_point_id = message['body']['parameters']['charge_point_id']
    charging_session = message['body']['parameters']['charging_session']
    cpo_cs = message['body']['parameters']['cpo_cs']
    try:
        has_breakdown = message['body']['parameters']['has_breakdown']
    except KeyError:
        has_breakdown = False

    cpo_id = None
    if cpo_cs:
        cpo_id = cpo_cs.get('id')
    hogging_fee = 0
    hogging_tariff = charging_session.get('meta', {}).get('billing_info', {}).get('hogging_tariff')

    if hogging_tariff:
        if hogging_tariff is not None:
            if len(hogging_tariff) > 0:
                if charging_session['hogging_start'] and charging_session['hogging_end']:
                    hogging_fee = calculate_hogging_fee(hogging_tariff,
                                                        charging_session['hogging_start'],
                                                        charging_session['hogging_end'],
                                                        has_breakdown)
    hogging_fee = round(hogging_fee, 2)

    # OCPI Charging - We as CPO
    if charging_session['id_tag'].startswith('EMSP-'):
        subscription_discount = {'subscription_discount': float(0)}
        organization_name = 'MGT'
        if str(settings.OCPI_PARTY_ID).upper() == 'CDG':
            organization_name = 'CDG'
        elif str(settings.OCPI_PARTY_ID).upper() == 'HGM':
            organization_name = 'DCH'

        with contextmanager(create_session)() as dbsession:
            # To complete
            if charging_session['id_tag'].startswith('EMSP-'):
                cdr_data = charging_session['meta']['cdr_data']
                cdr_uid = cdr_data['uid']
                db_cpo_token = dbsession.query(models.OCPICPOToken).filter(  # noqaa
                    models.OCPICPOToken.partner_ocpi_cpo_token_id == cdr_uid  # nosec
                ).first()

            if charging_session['meta']['billing_info']['billing_type'] == schema.BillingType.kwh:
                usage = calculate_charging_usage(
                    charging_session['meter_start'],
                    charging_session['meter_stop'],
                    charging_session['meta']['billing_info']['billing_type'],
                    charging_session['meta']['billing_info']['billing_cycle']
                )
            else:
                usage = calculate_charging_usage(
                    charging_session['session_start'],
                    charging_session['session_end'],
                    charging_session['meta']['billing_info']['billing_type'],
                    charging_session['meta']['billing_info']['billing_cycle']
                )
            charging_usage = float(charging_session['meter_stop']) - float(charging_session['meter_start'])

            # duration = datetime.strptime(charging_session['session_end'], "%Y-%m-%dT%H:%M:%SZ") - datetime.strptime(
            #     charging_session['session_start'], "%Y-%m-%dT%H:%M:%SZ")

            # changed to isoparse as it can automatically parse iso8601 timestamps
            duration = parser.isoparse(charging_session['session_end']) - parser.isoparse(
                charging_session['session_start'])

            amount = calculate_charging_cost(
                unit_price=charging_session['meta']['billing_info']['billing_unit_fee'],
                usage=usage,
                constant=charging_session['meta']['billing_info']['connection_fee']
            )
            amount = round(float(amount), 2)
            # tax_info = calculate_tax_amount(dbsession,
            #                                 charge_point_id,
            #                                 amount,
            #                                 charging_session['meta']['billing_info']['billing_currency'])

            # amount = tax_info['total_amount']

            # No discount for EXT
            meta_data = {
                'transaction_id': str(charging_session['transaction_id']),
                'organization_name': organization_name
            }
            invoice_number = str(organization_name) + '-' + str(charging_session['transaction_id'])

            total_charge_amount = round(float(amount) - float(subscription_discount['subscription_discount']), 2)
            total_charge_amount = round(total_charge_amount + hogging_fee, 2)
            tax_info = calculate_tax_amount(dbsession,
                                            charge_point_id,
                                            total_charge_amount,
                                            charging_session['meta']['billing_info']['billing_currency'])
            total_charge_amount = tax_info['total_amount']
            # total_charge_amount = tax_info['total_amount'] + hogging_fee

            charging_session_bill = schema.ChargingSessionBill(
                charging_session_id=charging_session['id'],
                usage_amount=amount,
                usage_type=charging_session['meta']['billing_info']['billing_type'],
                discount=subscription_discount,
                meta=meta_data,
                invoice_number=invoice_number,
                tax_amount=tax_info['tax_amount'],
                tax_rate=tax_info['tax_rate'],
                total_amount=total_charge_amount,
                charging_session_type=charging_session['charging_session_type'],
                charge_point_id=charge_point_id,
                charging_status=charging_session['status'],
                cpo_id=cpo_id,
                id_tag=charging_session['id_tag'],
                hogging_fee=hogging_fee,
                preferred_payment_flow=charging_session['meta']['billing_info'].get('preferred_payment_flow', None),
                preferred_payment_method=charging_session['meta']['billing_info'].get('preferred_payment_method', None),
                is_low_wallet_balance=charging_session['meta']['billing_info'].get('is_low_wallet_balance', None)
            )

            # if APPLY_FREE_CHARGING is true, set to free charging within 60 seconds or less than 5wh
            if APPLY_FREE_CHARGING:
                if duration.total_seconds() <= 60 or charging_usage <= 5:
                    # amount = 0
                    charging_session_bill.usage_amount = 0
                    charging_session_bill.discount = {"subscription_discount": 0.0}
                    # subscription_discount = {"subscription_discount": 0.0}

                    # We modify the charge_amount to be charging hogging only as we assume <= 60 seconds
                    # or <= 5wh charging to be free.
                    if hogging_fee > 0:
                        total_charge_amount = hogging_fee
                        charging_session_bill.total_amount = hogging_fee
                        hogging_tax_info = calculate_tax_amount(dbsession,
                                                                charge_point_id,
                                                                hogging_fee,
                                                                charging_session['meta']['billing_info']['billing_currency'])
                        charging_session_bill.tax_amount = hogging_tax_info['tax_amount']
                    else:
                        total_charge_amount = 0
                        charging_session_bill.total_amount = 0
                        charging_session_bill.tax_amount = 0

            try:
                db_charging_session_bill = dbsession.query(models.ChargingSessionBill).filter(
                    models.ChargingSessionBill.charging_session_id == charging_session['id'],
                ).first()
                if not db_charging_session_bill:
                    db_charging_session_bill = models.ChargingSessionBill(**charging_session_bill.dict())
                    dbsession.add(db_charging_session_bill)
                    dbsession.commit()
            except IntegrityError as e:
                logger.error(e)
                return None

            charging_session_bill_dict = schema.ChargingSessionBillResponse.from_orm(db_charging_session_bill).dict()
            # total_charge_amount = round(float(amount) -
            #                             float(subscription_discount['subscription_discount']), 2)
            # charging_session['total_charge_amount'] = total_charge_amount
            charging_session['total_charge_amount'] = float(charging_session_bill_dict['total_amount'])
            charging_session['charging_usage'] = charging_usage
            charging_session['duration'] = float(duration.total_seconds())
            # meta = json.loads(schema.MembershipResponse.from_orm(db_member).json())  # static membership data

            db_operator_chargepoint = dbsession.query(models.OperatorChargepoint).filter(
                models.OperatorChargepoint.charge_point_id == charge_point_id
            ).first()
            if not db_operator_chargepoint:
                logger.error('Operator charge point not found based on %s charge point', charge_point_id)

            response_message = schema.Message(
                reason=schema.MessageReason.response,
                body=schema.MessageBody(
                    message_type='ChargingBillResponse',
                    parameters={
                        'hash': hash,
                        'charging_session_bill_id': str(charging_session_bill_dict['id']),
                        'meta': {
                            # 'member': json.loads(schema.OCPICPOTokenResponse.from_orm(db_cpo_token).json()),
                            'operator': json.loads(schema.ShallowOperatorResponse.from_orm(
                                db_operator_chargepoint.operator).json())
                        }
                    }
                )
            )

            logger.info('Sent message # %s', response_message.dict())

            # loop = asyncio.get_event_loop()
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError as e:
                if str(e).startswith('There is no current event loop in thread'):
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                else:
                    logger.error('Runtime error on asyncio with error, %s', str(e))
            cs = {
                'cpo_cs': cpo_cs,
                'charging_session': charging_session,
                'charging_session_bill': charging_session_bill_dict,
            }
            loop.run_until_complete(push_update(dbsession, 'invoice', cs))

            return cs
    else:
        return False


@app.task(name='apollo.main.tasks.request_authorization', autoretry_for=(KombuError,),
          retry_backoff=3)
def request_authorization(message: dict):  # noqa: MC0001
    logger.info('request_authorization')
    publisher = ChargerServicePublisher()
    id_tag = message['body']['parameters']['id_tag']
    hash = message['body']['parameters']['hash']
    member_id = None
    try:
        request_type = message['body']['parameters']['request_type']
    except KeyError:
        request_type = None

    connector_id = message['body']['parameters'].get('connector_id', None)
    billing_unit_fee = message['body']['parameters'].get('billing_unit_fee', None)
    billing_currency = message['body']['parameters'].get('billing_currency', None)
    check_ocpi_roaming = message['body']['parameters'].get('check_ocpi_roaming', False)

    def check_charging_permission(dbsession,  # pylint: disable-all
                                  db_id_tag, connector_id, billing_unit_fee, billing_currency):
        if float(billing_unit_fee) <= 0.01:
            return True, f'FREE-CHARGING'
        discounted_amount = get_discounted_billing_unit_fee_by_id_tag(dbsession, db_id_tag, connector_id,
                                                                      billing_unit_fee)

        # if the charger is free
        if discounted_amount < 0.01:
            return True, f'FREE-CHARGING'

        if settings.REQUEST_AUTH_PERFORM_PRE_AUTH:
            # If not wallet preferred payment method
            if not db_id_tag.member.preferred_payment_method == 'Wallet':
                if db_id_tag.member and not has_primary_credit_card_by_id_tag(dbsession, db_id_tag, billing_currency):
                    return False, 'No-CC-Not-Wallet'
        else:
            if db_id_tag.member and not has_primary_credit_card_by_id_tag(dbsession, db_id_tag, billing_currency):
                return False, 'No-CC'

        # if is not member, and not free
        if not db_id_tag.member and discounted_amount >= 0.01:
            return False, 'No-CC'

        if settings.DUNNING_ENABLED:
            if db_id_tag.member:
                is_allowed_by_dunning_check = check_user_dunning_status(dbsession, db_id_tag.member_id)
                if not is_allowed_by_dunning_check:
                    return False, 'Dunning-Blocked'

        # Only run if the request type is authorize, as stop or start do not need pre-auth or whatso
        if request_type and request_type == 'Authorize':
            # Check is required to perform pre-auth during RFID
            if settings.REQUEST_AUTH_PERFORM_PRE_AUTH:
                # Check if is required to perform pre-auth for the specific country
                if billing_currency in settings.REQUEST_AUTH_PRE_AUTH_CURRENCY_LIST:
                    set_admin_as_context_user(dbsession)
                    use_wallet = False
                    db_pre_auth_info = get_or_create_pre_auth_info_cc(dbsession, str(db_id_tag.member.id),
                                                                      billing_currency)
                    pre_auth_amount = db_pre_auth_info.pre_auth_amount
                    if db_id_tag.member.preferred_payment_method == 'Wallet':
                        use_wallet = True
                        db_wallet = get_wallet_by_member_id_currency(dbsession, str(db_id_tag.member.id),
                                                                     billing_currency)
                        db_wallet = update_wallet_balance(dbsession, str(db_wallet.id))
                    if use_wallet:
                        if float(db_wallet.balance) < round(float(discounted_amount), 2) or float(
                                db_wallet.balance) <= 0.1:
                            logger.error("ID-TAG %s do not have enough wallet to pre-auth via wallet",
                                         str(db_id_tag.id_tag))
                            return False, 'Insufficient wallet-balance'
                        pre_auth_amount = float(db_wallet.balance)

                    payment_type = 'Credit-Card'
                    if use_wallet:
                        payment_type = 'Wallet'

                    old_pre_auth_payment = get_non_binded_pre_auth_by_member_id_with_payment_type(dbsession,
                                                                                                  str(db_id_tag.member.id),
                                                                                                  billing_currency,
                                                                                                  payment_type)
                    if old_pre_auth_payment:
                        return True, f'OLD-PRE-AUTH-ID-{str(old_pre_auth_payment.id)}'

                    if use_wallet:
                        db_pre_auth = handle_wallet_pre_auth(dbsession, str(db_id_tag.member.id), pre_auth_amount,
                                                             billing_currency)
                        return True, f'WALLET-PRE-AUTH-{str(db_pre_auth.id)}'

                    # Else, perform Credit-Card Pre-Auth.
                    processed_response_code, db_pre_auth_payment = handle_credit_card_pre_auth('request', dbsession,
                                                                                               str(db_id_tag.member.id),
                                                                                               False, pre_auth_amount,
                                                                                               billing_currency,
                                                                                               is_from_task=True)
                    if processed_response_code != 200:
                        return False, '3DS-NOT-ALLOWED'
                    return True, f'PRE-AUTH-SUCCESSFUL-{db_pre_auth_payment.id}'
                else:
                    return True, f'PRE-AUTH-NOT-ENABLED-COUNTRY-SPECIFIED'
            return True, f'PRE-AUTH-NOT-ENABLED-CC-ALLOWED'
        return True, f'NOT-AUTHORIZE-REQUEST-TYPE-NOT-CHECKING-PRE-AUTH'

    with contextmanager(create_session)() as dbsession:
        result = True
        reason = None
        db_id_tag = dbsession.query(models.IDTag).filter(
            func.lower(models.IDTag.id_tag) == id_tag.lower(),
        ).first()
        authorization_type = None
        vehicle_info = None

        if db_id_tag:
            member_id = db_id_tag.member_id
            expiry_date = db_id_tag.expiration
            parent_id_tag = db_id_tag.parent_id_tag.id_tag if db_id_tag.parent_id_tag else None
            authorization_type = db_id_tag.type
            if authorization_type == 'VID' and parent_id_tag is None:
                db_membership = dbsession.query(models.Membership).filter(
                    models.Membership.id == db_id_tag.member_id,
                ).first()
                parent_id_tag = db_membership.user_id_tag

            def get_vehicle_info_by_id(vehicle_id):
                vehicle = dbsession.query(models.Vehicle).filter_by(id=vehicle_id).first()
                if vehicle is None:
                    return None
                vehicle_info = json.dumps(schema.VehicleAuthResponse.from_orm(vehicle).dict(), default=str)
                return vehicle_info

            if authorization_type == 'VID':
                if db_id_tag.emaid_id:
                    db_emaid = dbsession.query(models.Emaid).filter_by(id=db_id_tag.emaid_id).first()
                    if db_emaid:
                        vehicle_info = get_vehicle_info_by_id(db_emaid.vehicle_id)

                elif db_id_tag.autocharge_id:
                    db_autocharge = dbsession.query(models.Autocharge).filter_by(id=db_id_tag.autocharge_id).first()
                    if db_autocharge:
                        vehicle_info = get_vehicle_info_by_id(db_autocharge.vehicle_id)

            if not db_id_tag.is_active:
                result = False
                reason = 'Blocked'

            if db_id_tag.expiration.astimezone(timezone.utc) < datetime.now(timezone.utc):
                result = False
                reason = 'Expired'

            # disable in prod until confirmation
            # This is Pre-Authorization (RFID Flow) instead of Credit-Card's Pre-Authorization
            if settings.PRE_AUTH_CC_CHECK:
                if result:
                    result, reason = check_charging_permission(dbsession, db_id_tag, connector_id, billing_unit_fee,
                                                               billing_currency)

        else:
            db_membership = dbsession.query(models.Membership).filter(
                func.lower(models.Membership.user_id_tag) == id_tag.lower(),
            ).first()

            if db_membership:
                member_id = db_membership.id
                expiry_date = (datetime.now(timezone.utc) + timedelta(
                    days=100)).isoformat()  # user_id_tag has no expiration
                parent_id_tag = None
                authorization_type = "REMOTE"

            else:
                member_id = None
                result = False
                reason = 'Invalid'
                expiry_date = None
                parent_id_tag = None
        
        def validate_ocpi_emaid():
            db_ocpi_cpo_token = dbsession.query(models.OCPICPOToken).filter(
                models.OCPICPOToken.contract_id == id_tag,
                models.OCPICPOToken.valid == True,
                models.OCPICPOToken.type == 'OTHER'
            ).first()

            result = False
            reason = "Invalid"

            if db_ocpi_cpo_token:
                result = True
                reason = 'CPO-Emaid'
                return result, reason, db_ocpi_cpo_token
            return result, reason, None

        token_payload = None
        if check_ocpi_roaming and not result:
            result, reason, db_ocpi_cpo_token = validate_ocpi_emaid()
            if db_ocpi_cpo_token:
                token_payload = schema.OCPICPOToken(**vars(db_ocpi_cpo_token)) # TO CONFIRM
        parameters = {
            'hash': hash,
            'id_tag': id_tag,
            'expiry_date': str(expiry_date),
            'parent_id_tag': parent_id_tag,
            'is_authorized': result,
            'reason': reason,
        }
        if token_payload:
            parameters['token_payload'] = token_payload
        if authorization_type:
            parameters['authorization_type'] = authorization_type
        if vehicle_info:
            parameters['vehicle_info'] = vehicle_info

        response_message = schema.Message(
            reason=schema.MessageReason.response,
            body=schema.MessageBody(
                message_type='AuthorizationResponse',
                parameters=parameters
            )
        )
        publisher.publish_message(response_message.dict(), message['reply_to'])

        logger.info('Sent message # %s', response_message.dict())

        id_tag_auth_histories_schema = schema.IDTagAuthorizationHistories(id_tag=id_tag, result=str(result),
                                                                          result_reason=reason,
                                                                          connector_id=connector_id,
                                                                          request_type=request_type)
        id_tag_auth_histories = models.IDTagAuthorizationHistories(**id_tag_auth_histories_schema.dict())
        dbsession.add(id_tag_auth_histories)
        dbsession.commit()
        if settings.AUTHORIZE_MESSAGE_ENABLED:
            if not result and (request_type == 'Authorize' or request_type == 'DataTransferAuthorize') \
                and member_id:
                message_webhook_url = settings.AUTHORIZATION_MESSAGE_WEBHOOK_URL
                message_webhook_api_key = settings.AUTHORIZATION_MESSAGE_WEBHOOK_API_KEY
                headers = {
                    'X-API-Key': message_webhook_api_key,
                    'content-type': 'application/json'
                }
                message_data = {
                    "event": "Message Callback",
                    "payload": {"member_id": str(member_id)}
                }
                if reason == 'Dunning-Blocked':
                    message_data["event_banner_title"] = "User Blocked"
                    message_data["event_banner"] = "Please clear off ALL outstanding transaction before start charging."

                elif reason == 'Blocked':
                    message_data["event_banner_title"] = "Tag Blocked"
                    message_data["event_banner"] = "User Blocked due to an inactive tag used"

                elif reason == 'Expired':
                    message_data["event_banner_title"] = "Tag Expired"
                    message_data["event_banner"] = "User Blocked due to an expired tag used"

                elif reason == 'No-CC':
                    message_data["event_banner_title"] = "No Payment Method found"
                    message_data["event_banner"] = "Please add a valid payment method before start charging."

                else:
                    message_data["event_banner_title"] = "Start Charging Failed"
                    message_data["event_banner"] = "You may have used an invalid tag to start charging. Please check."

                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError as e:
                    if str(e).startswith('There is no current event loop in thread'):
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                    else:
                        logger.error('Runtime error on asyncio with error, %s', str(e))
                try:
                    loop.run_until_complete(
                        send_request('POST', message_webhook_url, data=json.dumps(message_data),
                                    headers=headers))
                except Exception as e:  # pylint: disable=broad-except
                    logger.error("Error calling message webhook on chargEV app with error: %s", str(e))


@app.task(name='apollo.main.tasks.send_ocpp_status_change', autoretry_for=(ConnectTimeout,),
          retry_backoff=3)
def send_ocpp_status_change(message: dict):  # pylint:disable=too-many-statements, # noqa: MC0011
    """
    Send callback request for OCPP status change
    """

    # https://stackoverflow.com/questions/26685248/difference-between-data-and-json-parameters-in-python-requests-package
    headers = {
        'X-API-Key': settings.CALL_BACK_API_KEY,
        'content-type': 'application/json'
    }
    urls = settings.CALL_BACK_URL
    if urls.startswith("[") and urls.endswith("]"):
        # Remove the square brackets and split the string by comma
        urls = [x.strip() for x in urls[1:-1].split(',')]
    else:
        urls = [urls]  # Convert the single URL to a list with one item

    pre_auth_webhook_url = settings.PRE_AUTH_CALL_BACK_URL
    hogging_webhook_url = settings.HOGGING_CALL_BACK_URL

    params = message['body']['parameters']
    id_tag = params.pop('id_tag')
    is_spam = params.pop('spam', None)
    special_condition = params.pop('special_condition', None)
    logger.info('send_ocpp_status_change, special_condition: %s', str(special_condition))

    # logger.info('is_special_condition %s', str(special_condition))
    db_member = None

    if id_tag is not None:
        with contextmanager(create_session)() as dbsession:
            db_id_tag = dbsession.query(models.IDTag).filter(
                func.lower(models.IDTag.id_tag) == str(id_tag).lower(),
            ).first()
            if db_id_tag:
                db_member = db_id_tag.member
            if not db_id_tag:
                db_id_tag = dbsession.query(models.Membership).filter(
                    func.lower(models.Membership.user_id_tag) == str(id_tag).lower(),
                ).first()
                if db_id_tag:
                    db_member = db_id_tag

    if db_member:
        data = schema.StatusCallbackPayload(**params, member_id=str(db_member.id))
    else:
        data = schema.StatusCallbackPayload(**params, member_id=None)

    callback_data = schema.Callback(
        payload=data.dict(),
        event='OCPP Status Change'
    )

    # loop = asyncio.get_event_loop()
    # All info related to chargEV apps (Manja Labs)
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError as e:
        if str(e).startswith('There is no current event loop in thread'):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        else:
            logger.error('Runtime error on asyncio with error, %s', str(e))
    try:
        if special_condition is not None:
            if special_condition.upper() in ['AUTO-CUT', 'AUTO-CUT-SOON']:
                if pre_auth_webhook_url is not None:
                    with contextmanager(create_session)() as dbsession:
                        charging_session_id = params['charging_session_id']
                        pre_auth = get_pre_auth_payment_by_charging_session_id(dbsession, charging_session_id)
                        if pre_auth:
                            # Notify user that their session being auto-cut due to estimated cost over budget.
                            pre_auth_dict = json.loads(schema.PreAuthPaymentResponseToMobile.from_orm(pre_auth).json())
                            member_id = str(pre_auth.payment_request.member_id)
                            pre_auth_dict['member_id'] = member_id
                            event_banner = None
                            event_in_app_description = None
                            if special_condition.upper() == 'AUTO-CUT':
                                if pre_auth.payment_type == 'Wallet':
                                    event_banner = ('Your charging session has ended as the charging fee '
                                                    'has reached your remaining Wallet balance.')

                                else:
                                    event_banner = ('Your charging session has ended as the charging fee '
                                                    'has reached the pre-authorized hold amount placed on your card')
                            else:
                                if pre_auth.payment_type == 'Wallet':
                                    event_banner = ('Your charging session may be automatically stopped soon as the '
                                                    'remaining balance is running low.')
                                else:
                                    event_banner = ('Your charging session may be automatically stopped soon as the '
                                                    'pre-authorized hold amount is running low.')
                            event_in_app_description = event_banner
                            callback_data = schema.CallbackWithNotification(
                                payload=pre_auth_dict,
                                event=f'SESSION-{special_condition.upper()}',
                                show_in_app=True,
                                event_banner=event_banner,
                                event_in_app_description=event_in_app_description,

                            )
                            response = loop.run_until_complete(
                                send_request('POST', pre_auth_webhook_url, data=json.dumps(callback_data.dict()),
                                             headers=headers))

                            _response = {'response_code': response.status_code,
                                         'response_body': str(response.content.decode())}
                            callback_data.payload = json.loads(json.dumps(callback_data.dict()))
                            callback_response = schema.CallbackHTTPResponse(**callback_data.dict(), response=_response)

                            callback = models.Callback(**callback_response.dict())

                            dbsession.add(callback)
                            dbsession.commit()

            if special_condition.upper() in ['HOGGING-STARTED']:
                if hogging_webhook_url is not None:
                    with contextmanager(create_session)() as dbsession:
                        hogging_event = schema.HoggingCallback(ocpp_status=params['ocpp_status'],
                                                               hogging_status='Started')
                        if db_member is not None:
                            hogging_event.member_id = str(db_member.id)
                        else:
                            return None

                        if params['grace_period_seconds'] is not None:
                            hogging_event.grace_period_seconds = params['grace_period_seconds']
                        else:
                            hogging_event.grace_period_seconds = float(settings.HOGGING_START_GRACE_PERIOD_SECONDS)

                        callback_data = schema.Callback(
                            payload=hogging_event.dict(),
                            event=f'{special_condition.upper()}'
                        )
                        response = loop.run_until_complete(
                            send_request('POST', hogging_webhook_url, data=json.dumps(hogging_event.dict()),
                                         headers=headers))

                        _response = {'response_code': response.status_code,
                                     'response_body': str(response.content.decode())}
                        callback_response = schema.CallbackHTTPResponse(**callback_data.dict(), response=_response)

                        callback = models.Callback(**callback_response.dict())

                        dbsession.add(callback)
                        dbsession.commit()
        else:
            for url in urls:
                if is_spam is not None and is_spam is False:
                    response = loop.run_until_complete(
                        send_request('POST', url, data=json.dumps(callback_data.dict()), headers=headers))

                    _response = {'response_code': response.status_code, 'response_body': str(response.content.decode())}
                    callback_response = schema.CallbackHTTPResponse(**callback_data.dict(), response=_response)

                    callback = models.Callback(**callback_response.dict())

                    with contextmanager(create_session)() as dbsession:
                        dbsession.add(callback)
                        dbsession.commit()

    except Exception as e:  # pylint: disable=broad-except
        logger.error("Error calling callback on chargEV app with error: %s", str(e))

    # All info related to partner
    try:
        with contextmanager(create_session)() as dbsession:
            for partner in dbsession.query(models.ExternalOrganization).filter(
                    models.ExternalOrganization.organization_type == 'Partner').all():
                if isinstance(data, dict):
                    cp_id = data['charge_point_id']
                else:
                    cp_id = data.charge_point_id

                db_operator_chargepoint = dbsession.query(models.OperatorChargepoint).filter(
                    models.OperatorChargepoint.charge_point_id == cp_id,
                    models.OperatorChargepoint.operator_id == partner.id,
                ).first()

                if not db_operator_chargepoint and partner.roaming_organization:
                    db_operator = dbsession.query(models.Operator).filter(models.Operator.organization_id ==
                                                                          partner.roaming_organization).first()
                    if db_operator:
                        db_operator_chargepoint = dbsession.query(models.OperatorChargepoint).filter(
                            models.OperatorChargepoint.charge_point_id == cp_id,
                            models.OperatorChargepoint.operator_id == db_operator.id,
                        ).first()

                if db_operator_chargepoint:
                    ocpp_callback = partner.meta['ocpp_callback_url']
                    api_key = partner.meta['api_key']
                    headers = {
                        'X-API-Key': api_key,
                        'content-type': 'application/json'
                    }
                    partner_callback_data = json.loads(json.dumps(callback_data.dict()))
                    # partner should not be seeing member_id
                    partner_callback_data['payload']['member_id'] = None

                    partner_callback_data['payload']['timestamp'] = datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ")
                    del partner_callback_data['payload']['member_id']
                    partner_callback_data = partner_callback_data['payload']
                    # partner_callback_data = remove_null_values(partner_callback_data)

                    # wrap with generic response
                    partner_callback = {"success": True, "message": "", "error": None, 'data': partner_callback_data}
                    response = loop.run_until_complete(
                        send_request('POST', ocpp_callback, data=json.dumps(partner_callback), headers=headers))

                    _response = {'response_code': response.status_code, 'response_body': str(response.content.decode())}
                    callback_response = schema.CallbackHTTPResponse(**callback_data.dict(), response=_response)

                    callback = models.Callback(**callback_response.dict())
                    dbsession.add(callback)
                    dbsession.commit()
    except Exception as e:  # pylint: disable=broad-except
        logger.error("Error calling callback on partner with error as: %s", str(e))


@app.task(name='apollo.main.tasks.notify_command', autoretry_for=(ConnectTimeout,))
def notify_command_module(message: dict):  # noqa: MC0001
    """
    Notify Partner on their initiation command
    """
    logger.info('notify_command')

    # https://stackoverflow.com/questions/26685248/difference-between-data-and-json-parameters-in-python-requests-package
    headers = {
        'X-API-Key': settings.CALL_BACK_API_KEY,
        'content-type': 'application/json'
    }
    urls = settings.COMMAND_CALL_BACK_URL
    if urls is None:
        logger.error("Command notification callback is not set")
        return False
    if urls.startswith("[") and urls.endswith("]"):
        # Remove the square brackets and split the string by comma
        urls = [x.strip() for x in urls[1:-1].split(',')]
    else:
        urls = [urls]  # Convert the single URL to a list with one item

    params = message['body']['parameters']
    params['display_support'] = False

    id_tag = params.pop('id_tag')
    command_type = params.pop('command_type')

    db_member = None
    with contextmanager(create_session)() as dbsession:
        if id_tag is not None:
            if command_type.startswith('OCPP'):
                event_type = 'Command Callback - OCPP'
                db_id_tag = dbsession.query(models.IDTag).filter(
                    func.lower(models.IDTag.id_tag) == str(id_tag).lower(),
                ).first()

                if db_id_tag:
                    db_member = db_id_tag.member
                if not db_id_tag:
                    db_id_tag = dbsession.query(models.Membership).filter(
                        func.lower(models.Membership.user_id_tag) == str(id_tag).lower(),
                    ).first()
                    if db_id_tag:
                        logger.info("Command Callback Match")
                        db_member = db_id_tag
            else:
                event_type = 'Command Callback - OCPI'
                db_member = get_membership_by_ocpi_token_id(dbsession, id_tag)

        command_message = params.get('command_message', None)
        command_result = params.get('command_result', None)
        if command_message is not None:
            command_message, display_support = map_command_message_to_friendly_message(command_message, command_result)
            params['display_support'] = display_support
            if command_message is not None:
                params['command_message'] = command_message

        if db_member:
            data = schema.CommandCallbackPayload(**params, member_id=str(db_member.id))
        else:
            data = schema.CommandCallbackPayload(**params, member_id=None)
        callback_data = schema.Callback(
            payload=data.dict(),
            event=event_type
        )
        logger.info('Notify Command Payload: %s', str(callback_data.dict()))

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))
        try:
            for url in urls:
                response = loop.run_until_complete(
                    send_request('POST', url, data=json.dumps(callback_data.dict()), headers=headers))

                _response = {'response_code': response.status_code, 'response_body': str(response.content.decode())}
                callback_response = schema.CallbackHTTPResponse(**callback_data.dict(), response=_response)

                callback = models.Callback(**callback_response.dict())

                dbsession.add(callback)
                dbsession.commit()

        except Exception as e:  # pylint: disable=broad-except
            logger.error("Error notify partner on command module with error as: %s", str(e))


@app.task(name='apollo.main.tasks.notify_pre_auth', autoretry_for=(ConnectTimeout,),
          retry_backoff=3)
def notify_pre_auth(message: dict):  # noqa: MC0001
    message_type = message.pop('message_type')
    pre_auth_id = message.pop('pre_auth_id')
    pre_auth_webhook_url = settings.PRE_AUTH_CALL_BACK_URL
    used_amount = 0.00
    payment_request = None
    if pre_auth_webhook_url is None:
        logger.error("Pre-auth notification callback is not set")
        return False

    headers = {
        'X-API-Key': settings.CALL_BACK_API_KEY,
        'content-type': 'application/json'
    }

    try:
        loop = asyncio.get_event_loop()
    except RuntimeError as e:
        if str(e).startswith('There is no current event loop in thread'):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        else:
            logger.error('Runtime error on asyncio with error, %s', str(e))

    with contextmanager(create_session)() as dbsession:
        pre_auth = get_pre_auth_payment_by_id(dbsession, pre_auth_id)

        if pre_auth:
            if pre_auth.used_amount:
                used_amount = float(pre_auth.used_amount)
            member_id = pre_auth.payment_request.member_id
            pre_auth_dict = json.loads(schema.PreAuthPaymentResponseToMobile.from_orm(pre_auth).json())
            pre_auth_dict['member_id'] = str(member_id)
            callback_data = schema.Callback(
                payload=pre_auth_dict,
                event=f'PRE-AUTH-{message_type.upper()}'
            )
            if message_type.upper() in ['CAPTURE', 'WALLET-CAPTURE']:
                try:
                    session_id = pre_auth.charging_session_id
                    cs_bill = get_charging_session_bill_by_charging_session(dbsession, str(session_id))
                    payment_request = get_payment_request_by_charging_session_bill_id(dbsession, str(cs_bill.id))
                    used_amount = float(cs_bill.total_amount)
                except Exception as e:  # pylint: disable=broad-except
                    logger.error('Exception on callback with error as %s', str(e))
                    _ = ''

            if message_type.upper() == 'SUCCESS':
                if pre_auth.payment_type == 'Wallet':
                    event_banner = (f'A pre-authorization hold of {pre_auth.currency} {float(pre_auth.amount):.2f}  '
                                    f'has been placed on your account.')
                else:
                    event_banner = (f'A pre-authorization hold of {pre_auth.currency} {float(pre_auth.amount):.2f}  '
                                    f'has been placed on your account.')
            elif message_type.upper() == 'CAPTURE':
                if pre_auth.payment_type == 'Wallet':
                    event_banner = (f'Your payment of {pre_auth.currency} {float(used_amount):.2f} '
                                    f'has been deducted.')
                else:
                    event_banner = (f'Your payment of {pre_auth.currency} {float(used_amount):.2f} '
                                    f'has been deducted.')
            elif message_type.upper() == 'WALLET-CAPTURE':
                if payment_request and payment_request.type in [PaymentRequestType.partial,
                                                                PaymentRequestType.partial_direct]:
                    event_banner = (f'Your payment of {pre_auth.currency} {float(used_amount):.2f} '
                                    f'has been deducted.')
                else:
                    if pre_auth.payment_type == 'Wallet':
                        event_banner = (f'The amount of {pre_auth.currency} {float(used_amount):.2f} '
                                        f'has been deducted from your Wallet balance')
                    else:
                        event_banner = (f'The amount of {pre_auth.currency} {float(used_amount):.2f} '
                                        f'has been deducted from your Wallet balance')

            event_in_app_description = event_banner

            callback_data = schema.CallbackWithNotification(
                payload=pre_auth_dict,
                event=f'PRE-AUTH-{message_type.upper()}',
                show_in_app=False,
                event_banner=event_banner,
                event_in_app_description=event_in_app_description,

            )
            response = loop.run_until_complete(
                send_request('POST', pre_auth_webhook_url, data=json.dumps(callback_data.dict()),
                             headers=headers))

            _response = {'response_code': response.status_code,
                         'response_body': str(response.content.decode())}
            callback_data.payload = json.loads(json.dumps(callback_data.dict()))

            callback_response = schema.CallbackHTTPResponse(**callback_data.dict(), response=_response)

            callback = models.Callback(**callback_response.dict())

            dbsession.add(callback)
            dbsession.commit()
    return True


@app.task(name='apollo.main.tasks.associate_cp_with_operator')
def associate_cp_with_operator(operator_id: str, charge_point_id: str):
    with contextmanager(create_session)() as dbsession:
        try:
            dbsession.query(models.Operator).filter(
                models.Operator.id == operator_id,
            ).one()

            # get previous association
            db_chargepoint_operator = dbsession.query(
                models.OperatorChargepoint
            ).filter(models.OperatorChargepoint.charge_point_id == charge_point_id).first()

            if db_chargepoint_operator:
                db_chargepoint_operator.operator_id = operator_id
            else:
                dbsession.add(models.OperatorChargepoint(
                    operator_id=operator_id,
                    charge_point_id=charge_point_id
                ))
            dbsession.commit()
        except IntegrityError:
            logger.error('could not associate operator %s with charge point %s', operator_id, charge_point_id)
        except NoResultFound:
            logger.error('could not associate operator %s with charge point %s, operator not found',
                         operator_id, charge_point_id)


@app.task(name='apollo.main.tasks.associate_pc_with_operator')
def associate_pc_with_operator(operator_id: str, power_cable_id: str):
    with contextmanager(create_session)() as dbsession:
        try:
            dbsession.query(models.Operator).filter(
                models.Operator.id == operator_id,
            ).one()

            # get previous association
            db_chargepoint_operator = dbsession.query(
                models.OperatorPowercable
            ).filter(models.OperatorPowercable.power_cable_id == power_cable_id).first()

            if db_chargepoint_operator:
                db_chargepoint_operator.operator_id = operator_id
            else:
                dbsession.add(models.OperatorPowercable(
                    operator_id=operator_id,
                    power_cable_id=power_cable_id
                ))
            dbsession.commit()
        except IntegrityError:
            logger.error('could not associate operator %s with power cable %s', operator_id, power_cable_id)
        except NoResultFound:
            logger.error('could not associate operator %s with power cable %s, operator not found',
                         operator_id, power_cable_id)


@app.task(name='apollo.ocpi.tasks.send_command_response')
def send_command_response(self, cms_url, emsp_url):
    try:
        cms_headers = {'is_superuser': 'True', 'is_staff': 'True'}
        response = requests.get(cms_url, headers=cms_headers, timeout=100)
        if response.json()['status'] in ['Reserved', 'Done', 'Canceled']:
            result_data = {
                "result": 'ACCEPTED',
                "message": 'Command accepted'
            }
            requests.post(emsp_url, json=result_data, timeout=100)
        else:
            self.retry(exc=MaxRetriesExceededError, countdown=10, max_retries=12)
    except MaxRetriesExceededError:
        result_data = {
            "result": 'FAILED',
            "message": 'Command failed'
        }
        requests.post(emsp_url, json=result_data, timeout=100)


@app.task(name='apollo.main.tasks.request_discount_info', autoretry_for=(ConnectTimeout,),
          retry_backoff=3)
def request_discounted_amount(message: dict):
    """Request a discounted price
    """

    logger.info('request_discounted_amount')
    publisher = ChargerServicePublisher()
    id_tag = message['body']['parameters']['id_tag']
    hash = message['body']['parameters']['hash']
    connector_id = message['body']['parameters']['connector_id']
    connector_currency = message['body']['parameters']['connector_currency']
    is_guest = False
    with contextmanager(create_session)() as dbsession:
        discount_info = get_discount_type_by_id_tag(dbsession, id_tag, connector_id)
        try:
            db_member = crud.get_membership_by_id_tag(dbsession, id_tag)
            membership_id = db_member.id
        except exceptions.ApolloObjectDoesNotExist:
            membership_id = None
        except Exception as e:  # pylint: disable=broad-except
            membership_id = None
            logger.error('Getting membership id from id-tage error, marking promo usage as None with error as %s',
                         str(e))
        if membership_id is not None:
            db_user = crud.get_user_by_id(dbsession, db_member.user_id)
            if db_user.is_guest:
                is_guest = True
            try:
                promo_code_usage = crud.get_one_promo_code_usage(dbsession, membership_id, is_booked=True,
                                                                 is_used=False)
            except Exception as e:  # pylint: disable=broad-except
                promo_code_usage = None
                logger.error('Getting promo code from membership error, marking promo usage as None %s', str(e))
        else:
            promo_code_usage = None
        preferred_payment_flow = get_preferred_payment_flow_by_id_tag(dbsession, id_tag)
        preferred_payment_method = get_preferred_payment_method_by_id_tag(dbsession, id_tag)
        is_low_wallet_balance = check_if_low_wallet_balance(dbsession, id_tag, connector_currency)

        subscription_plan = None
        subscription_custom_plan = None
        subscription_tariff_plan = None
        subscription_operator_plan = None
        try:
            connector = get_connector(dbsession, connector_id)
            db_subscription_plans = get_default_subscription_and_plans(
                db=dbsession,
                connector=connector,
                id_tag=id_tag,
            )

            # inconsistent terminology. Here subscription_plan is actually the default subscription
            subscription_plan = db_subscription_plans.get('subscription')
            subscription_custom_plan = db_subscription_plans.get('subscription_custom_plan')
            subscription_tariff_plan = db_subscription_plans.get('subscription_tariff_plan')
            subscription_operator_plan = db_subscription_plans.get('subscription_operator_plan')

            if subscription_plan is not None:
                try:
                    subscription_plan = json.loads(
                        schema.SubscriptionResponseNoMember.from_orm(subscription_plan).json())
                except Exception as e:  # pylint: disable=broad-except
                    subscription_plan = None
                    logger.error('Getting subscription_plan error, marking these as None with error as %s',
                                 str(e))
            if subscription_custom_plan is not None:
                try:
                    subscription_custom_plan = json.loads(
                        schema.ShallowSubscriptionCustomPlanResponse.from_orm(subscription_custom_plan).json())
                except Exception as e:  # pylint: disable=broad-except
                    subscription_custom_plan = None
                    logger.error('Getting subscription_custom_plan error, marking these as None with error as %s',
                                 str(e))
                    
            if subscription_tariff_plan is not None:
                try:
                    subscription_tariff_plan = json.loads(
                        schema.ShallowSubscriptionTariffPlanResponse.from_orm(subscription_tariff_plan).json())
                except Exception as e:
                    subscription_tariff_plan = None
                    logger.error('Getting subscription_tariff_plan error, marking these as None with error as %s',
                                 str(e))
                
            if subscription_operator_plan is not None:
                try:
                    subscription_operator_plan = json.loads(
                        schema.ShallowSubscriptionOperatorPlanResponse.from_orm(subscription_operator_plan).json())
                except Exception as e:
                    subscription_operator_plan = None
                    logger.error('Getting subscription_operator_plan error, marking these as None with error as %s',
                                 str(e))

        except Exception as e:  # pylint: disable=broad-except
            logger.error('Getting billing info on subscription failed, marking these as None with error as %s', str(e))

        try:
            if promo_code_usage is not None:
                promo_code_usage = json.loads(schema.CampaignPromoCodeUsageResponse.from_orm(promo_code_usage).json())

        except Exception as e:  # pylint: disable=broad-except
            promo_code_usage = None
            logger.error('Getting Promo Code Usage error, marking these as None with error as %s', str(e))
        response_message = schema.Message(
            reason=schema.MessageReason.response,
            body=schema.MessageBody(
                message_type='DiscountResponse',
                parameters={
                    'hash': hash,
                    'id_tag': id_tag,
                    'discount_type': discount_info.get('discount_type').value,
                    'discount_fee': float(discount_info.get('discount_fee')),
                    'preferred_payment_flow': preferred_payment_flow,
                    'is_low_wallet_balance': is_low_wallet_balance,
                    'preferred_payment_method': preferred_payment_method,
                    'subscription_info': {
                        'subscription_plan': subscription_plan,
                        'subscription_custom_plan': subscription_custom_plan,
                        'subscription_tariff_plan': subscription_tariff_plan,
                        'subscription_operator_plan': subscription_operator_plan,
                    },
                    'promo_code_usage': promo_code_usage,
                    'is_guest_user': is_guest,
                }
            )
        )
        publisher.publish_message(response_message.dict(), message['reply_to'])

        logger.info('Sent message # %s', response_message.dict())

        return {
            'hash': hash,
            'id_tag': id_tag,
            'discount_type': discount_info.get('discount_type').value,
            'discount_fee': float(discount_info.get('discount_fee')),
        }


@app.task(
    name='apollo.main.tasks.use_booked_promo_code',
    autoretry_for=(ConnectTimeout,),
    retry_backoff=3,
)
def use_booked_promo_code_background(message: dict):
    with contextmanager(create_session)() as dbsession:
        crud.use_booked_promo_code(
            db=dbsession,
            # promo_code=message["promo_code"],
            promo_code_usage_id=message["promo_code_usage_id"],
            charging_session_id=message["charging_session_id"],
            transaction_id=message.get('transaction_id')
        )


@app.task(name='apollo.main.tasks.get_charging_sessions', autoretry_for=(ConnectTimeout,),
          retry_backoff=3, soft_time_limit=3600, time_limit=7200)
def send_via_email(message: dict):
    """Get large amount of charging sessions from charger
    """
    request_message = {
        'body': {
            'parameter': {
                'accessible_cp': message['accessible_cp'],
                'is_superuser': message['is_superuser'],
                'filters': message['filters'],
                'id_tags': message['id_tags'],
                'ruby_user_ids': message['ruby_user_ids'],
                'ids': message['ids']
            }
        },
        'reply_to': settings.CHARGER_SERVICE_WORKER_ROUTE
    }
    logger.debug(request_message)

    # result = app.send_task(
    #     'apollo.charger.tasks.get_charging_sessions_data',
    #     kwargs={'message': request_message},
    #     queue=settings.CHARGER_SERVICE_WORKER_QUEUE,
    #     routing_key=settings.CHARGER_SERVICE_WORKER_ROUTE
    # )


@app.task(name='apollo.main.tasks.send_charging_history_via_email', autoretry_for=(ConnectTimeout,),
          retry_backoff=3)
def send_charging_history_via_email_sync(message: dict):
    def send_charging_history():
        async def send_charging_history_coroutine():
            filters = message['filters']
            query_params = message['query_params']
            headers = message['headers']
            filename = message['filename']
            columns = message['columns']
            email = message['email']
            req_headers = message['req_headers']
            membership_id = message['membership_id']
            ext_org = message['ext_org']
            ext_org_party = message['ext_org_party']
            url = f'{CHARGER_URL_PREFIX}/charging/'
            with contextmanager(create_session)() as dbsession:
                membership = crud.MembershipCRUD.query(dbsession, check_permission=False) \
                    .filter(models.Membership.id == membership_id).options(joinedload(models.Membership.user),
                                                                           joinedload(models.Membership.operators),
                                                                           joinedload(models.Membership.roles)).first()
                membership = schema.MembershipResponse(**membership.__dict__)
                _request_user_ctx_var.set(membership)
                get_data = partial(get_charging_history_data, dbsession=dbsession, url=url, ext_org=ext_org,
                                   ext_org_party=ext_org_party, headers=req_headers,
                                   query_params=query_params, filters=filters, operator_filter=False)
                report = GenerateReport('charging_history', headers, columns, function=get_data)
                # await report.datetime_reformat('session_start')
                # await report.datetime_reformat('session_end')

                await report.set_send_via_email(filename, True, ['session_start', 'session_end'])
                await report.generate_dataframe(send_via_email=True)

                await report.send_report_via_email('Your Charging History Report', filename, email)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        loop.run_until_complete(send_charging_history_coroutine())
        # asyncio.run(send_charging_history_coroutine())

    send_charging_history()


@app.task(name='apollo.main.tasks.cp_breakdown_notification', autoretry_for=(KombuError,), retry_backoff=3)
def cp_breakdown_notification(message: dict):
    logger.info('main: blast email for brokedown cp')

    def send_cp_breakdown_email_task():
        async def send_cp_breakdown_email_coroutine():
            data = message['body']['parameters']
            await send_cp_breakdown_email(data)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        loop.run_until_complete(send_cp_breakdown_email_coroutine())

    send_cp_breakdown_email_task()


@app.task(name='apollo.main.tasks.hung_session_notification', autoretry_for=(KombuError,), retry_backoff=3)
def hung_session_notification(message: dict):
    logger.info('main: blast email for hung session cp')

    def send_hung_session_to_cp_notifications():
        async def send_hung_session_to_cp_notifications_coroutine():
            data = message['body']['parameters']
            await send_hung_session_email(data)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        loop.run_until_complete(send_hung_session_to_cp_notifications_coroutine())

    send_hung_session_to_cp_notifications()


@app.task(name='apollo.main.tasks.update_billing_discount_info', autoretry_for=(KombuError,), retry_backoff=3)
def update_billing_discount_info(message: dict):
    logger.info('main: get user subscription')

    def update_billing_discount_info_task():
        async def get_user_subscription():
            data = message['body']['parameters']['charging_session']
            with contextmanager(create_session)() as dbsession:
                charging_session_id = data['id']
                user_id_tag = data['id_tag']
                connector = {'id': data['ocpi_evse_connector']['id']}
                db_subscription_plans = get_default_subscription_and_plans(dbsession,
                                                                           connector=connector,
                                                                           id_tag=user_id_tag, is_ocpi=True)

                if db_subscription_plans and db_subscription_plans.get('subscription_ocpi_custom_plan'):
                    subscription_plan = db_subscription_plans.get('subscription')
                    subscription_ocpi_custom_plan = db_subscription_plans.get('subscription_ocpi_custom_plan')
                    if subscription_plan is not None:
                        try:
                            subscription_plan = json.loads(
                                schema.SubscriptionResponseNoMember.from_orm(subscription_plan).json())
                        except Exception as e:  # pylint: disable=broad-except
                            subscription_plan = None
                            logger.error('Getting subscription_plan error, marking these as None with error as %s',
                                         str(e))
                    if subscription_ocpi_custom_plan is not None:
                        try:
                            subscription_ocpi_custom_plan = json.loads(
                                schema.ShallowSubscriptionOCPICustomPlanResponse.from_orm(
                                    subscription_ocpi_custom_plan).json())
                        except Exception as e:  # pylint: disable=broad-except
                            subscription_ocpi_custom_plan = None
                            logger.error(
                                'Getting subscription_ocpi_custom_plan error, '
                                'marking these as None with error as %s',str(e))

                    if subscription_ocpi_custom_plan.get('amount') is not None:
                        body = {
                            'billing_discounted_type': 'Fixed',
                            'billing_discounted_amount': subscription_ocpi_custom_plan.get('amount'),
                            'subscription_info': {
                                'subscription_plan': subscription_plan,
                                'subscription_custom_plan': subscription_ocpi_custom_plan,
                            },
                        }

                        path = f'ocpi/emsp/update_billing_info/{charging_session_id}'
                        url = f'{CHARGER_URL_PREFIX}/{path}'
                        await send_request('POST', url, data=json.dumps(body))

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        loop.run_until_complete(get_user_subscription())

    update_billing_discount_info_task()


@app.task(name='apollo.main.tasks.push_native', autoretry_for=(KombuError,), retry_backoff=3)
def push_native_session(message: dict):  # noqa: MC0001
    logger.info('push_native')
    publisher = ChargerServicePublisher()
    # hash = message['body']['parameters']['hash']

    # No hash mean we are to response using REST-API to return afterward (typically mean its OCPI transaction)
    hash = message['body']['parameters'].get('hash')
    data = message['body']['parameters']['db_cs']
    charge_point_id = message['body']['parameters']['charge_point_id']
    id_tag = data['id_tag']
    charging_session_id = data['id']

    auto_cut_session = None

    def send_response(publisher, hash, auto_cut_session, charging_session_id, message):
        response_message = schema.Message(
            reason=schema.MessageReason.response,
            body=schema.MessageBody(
                message_type='AutoCutSessionCostReply',
                parameters={
                    'auto_cut_session': auto_cut_session,
                    'hash': hash
                }
            )
        )
        if hash is not None:
            publisher.publish_message(response_message.dict(), message['reply_to'])
        else:
            billing_currency = data['meta']['billing_info']['billing_currency']

            # The reason why we put some nonsense here is because there is no discount calls,
            # thus we are required to use this.
            with contextmanager(create_session)() as dbsession:
                preferred_payment_flow = get_preferred_payment_flow_by_id_tag(dbsession, id_tag)
                preferred_payment_method = get_preferred_payment_method_by_id_tag(dbsession, id_tag)
                is_low_wallet_balance = check_if_low_wallet_balance(dbsession, id_tag, billing_currency)

            response_message.body.parameters['preferred_payment_flow'] = preferred_payment_flow
            response_message.body.parameters['preferred_payment_method'] = preferred_payment_method
            response_message.body.parameters['is_low_wallet_balance'] = is_low_wallet_balance

            path = f'ocpi/emsp/auto-cut-reply/{charging_session_id}'
            url = f'{CHARGER_URL_PREFIX}/{path}'

            try:
                loop = asyncio.get_event_loop()
            except RuntimeError as e:
                if str(e).startswith('There is no current event loop in thread'):
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                else:
                    logger.error('Runtime error on asyncio with error, %s', str(e))

            loop.run_until_complete(send_request('POST', url, data=json.dumps(response_message.dict())))

        return response_message

    with contextmanager(create_session)() as dbsession:
        set_admin_as_context_user(dbsession)

        old_pap = crud.get_pre_auth_payment_by_charging_session_id(dbsession, charging_session_id)
        if old_pap:
            auto_cut_session = old_pap.amount
            return send_response(publisher, hash, auto_cut_session, charging_session_id, message)
        db_id_tag = dbsession.query(models.IDTag).filter(
            func.lower(models.IDTag.id_tag) == id_tag.lower(),
        ).first()
        if db_id_tag:
            db_member = db_id_tag.member
        else:
            db_member = dbsession.query(models.Membership).filter(
                func.lower(models.Membership.user_id_tag) == id_tag.lower()).first()

        if not db_member:
            auto_cut_session = None

        # if the charger is free, let it pass without require a cc
        else:
            if data['meta']['location']['protocol_type'] != 'OCPP':
                is_free = False
            else:
                discounted_fee = apply_subscription_discount_connector(dbsession, db_member.id,
                                                                       data['charge_point_connector_id'],
                                                                       data['meta']['billing_info']['billing_unit_fee'])
                epsilon = 1e-4
                is_free = abs(discounted_fee) < epsilon

            if not is_free:
                connector_currency = data['meta']['billing_info']['billing_currency']
                db_pre_auth = crud.get_non_binded_pre_auth_by_member_id(dbsession, member_id=db_member.id,
                                                                        currency=connector_currency)
                if db_pre_auth:
                    pre_auth_payment_update = schema.PreAuthPaymentResponse(
                        is_binded=True,
                        charging_session_id=charging_session_id
                    )
                    crud.update_pre_auth_payment(dbsession, db_pre_auth.id, pre_auth_payment_update)
                    payment_request_update = schema.PaymentRequestUpdateChargePointID(
                        connector_id=str(charge_point_id)
                    )
                    crud.update_payment_request(dbsession, payment_request_update, str(db_pre_auth.payment_request_id))
                    auto_cut_session = db_pre_auth.amount
                if db_member:
                    if settings.ENABLE_PARTIAL_PAYMENT_FLOW:
                        if db_member.preferred_payment_method == PreAuthPaymentMethodEnums.wallet:
                            db_wallet = get_wallet_by_member_id_currency(dbsession, str(db_member.id),
                                                                         connector_currency)
                            db_wallet = update_wallet_balance(dbsession, str(db_wallet.id))
                            if float(db_wallet.balance) <= settings.LOW_WALLET_BALANCE:
                                # If is Partial flow, add in wallet + cc
                                if db_member.preferred_payment_flow == PreferredPaymentFlowEnums.partial:
                                    if not db_pre_auth:
                                        auto_cut_session = None
                                    else:
                                        if auto_cut_session is not None:
                                            auto_cut_session = float(db_wallet.balance) + float(auto_cut_session)
                                if not db_pre_auth and db_member.preferred_payment_flow == PreferredPaymentFlowEnums.wallet:
                                    if float(db_wallet.balance) <= 0.00:
                                        # Reason why we put 0.01 is we put 0.0 as default, so 0.01 is min to cut
                                        auto_cut_session = 0.01
                                    else:
                                        auto_cut_session = float(db_wallet.balance)
                            else:
                                if not db_pre_auth:
                                    if float(db_wallet.balance) <= 0.00:
                                        # Reason why we put 0.01 is we put 0.0 as default, so 0.01 is min to cut
                                        auto_cut_session = 0.01
                                    else:
                                        auto_cut_session = float(db_wallet.balance)

    return send_response(publisher, hash, auto_cut_session, charging_session_id, message)


@app.task(name='apollo.main.tasks.calculate_current_session_estimated_cost', autoretry_for=(KombuError,),
          retry_backoff=3)
def calculate_current_session_estimated_cost(message: dict):
    logger.info('estimated_cost')
    publisher = ChargerServicePublisher()
    # If no hash, mean we need to use rest-api to reply instead
    hash = message['body']['parameters'].get('hash')
    charge_point_id = message['body']['parameters']['charge_point_id']
    charging_session = message['body']['parameters']['charging_session']
    current_meter_value = float(charging_session['current_wh'])
    current_datetime = datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
    promo_code_usage = charging_session.get('meta', {}).get('billing_info', {}).get('promo_code_usage')

    if charging_session['meta']['billing_info']['billing_type'] == schema.BillingType.kwh:
        usage = calculate_charging_usage(
            charging_session['meter_start'],
            float(current_meter_value + charging_session['meter_start']),
            charging_session['meta']['billing_info']['billing_type'],
            charging_session['meta']['billing_info']['billing_cycle']
        )
    else:
        usage = calculate_charging_usage(
            charging_session['session_start'],
            current_datetime,
            charging_session['meta']['billing_info']['billing_type'],
            charging_session['meta']['billing_info']['billing_cycle']
        )

    amount = calculate_charging_cost(
        unit_price=charging_session['meta']['billing_info']['billing_unit_fee'],
        usage=usage,
        constant=charging_session['meta']['billing_info']['connection_fee']
    )
    amount = round(float(amount), 2)

    # If is native, foreign (ruby charger)
    try_discount = False

    with contextmanager(create_session)() as dbsession:
        if charging_session['charging_session_type'] and \
                charging_session['charging_session_type'] in ['Native', 'Foreign', 'Local', 'OCPI-EMSP']:
            db_id_tag = dbsession.query(models.IDTag).filter(
                func.lower(models.IDTag.id_tag) == charging_session['id_tag'].lower(),
            ).first()
            if db_id_tag:
                db_member = db_id_tag.member
                if db_member:
                    try_discount = True
            else:
                db_member = dbsession.query(models.Membership).filter(
                    func.lower(models.Membership.user_id_tag) == charging_session['id_tag'].lower()).first()
                if db_member:
                    try_discount = True

        if try_discount:
            if charging_session['charging_session_type'] == 'OCPI-EMSP':
                # Actually no needed for charging session id and ETC
                subscription_discount = apply_subscription_discount(dbsession, db_member.id,
                                                               charging_session['id'],
                                                               usage, float(amount),
                                                               charging_session['meta']
                                                               ['billing_info']['billing_discounted_type'],
                                                               charging_session['meta']
                                                               ['billing_info']['billing_discounted_amount'])
            else:
                subscription_discount = apply_subscription_discount(dbsession, db_member.id,
                                                                    charging_session['charge_point_connector']['id'],
                                                                    usage, float(amount),
                                                                    charging_session['meta']
                                                                    ['billing_info']['billing_discounted_type'],
                                                                    charging_session['meta']
                                                                    ['billing_info']['billing_discounted_amount'])
        else:
            subscription_discount = {'subscription_discount': float(0)}
        campaign_promo_code_discount = apply_campaign_promo_code_discount(promo_code_usage, amount)

        # total_charge_amount = round(float(amount) - float(subscription_discount['subscription_discount']), 2)
        discount_summary = {
            **subscription_discount,
            **campaign_promo_code_discount,
        }
        total_charge_amount = round(
            (
                    float(amount)
                    - float(discount_summary['subscription_discount'])
                    - float(discount_summary['campaign_promo_code_discount'])
            ), 2
        )
        tax_info = calculate_tax_amount(dbsession,
                                        charge_point_id,
                                        total_charge_amount,
                                        charging_session['meta']['billing_info']['billing_currency'])

        response_message = schema.Message(
            reason=schema.MessageReason.response,
            body=schema.MessageBody(
                message_type='EstimatedCostReply',
                parameters={
                    'tax_info': tax_info,
                    'hash': hash
                }
            )
        )
        if hash is not None:
            publisher.publish_message(response_message.dict(), message['reply_to'])
        else:
            path = f'ocpi/emsp/estimated-session-cost-reply/{str(charging_session["id"])}'
            url = f'{CHARGER_URL_PREFIX}/{path}'

            try:
                loop = asyncio.get_event_loop()
            except RuntimeError as e:
                if str(e).startswith('There is no current event loop in thread'):
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                else:
                    logger.error('Runtime error on asyncio with error, %s', str(e))

            loop.run_until_complete(send_request('POST', url, data=json.dumps(response_message.dict())))
        return tax_info


@app.task(name='apollo.main.tasks.calculate_current_hogging_estimated_cost', autoretry_for=(KombuError,),
          retry_backoff=3)
def calculate_hogging_estimated_cost(message: dict):
    """
    Calculate hogging estimated cost
    """
    logger.info('hogging_estimated_cost')
    try:
        has_breakdown = message['body']['parameters']['has_breakdown']
    except KeyError:
        has_breakdown = False
    publisher = ChargerServicePublisher()
    hash = message['body']['parameters']['hash']
    charging_session = message['body']['parameters']['charging_session']
    charge_point_id = message['body']['parameters']['charge_point_id']

    hogging_tariff = charging_session.get('meta', {}).get('billing_info', {}).get('hogging_tariff')
    with contextmanager(create_session)() as dbsession:
        hogging_fee = 0
        if hogging_tariff:
            if hogging_tariff is not None:
                if len(hogging_tariff) > 0:
                    if charging_session['hogging_start']:
                        hogging_fee = calculate_hogging_fee(hogging_tariff,
                                                            charging_session['hogging_start'],
                                                            hogging_end=None,
                                                            has_breakdown=has_breakdown, is_estimated=True)

        tax_info = calculate_tax_amount(dbsession,
                                        charge_point_id,
                                        hogging_fee,
                                        charging_session['meta']['billing_info']['billing_currency'])

        response_message = schema.Message(
            reason=schema.MessageReason.response,
            body=schema.MessageBody(
                message_type='EstimatedCostReply',
                parameters={
                    'tax_info': tax_info,
                    'hash': hash
                }
            )
        )
        publisher.publish_message(response_message.dict(), message['reply_to'])
        return tax_info


@app.task(name='apollo.main.tasks.process_razerpay_reconciliation', autoretry_for=(KombuError,),
          retry_backoff=3)
def process_razerpay_reconciliation(message=None):
    if message is None:
        message = {}

    start_date = message.pop('start_date', None)
    if start_date is not None:
        try:
            # Convert the ISO format date string back to a datetime object
            start_date = datetime.fromisoformat(start_date)
        except TypeError:
            # Handle potential error if the date format is wrong or conversion fails
            print("Error: Incorrect date format for 'start_date'")
            start_date = None

    if start_date is None:
        start_date = datetime.now()
    currency_list = ['MYR', 'SGD']
    lookback_duration = settings.RAZER_RECONCILIATION_LOOKBACK_DURATION
    lookback_duration = timedelta(days=lookback_duration).total_seconds()
    with contextmanager(create_session)() as dbsession:
        set_admin_as_context_user(dbsession)
        for currency in currency_list:

            try:
                loop = asyncio.get_event_loop()
            except RuntimeError as e:
                if str(e).startswith('There is no current event loop in thread'):
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                else:
                    logger.error('Runtime error on asyncio with error, %s', str(e))

            loop.run_until_complete(process_razerpay_reconcilation(dbsession, currency, start_date, lookback_duration))


@app.task(name='apollo.main.tasks.ocpi_auto_cut', autoretry_for=(KombuError,),
          retry_backoff=3)
def ocpi_auto_cut(message: dict):  # noqa: MC0001
    data = message['body']['parameters']['db_cs']
    id_tag = data['id_tag']
    charging_session_id = data['id']
    transaction_id = data['transaction_id']
    with contextmanager(create_session)() as dbsession:
        set_admin_as_context_user(dbsession)
        # pylint: disable=too-many-nested-blocks
        if id_tag is not None:
            db_id_tag = dbsession.query(models.IDTag).filter(
                func.lower(models.IDTag.id_tag) == str(id_tag).lower(),
            ).first()
            if db_id_tag:
                db_member = db_id_tag.member
            if not db_id_tag:
                db_id_tag = dbsession.query(models.Membership).filter(
                    func.lower(models.Membership.user_id_tag) == str(id_tag).lower(),
                ).first()
                if db_id_tag:
                    db_member = db_id_tag

            membership_id = db_member.id

            headers = generate_charger_header(dbsession, membership_id)
            ocpi_token = get_or_create_ocpi_app_token(dbsession, membership_id)

            try:
                loop = asyncio.get_event_loop()
            except RuntimeError as e:
                if str(e).startswith('There is no current event loop in thread'):
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                else:
                    logger.error('Runtime error on asyncio with error, %s', str(e))

            loop.run_until_complete(auto_cut_ocpi_send_to_partner(dbsession, transaction_id, ocpi_token,
                                                                  charging_session_id, headers))


@app.task(name='apollo.main.tasks.refund_non_used_preauth_to_user', autoretry_for=(KombuError,), retry_backoff=3)
def refund_non_used_preauth():
    logger.info('refund_non_used_preauth_to_user')

    with contextmanager(create_session)() as dbsession:
        created_at = datetime.now() - timedelta(minutes=DURATION_MINUTE_TILL_PRE_AUTH_EXPIRY)
        set_admin_as_context_user(dbsession)
        db_pre_auths = crud.get_all_non_binded_pre_auth_more_than_created_at(dbsession, created_at)
        for db_pre_auth in db_pre_auths:
            try:
                perform_pre_auth_refund(dbsession, db_pre_auth)
            except Exception as e:  # pylint: disable=broad-except
                logger.error("Refunding pre-auth failed with error as: %s", str(e))


@app.task(name='apollo.main.tasks.ess_breakdown_notification', autoretry_for=(KombuError,), retry_backoff=3)
def ess_breakdown_notification(message: dict):
    logger.info('main: blast email for brokedown ess')

    def send_ess_breakdown_email_task():
        async def send_ess_breakdown_email_coroutine():
            data = message['body']['parameters']
            print(data)
            await send_ess_breakdown_email(data)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))

        loop.run_until_complete(send_ess_breakdown_email_coroutine())

    send_ess_breakdown_email_task()


@app.task(name='apollo.main.tasks.incomplete_release_preauth', autoretry_for=(KombuError,), retry_backoff=3)
def incomplete_release_preauth_payment(message: dict):
    logger.info("Incomplete release preauth payment")
    db_cs = message['body']['parameters']['db_cs']
    logger.info('Incomplete, release preauth payment with session id as, %s', str(db_cs['id']))
    with contextmanager(create_session)() as dbsession:
        db_pre_auth = get_pre_auth_payment_by_charging_session_id(dbsession, db_cs['id'])
        if db_pre_auth.payment_type == 'Wallet':
            if not REFUND_PRE_AUTH_WALLET_UPON_INCOMPLETE:
                return

        if PRE_AUTH_INSTANT_VOID:
            perform_pre_auth_refund(dbsession, db_pre_auth)
        else:
            cutoff_time = db_pre_auth.created_at + timedelta(minutes=PRE_AUTH_VOID_AFTER_MINUTES)
            if datetime.now().astimezone() >= cutoff_time:
                perform_pre_auth_refund(dbsession, db_pre_auth)


@app.task(name='apollo.main.tasks.onboard_vid', autoretry_for=(KombuError,), retry_backoff=3)
def onboard_vid(message: dict):
    def process_vid():
        async def callback_vehicle_autocharge(db_session, member_id, vehicle_id_list, mac_vid):
            logger.info('Callback vehicle autocharge for member id: %s - %s - %s', member_id, vehicle_id_list, mac_vid)
            logger.info('Callback vehicle autocharge with : %s', message)
            headers = {
                'X-API-Key': settings.CALL_BACK_API_KEY,
                'content-type': 'application/json'
            }
            url = settings.VEHICLE_AUTOCHARGE_CALL_BACK_URL
            callback_data = {
                "member_id": str(member_id),
                "vehicle_id_list": vehicle_id_list,
                "vid": mac_vid
            }

            callback_schema_dict = {
                'payload': callback_data,
                'event': 'Vehicle Callback'
            }

            logger.info('callback vehicle autocharge with data: %s', callback_data)
            logger.info('callback vehicle autocharge with dict: %s', callback_schema_dict)
            response = await send_request('POST', url, data=json.dumps(callback_schema_dict), headers=headers)
            logger.info('response from callback vehicle autocharge: %s', response.status_code)

            if response.status_code not in [httpx.codes.OK, httpx.codes.CREATED]:
                logger.error('Error while sending callback to vehicle autocharge: %s', response.status_code)

            _response = {'response_code': response.status_code, 'response_body': str(response.content.decode())}

            callback_scheme = schema.CallbackHTTPResponse(**callback_schema_dict, response=_response)
            return crud.create_callback(db_session, callback_scheme)

        try:
            vid = message['body']['parameters']['vid']
            id_tag = message['body']['parameters']['id_tag']
            tx_id = message['body']['parameters']['tx_id']
            logger.info('Received onboarding request %s, %s, %s', vid, id_tag, tx_id)
            vid_mac = str(macaddress.MAC(vid)).replace("-", "")

            with contextmanager(create_session)() as dbsession:
                # Check if vid_mac already active, skip it if it does.
                # ac_data = dbsession.query(models.Autocharge).filter(
                #    models.Autocharge.mac_address == vid_mac,
                #    models.Autocharge.status == AutochargeStatus.active
                #).first()
                try:
                    ac_data = crud.get_autocharge_by_vid(dbsession, vid_mac)

                    if ac_data and ac_data.status in (AutochargeStatus.active, AutochargeStatus.disabled):
                        logger.info('Vehicle already active with vid_mac: %s', vid_mac)
                        return
                except Exception as e:
                    logger.info('Error Querying VID %s', str(e))
                    pass

                db_id_tag = dbsession.query(models.IDTag).filter(
                    func.lower(models.IDTag.id_tag) == id_tag.lower()).first()

                if db_id_tag:
                    if not db_id_tag.is_active:
                        return

                    if db_id_tag.expiration.astimezone(timezone.utc) < datetime.now(timezone.utc):
                        return

                    db_membership = db_id_tag.member

                else:
                    db_membership = dbsession.query(models.Membership).filter(
                        func.lower(models.Membership.user_id_tag) == id_tag.lower(), ).first()

                    if db_membership is None:
                        return
                vehicles = db_membership.vehicles
                if vehicles is None or len(vehicles) == 0:
                    return
                vehicle_id_list = []
                for vehicle in vehicles:
                    if vehicle.autocharge is None:
                        continue
                    if vehicle.autocharge.status == AutochargeStatus.pending:
                        vehicle_id_list.append(str(vehicle.id))
                if len(vehicle_id_list) > 0:
                    try:
                        loop = asyncio.get_event_loop()
                        loop.run_until_complete(callback_vehicle_autocharge(dbsession, db_membership.id,
                                                                            vehicle_id_list, vid_mac))
                    except RuntimeError as e:
                        if str(e).startswith('There is no current event loop in thread'):
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                        else:
                            logger.error('Runtime error on asyncio with error, %s', str(e))

        except KeyError as e:
            logger.error('Key error while onboarding vid, %s', str(e))
        except Exception as e:
            logger.error('Error while extracting member vehicle details, %s', str(e))

    process_vid()


@app.task(name='apollo.main.tasks.audit_log_entries', autoretry_for=(KombuError,), retry_backoff=3)
def audit_log_entries(message: dict):
    with contextmanager(create_session)() as dbsession:
        data = message['body']['parameters']
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError as e:
            if str(e).startswith('There is no current event loop in thread'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            else:
                logger.error('Runtime error on asyncio with error, %s', str(e))
        loop.run_until_complete(audit_log_entry(dbsession, data))


@app.task(name='apollo.main.tasks.handle_prepaid_wallet_subscription', autoretry_for=(KombuError,), retry_backoff=3)
def handle_prepaid_wallet_subscription():
    def process_prepaid_wallet_subscription(db: Session):
        timezone = ZoneInfo("Asia/Kuala_Lumpur")
        batch_ids = db.query(models.PrepaidWalletPlanBatch.id).filter(
            models.PrepaidWalletPlanBatch.status != schema.PrepaidWalletPlanBatchStatus.deduction_done,
            models.PrepaidWalletPlanBatch.end_time <= datetime.now(timezone)
        )
        for batch_id in batch_ids:
            try:
                reset_or_carry_over_prepaid_wallet_subscription(db, batch_id)
            except Exception as e:
                logger.error('Error while processing prepaid wallet subscription, %s', str(e))

    with contextmanager(create_session)() as dbsession:
        process_prepaid_wallet_subscription(dbsession)


@app.task(name='apollo.main.tasks.scan_refund_status_from_pg', autoretry_for=(KombuError,), retry_backoff=3)
def scan_refund_status_from_pg():
    def process_refund_status_from_pg(db: Session):
        db_refund = crud.PaymentRefundCRUD.query(dbsession).filter(
            models.PaymentRefund.refund_status == schema.RefundStatus.pending,
            models.PaymentRefund.refund_type == schema.RefundType.credit_card
        ).all()
    
        for refund in db_refund:
            pg_payload = {
                "RefID": refund.reference_id,
                "MerchantID": settings.MERCHANT_ID
            }
            signature = calculate_md5_hash(f'{refund.reference_id}{settings.MERCHANT_ID}{settings.VERIFY_KEY}')
            pg_payload['Signature'] = signature
            response = requests.request('POST', url=settings.RAZER_REFUND_STATUS_API_URL, data=pg_payload)
            response_data = response.json()
            
            if response_data[0].get('Status') == 'success':
                crud.update_payment_refund(
                    dbsession, refund.id, schema.UpdatePaymentRefund(
                        refund_status=schema.RefundStatus.success,
                        pg_refund_callback=response_data[0]
                        )
                    )
    with contextmanager(create_session)() as dbsession:
        process_refund_status_from_pg(dbsession)


@app.task(name='apollo.main.tasks.send_idle_kicked_in_notification', autoretry_for=(ConnectTimeout,),
          retry_backoff=3)
def send_idle_kicked_in_notification(message: dict):  # pylint:disable=too-many-statements, # noqa: MC0011
    """
    Send mobile notification when idle fee kicked in
    """

    hogging_webhook_url = settings.HOGGING_CALL_BACK_URL
    headers = {
        'X-API-Key': settings.CALL_BACK_API_KEY,
        'content-type': 'application/json'
    }

    params = message['body']['parameters']
    id_tag = params.pop('id_tag')
    db_member = None

    if id_tag is not None:
        with contextmanager(create_session)() as dbsession:
            db_id_tag = dbsession.query(models.IDTag).filter(
                func.lower(models.IDTag.id_tag) == str(id_tag).lower(),
            ).first()
            if db_id_tag:
                db_member = db_id_tag.member
            if not db_id_tag:
                db_id_tag = dbsession.query(models.Membership).filter(
                    func.lower(models.Membership.user_id_tag) == str(id_tag).lower(),
                ).first()
                if db_id_tag:
                    db_member = db_id_tag

    try:
        loop = asyncio.get_event_loop()
    except RuntimeError as e:
        if str(e).startswith('There is no current event loop in thread'):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        else:
            logger.error('Runtime error on asyncio with error, %s', str(e))

    if hogging_webhook_url is not None:
        with contextmanager(create_session)() as dbsession:
            hogging_tariff = params['hogging_tariff']
            hogging_tariff['billing_currency'] = params['billing_currency']
            hogging_event = schema.HoggingNotification(hogging_status='Fee-Kicked-In',
                                                       hogging_tariff=params['hogging_tariff'])
            if db_member is not None:
                hogging_event.member_id = str(db_member.id)
            else:
                return None

            callback_data = schema.Callback(
                payload=hogging_event.dict(),
                event=f'Idle Fee Notification'
            )
            response = loop.run_until_complete(
                send_request('POST', hogging_webhook_url, data=json.dumps(hogging_event.dict()),
                                headers=headers))

            _response = {'response_code': response.status_code,
                            'response_body': str(response.content.decode())}
            callback_response = schema.CallbackHTTPResponse(**callback_data.dict(), response=_response)

            callback = models.Callback(**callback_response.dict())

            dbsession.add(callback)
            dbsession.commit()
