"""84

Revision ID: 30b8ef480ac4
Revises: c69b93c888df
Create Date: 2024-04-04 16:58:13.402817

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '30b8ef480ac4'
down_revision = 'c69b93c888df'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_pre_auth_info',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('pre_auth_amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('pre_auth_currency', sa.String(), nullable=True),
    sa.Column('pre_auth_type', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('main_membership', sa.Column('preferred_payment_method', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_membership', 'preferred_payment_method')
    op.drop_table('main_pre_auth_info')
    # ### end Alembic commands ###
