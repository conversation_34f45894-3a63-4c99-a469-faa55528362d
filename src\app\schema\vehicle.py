from datetime import datetime
from uuid import UUID
from typing import Optional, List, Union
import re
from pydantic import BaseModel, constr, validator


class UserMembershipVehicleResponse(BaseModel):
    id: UUID
    email: Optional[str]  # pylint: disable=unsubscriptable-object
    phone_number: Optional[str]  # pylint: disable=unsubscriptable-object

    class Config:
        orm_mode = True


class EmaidVehicleResponse(BaseModel):
    id: UUID
    emaid: Optional[str]  # pylint: disable=unsubscriptable-object
    vehicle_id: Optional[UUID]  # pylint: disable=unsubscriptable-object
    status: Optional[str]  # pylint: disable=unsubscriptable-object
    contract_begin: Optional[datetime]  # pylint: disable=unsubscriptable-object
    contract_end: Optional[datetime]  # pylint: disable=unsubscriptable-object

    class Config:
        orm_mode = True


class AutochargeVehicleResponse(BaseModel):
    '''Autocharge Response Schema'''
    id: UUID
    mac_address: Optional[str]  # pylint: disable=unsubscriptable-object
    status: str
    vehicle_id: UUID
    expiry: Optional[str]  # pylint: disable=unsubscriptable-object

    class Config:
        orm_mode = True


class MembershipVehicleResponse(BaseModel):
    id: UUID
    user_id: Optional[UUID]  # pylint: disable=unsubscriptable-object
    organization_id: Optional[UUID]  # pylint: disable=unsubscriptable-object
    first_name: Optional[str]  # pylint: disable=unsubscriptable-object
    last_name: Optional[str]  # pylint: disable=unsubscriptable-object
    user: Optional[UserMembershipVehicleResponse]  # pylint: disable=unsubscriptable-object

    class Config:
        orm_mode = True


# pylint:disable=unsubscriptable-object
class Vehicle(BaseModel):
    name: Optional[str]  # pylint: disable=unsubscriptable-object
    model: Optional[str]  # pylint: disable=unsubscriptable-object
    brand: Optional[str]  # pylint: disable=unsubscriptable-object
    connector_type_id: Optional[UUID]  # pylint: disable=unsubscriptable-object
    acceptance_rate: Optional[float]  # pylint: disable=unsubscriptable-object
    battery_capacity: Optional[float]  # pylint: disable=unsubscriptable-object
    suspend_timer: Optional[int]  # pylint: disable=unsubscriptable-object
    pcid: Optional[str] = None

    @validator('pcid')
    def validate_pcid(cls, v):
        if v is not None and v != '':
            if not re.match(r'^[a-zA-Z0-9]{17,18}$', v):
                raise ValueError('string does not match regex "^[a-zA-Z0-9]{17,18}$"')
        return v
    registration_number: Optional[str]
    membership: Optional[MembershipVehicleResponse] = None  # pylint: disable=unsubscriptable-object
    emaid: Optional[List[EmaidVehicleResponse]] = []  # pylint: disable=unsubscriptable-object


class VehicleRegistration(BaseModel):
    name: Optional[str]  # pylint: disable=unsubscriptable-object
    model: Optional[str]  # pylint: disable=unsubscriptable-object
    brand: Optional[str]  # pylint: disable=unsubscriptable-object
    connector_type_id: Optional[UUID]  # pylint: disable=unsubscriptable-object
    acceptance_rate: Optional[float]  # pylint: disable=unsubscriptable-object
    battery_capacity: Optional[float]  # pylint: disable=unsubscriptable-object
    suspend_timer: Optional[int]  # pylint: disable=unsubscriptable-object
    pcid: Optional[str]  # pylint: disable=unsubscriptable-object
    ac_status: Optional[str]  # pylint: disable=unsubscriptable-object
    registration_number: str
    membership: Optional[MembershipVehicleResponse] = None  # pylint: disable=unsubscriptable-object


class VehicleCreate(BaseModel):
    name: Optional[str]  # pylint: disable=unsubscriptable-object
    model: str
    brand: str
    connector_type_id: Optional[UUID]  # pylint: disable=unsubscriptable-object
    acceptance_rate: Optional[float]  # pylint: disable=unsubscriptable-object
    battery_capacity: Optional[float]  # pylint: disable=unsubscriptable-object
    suspend_timer: Optional[int]  # pylint: disable=unsubscriptable-object
    pcid: Optional[constr(regex=r'^[a-zA-Z0-9]{17,18}$')]  # type: ignore  # pylint: disable=unsubscriptable-object
    registration_number: Optional[str]  # pylint: disable=unsubscriptable-object


class VehicleUpdate(BaseModel):
    name: Optional[str]  # pylint: disable=unsubscriptable-object
    model: Optional[str]  # pylint: disable=unsubscriptable-object
    brand: Optional[str]  # pylint: disable=unsubscriptable-object
    connector_type_id: Optional[UUID]  # pylint: disable=unsubscriptable-object
    acceptance_rate: Optional[float]  # pylint: disable=unsubscriptable-object
    battery_capacity: Optional[float]  # pylint: disable=unsubscriptable-object
    suspend_timer: Optional[int]  # pylint: disable=unsubscriptable-object
    pcid: Optional[constr(regex=r'^(|[a-zA-Z0-9]{17,18})$')]   # type: ignore  # pylint: disable=unsubscriptable-object
    registration_number: Optional[str]  # pylint: disable=unsubscriptable-object
    mac_address: Optional[str]  # pylint: disable=unsubscriptable-object
    ac_status: Optional[str]  # pylint: disable=unsubscriptable-object
    vehicle_histories: Optional[list] = []


class VehicleResponse(Vehicle):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]  # pylint: disable=unsubscriptable-object
    vehicle_histories: Optional[list] = []
    autocharge: Optional[Union[AutochargeVehicleResponse, None]] = {}

    class Config:
        orm_mode = True


class VehicleAuthResponse(BaseModel):
    id: UUID
    name: Optional[str]  # pylint: disable=unsubscriptable-object
    model: Optional[str]  # pylint: disable=unsubscriptable-object
    brand: Optional[str]  # pylint: disable=unsubscriptable-object
    registration_number: Optional[str]
    connector_type_id: Optional[UUID]  # pylint: disable=unsubscriptable-object
    acceptance_rate: Optional[float]  # pylint: disable=unsubscriptable-object
    battery_capacity: Optional[float]  # pylint: disable=unsubscriptable-object
    suspend_timer: Optional[int]  # pylint: disable=unsubscriptable-object
    pcid: Optional[str] = None
    emaid: Optional[List[EmaidVehicleResponse]] = []  # pylint: disable=unsubscriptable-object

    @validator('pcid')
    def validate_pcid(cls, v):
        if v is not None and v != '':
            if not re.match(r'^[a-zA-Z0-9]{17,18}$', v):
                raise ValueError('string does not match regex "^[a-zA-Z0-9]{17,18}$"')
        return v

    class Config:
        orm_mode = True


class CertificateOrderResponse(BaseModel):
    '''Certificate Order Response Schema'''
    amount: float
    emaid: EmaidVehicleResponse  # pylint: disable=unsubscriptable-object
    created_at: Optional[datetime]  # pylint: disable=unsubscriptable-object
    currency: str
    status: str

    class Config:
        orm_mode = True


class VehicleFiltersParam(BaseModel):
    vehicle_reg_number: Optional[str] = None
    brand: Optional[str] = None
    model: Optional[str] = None
    pcid: Optional[str] = None
    emaid: Optional[str] = None
    mac_address: Optional[str] = None
    owner: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    pnc_status: Optional[str] = None
    pnc_expiry: Optional[datetime] = None
    ac_status: Optional[str] = None
    expiry: Optional[datetime] = None
