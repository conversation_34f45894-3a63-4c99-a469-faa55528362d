"""28

Revision ID: 808d41e8a588
Revises: 5b5c3002f819
Create Date: 2023-01-18 07:27:30.441997

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '808d41e8a588'
down_revision = '5b5c3002f819'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("ALTER TYPE membershiptype ADD VALUE 'custom'")
    # ### end Alembic commands ###


def downgrade():
    op.execute("ALTER TYPE membershiptype RENAME TO membershiptype_old")
    op.execute("CREATE TYPE membershiptype AS ENUM('staff', 'sub_staff', 'regular_user')")
    op.execute("ALTER TABLE main_membership ALTER COLUMN membership_type TYPE membershiptype USING membership_type::text::membershiptype")
    op.execute("DROP TYPE membershiptype_old")
    # ### end Alembic commands ###