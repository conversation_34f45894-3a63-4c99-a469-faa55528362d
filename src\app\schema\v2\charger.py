from datetime import datetime, timedelta
from typing import Optional
from pydantic import BaseModel, validator


class BillingRequestRuby(BaseModel):
    meter_start: float
    meter_stop: float
    session_start: str
    session_end: str


# pylint:disable=unsubscriptable-object
class ForeignSessionUpdate(BaseModel):
    meter_start: Optional[float]
    meter_stop: Optional[float]
    session_start: Optional[datetime]
    session_end: Optional[datetime]
    meter_values: Optional[dict]
    status: Optional[str]
    charging_session_bill_id: Optional[str]

    @validator('session_start', pre=True)
    def validate_session_start(cls, session_start):
        if session_start is not None:
            session_start = datetime.fromisoformat(session_start)
            # session_start = datetime.strptime(session_start, '%Y-%m-%d %H:%M:%S')
            session_start = session_start - timedelta(hours=8)
        return session_start

    @validator('session_end', pre=True)
    def validate_session_end(cls, session_end):
        if session_end is not None:
            session_end = datetime.fromisoformat(session_end)
            # session_end = datetime.strptime(session_end, '%Y-%m-%d %H:%M:%S')
            session_end = session_end - timedelta(hours=8)
        return session_end


class RubyBillingUpdate(BaseModel):
    meter_start: Optional[float]
    meter_stop: Optional[float]
    session_start: Optional[datetime]
    session_end: Optional[datetime]
    meter_values: Optional[dict]
    status: Optional[str]
    charging_session_bill_id: Optional[str]

    @validator('session_start', pre=True)
    def validate_session_start(cls, session_start):
        if session_start is not None:
            # session_start = datetime.fromisoformat(session_start)
            session_start = datetime.strptime(session_start, '%Y-%m-%d %H:%M:%S')
            session_start = session_start - timedelta(hours=8)
        return session_start

    @validator('session_end', pre=True)
    def validate_session_end(cls, session_end):
        if session_end is not None:
            # session_start = datetime.fromisoformat(session_end)
            session_end = datetime.strptime(session_end, '%Y-%m-%d %H:%M:%S')
            session_end = session_end - timedelta(hours=8)
        return session_end


class TransactionDataRuby(BaseModel):
    id_tag: str
    meter_start: float
    session_start: datetime
    id: int

    @validator('session_start', pre=True)
    def validate_session_start(cls, session_start):
        if session_start is not None:
            session_start = datetime.strptime(session_start, '%Y-%m-%d %H:%M:%S')
            # session_start = session_start - timedelta(hours=8)
        return session_start


class CurrentSessionRuby(BaseModel):
    meter_start: Optional[float]
    meter_stop: Optional[float]
    session_start: Optional[datetime]
    session_end: Optional[datetime]
    meter_values: Optional[dict]
    status: Optional[str]
    charging_session_bill_id: Optional[str]

    @validator('session_start', pre=True)
    def validate_session_start(cls, session_start):
        if session_start is not None:
            session_start = datetime.fromisoformat(session_start)
            # session_start = datetime.strptime(session_start, '%Y-%m-%d %H:%M:%S')
        return session_start

    @validator('session_end', pre=True)
    def validate_session_end(cls, session_end):
        if session_end is not None:
            session_end = datetime.fromisoformat(session_end)
            # session_end = datetime.strptime(session_end, '%Y-%m-%d %H:%M:%S')
        return session_end


# pylint: disable=unsubscriptable-object
class RubyConnectorStatusUpdate(BaseModel):
    connector_number: int
    ocpp_status: Optional[str]
    status: Optional[str]


class ChargingSessionFiltersParams(BaseModel):
    charging_session_id: Optional[str] = None
    min_duration: Optional[float] = None
    max_duration: Optional[float] = None
    min_power: Optional[int] = None
    max_power: Optional[int] = None
    cp_serial_number: Optional[str] = None
    from_date: Optional[datetime] = None
    to_date: Optional[datetime] = None
    order: Optional[int] = None
    reservation_id: Optional[int] = None
    id_tag: Optional[str] = None
    status: Optional[str] = None
    chargepoint_id: Optional[str] = None
    connector_id: Optional[str] = None
    user_rfid: Optional[str] = None
    total_price: Optional[int] = None
    usage: Optional[int] = None
    partner_name: Optional[str] = None
    country: Optional[str] = None
    operator: Optional[str] = None
    charging_session_type: Optional[str] = None
    email: Optional[str] = None
    hogging_status: Optional[str] = None
    charge_point_id: Optional[str] = None
    phone_number: Optional[str] = None
    session_id: Optional[str] = None
    vehicle_registration_number: Optional[str] = None
    outlier_status: Optional[str] = None
