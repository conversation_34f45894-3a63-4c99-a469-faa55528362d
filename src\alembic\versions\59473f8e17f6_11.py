"""11

Revision ID: 59473f8e17f6
Revises: 3a9699d55769
Create Date: 2022-09-16 07:52:46.068464

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '59473f8e17f6'
down_revision = '3a9699d55769'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_id_tag', sa.Column('is_external', sa.<PERSON>(), nullable=True))
    op.add_column('main_id_tag', sa.Column('meta', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_id_tag', 'meta')
    op.drop_column('main_id_tag', 'is_external')
    # ### end Alembic commands ###
