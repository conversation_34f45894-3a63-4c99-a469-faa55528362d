import sqlalchemy as db
from sqlalchemy.dialects.postgresql import J<PERSON><PERSON><PERSON>

from app.models.base import BaseModel


class Callback(BaseModel):
    __tablename__ = 'main_callback'

    payload = db.Column(JSONB)
    response = db.Column(JSONB)
    event = db.Column(db.String)


class OCPIHistories(BaseModel):
    __tablename__ = 'main_ocpi_histories'

    type = db.Column(db.String)

    ocpi_module = db.Column(db.String)
    request_url = db.Column(db.String)
    request_header = db.Column(db.String)
    request_content = db.Column(db.String)
    request_method = db.Column(db.String)

    request_body = db.Column(JSONB)

    response_content = db.Column(db.String)
    response_code = db.Column(db.String)

    response_body = db.Column(JSONB)


class TraceMalloc(BaseModel):
    __tablename__ = 'main_trace_malloc'

    content = db.Column(db.String)
    mem_allocated = db.Column(db.Integer)
