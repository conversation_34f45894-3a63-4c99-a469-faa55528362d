from datetime import datetime, timed<PERSON><PERSON>
from contextlib import contextmanager
import uuid
import pytest
import secrets

from unittest.mock import patch

import jwt
from fastapi.testclient import TestClient

from app import settings, schema, models
from app.main import app, ROOT_PATH
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory, CreditCardFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory,
                                 OrganizationAuthenticationServiceFactory, )
from app.database import SessionLocal, create_session, Base, engine
from app.tests.mocks.async_client import MockAsyncClientGeneratorPaymentIPN

client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session

CREDIT_CARD_BASE_URL = f'{ROOT_PATH}/api/v1/cc'


def test_create_credit_card(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/'
        response = client.post(url, headers={'authorization': token, 'x-api-key': secret_key,
                                             'x-api-sid': organization_id})
        assert response.status_code == 200

        db_activity = db.query(models.ActivityLog).order_by(models.ActivityLog.created_at).first()
        assert db_activity.type == schema.ActivityLogType.create


def test_get_credit_cards_list(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')

        CreditCardFactory(member_id=membership_id, primary=True, last_four_digit='1111')
        db.commit()

        CreditCardFactory(member_id=membership_id, primary=False, last_four_digit='2222')
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/'
        response = client.get(url, headers={'authorization': token, 'x-api-key': secret_key,
                                            'x-api-sid': organization_id})
        assert response.json()['success']


# @patch('app.routers.credit_card.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
@patch('app.routers.v2.credit_card.check_active_charging_session')
def test_delete_credit_card_success(check_active_charging_session_mock, test_db):
    check_active_charging_session_mock.return_value = 200

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        cc_1 = CreditCardFactory(member_id=membership_id, primary=True)
        db.commit()

        CreditCardFactory(member_id=membership_id, primary=False)
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        url = f'{CREDIT_CARD_BASE_URL}/{cc_1.id}'
        response = client.delete(url, headers={'authorization': token, 'x-api-key': secret_key,
                                               'x-api-sid': organization_id})
        assert check_active_charging_session_mock.called

        assert response.json()['success']


# @patch('app.routers.credit_card.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_delete_credit_card_body_failure(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        CreditCardFactory(member_id=membership_id, primary=False)
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/1'
        response = client.delete(url, headers={'authorization': token, 'x-api-key': secret_key,
                                               'x-api-sid': organization_id})
        assert not response.json()['success']
        assert response.json()['error']['code'] == 4222


# @patch('app.routers.credit_card.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
@patch('app.routers.v2.credit_card.check_active_charging_session')
def test_delete_credit_card_body_not_found(check_active_charging_session_mock, test_db):
    check_active_charging_session_mock.return_value = 200

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        CreditCardFactory(member_id=membership_id, primary=False)
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/{uuid.uuid1()}'
        response = client.delete(url, headers={'authorization': token, 'x-api-key': secret_key,
                                               'x-api-sid': organization_id})
        # assert check_active_charging_session_mock.called
        assert not response.json()['success']
        assert response.json()['error']['code'] == 4040


@patch('app.routers.v2.credit_card.check_active_charging_session')
def test_delete_credit_card_fail_because_ongoing_session(check_active_charging_session_mock, test_db):
    check_active_charging_session_mock.return_value = 402

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{CREDIT_CARD_BASE_URL}/.*',
                                    'get,patch,post,delete', 'apollo-main')

        cc_1 = CreditCardFactory(member_id=membership_id, primary=True)
        db.commit()

        CreditCardFactory(member_id=membership_id, primary=False)
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{CREDIT_CARD_BASE_URL}/{cc_1.id}'
        response = client.delete(url, headers={'authorization': token, 'x-api-key': secret_key,
                                               'x-api-sid': organization_id})

        assert check_active_charging_session_mock.called
        assert response.json()['error']['message'] == ['On-going session, cannot delete card']

        # assert response.json()['success']
