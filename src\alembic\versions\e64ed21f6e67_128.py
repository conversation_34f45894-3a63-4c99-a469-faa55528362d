"""128

Revision ID: e64ed21f6e67
Revises: 37a933a1684a
Create Date: 2024-10-14 14:07:00.914736

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e64ed21f6e67'
down_revision = '37a933a1684a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_payment_request', sa.Column('wallet_deduct_amount', sa.String(), nullable=True))
    op.add_column('main_payment_request', sa.Column('wallet_deduct_status', sa.String(), nullable=True))
    op.add_column('main_payment_request', sa.Column('non_wallet_deduct_amount', sa.String(), nullable=True))
    op.add_column('main_payment_request', sa.Column('non_wallet_deduct_status', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by <PERSON>embic - please adjust! ###
    op.drop_column('main_payment_request', 'non_wallet_deduct_status')
    op.drop_column('main_payment_request', 'non_wallet_deduct_amount')
    op.drop_column('main_payment_request', 'wallet_deduct_status')
    op.drop_column('main_payment_request', 'wallet_deduct_amount')
    # ### end Alembic commands ###
