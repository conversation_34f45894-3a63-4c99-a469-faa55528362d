import json
import logging
from fastapi import APIRouter, Depends, Request, HTTPException, status
from fastapi_pagination import Params, Page

from app import settings, schema, crud, exceptions
# from app.crud import get_charging_session_list, UserCRUD, get_operator_list_by_organization_id
# from app.crud import UserCRUD
from app.database import create_session, SessionLocal
from app.permissions import permission
from app.utils import decode_auth_token_from_headers, send_request, generate_charger_header

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/outlier",
    tags=['outlier', ],
    dependencies=[Depends(permission)],
)
CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'


async def outlier_filter_params(status: str = None, charging_session_id: str = None,
                                charging_session_type: str = None,
                                transaction_id: str = None,
                                id_tag: str = None, billing_currency: str = None):
    return {
        'status': status, 'charging_session_id': charging_session_id,
        'charging_session_type': charging_session_type, 'transaction_id': transaction_id,
        'id_tag': id_tag, 'billing_currency': billing_currency,
    }


@router.get("/", status_code=status.HTTP_200_OK, response_model=Page[schema.OutlierSessionCSMSResponse])
async def get_outlier_listing(request: Request,  # noqa: MC0001, # pylint: disable=too-many-locals
                              params: Params = Depends(),
                              filter_params: dict = Depends(outlier_filter_params),
                              dbsession: SessionLocal =
                              Depends(create_session)) -> Page[schema.OutlierSessionCSMSResponse]:
    """
    Get Outlier Listing
    """

    _ = decode_auth_token_from_headers(request.headers)

    try:
        outlier_list = crud.get_outlier_list(dbsession, filter_params, params)
        return outlier_list

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/{outlier_id}/", status_code=status.HTTP_200_OK, response_model=schema.OutlierSessionCSMSResponse)
async def get_outlier_by_id(request: Request,  # noqa: MC0001, # pylint: disable=too-many-locals
                              outlier_id: str,
                              dbsession: SessionLocal =
                              Depends(create_session)) -> schema.OutlierSessionCSMSResponse:
    """
    Get Outlier By ID
    """

    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    _ = generate_charger_header(dbsession, membership_id)

    try:
        # invoice_list = await crud.get_member_invoice_list(dbsession, headers, filter_params)
        db_outlier = crud.get_outlier_by_id(dbsession, outlier_id)

        return db_outlier
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.patch("/{outlier_id}/", status_code=status.HTTP_200_OK, response_model=schema.OutlierSessionCSMSResponse)
async def update_outlier(request: Request,  # noqa: MC0001, # pylint: disable=too-many-locals
                         outlier_id: str,
                         outlier_session_update: schema.OutlierSessionUpdate,
                         dbsession: SessionLocal =
                         Depends(create_session)) -> schema.OutlierSessionCSMSResponse:
    """
    Update Outlier
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)

    try:
        # invoice_list = await crud.get_member_invoice_list(dbsession, headers, filter_params)
        db_outlier = crud.get_outlier_by_id(dbsession, outlier_id)
        if db_outlier.status not in [schema.OutlierStatus.pending]:
            raise HTTPException(400, 'No update permitted for updated sessions')

        outlier = crud.update_outlier(dbsession, outlier_session_update, outlier_id)
        if outlier.status == schema.OutlierStatus.approved:
            url = f'{CHARGER_URL_PREFIX}/charging/rebilling-issued-invoice'

            _ = await send_request('POST', url=url, headers=headers, data=json.dumps([outlier.transaction_id]))

        return outlier
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
