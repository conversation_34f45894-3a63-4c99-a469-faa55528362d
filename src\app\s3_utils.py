# pylint:disable=too-many-lines
from datetime import datetime
import json
import logging
from io import BytesIO

import boto3
import pytz
import requests
from botocore.exceptions import ClientError
from fastapi import HTTPException

from app import settings
from app.settings import S3_BUCKET_REPORT

logger = logging.getLogger(__name__)
S3_REGION = settings.S3_REGION
S3_SESSION = boto3.Session(
    aws_access_key_id=settings.S3_ACCESS_KEY,
    aws_secret_access_key=settings.S3_SECRET_ACCESS_KEY,
    region_name=S3_REGION,
    aws_session_token=settings.S3_SESSION_TOKEN,
)

S3 = S3_SESSION.resource('s3')
S3_BUCKET = settings.S3_BUCKET
S3_CLIENT = S3_SESSION.client('s3')
S3_DEFAULT_URL = f'https://s3-{S3_REGION}.amazonaws.com/{S3_BUCKET}'


async def upload_image(file_bytes: bytes, key: str):
    try:
        S3_CLIENT.upload_fileobj(BytesIO(file_bytes), S3_BUCKET, key, ExtraArgs={'ACL': 'public-read'})
        logger.info("Image uploaded to S3")
        url = S3_DEFAULT_URL + f'/{key}'
        return url
    except ClientError as e:
        logger.error('Error uploading image to S3: %s', e)
        raise HTTPException(status_code=500, detail="Failed to upload file to s3")


async def upload_image_with_presigned_url(file_bytes: bytes, key: str):
    try:
        upload_details = S3_CLIENT.generate_presigned_post(Bucket=S3_BUCKET, Key=key)
        files = {'file': (key, file_bytes)}
        upload_response = requests.post(upload_details['url'], data=upload_details['fields'], files=files, timeout=2)
        if upload_response.status_code == 204:
            url = S3_DEFAULT_URL + f'/{key}'
            return url

        logger.error('Error uploading image with presigned url to S3')
        raise HTTPException(status_code=500, detail="Failed to upload file with presigned url to s3")
    except ClientError as e:
        logger.error('Error uploading image with presigned url to S3: %s', e)
        raise HTTPException(status_code=500, detail="Failed to upload file with presigned url to s3")


async def generate_presigned_url_for_view_image(key: str, filename: str):
    try:
        download_url = S3_CLIENT.generate_presigned_url(
            'get_object',
            Params={'Bucket': S3_BUCKET,
                    'Key': key,
                    'ResponseContentDisposition': f'inline; filename={filename}.png',
                    },
            ExpiresIn=3600,
            HttpMethod='GET'
        )
        return download_url
    except ClientError as e:
        logger.error('Error generating pre-signed URL for image: %s', e)
        raise HTTPException(status_code=500, detail="Failed to generate pre-signed URL for image")


async def delete_image(key: str):
    try:
        S3_CLIENT.delete_object(Bucket=S3_BUCKET, Key=key)
        logger.info("Image deleted from S3")
    except ClientError as e:
        logger.error('Error deleting image from S3: %s', e)
        raise HTTPException(status_code=500, detail="Failed to delete file from s3")


def lta_upload_or_update_s3(retry_count, max_retries, status_code, data):
    tz = pytz.timezone('Asia/Singapore')
    date = datetime.now(tz)
    current_date = date.strftime('%Y-%m-%d')
    filename = f'Dynamic_LTA_{current_date}.json'
    json_data = {
        "timestamp": date.strftime('%Y-%m-%d %H:%M:%S'),
        "number_of_attempt": f"{retry_count} / {max_retries}",
        "response_status_code": status_code,
        "data": data
    }
    key = f'dynamic_lta/{filename}'
    try:
        try:
            response = S3_CLIENT.get_object(Bucket=settings.S3_BUCKET, Key=key)
            existing_data = response['Body'].read().decode('utf-8')
            existing_data_json = json.loads(existing_data)
        except S3_CLIENT.exceptions.NoSuchKey:
            existing_data_json = []

        existing_data_json.append(json_data)
        updated_data_str = json.dumps(existing_data_json)
        S3_CLIENT.put_object(Bucket=settings.S3_BUCKET, Key=key, Body=updated_data_str)

        logger.info('Dynamic LTA data uploaded to S3')
    except Exception as e:  # pylint: disable=broad-except
        logger.error('An error occurred: %s', e)


def read_lta_json_from_s3(key):
    try:
        response = S3_CLIENT.get_object(Bucket=settings.S3_BUCKET, Key=key)
        json_data = json.loads(response['Body'].read())
        return json_data
    except Exception as e:  # pylint: disable=broad-except
        logger.error('An error occurred while reading JSON from S3: %s', e)
        return None


async def upload_csv_with_presigned_url(csv_file_path: str, key: str):
    try:
        # Read CSV file contents
        with open(csv_file_path, 'rb') as file:
            file_bytes = file.read()

        # Generate a presigned URL for the file upload
        upload_details = S3_CLIENT.generate_presigned_post(Bucket=S3_BUCKET_REPORT, Key=key)

        # Prepare the data and files for the post request
        data = upload_details['fields']
        files = {'file': (key, file_bytes)}
        upload_response = requests.post(upload_details['url'], data=data, files=files, timeout=30)
        if upload_response.status_code == 204:
            url = f"{S3_DEFAULT_URL}/{key}"
            return url
        logger.error('Error uploading csv with presigned url to S3')
        raise HTTPException(status_code=500, detail="Failed to upload csv with presigned url to s3")
    except ClientError as e:
        logger.error('ClientError while uploading CSV file with presigned URL to S3: %s', e)
        raise HTTPException(status_code=500, detail="Failed to upload csv with presigned URL to S3")


# Initialize S3 client
async def generate_presigned_url_for_view_report(bucket, object_key, expiry_day: int):
    # Specify the bucket name and the object key (file name)
    # object_key = 'path/to/your/file.txt'

    expiry_seconds = int(expiry_day) * 24 * 60 * 60

    # Generate a pre-signed URL for downloading the file
    url = S3_CLIENT.generate_presigned_url(
        'get_object',  # The operation to perform
        Params={'Bucket': bucket,
                'Key': object_key,
                'ResponseContentDisposition': 'attachment'},
        ExpiresIn=expiry_seconds,
        HttpMethod='GET'
    )

    return url
