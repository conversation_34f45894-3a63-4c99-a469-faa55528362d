"""115

Revision ID: df86f275c34c
Revises: a651ea1b327e
Create Date: 2024-08-12 10:24:52.791739

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'df86f275c34c'
down_revision = 'a651ea1b327e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_auth_user', sa.Column('login_attempts', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_auth_user', 'login_attempts')
    # ### end Alembic commands ###
