"""125

Revision ID: 9ba2895a8646
Revises: 155fd6c7f4a0
Create Date: 2024-10-10 15:31:26.785820

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '9ba2895a8646'
down_revision = '155fd6c7f4a0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_id_tag_auth_histories', sa.Column('request_type', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_id_tag_auth_histories', 'request_type')
    # ### end Alembic commands ###
