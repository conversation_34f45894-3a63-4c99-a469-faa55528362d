"""17

Revision ID: 5734a7471027
Revises: efa4c238dad6
Create Date: 2022-11-08 16:23:20.176711

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5734a7471027'
down_revision = 'efa4c238dad6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'main_subscription', ['number'])
    op.create_unique_constraint(None, 'main_subscription', ['subscription_card_id'])
    op.create_unique_constraint(None, 'main_subscription_fee', ['name'])
    op.create_unique_constraint(None, 'main_subscription_plan', ['name'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'main_subscription_plan', type_='unique')
    op.drop_constraint(None, 'main_subscription_fee', type_='unique')
    op.drop_constraint(None, 'main_subscription', type_='unique')
    op.drop_constraint(None, 'main_subscription', type_='unique')
    # ### end Alembic commands ###
