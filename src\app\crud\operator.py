import typing

from sqlalchemy.orm import Session
from sqlalchemy.exc import NoResultFound, IntegrityError
from fastapi import HTTPException
from app import models, schema, exceptions

from .base import BaseCRUD, RelationBaseCRUD
from .auth import MembershipCRUD


class OperatorCRUD(BaseCRUD):
    model = models.Operator

    @classmethod
    def query(cls, dbsession: Session, *columns):
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession).filter(
                cls.model.organization_id.in_(allowed_orgs)
            )
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     return super().query(dbsession, *columns).filter(
        #         cls.model.organization_id == membership.organization_id
        #     )
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        db_operator = dbsession.query(cls.model).get(object_id)
        if not db_operator:
            return True
        if membership.user.is_superuser:
            return True
        cls.check_root_organization(dbsession, db_operator.organization_id)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_operator.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_operator.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_create(cls, dbsession: Session, data, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        cls.check_root_organization(dbsession, data['organization_id'])
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if data['organization_id'] in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if data['organization_id'] == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()


class OperatorChargepointCRUD(RelationBaseCRUD):
    model = models.OperatorChargepoint

    @classmethod
    def query(cls, dbsession: Session, *columns):
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession).join(models.Operator).filter(
                models.Operator.organization_id.in_(allowed_orgs)
            )
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     return super().query(dbsession, *columns).join(models.Operator).filter(
        #         models.Operator.organization_id == membership.organization_id
        #     )
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        db_cp_operator = dbsession.query(cls.model).get(object_id)
        if not db_cp_operator:
            return True
        if membership.user.is_superuser:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_cp_operator.operator.organization_id in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if db_cp_operator.operator.organization_id == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_create(cls, dbsession: Session, data, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True
        db_operator = dbsession.query(models.Operator).get(data['operator_id'])
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if data[db_operator.organization_id] in allowed_orgs:
                return True
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     if data[db_operator.organization_id] == membership.organization_id:
        #         return True
        raise exceptions.ApolloPermissionError()


class SharedOperatorCRUD(RelationBaseCRUD):
    model = models.SharedOperator


# Operators(CPO)
def get_operator_by_id(db: Session, operator_id: str) -> models.Operator:
    try:
        db_operator = OperatorCRUD.get(db, operator_id)
        return db_operator
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Operator')


def get_operator_list(db: Session, organization_id: str,  # noqa: MC0011
                      membership_id: str, name: str,
                      include_children_orgs: bool = False
                      ) -> typing.List[models.Operator]:
    db_operators = OperatorCRUD.query(db)

    if organization_id:

        db_org = db.query(models.Organization).filter(
            models.Organization.id == organization_id
        ).first()

        db_membership = db.query(models.Membership).filter(
            models.Membership.id == membership_id
        ).first()

        if db_org is None:
            raise HTTPException(400, 'Organization not found')

        if include_children_orgs:
            children_org_ids = []
            org_list = [db_org]

            while org_list:
                org = org_list.pop()
                for child in org.children:
                    children_org_ids.append(child.id)
                org_list.extend(org.children)

            db_operators = db_operators.filter(
                models.Operator.organization_id.in_([organization_id] + children_org_ids)
            )
        else:
            db_operators = db_operators.filter(
                models.Operator.organization_id == organization_id
            )

        if db_membership:
            # Superuser bypass
            if not db_membership.user.is_superuser:
                # Get list of orgs the user can access
                accessible_org_ids = [str(db_membership.organization.id)]
                org_list = [db_membership.organization]
                while org_list:
                    org = org_list.pop()
                    for child in org.children:
                        accessible_org_ids.append(str(child.id))
                        org_list.append(child)

                # Ensure the target org is accessible
                if organization_id not in accessible_org_ids:
                    raise HTTPException(403,
                                        'Access to resource denied, User has no assigned role for this resource.')

    if name:
        db_operators = db_operators.filter(
            models.Operator.name.ilike(f'%{name}%')
        )

    return db_operators


def get_operator_list_by_organization_id(db: Session, organization_id: str) -> typing.List[models.Operator]:  # noqa
    db_operators = OperatorCRUD.query(db)
    if organization_id:

        db_org = db.query(models.Organization).filter(
            models.Organization.id == organization_id
        ).first()

        if db_org is None:
            raise HTTPException(400, 'Organization not found')

        db_operators = db_operators.filter(
            models.Operator.organization_id == organization_id
        )

    return db_operators.all()


def create_operator(db: Session, data: schema.Operator) -> models.Operator:
    try:
        db_operator = OperatorCRUD.add(db, data.dict())
        return db_operator
    except IntegrityError:
        raise exceptions.ApolloOperatorError


def update_operator(db: Session, data: schema.Operator, operator_id: str) -> models.Operator:
    try:
        db_operator = OperatorCRUD.update(db,
                                          operator_id,
                                          data.dict(exclude_unset=True, exclude_defaults=True))
        return db_operator
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Operator')


def delete_operator(db: Session, operator_id: str) -> str:
    try:
        OperatorCRUD.delete(db, operator_id)
        return operator_id
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Operator')


def add_charge_point_to_operator(db: Session, operator_id: str, charge_point_id: str):
    try:
        OperatorChargepointCRUD.add(db, {
            'operator_id': operator_id,
            'charge_point_id': charge_point_id
        })
        return get_operator_by_id(db, operator_id)
    except IntegrityError:
        raise exceptions.ApolloOperatorChargePointError


def delete_charge_point_from_operator(db: Session, operator_id: str, charge_point_id: str):
    try:
        db_operator_charge_point = OperatorChargepointCRUD.query(db).filter(
            models.OperatorChargepoint.operator_id == operator_id,
            models.OperatorChargepoint.charge_point_id == charge_point_id,
        ).one()
        OperatorChargepointCRUD.delete(db, str(db_operator_charge_point.id))
        return get_operator_by_id(db, operator_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OperatorChargepoint')


def share_operator_with_organization(db: Session, operator_id: str, organization_id: str):
    try:
        SharedOperatorCRUD.add(db, {
            'operator_id': operator_id,
            'organization_id': organization_id,
            'type': schema.OperatorSharingType.shared
        })
        return get_operator_by_id(db, operator_id)
    except IntegrityError:
        raise exceptions.ApolloSharedOperatorError


def add_operator_to_sub_organization(db: Session, operator_id: str, organization_id: str):
    try:
        SharedOperatorCRUD.add(db, {
            'operator_id': operator_id,
            'organization_id': organization_id,
            'type': schema.OperatorSharingType.sub_organization
        })
        return get_operator_by_id(db, operator_id)
    except IntegrityError:
        raise exceptions.ApolloSharedOperatorError


def withhold_operator_from_organization(db: Session, operator_id: str, organization_id: str):
    try:
        db_shared_operator = SharedOperatorCRUD.query(db).filter(
            models.SharedOperator.operator_id == operator_id,
            models.SharedOperator.organization_id == organization_id,
        ).one()
        SharedOperatorCRUD.delete(db, str(db_shared_operator.id))
        return get_operator_by_id(db, operator_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='SharedOperator')


def list_membership_operator_charge_points(db: Session, organization_id: str, membership_id: str):
    try:
        mem = MembershipCRUD.get(db, membership_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Membership')
    db_cp = OperatorChargepointCRUD.query(db).join(models.Operator).filter(
        models.Operator.organization_id == mem.organization_id
    ).all()
    charge_points = []
    if db_cp:
        for cp in db_cp:
            charge_points.append(cp.charge_point_id)
    return charge_points


class UserAccessOperatorCRUD(BaseCRUD):
    model = models.UserAccessOperator
