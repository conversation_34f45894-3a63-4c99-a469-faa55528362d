import logging
import io
import base64

from typing import List, Union
from urllib.parse import urlparse
# from uuid import UUID
from datetime import datetime, timedelta

import pyotp
import qrcode
import bcrypt
from pydantic import EmailStr

from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import HTMLResponse
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.exc import NoResultFound

from twilio.base.exceptions import TwilioRestException
from twilio.rest import Client

from app import settings, schema, crud, token, exceptions, models
from app.database import create_session, SessionLocal
from app.mail import SendGridMail, RecipientsSchema
from app.middlewares import set_admin_as_context_user, set_member_as_context_user, deactivate_audit
from app.utils import decode_auth_token_from_headers, get_verification_template, \
    get_manual_linking_template, restrict_regular_user_registration_v2, \
    decode_and_validate_temp_token, staff_disable_simultaneous_login

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

twilio_client = Client(settings.TWILIO['ACCOUNT_SID'], settings.TWILIO['AUTH_TOKEN'])

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/auth",
    tags=['auth', ],
)


def send_otp(phone_number: str):
    # OTP expires in 10mins
    # https://support.twilio.com/hc/en-us/articles/************-What-is-the-Default-Verify-V2-Expiration-Time-
    return twilio_client.verify \
        .services(settings.TWILIO['VERIFY_SERVICE_SID']) \
        .verifications \
        .create(to=phone_number, channel='sms')


def verify_otp(phone_number: str, token: str):
    return twilio_client.verify \
        .services(settings.TWILIO['VERIFY_SERVICE_SID']) \
        .verification_checks \
        .create(to=phone_number, code=token)


def cpo_token_filters(token: str = None, party_id: str = None, issuer: str = None,
                      token_type: str = None, whitelist_status: str = None, id: str = None,
                      token_contract_id: str = None):
    return {'partner_ocpi_cpo_token_id': token, 'party_id': party_id, 'issuer': issuer,
            'type': token_type, 'whitelist': whitelist_status, 'id': id, 'token_contract_id': token_contract_id}


def emsp_token_filters(token: str = None, party_id: str = None, issuer: str = None,
                       token_type: str = None, whitelist_status: str = None, id: str = None,
                       token_contract_id: str = None, valid: str = None, whiteliststatus: str = None):
    if whiteliststatus:
        whitelist_status = whiteliststatus

    return {'partner_ocpi_cpo_token_id': token, 'party_id': party_id, 'issuer': issuer,
            'type': token_type, 'whitelist': whitelist_status, 'id': id, 'token_contract_id': token_contract_id,
            'valid': valid}


@router.get("/auth_request", response_model=schema.AuthRequestResponse, tags=['auth', ])
async def auth_request(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Endpoint used by external services to validate a user's token and return the user data if
    the token is valid.
    """
    auth_token = request.headers.get('authorization')
    if auth_token:
        auth_token = auth_token.removeprefix(f'{schema.TokenTypeEnum.bearer} ')
        decoded = token.decode_token(auth_token)
    else:
        raise HTTPException(400, 'Invalid authorization token.')

    try:
        user = crud.get_user_by_id(dbsession, decoded['user_id'])
        data = schema.AuthRequestResponse.from_orm(user)
        return data
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Invalid authorization token.')


@router.post("/resource_access_check", response_model=schema.ResourceAccessCheckResponse, tags=['auth', ])
async def resource_access_check(request: Request, data: schema.ResourceAccessCheck,
                                dbsession: SessionLocal = Depends(create_session)):
    """
    Endpoint used by external services to check if a user's access to a resource is allowed
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    user_id = auth_token_data.get('user_id')
    membership_id = auth_token_data.get('membership_id')

    resource = urlparse(data.resource_url)

    check = crud.get_user_resource_authorization(dbsession, resource.path, data.method, user_id, membership_id)
    return check


@router.post("/signup", response_model=schema.BasicMessage, tags=['auth', ])
async def signup(data: schema.SignupRequest, dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-up endpoint for a normal user using their phone and password
    """
    success_msg = 'Verification code has been sent to your phone number.'

    try:
        user_data = data.dict()
        user_data['user_type'] = schema.UserType.regular
        password = user_data.pop('password')
        # organization_id = user_data.pop('organization_id')
        first_name = user_data.pop('first_name')
        last_name = user_data.pop('last_name')
        user_id_tag = user_data.pop('user_id_tag')
        vehicle_model = user_data.pop('vehicle_model', None)
        _ = user_data.pop('vehicle_brand', None)
        allow_marketing = user_data.pop('allow_marketing', False)

        restrict_regular_user_registration_v2(dbsession, data.phone_number, data.email, user_data['user_type'])
        db_user = crud.create_user_v2(dbsession, user_data)

        if user_id_tag:
            if crud.user_id_tag_exists(dbsession, user_id_tag):
                raise exceptions.ApolloDuplicateUserIdTagError()
        else:
            user_id_tag = crud.create_user_id_tag(dbsession)

        # Create User's membership
        membership_data = schema.Membership(
            first_name=first_name,
            last_name=last_name,
            user_id_tag=user_id_tag,
            organization_id=user_data['organization_id'],
            user_id=str(db_user.id),
            password=password,
            vehicle_model=vehicle_model,
            allow_marketing=allow_marketing
        )
        db_member = crud.create_user_membership(dbsession, membership_data)

        # Attach default role to the membership
        regular_role = crud.get_regular_role(dbsession)
        db_member.roles.append(regular_role)

        send_otp(data.phone_number)

        return schema.BasicMessage(detail=success_msg)
    except Exception as e:
        raise HTTPException(400, str(e))


@router.post("/signup/resend-token", response_model=schema.BasicMessage, tags=['auth', ])
async def signup_user_resend(data: schema.ResendPhoneToken, dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-up endpoint for a normal user to request a new verification token
    """
    success_msg = 'Token has been re-sent to your phone number.'
    try:
        user = crud.get_user_by_phone_number_and_organization_id_v2(dbsession, data.phone_number,
                                                                    data.organization_id, schema.UserType.staff)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'User not found.')

    if not user.is_verified:
        send_otp(data.phone_number)
        return schema.BasicMessage(detail=success_msg)

    raise HTTPException(400, 'User is already verified.')


@router.post("/signup/verify", response_model=schema.BasicMessage, tags=['auth', ])
async def signup_user_verify(data: schema.SubmittedPhoneVerificationToken,
                             dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-up endpoint for a normal user to verify their phone number with the sent token
    """
    success_msg = 'Account has been successfully verified.'

    try:
        user = crud.get_user_by_phone_number_and_organization_id_v2(dbsession, data.phone_number,
                                                                    data.organization_id, schema.UserType.staff)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'User not found.')

    status_update_data = {
        'verification_method': schema.VerificationMethodEnum.phone,
        'is_verified': True,
        'user_id': str(user.id)
    }

    # https://www.twilio.com/docs/verify/api/verification-check
    try:
        response = verify_otp(data.phone_number, data.token)
        if response.status == 'approved':
            crud.update_user_verification_status(dbsession, schema.VerificationMethodUpdate(**status_update_data))
            return schema.BasicMessage(detail=success_msg)

        raise HTTPException(400, 'Invalid token')
    except TwilioRestException:
        raise HTTPException(400, 'Verification failed.')


@router.post("/signin/user/password", response_model=schema.TokenResponse, tags=['auth', ])
async def signin_user_password(data: schema.SigninPhonePasswordRequest,
                               dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-in endpoint for a normal user using their phone and password
    """
    try:
        user = crud.get_user_by_phone_number_and_organization_id_v2(dbsession, data.phone_number,
                                                                    data.organization_id,
                                                                    schema.UserType.regular)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'User not found.')

    if not user.is_verified:
        raise HTTPException(400, 'Please verify your account.')

    # Get user's membership to organization
    try:
        membership = crud.get_user_membership_by_organization_id(
            dbsession,
            str(user.id),
            data.organization_id
        )
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Membership with organization does not exist.')

    user.password = membership.password
    user = schema.UserAuth.from_orm(user)
    user.verify_password(data.password)
    return user.generate_token(str(membership.id))


# @router.post("/signin/staff", tags=['auth', ])
@router.post("/signin/staff", response_model=schema.TokenResponseWithUser,
             tags=['auth', ])
async def signin_staff_password(data: schema.SigninEmailPasswordRequest,  # noqa: MC0001
                                dbsession: SessionLocal = Depends(create_session)):  # noqa: MC0001
    """
    Sign-in endpoint for a normal user using their phone and password
    If MFA is enforced, returns a temporary token or MFA setup instructions.
    """
    try:
        user = crud.get_user_by_email_and_organization_id_v2(dbsession, data.email, data.organization_id,
                                                             schema.UserType.staff)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'User not found.')

    if not user.is_verified:
        raise HTTPException(400, 'Please verify your account.')

    try:
        membership = crud.get_user_staff_membership_by_organization_id(
            dbsession,
            str(user.id),
            data.organization_id
        )
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Membership with organization does not exist.')

    user.password = membership.password
    user_orm = schema.UserAuth.from_orm(user)
    user_orm.verify_password(data.password)
    mem_data = crud.get_membership_type_by_membership_id(
        dbsession,
        str(membership.id)
    )
    # From Settings, if MFA is enabled.
    if settings.ENFORCE_MFA:
        # For user, if they already completed MFA setup
        if user.mfa_enabled and user.mfa_secret:
            resp = user_orm.generate_mfa_token(str(membership.id))
            return {"user": user,
                    "mfa_setup_required": False,
                    "mfa_enabled": True,
                    **resp,
                    **mem_data}
        # If user haven't setup MFA, prompt to setup.
        return {"user": user,
                "mfa_setup_required": True,
                "mfa_enabled": False,
                **mem_data}

    resp = user_orm.generate_token_dict(str(membership.id))

    await staff_disable_simultaneous_login(membership.id, resp["auth_token"])

    return {"user": user, **resp, **mem_data}


@router.post("/signin/staff/mfa-verify", response_model=schema.TokenResponseWithUser, tags=['auth', 'mfa'])
async def mfa_verify(data: schema.MFAValidateRequest, dbsession: SessionLocal = Depends(create_session)):
    """
    Verify MFA using MFA token and code.
    """
    auth_token = data.mfa_token
    if not auth_token:
        raise HTTPException(404, "Authorization header is missing")

    auth_token = auth_token.removeprefix(f'{schema.TokenTypeEnum.bearer} ')
    payload = decode_and_validate_temp_token(auth_token)

    user_id = payload.get("user_id")
    membership_id = payload.get("membership_id")

    user = crud.get_user_by_id_and_type(dbsession, user_id, schema.UserType.staff)
    if not user:
        raise HTTPException(404, "User not found")

    if not user.mfa_secret:
        raise HTTPException(400, "MFA not set up for this user.")

    totp = pyotp.TOTP(user.mfa_secret)
    if not totp.verify(data.code, valid_window=settings.MFA_VALID_WINDOWS):
        raise HTTPException(status_code=401, detail="Invalid MFA code")

    # Generate full auth token now
    user_auth = schema.UserAuth.from_orm(user)
    token_data = user_auth.generate_token_dict(membership_id)

    await staff_disable_simultaneous_login(membership_id, token_data["auth_token"])

    mem_data = crud.get_membership_type_by_membership_id(dbsession, membership_id)

    return {
        "user": user,
        **token_data,
        **mem_data
    }


@router.post("/mfa/setup/initiate", tags=["auth", "mfa"])
def initiate_mfa_setup(data: schema.MFAInitiateRequest, dbsession: SessionLocal = Depends(create_session)):
    """
    Initiate MFA setup for a user. Generates a temp secret and returns a QR provisioning URI.
    """
    user = crud.get_user_by_id_and_type(dbsession, data.user_id, schema.UserType.staff)
    if not user:
        raise HTTPException(404, "User not found")

    if user.mfa_enabled:
        raise HTTPException(400, "MFA is already enabled for this user.")

    secret = pyotp.random_base32()

    user.temp_mfa_secret = secret
    user.temp_mfa_secret_created_at = datetime.now().astimezone()
    dbsession.commit()

    totp = pyotp.TOTP(secret)
    uri = totp.provisioning_uri(name=user.email, issuer_name=settings.MFA_ISSUER_NAME)

    qr_img = qrcode.make(uri)
    buffer = io.BytesIO()
    qr_img.save(buffer, format="PNG")
    qr_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")
    qr_data_url = f"data:image/png;base64,{qr_base64}"

    return {
        "provisioning_uri": uri,
        "authenticator_qr": qr_data_url,
        "user_id": user.id
    }


@router.post("/mfa/setup/verify", response_model=schema.TokenResponseWithUser, tags=['auth', 'mfa'])
async def initial_mfa_verify(data: schema.MFAInitialVerifyRequest, dbsession: SessionLocal = Depends(create_session)):
    """
    Initial MFA Verification using temporary token. Only required to call on the first time.
    """
    user = crud.get_user_by_id_and_type(dbsession, data.user_id, schema.UserType.staff)
    if not user:
        raise HTTPException(404, "User not found")

    try:
        membership = crud.get_user_staff_membership_by_organization_id(
            dbsession,
            str(user.id),
            (user.organization_id),
        )

    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'Membership with organization does not exist.')

    if not user.temp_mfa_secret:
        raise HTTPException(400, "No MFA setup in progress")

    now = datetime.now()
    if now - user.temp_mfa_secret_created_at > timedelta(minutes=settings.MFA_EXPIRY_MINUTES):
        raise HTTPException(400, "MFA setup expired")

    totp = pyotp.TOTP(user.temp_mfa_secret)

    if not totp.verify(data.code, valid_window=settings.MFA_VALID_WINDOWS):
        raise HTTPException(400, "Invalid MFA code")

    user.mfa_secret = user.temp_mfa_secret
    user.mfa_enabled = True
    user.temp_mfa_secret = None
    user.temp_mfa_secret_created_at = None
    dbsession.commit()

    # Generate full auth token if setup succeed
    user_auth = schema.UserAuth.from_orm(user)
    mem_data = crud.get_membership_type_by_membership_id(dbsession, membership.id)
    token_data = user_auth.generate_token_dict(str(membership.id))

    await staff_disable_simultaneous_login(membership.id, token_data["auth_token"])

    return {
        "user": user,
        **token_data,
        **mem_data
    }


@router.post("/signin/user/otp", response_model=schema.BasicMessage, tags=['auth', ])
async def signin_user_otp(data: schema.SigninOtpRequest, dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-in endpoint for a normal user using their phone number only
    """
    success_msg = 'OTP has been sent to your phone number.'

    try:
        user = crud.get_user_by_phone_number_v2(dbsession, data.phone_number, schema.UserType.staff)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'User not found.')

    if not user.is_verified:
        raise HTTPException(400, 'Please verify your account.')

    send_otp(user.phone_number)
    return schema.BasicMessage(detail=success_msg)


@router.post("/signin/user/otp/verify", response_model=schema.TokenResponse, tags=['auth', ])
async def signin_user_otp_verify(data: schema.SubmittedPhoneVerificationToken,
                                 dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-up endpoint for a normal user to verify their phone number with the sent token
    """
    try:
        user = crud.get_user_by_phone_number_and_organization_id_v2(dbsession, data.phone_number,
                                                                    data.organization_id,
                                                                    schema.UserType.staff)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'User not found.')

    if not user.is_verified:
        raise HTTPException(400, 'Please verify your account.')

    # https://www.twilio.com/docs/verify/api/verification-check
    try:
        response = verify_otp(data.phone_number, data.token)
        if response.status == 'approved':

            # Get user's membership to organization
            try:
                membership = crud.get_user_membership_by_organization_id(
                    dbsession,
                    str(user.id),
                    data.organization_id
                )
            except exceptions.ApolloObjectDoesNotExist:
                raise HTTPException(400, 'Membership with organization does not exist.')

            # user.password = ''  # nosec
            user = schema.UserAuth.from_orm(user)

            return user.generate_token(str(membership.id))

        raise HTTPException(400, 'Invalid token')

    except TwilioRestException:
        raise HTTPException(400, 'Verification failed.')


@router.post("/reset-password", response_model=schema.BasicMessage, tags=['auth', ])
async def reset_password(data: schema.ResetPasswordRequest,
                         dbsession: SessionLocal = Depends(create_session)):
    """
    Endpoint for the user to request a reset password token
    """
    # Get user by email
    try:
        user = crud.get_user_by_email_and_organization_id_v2(dbsession, data.email, data.organization_id,
                                                             schema.UserType.staff)
    except exceptions.ApolloObjectDoesNotExist:
        # raise HTTPException(400, 'User not found.')
        return schema.BasicMessage(detail='Reset password code sent.')

    if not user.is_verified:
        raise HTTPException(400, 'Please verify your account.')

    exceed_limit = crud.check_reset_password_limit(dbsession, user.id, 3)
    if exceed_limit:
        raise HTTPException(400, 'Your daily limit to request password change is exceeded.')

    # Get user method of verification
    method = user.verification_method

    # Get user membership
    try:
        crud.get_user_membership_by_organization_id(
            dbsession,
            str(user.id),
            data.organization_id
        )
    except exceptions.ApolloObjectDoesNotExist:
        # raise HTTPException(400, 'Membership with organization does not exist.')
        return schema.BasicMessage(detail='Reset password code sent.')

    # Send reset password code based on the method of verification
    if method == schema.VerificationMethodEnum.email:
        token = crud.generate_reset_password_token(dbsession, user.id)
        sendgrid_mail = SendGridMail('Reset Password Code', RecipientsSchema(emails=[user.email, ]),
                                     settings.SENDER_EMAIL, settings.SENDER_NAME)
        data_content = dict(token=token)
        sendgrid_mail.send_html_mail(data_content, 'forgot_token.html')
    elif method == schema.VerificationMethodEnum.phone:
        twilio_client.verify \
            .services(settings.TWILIO['VERIFY_SERVICE_SID']) \
            .verifications \
            .create(to=user.phone_number, channel='sms')
    return schema.BasicMessage(detail='Reset password code sent.')


@router.post("/reset-password-confirm", response_model=schema.BasicMessage, tags=['auth', ])
async def reset_password_confirm(data: schema.ResetPasswordConfirm,  # noqa: MC0001
                                 dbsession: SessionLocal = Depends(create_session)):
    try:
        user = crud.get_user_by_email_and_organization_id_v2(dbsession, data.email, data.organization_id,
                                                             schema.UserType.staff)
    except exceptions.ApolloObjectDoesNotExist:
        # raise HTTPException(400, 'User not found.')
        raise HTTPException(400, 'Invalid verification method.')

    try:
        membership = crud.get_user_membership_by_organization_id(
            dbsession,
            str(user.id),
            data.organization_id
        )
    except exceptions.ApolloObjectDoesNotExist:
        # raise HTTPException(400, 'Membership with organization does not exist.')
        raise HTTPException(400, 'Invalid verification method.')

    if data.new_password != data.new_password_confirm:
        raise HTTPException(400, 'Passwords do not match.')

    method = user.verification_method
    if method == schema.VerificationMethodEnum.email:
        try:
            token = crud.get_reset_password_token(dbsession, data, user.id)
        except exceptions.ApolloObjectDoesNotExist:
            raise HTTPException(400, 'Invalid token.')

        if token.token != data.token:
            raise HTTPException(400, 'Invalid token.')

    elif method == schema.VerificationMethodEnum.phone:
        response = verify_otp(user.phone_number, data.token)
        if response.status != 'approved':
            raise HTTPException(400, 'Invalid token.')

    else:
        raise HTTPException(400, 'Invalid verification method.')

    # Reset password
    crud.update_user_password(
        dbsession,
        data.new_password,
        str(membership.id),
    )
    return schema.BasicMessage(detail='Password has been successfully reset.')


@router.post("/renew-token", response_model=schema.TokenResponse, tags=['auth', ])
async def renew_token(request: Request, dbsession: SessionLocal = Depends(create_session)):
    auth_token = request.headers.get('authorization')
    if auth_token:
        auth_token = auth_token.removeprefix(f'{schema.TokenTypeEnum.bearer} ')
        decoded = token.decode_token(auth_token)

        try:
            user = crud.get_user_by_id(dbsession, decoded['user_id'])
            user = schema.UserAuth.from_orm(user)
            return user.renew_token(decoded, token)
        except exceptions.ApolloObjectDoesNotExist:
            raise HTTPException(400, 'User not found.')

    raise HTTPException(400, 'Authorization token is missing.')


@router.post("/verification-method", response_model=schema.BasicMessage, tags=['auth', ])
async def verification_method(data: schema.VerificationMethod, dbsession: SessionLocal = Depends(create_session)):
    # create verification code
    try:
        user = crud.get_user_by_email(dbsession, data.email)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'User not found.')

    if data.verification_method == schema.VerificationMethodEnum.email:
        token = crud.generate_verification_token(dbsession, str(user.id))
        body = f'Please verify your account with this code: {token}'
        sendgrid_mail = SendGridMail('Verification Code', RecipientsSchema(emails=[user.email, ]),
                                     '<EMAIL>')
        sendgrid_mail.send_text_mail(body)
    elif data.verification_method == schema.VerificationMethodEnum.phone:
        crud.update_user_phone_number(dbsession, str(user.id), data.phone_number)
        twilio_client.verify \
            .services(settings.TWILIO['VERIFY_SERVICE_SID']) \
            .verifications \
            .create(to=data.phone_number, channel='sms')

    return schema.BasicMessage(detail='Verification code sent.')


@router.post("/verify", response_model=schema.BasicMessage, tags=['auth', ])
async def verify(data: schema.SubmittedVerificationToken, dbsession: SessionLocal = Depends(create_session)):
    success_msg = 'Account has been successfully verified.'

    try:
        user = crud.get_user_by_email(dbsession, data.email)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'User not found.')

    try:
        token = crud.get_verification_token(dbsession, data, str(user.id))

        status_update_data = {
            'verification_method': schema.VerificationMethodEnum.email,
            'is_verified': True,
            'user_id': str(user.id)
        }
        crud.update_verification_token_used_status(dbsession, token.id, True)
        crud.update_user_verification_status(dbsession, schema.VerificationMethodUpdate(**status_update_data))
        return schema.BasicMessage(detail=success_msg)
    except exceptions.ApolloObjectDoesNotExist:
        if user.phone_number:
            # https://www.twilio.com/docs/verify/api/verification-check
            try:
                response = twilio_client.verify \
                    .services(settings.TWILIO['VERIFY_SERVICE_SID']) \
                    .verification_checks \
                    .create(to=user.phone_number, code=data.token)
                if response.status == 'approved':
                    status_update_data['verification_method'] = schema.VerificationMethodEnum.phone
                    crud.update_user_verification_status(dbsession,
                                                         schema.VerificationMethodUpdate(**status_update_data))
                    return schema.BasicMessage(detail=success_msg)
            except TwilioRestException:
                raise HTTPException(400, 'Verification failed.')

    raise HTTPException(400, 'Invalid token')


@router.get("/validate-email", response_model=List[schema.OrganizationNameResponse], tags=['auth', ])
async def validate_staff_email(email: EmailStr, dbsession: SessionLocal = Depends(create_session)):
    """
    Validate staff email endpoint to show which organization they have access
    """
    try:
        users = crud.get_all_user_by_email(dbsession, email)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'The provided email ID is not found, please re-check')

    organizations = []
    for user in users:
        organization = crud.get_organization_from_user_id(
            dbsession,
            user.id
        )
        organizations.extend(organization)
    if len(organizations) >= 1:
        return organizations
    raise HTTPException(400, 'No Organization Linked to this email')


@router.get("/verify-email", tags=['auth', ])
async def verify_email(request: Request, membership_id: str, token: str,
                       dbsession: SessionLocal = Depends(create_session)):
    def respond(is_verified, org='chargEV'):
        return HTMLResponse(content=get_verification_template(is_verified, org), status_code=200)

    _ = set_admin_as_context_user(dbsession)
    deactivate_audit()
    # Attempt to get db_membership
    try:
        db_membership = crud.get_membership_by_id(dbsession, membership_id)
    except NoResultFound:
        return respond(False)

    # Check if organizaiton is YGT
    org_is_ygt = db_membership.organization.name.lower() == settings.YGT_ORGANIZATION_NAME.lower()
    try:
        membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, membership_id)
    except exceptions.ApolloObjectDoesNotExist:
        # if no such membership exist
        return respond(False, 'YGT' if org_is_ygt else 'chargEV')

    # Check if email is already verified
    if membership_extended.email_verified:
        return respond(True, 'YGT' if org_is_ygt else 'chargEV')

    # Check token and update membership_extended if valid
    if membership_extended.verification_token and membership_extended.verification_token.token == token:
        status_update_data = {
            'verification_method': schema.VerificationMethodEnum.phone,
            'email_verified': True,
            'id': str(membership_extended.id),
            'verification_token_id': None,
        }
        crud.update_membership_extended_verification(dbsession, status_update_data)
        return respond(True, 'YGT' if org_is_ygt else 'chargEV')
    return respond(False, 'YGT' if org_is_ygt else 'chargEV')


@router.get("/manual-linking-verify-email", tags=['auth', ])
async def manual_linking_verify_email(request: Request, token: str,  # pylint: disable=too-many-return-statements
                                      migrated_member_id: str,
                                      target_member_id: str,
                                      dbsession: SessionLocal = Depends(create_session)):
    def respond(is_verified, org='chargEV'):
        return HTMLResponse(content=get_manual_linking_template(is_verified, org), status_code=200)

    _ = set_admin_as_context_user(dbsession)
    deactivate_audit()
    # Attempt to get db_membership
    try:
        migrated_member = crud.get_membership_by_id(dbsession, migrated_member_id)
        target_member = crud.get_membership_by_id(dbsession, target_member_id)
    except NoResultFound:
        return respond(False)
    except exceptions.ApolloObjectDoesNotExist:
        return respond(False)

    try:
        manual_linking_token = crud.get_user_manual_linking_token_by_membership_id(dbsession,
                                                                                   str(migrated_member.id),
                                                                                   str(target_member.id))
    except exceptions.ApolloObjectDoesNotExist:
        return respond(False)

    # Check if email is already verified
    if manual_linking_token.is_used:
        return respond(True)

    if datetime.now().astimezone() > manual_linking_token.expiration:
        return respond(False)

    # Check token and update if valid
    if manual_linking_token.token == token:
        # delete migrated, update target.
        status_update_data = {
            'id': str(manual_linking_token.id),
            'is_used': True,
        }
        crud.update_manual_linking_verification(dbsession, status_update_data, migrated_member, target_member)
        return respond(True)
    return respond(False)


@router.post("/test", response_model=schema.BasicMessage, tags=['auth', ])
async def test(dbsession: SessionLocal = Depends(create_session),
               dtoken: token.DecodedTokenUser = Depends()):
    try:
        user = crud.get_user_by_id(dbsession, dtoken.user_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(400, 'User not found.')

    return schema.BasicMessage(detail=f"Test ok, user email {user.email}")


@router.get("/cpo-token", tags=['auth', ], response_model=Union[List[schema.OCPITokenResponse],  # noqa
                                                                Page[schema.OCPITokenResponse]])
async def get_list_token(params: Params = Depends(),  # noqa: MC0001
                         dbsession: SessionLocal = Depends(create_session),
                         filters: dict = Depends(cpo_token_filters),
                         pagination: bool = True) -> Union[List[schema.OCPITokenResponse],  # noqa
                                                           Page[schema.OCPITokenResponse]]:
    """
    Get list of CPO Token (ChargEV as CPO)
    """
    try:
        query = crud.get_all_cpo_token(dbsession)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICPOToken')

    if filters:
        for key, value in filters.items():
            if not value:
                continue
            try:
                attr = getattr(models.OCPICPOToken, key)
                if value:
                    if key == 'id':
                        query = models.filter_query(query, attr == value)
                    else:
                        query = models.filter_query(query, attr.ilike(f'%{value}%'))
            except AttributeError:
                if key == 'token_contract_id':
                    query = models.filter_query(query, models.OCPICPOToken.contract_id.ilike(f'%{value}%'))
                # else:
                #     pass
    data = dbsession.execute(query)
    if pagination:
        return paginate(query, params)
    return data


@router.get("/emsp-token", tags=['auth', ], response_model=Union[List[schema.OCPITokenResponse],  # noqa
                                                                 Page[schema.OCPITokenResponse]])
async def get_list_emsp_token(params: Params = Depends(),  # noqa: MC0001
                              dbsession: SessionLocal = Depends(create_session),
                              filters: dict = Depends(emsp_token_filters),
                              pagination: bool = True) -> Union[List[schema.OCPITokenResponse],  # noqa
                                                                Page[schema.OCPITokenResponse]]:
    """
    Get list of EMSP Token (ChargEV As EMSP)
    """
    try:
        query = crud.get_all_emsp_token(dbsession)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OCPICPOToken')

    if filters:
        for key, value in filters.items():
            if not value:
                continue
            try:
                attr = getattr(models.OCPIToken, key)

                if key == 'id':
                    query = models.filter_query(query, attr == value)
                elif key == 'valid':
                    value_lower = value.lower()
                    boolean_value = None
                    if value_lower in ['yes', 'true']:
                        boolean_value = True
                    elif value_lower in ['no', 'false']:
                        boolean_value = False
                    if boolean_value is not None:
                        query = models.filter_query(query, attr.is_(boolean_value))
                else:
                    query = models.filter_query(query, attr.ilike(f'%{value}%'))

            except AttributeError:
                if key == 'token_contract_id':
                    query = models.filter_query(query, models.OCPIToken.contract_id.ilike(f'%{value}%'))

    data = dbsession.execute(query)
    if pagination:
        return paginate(query, params)
    return data


@router.get("/emsp-token-token/{country_code}/{party_id}/{token_id}", tags=['auth', ],
            response_model=schema.OCPICPOTokenResponse)
async def get_token_token(party_id: str, country_code: str, token_id: str,
                          dbsession: SessionLocal = Depends(create_session)) -> schema.OCPICPOTokenResponse:
    """
    Get token based on token info
    """
    try:
        query = crud.get_cpo_token_by_partner_id(dbsession,
                                                 country_code=country_code,
                                                 party_id=party_id,
                                                 token_id=token_id)
        data = query.first()
        if data:
            return data
        raise HTTPException(status_code=404, detail='Token not found')
    except NoResultFound:
        raise HTTPException(status_code=404, detail='Token not found')


@router.patch("/emsp-token-token/{country_code}/{party_id}/{token_id}", tags=['auth', ],
              response_model=schema.OCPICPOTokenResponse)
async def update_token_token(party_id: str, country_code: str, token_id: str, token_schema: schema.OCPICPOTokenUpdate,
                             dbsession: SessionLocal = Depends(create_session)) -> schema.OCPICPOTokenResponse:
    """
    Update token based on token info
    """
    try:
        deactivate_audit()
        query = crud.get_cpo_token_by_partner_id(dbsession,
                                                 country_code=country_code,
                                                 party_id=party_id,
                                                 token_id=token_id)
        data = query.first()
        if data:
            return crud.update_cpo_token_by_token_uuid(dbsession, data.id, token_schema)
        raise HTTPException(status_code=404, detail='Token not found')
    except NoResultFound:
        raise HTTPException(status_code=404, detail='Token not found')


@router.get("/resource-page")
async def get_page_resource(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Get Resources Group by Page
    """
    data = crud.get_page_resources(dbsession)
    dct = {}
    for d in data:
        view, create, edit, delete, manage = [], [], [], [], []
        if d.view:
            view = [{"id": k, "name": v} for k, v in d.view.items()]
        if d.create:
            create = [{"id": k, "name": v} for k, v in d.create.items()]
        if d.edit:
            edit = [{"id": k, "name": v} for k, v in d.edit.items()]
        if d.delete:
            delete = [{"id": k, "name": v} for k, v in d.delete.items()]
        if d.manage:
            manage = [{"id": k, "name": v} for k, v in d.manage.items()]
        resource = {"view": view, "create": create, "edit": edit, "delete": delete, "manage": manage}
        dct[d.name] = resource
    return dct


@router.get("/list-permission")
async def get_page_permission_list(request: Request, role_id: str, dbsession: SessionLocal = Depends(create_session)):
    page_resource = crud.get_page_resources(dbsession)
    role = crud.get_role_by_id(dbsession, role_id)
    resource = [str(res.id) for res in role.resources]
    dct = {}
    for d in page_resource:
        deletes = all(delete in resource for delete in d.delete.keys())
        manages = all(manage in resource for manage in d.manage.keys())
        if deletes:
            edits, creates, views = True, True, True
        else:
            edits = all(edit in resource for edit in d.edit.keys())
            creates = all(create in resource for create in d.create.keys())
        if edits or creates or manages:
            views = True
        else:
            views = all(view in resource for view in d.view.keys())
        dct[d.name] = {'view': views, 'create': creates, 'edit': edits, 'delete': deletes, 'manage': manages}
    return dct


@router.patch("/membership", response_model=schema.MembershipResponse)
async def update_user(data: schema.MembershipUpdate,
                      request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Partially updates User and Membership (Staff level) objects for a user in the Organization

    :param str organization_id: Target Organization ID
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
        set_member_as_context_user(dbsession, str(membership_id))
        db_membership = crud.get_membership_by_id(dbsession, membership_id)
        data_dict = data.dict(exclude_unset=True)
        if 'password' in data_dict:
            if not bcrypt.checkpw(data_dict.get('current_password').encode('utf-8'),
                                  db_membership.password.encode('utf-8')):
                raise HTTPException(400, 'Invalid current password')

        res = crud.update_organization_membership(dbsession, data, db_membership.organization_id, membership_id)
        return res
    except (exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloEmailPhoneNumberAlreadyAssigned) as e:
        raise HTTPException(400, e.__str__())
