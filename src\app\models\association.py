from sqlalchemy import Table, Column, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from app.models.base import BaseModel


membership_vehicle_association_table = Table(
    'membership_vehicle_association',
    BaseModel.metadata,
    Column('main_membership_id', UUID(as_uuid=True), ForeignKey('main_membership.id', ondelete='CASCADE')),
    Column('main_vehicle_id', UUID(as_uuid=True), ForeignKey('main_vehicle.id', ondelete='CASCADE')),
)
