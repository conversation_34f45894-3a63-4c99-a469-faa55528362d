"""49

Revision ID: 83f208699819
Revises: 9ef8e81d00ca
Create Date: 2023-08-08 17:34:41.957038

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '83f208699819'
down_revision = '9ef8e81d00ca'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_organization_authentication_services', sa.Column('name', sa.String(), nullable=True))
    op.add_column('main_organization_authentication_services', sa.Column('is_editable', sa.<PERSON>(), nullable=True))
    op.add_column('main_organization_authentication_services', sa.Column('expiry_date', sa.DateTime(timezone=True), nullable=True))
    op.drop_constraint('main_organization_authentication_services_organization_id_key', 'main_organization_authentication_services', type_='unique')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('main_organization_authentication_services_organization_id_key', 'main_organization_authentication_services', ['organization_id'])
    op.drop_column('main_organization_authentication_services', 'expiry_date')
    op.drop_column('main_organization_authentication_services', 'is_editable')
    op.drop_column('main_organization_authentication_services', 'name')
    # ### end Alembic commands ###
