[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
fastapi = "==0.68.2"
uvicorn = "==0.30.*"
python-dotenv = "==0.19.*"
pydantic = {extras = ["email"], version = "1.8"}
sqlalchemy = "==1.4"
alembic = "==1.6.*"
psycopg2-binary = "==2.9.*"
argon2-cffi = "==21.1.*"
python-jose = {extras = ["cryptography"], version = "==3.3.*"}
requests = "==2.26.*"
twilio = "==7.3.*"
celery = "==5.2.7"
sentry-sdk = "==1.5"
pytest-asyncio = "==0.17"
sendgrid = "==6.7"
jinja2 = "==3.0.3"
bs4 = "==0.0"
phonenumbers = "==8.12"
typer = "==0.4.1"
httpx = "==0.23.0"
python-multipart = "==0.0.9"
fastapi-pagination = "==0.12.28"
flower = "==1.2.0"
boto3 = "==1.35.6"
pandas = "==2.2.2"
bcrypt = "==4.2.0"
tenacity = "==9.0.0"
openpyxl = "==3.1.5"
fastparquet = "==2024.5.0"
pyarrow = "==17.0.0"
qrcode = "==7.4.2"
pillow = "==10.4.0"
aiofiles = "==24.1.0"
curlify = "==2.2.1"
cybersource-rest-client-python = "==0.0.58"
pyjwt = "==2.9.0"
pdfkit = "==1.0.0"
xlsxwriter = "==3.2.0"
gunicorn = "==23.0.0"
defusedxml = "==0.7.1"
macaddress = "==2.0.2"
coverage = "==7.6.4"
ccy = "==1.3.1"
phone-iso3166 = "==0.4.1"
py-ocpi = {ref = "*******", git = "https://github.com/sam-agmostudio/ocpi"}
aioredis = "==2.0.1"
pyotp = "==2.9.0"

[dev-packages]
pytest = "==6.2.*"
bandit = "==1.7.9"
prospector = "==1.2.*"
faker = "==8.14.*"
factory-boy = "3.2"
autopep8 = "*"

[requires]
python_version = "3.9"
