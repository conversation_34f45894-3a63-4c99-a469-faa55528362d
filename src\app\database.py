from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.settings import (SQLALCHEMY_DATABASE_URL, DATABASE_SSL, DATABASE_POOL_SIZE,
                          DATABASE_MAX_OVERFLOW, DATABASE_POOL_RECYCLE, DATABASE_POOL_TIMEOUT)


if DATABASE_SSL:
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_pre_ping=True,
        connect_args={
            'sslmode': 'verify-full',
            'sslrootcert': 'ap-southeast-1-bundle.pem'
        },
        pool_size=int(DATABASE_POOL_SIZE),
        pool_recycle=int(DATABASE_POOL_RECYCLE),
        pool_timeout=float(DATABASE_POOL_TIMEOUT),
        max_overflow=int(DATABASE_MAX_OVERFLOW),
    )
else:
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_pre_ping=True,
        pool_size=int(DATABASE_POOL_SIZE),
        pool_recycle=int(DATABASE_POOL_RECYCLE),
        pool_timeout=float(DATABASE_POOL_TIMEOUT),
        max_overflow=int(DATABASE_MAX_OVERFLOW),
    )

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

meta = MetaData()


def create_schema():
    meta.create_all(engine)


def create_session() -> SessionLocal:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
