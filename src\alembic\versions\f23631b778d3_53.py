"""53

Revision ID: f23631b778d3
Revises: c6e0f541a597
Create Date: 2023-08-22 13:46:23.133682

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f23631b778d3'
down_revision = 'c6e0f541a597'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_partner_charge_point',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('charge_point_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.<PERSON>umn('partner_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['partner_id'], ['main_external_organization.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('charge_point_id', 'partner_id', name='_unique_partner_charge_point')
    )
    op.add_column('main_organization_authentication_services', sa.Column('partner_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key('main_oas_partner_fkey', 'main_organization_authentication_services', 'main_external_organization', ['partner_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('main_oas_partner_fkey', 'main_organization_authentication_services', type_='foreignkey')
    op.drop_column('main_organization_authentication_services', 'partner_id')
    op.drop_table('main_partner_charge_point')
    # ### end Alembic commands ###
