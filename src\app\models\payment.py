import sqlalchemy as db
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.schema import Sequence

from app.models.base import BaseModel


class ChargingSessionBill(BaseModel):
    __tablename__ = 'main_charging_session_bill'

    charging_session_id = db.Column(db.String)
    charging_session_type = db.Column(db.String)
    charge_point_id = db.Column(db.String)
    partner_id = db.Column(db.String)
    cpo_id = db.Column(db.String)
    id_tag = db.Column(db.String)
    usage_amount = db.Column(db.Numeric(scale=4))  # amount excluding tax
    usage_type = db.Column(db.String)
    status = db.Column(db.String, default='Pending')
    discount = db.Column(JSONB)
    meta = db.Column(JSONB)

    # This is normally the Charging Session's Transaction ID
    invoice_number = db.Column(db.String)

    # This is when the Charging Session is actually using started with Pre-auth, we need to tally back pre-auth
    reference_number = db.Column(db.String)
    tax_amount = db.Column(db.Numeric(scale=4))  # tax only
    tax_rate = db.Column(db.Numeric(scale=4))  # tax rate
    total_amount = db.Column(db.Numeric(scale=4))  # amount including tax
    hogging_fee = db.Column(db.Numeric(scale=4))

    outstanding_balance = db.Column(db.Numeric(scale=4))
    preferred_payment_flow = db.Column(db.String)
    preferred_payment_method = db.Column(db.String)
    is_low_wallet_balance = db.Column(db.Boolean)
    e_invoice_issued = db.Column(db.Boolean, default=False)


class PaymentRequest(BaseModel):
    __tablename__ = 'main_payment_request'

    type = db.Column(db.String)
    amount = db.Column(db.String)
    currency = db.Column(db.String)
    billing_description = db.Column(db.String)
    reason = db.Column(db.String)
    status = db.Column(db.String, default='Pending')
    update_token = db.Column(db.Boolean, default=False)
    save_credit_card = db.Column(db.Boolean, default=False)

    # This is charge point id instead of connector id
    connector_id = db.Column(db.String)
    meta = db.Column(JSONB, default={})
    charging_session_bill = db.orm.relationship('ChargingSessionBill', backref='payment_requests')
    member = db.orm.relationship('Membership', backref='payment_requests')
    promo_usage = db.orm.relationship('PromoCodeUsage', backref='payment_requests')
    invoice_number = db.Column(db.String)
    reference_number = db.Column(db.String)

    member_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_membership.id'))
    promo_usage_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_promo_code_usage.id'))
    charging_session_bill_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_charging_session_bill.id'))

    pr_running_number = db.Column(db.Integer,
                                  Sequence('pr_running_number_sequence', start=60000, increment=1), unique=True)

    preauth_bill_via_recurring = db.Column(db.Boolean, default=False)  # New field to indicate preauth failure
    wallet_deduct_amount = db.Column(db.String)
    wallet_deduct_status = db.Column(db.String)
    non_wallet_deduct_amount = db.Column(db.String)
    non_wallet_deduct_status = db.Column(db.String)

    prepaid_wallet_transaction_id = db.Column(
        UUID(as_uuid=True),
        db.ForeignKey('main_prepaid_wallet_transaction.id',
                      ondelete='SET NULL'))
    prepaid_wallet_transaction = db.orm.relationship('PrepaidWalletTransaction', backref='payment_requests')
    payment_refund = db.orm.relationship('PaymentRefund', backref='payment_request')
    pre_auth_outstanding_amount = db.Column(db.String)
    pre_auth_outstanding_capture_status = db.Column(db.String)
    pre_auth_outstanding_order_id = db.Column(db.String)
    pay_via_preauth_and_recurring = db.Column(db.Boolean, default=False)


class RecurringPayment(BaseModel):
    __tablename__ = 'main_recurring_payment'

    record_type = db.Column(db.String)
    sub_merchant = db.Column(db.String)
    token = db.Column(db.String)
    pan = db.Column(db.String)
    expired_date = db.Column(db.String)
    currency = db.Column(db.String)
    amount = db.Column(db.String)
    bill_name = db.Column(db.String)
    bill_email = db.Column(db.String)
    bill_mobile = db.Column(db.String)
    bill_desc = db.Column(db.String)
    response = db.Column(JSONB)

    order_id = db.Column(db.String)
    payment_request_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_payment_request.id'))


class TokenCheckPreAuthPayment(BaseModel):
    __tablename__ = 'main_token_check_pre_auth_payment'

    transaction_type = db.Column(db.String)
    transaction_channel = db.Column(db.String)
    transaction_id = db.Column(db.String)
    reference_id = db.Column(db.String)

    expired_date = db.Column(db.String)

    token = db.Column(db.String)
    currency = db.Column(db.String)
    amount = db.Column(db.String)
    bill_name = db.Column(db.String)
    bill_email = db.Column(db.String)
    bill_mobile = db.Column(db.String)
    bill_desc = db.Column(db.String)
    response = db.Column(JSONB)
    callback_response = db.Column(JSONB)

    payment_request_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_payment_request.id'))
    is_3ds = db.Column(db.Boolean, default=False)

    is_successful = db.Column(db.Boolean, default=False)
    is_binded = db.Column(db.Boolean, default=False)
    is_used = db.Column(db.Boolean, default=False)
    is_refunded = db.Column(db.Boolean, default=False)
    failed_refund = db.Column(db.Boolean, default=False)
    used_amount = db.Column(db.String)

    payment_request = db.orm.relationship('PaymentRequest', backref='token_check_pre_auth_payment')
    payment_type = db.Column(db.String)
    charging_session_id = db.Column(db.String)
    payment_gateway = db.Column(db.String)
    stat_code = db.Column(db.String)
    error_description = db.Column(db.String)


class TokenCheckPreAuthPaymentRefund(BaseModel):
    __tablename__ = 'main_token_check_pre_auth_payment_refund'

    transaction_id = db.Column(db.String)
    domain = db.Column(db.String)

    response = db.Column(JSONB)
    stat_code = db.Column(db.String)

    token_check_pre_auth_payment_id = db.Column(UUID(as_uuid=True),
                                                db.ForeignKey('main_token_check_pre_auth_payment.id'))
    token_check_pre_auth_payment = db.orm.relationship('TokenCheckPreAuthPayment',
                                                       backref='token_check_pre_auth_payment_refund')


class PreAuthPayment(BaseModel):
    __tablename__ = 'main_pre_auth_payment'

    transaction_type = db.Column(db.String)
    transaction_channel = db.Column(db.String)
    transaction_id = db.Column(db.String)
    reference_id = db.Column(db.String)

    expired_date = db.Column(db.String)

    token = db.Column(db.String)
    currency = db.Column(db.String)
    amount = db.Column(db.String)
    bill_name = db.Column(db.String)
    bill_email = db.Column(db.String)
    bill_mobile = db.Column(db.String)
    bill_desc = db.Column(db.String)
    response = db.Column(JSONB)
    callback_response = db.Column(JSONB)

    payment_request_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_payment_request.id'))
    is_3ds = db.Column(db.Boolean, default=False)

    is_successful = db.Column(db.Boolean, default=False)
    is_binded = db.Column(db.Boolean, default=False)
    is_used = db.Column(db.Boolean, default=False)
    is_refunded = db.Column(db.Boolean, default=False)
    failed_refund = db.Column(db.Boolean, default=False)
    used_amount = db.Column(db.String)

    payment_request = db.orm.relationship('PaymentRequest', backref='pre_auth_payment')
    payment_type = db.Column(db.String)
    charging_session_id = db.Column(db.String)
    payment_gateway = db.Column(db.String)
    stat_code = db.Column(db.String)
    error_description = db.Column(db.String)


class PreAuthPaymentCapture(BaseModel):
    __tablename__ = 'main_pre_auth_payment_capture'

    domain = db.Column(db.String)
    transaction_id = db.Column(db.String)
    amount = db.Column(db.String)
    currency = db.Column(db.String)
    reference_id = db.Column(db.String)

    response = db.Column(JSONB)
    response_content = db.Column(db.String)

    pre_auth_payment_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_pre_auth_payment.id'))
    pre_auth_payment = db.orm.relationship('PreAuthPayment', backref='pre_auth_payment_capture')
    charging_session_id = db.Column(db.String)

    payment_request_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_payment_request.id'))
    payment_request = db.orm.relationship('PaymentRequest', backref='pre_auth_payment_capture')


class PreAuthPaymentRefund(BaseModel):
    __tablename__ = 'main_pre_auth_payment_refund'

    transaction_id = db.Column(db.String)
    domain = db.Column(db.String)

    response = db.Column(JSONB)
    stat_code = db.Column(db.String)

    pre_auth_payment_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_pre_auth_payment.id'))
    pre_auth_payment = db.orm.relationship('PreAuthPayment', backref='pre_auth_payment_refund')


class PaymentCallback(BaseModel):
    __tablename__ = 'main_payment_callback'

    nbcb = db.Column(db.Integer)
    amount = db.Column(db.Numeric(scale=2))
    tran_id = db.Column(db.String)
    domain = db.Column(db.String)
    status = db.Column(db.String)
    appcode = db.Column(db.String)
    error_code = db.Column(db.String)
    error_desc = db.Column(db.String)
    skey = db.Column(db.String)
    currency = db.Column(db.String)
    paydate = db.Column(db.DateTime(timezone=True))
    channel = db.Column(db.String)
    extra_p = db.Column(JSONB)

    order_id = db.Column(db.String)
    payment_request_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_payment_request.id'))


class OCPICPOCdr(BaseModel):
    __tablename__ = 'main_ocpi_cpo_cdr'

    country_code = db.Column(db.String)
    party_id = db.Column(db.String)
    partner_ocpi_cpo_cdr_id = db.Column(db.String)

    start_date_time = db.Column(db.DateTime)
    end_date_time = db.Column(db.DateTime)

    session_id = db.Column(db.String)
    cdr_token = db.Column(JSONB)

    auth_method = db.Column(db.String)
    authorization_reference = db.Column(db.String)

    cdr_location = db.Column(JSONB)

    meter_id = db.Column(db.String)
    currency = db.Column(db.String)

    tariffs = db.Column(JSONB)

    charging_periods = db.Column(JSONB)

    signed_data = db.Column(JSONB)

    total_cost = db.Column(JSONB)
    total_fixed_cost = db.Column(JSONB)

    total_energy = db.Column(db.String)
    total_energy_cost = db.Column(JSONB)

    total_time = db.Column(db.String)
    total_time_cost = db.Column(JSONB)

    total_parking_time = db.Column(db.String)
    total_parking_cost = db.Column(JSONB)

    total_reservation_cost = db.Column(JSONB)

    remark = db.Column(db.String)

    invoice_reference_id = db.Column(db.String)

    credit = db.Column(db.Boolean, default=False)
    credit_reference_id = db.Column(db.String)

    home_charging_compensation = db.Column(db.Boolean, default=False)
    native_charging_session_id = db.Column(db.String)
    transaction_id = db.Column(db.String)
    operator_id = db.Column(db.String)
    # charging_session_bill_id = db.Column(UUID(as_uuid=True),
    #                                      db.ForeignKey('charging_session_bill.id', ondelete='SET NULL'))


class PartnerPaymentNotification(BaseModel):
    __tablename__ = 'main_partner_payment_notification'

    charging_session_id = db.Column(db.String)
    charging_session_bill_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_charging_session_bill.id'))
    status = db.Column(db.String)
    message = db.Column(db.String)
    partner_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_external_organization.id'))


class PaymentGatewayReconcilation(BaseModel):
    __tablename__ = 'main_payment_gateway_reconcilation'

    billing_date = db.Column(db.DateTime(timezone=True))
    order_id = db.Column(db.String)
    tran_id = db.Column(db.String)
    channel = db.Column(db.String)
    amount = db.Column(db.String)
    stat_code = db.Column(db.String)
    stat_name = db.Column(db.String)
    billing_name = db.Column(db.String)
    service_item = db.Column(db.String)
    bin_number = db.Column(db.String)
    currency = db.Column(db.String)
    billing_email = db.Column(db.String)
    transaction_rate = db.Column(db.String)
    gst = db.Column(db.String)
    net_amount = db.Column(db.String)
    bank_name = db.Column(db.String)
    settlement_date = db.Column(db.DateTime(timezone=True))
    payment_request = db.orm.relationship('PaymentRequest', backref='payment_gateway_reconcilation')
    payment_request_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_payment_request.id'))
    member = db.orm.relationship('Membership', backref='payment_gateway_reconcilation')
    member_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_membership.id'))
    transaction_cost = db.Column(db.String)
    billing_mobile_number = db.Column(db.String)
    transaction_fee = db.Column(db.Float)
    expiry_date = db.Column(db.DateTime(timezone=True))
    status_description = db.Column(db.String)
    paid_date = db.Column(db.DateTime(timezone=True))
    capture_ref_id = db.Column(db.String)
    refund_ref_id = db.Column(db.String)
    charging_session_bill = db.orm.relationship('ChargingSessionBill', backref='payment_gateway_reconcilation')
    charging_session_bill_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_charging_session_bill.id'))


class CommercialInvoice(BaseModel):
    __tablename__ = 'main_commercial_invoice'

    type = db.Column(db.String)
    invoice_number = db.Column(db.String)
    amount = db.Column(db.String)
    currency = db.Column(db.String)
    member_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_membership.id'))
    member = db.orm.relationship('Membership', backref='commercial_invoices')

    invoice_running_number = db.Column(db.Integer,
                                       Sequence('commercial_invoice_seq', start=100000, increment=1), unique=True)

    subscription_order_id = db.Column(
        UUID(as_uuid=True),
        db.ForeignKey('main_subscription_order.id', ondelete='CASCADE'),
        nullable=True
    )
    subscription_order = db.orm.relationship('SubscriptionOrder', backref='commercial_invoices')


class PaymentRefund(BaseModel):
    __tablename__ = 'main_payment_refund'

    payment_request_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_payment_request.id'))
    refund_amount = db.Column(db.String)
    refund_status = db.Column(db.String)
    refund_type = db.Column(db.String)
    remark = db.Column(db.String)
    meta = db.Column(JSONB)
    reference_id = db.Column(db.String, unique=True)
    pg_refund_id = db.Column(db.String)
    pg_refund_response = db.Column(JSONB)
    pg_refund_callback = db.Column(JSONB)

    pg_transaction_id = db.Column(db.String)
    e_credit_note_issued = db.Column(db.Boolean, default=False)


class OutlierSession(BaseModel):
    __tablename__ = 'main_outlier_session'

    charging_session_id = db.Column(db.String)
    charging_session_type = db.Column(db.String)
    transaction_id = db.Column(db.String, unique=True)
    id_tag = db.Column(db.String)
    billing_currency = db.Column(db.String)
    billing_amount = db.Column(db.Numeric(scale=4))
    billing_kwh = db.Column(db.Numeric(scale=4))
    charging_duration = db.Column(db.Numeric(scale=4))
    reasons = db.Column(ARRAY(db.Text), default=[])
    status = db.Column(db.String, default='Pending')
    remarks = db.Column(db.String)


class EInvoice(BaseModel):
    __tablename__ = 'main_e_invoice'

    e_invoice_date_time = db.Column(db.DateTime(timezone=True))
    e_invoice_number = db.Column(db.String)
    charging_session_id = db.Column(db.String)
    # charging_session_bill_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_charging_session_bill.id'))
    charging_session_bill_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_charging_session_bill.id'),
                                         unique=True, nullable=False)

    status = db.Column(db.String)
    submission_payload = db.Column(JSONB)
    submission_response = db.Column(JSONB)
    submission_callback = db.Column(JSONB)

    lhdn_uid = db.Column(db.String)
    lhdn_qr = db.Column(db.String)
    lhdn_validated_date_time = db.Column(db.DateTime(timezone=True))

    last_submission_date = db.Column(db.DateTime(timezone=True))
    last_rejection_date = db.Column(db.DateTime(timezone=True))
    status_timeline = db.Column(JSONB)
    error_message_timeline = db.Column(JSONB)
    charging_session_bill = db.orm.relationship('ChargingSessionBill',
                                                backref=db.orm.backref('e_invoice', uselist=False))

    currency_code = db.Column(db.String)
    payment_terms = db.Column(db.String)
    bill_reference_number = db.Column(db.String)
    is_self_issued = db.Column(db.Boolean)
    is_consolidated_invoice = db.Column(db.Boolean)

    total_net_amount = db.Column(db.String)
    total_tax_amount = db.Column(db.String)
    total_charge_amount = db.Column(db.String)
    total_discount_value = db.Column(db.String)
    total_payable_amount = db.Column(db.String)
    total_tax_excluded_amount = db.Column(db.String)
    total_tax_included_amount = db.Column(db.String)

    invoice_tax = db.Column(JSONB)
    invoice_discount = db.Column(JSONB)
    invoice_total_excluding_tax = db.Column(db.String)
    quantity = db.Column(db.String)
    measurement = db.Column(db.String)
    unit_price = db.Column(db.String)
    sub_total = db.Column(db.String)

    e_invoice_running_number = db.Column(db.Integer,
                                         Sequence('e_invoice_running_number_sequence',
                                                  start=10000, increment=1), unique=True)


class EInvoiceRequest(BaseModel):
    __tablename__ = 'main_e_invoice_reqeust'

    e_invoice_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_e_invoice.id',
                                                               ondelete='CASCADE'))
    e_invoice = db.orm.relationship('EInvoice', backref='e_invoice_request')


class ECreditNote(BaseModel):
    __tablename__ = 'main_e_credit_note'

    e_credit_note_date_time = db.Column(db.DateTime(timezone=True))
    e_credit_note_number = db.Column(db.String)

    # charging_session_bill_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_charging_session_bill.id'))
    payment_refund_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_payment_refund.id'),
                                  unique=True, nullable=False)
    e_invoice_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_e_invoice.id'), nullable=True)
    e_invoice = db.orm.relationship('EInvoice')

    status = db.Column(db.String)

    submission_payload = db.Column(JSONB)
    submission_response = db.Column(JSONB)
    submission_callback = db.Column(JSONB)

    lhdn_uid = db.Column(db.String)
    lhdn_qr = db.Column(db.String)
    lhdn_validated_date_time = db.Column(db.DateTime(timezone=True))

    last_submission_date = db.Column(db.DateTime(timezone=True))
    last_rejection_date = db.Column(db.DateTime(timezone=True))

    status_timeline = db.Column(JSONB)
    error_message_timeline = db.Column(JSONB)

    payment_refund = db.orm.relationship('PaymentRefund',
                                         backref=db.orm.backref('e_credit_note', uselist=False))

    currency_code = db.Column(db.String)
    payment_terms = db.Column(db.String)
    bill_reference_number = db.Column(db.String)
    is_self_issued = db.Column(db.Boolean)
    is_consolidated_invoice = db.Column(db.Boolean)

    total_net_amount = db.Column(db.String)
    total_tax_amount = db.Column(db.String)
    total_charge_amount = db.Column(db.String)
    total_discount_value = db.Column(db.String)
    total_payable_amount = db.Column(db.String)
    total_tax_excluded_amount = db.Column(db.String)
    total_tax_included_amount = db.Column(db.String)

    invoice_tax = db.Column(JSONB)
    invoice_discount = db.Column(JSONB)
    invoice_total_excluding_tax = db.Column(db.String)
    quantity = db.Column(db.String)
    measurement = db.Column(db.String)
    unit_price = db.Column(db.String)
    sub_total = db.Column(db.String)

    e_cn_running_number = db.Column(db.Integer,
                                    Sequence('e_cn_running_number_sequence',
                                             start=10000, increment=1), unique=True)


class ECreditNoteRequest(BaseModel):
    __tablename__ = 'main_e_credit_note_request'

    e_credit_note_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_e_credit_note.id',
                                                                   ondelete='CASCADE'))
    e_credit_note = db.orm.relationship('ECreditNote', backref='e_credit_note_request')
