"""114

Revision ID: a651ea1b327e
Revises: 45aec3115bb2
Create Date: 2024-08-07 18:04:04.883239

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a651ea1b327e'
down_revision = '45aec3115bb2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_pre_auth_payment_capture', sa.Column('response_content', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_pre_auth_payment_capture', 'response_content')
    # ### end Alembic commands ###
