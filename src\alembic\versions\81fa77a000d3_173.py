"""173

Revision ID: 81fa77a000d3
Revises: 8ba9b8de52ce
Create Date: 2025-05-30 14:10:09.879213

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '81fa77a000d3'
down_revision = '8ba9b8de52ce'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_charging_session_bill', sa.Column('e_invoice_issued', sa.<PERSON>(), nullable=True))
    op.add_column('main_payment_refund', sa.Column('e_credit_note_issued', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_payment_refund', 'e_credit_note_issued')
    op.drop_column('main_charging_session_bill', 'e_invoice_issued')
    # ### end Alembic commands ###
