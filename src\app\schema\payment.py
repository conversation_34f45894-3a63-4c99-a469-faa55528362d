# pylint:disable=too-many-lines
import json
from datetime import datetime, timed<PERSON><PERSON>
from uuid import UUID
from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field, Json, validator

from .auth import User
from .organization import ChargePoint, ShallowMembershipResponse, OrganizationSimpleResponse


# pylint:disable=unsubscriptable-object
class RecordType(str, Enum):
    credit_card = 'P'  # nosec
    token = 'T'  # nosec
    e_mandata = 'E'  # nosec
    f_token = 'F'  # nosec
    thb_token = 'K'  # nosec
    recurring = 'O'  # nosec


class TransactionType(str, Enum):
    auts = 'AUTS'


class TransactionChannel(str, Enum):
    credit = 'CREDITAN'
    credit_ai = 'CREDITAI'
    credit_an = 'CREDITAN'


class Currency(str, Enum):
    myr = 'MYR'
    sgd = 'SGD'
    thb = 'THB'
    rm = 'RM'
    usd = 'USD'
    bnd = 'BND'
    khr = 'KHR'
    idr = 'IDR'
    vnd = 'VND'
    php = 'PHP'


class PaymentStatus(str, Enum):
    successful = '00'
    failure = '11'
    pending = '22'


class PaymentRequestType(str, Enum):
    direct = 'Direct Payment'
    pre_auth = 'Pre-Auth'
    recurring = 'Recurring Payment'
    wallet = 'Wallet'
    payment_terminal = 'Payment Terminal'
    partial = 'Partial'
    partial_direct = 'Direct Partial Payment'


class PaymentRequestStatus(str, Enum):
    pre_charging = 'Pre Charging'
    pending = 'Pending'
    done = 'Done'
    failed = 'Failed'
    free = 'Free'
    rejected = 'Rejected'


class PaymentRequestReason(str, Enum):
    payment = 'Payment'
    deposit = 'Deposit'
    prepaid_wallet_topup = 'Prepaid Wallet Topup'
    prepaid_wallet_deduction = 'Prepaid Wallet Deduction'
    subscription = 'Subscription Order'
    renewal = 'Renewal Order'
    pre_auth = 'Pre-Auth'
    token_check = 'Token-Check'  # nosec
    contract_certificate = 'Contract Certificate Order'
    contract_certificate_renewal = 'Contract Certificate Renewal Order'
    wallet_refund = 'Wallet Refund'


class RefundType(str, Enum):
    wallet = 'Wallet'
    credit_card = 'Credit Card'


class RefundStatus(str, Enum):
    pending = 'Pending'
    rejected = 'Rejected'
    success = 'Success'


class PaymentRequest(BaseModel):
    type: PaymentRequestType
    amount: str
    currency: Currency = Currency.myr
    billing_description: str
    reason: PaymentRequestReason
    update_token: bool = False
    save_credit_card: bool = False
    meta: dict = {}
    promo_usage_id: Optional[UUID]
    connector_id: Optional[str]
    charging_session_bill_id: Optional[str]
    invoice_number: Optional[str]
    reference_number: Optional[str]
    wallet_deduct_amount: Optional[str] = '0.00'
    wallet_deduct_status: Optional[str]
    non_wallet_deduct_amount: Optional[str] = '0.00'
    non_wallet_deduct_status: Optional[str]

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))

    @validator('wallet_deduct_amount')
    def validate_wallet_amount(cls, wallet_deduct_amount):
        if wallet_deduct_amount:
            return "{:.2f}".format(float(wallet_deduct_amount))
        return "{:.2f}".format(float(0.00))

    @validator('non_wallet_deduct_amount')
    def validate_non_wallet_amount(cls, non_wallet_deduct_amount):
        if non_wallet_deduct_amount:
            return "{:.2f}".format(float(non_wallet_deduct_amount))
        return "{:.2f}".format(float(0.00))


class PaymentRequestWithStatus(PaymentRequest):
    status: PaymentRequestStatus


class PaymentRequestRedeemPackage(BaseModel):
    type: PaymentRequestType
    amount: str
    currency: Currency = Currency.myr
    billing_description: str
    reason: PaymentRequestReason
    update_token: bool = False
    save_credit_card: bool = False
    status: PaymentRequestStatus
    meta: dict = {}
    promo_usage_id: Optional[UUID]
    connector_id: Optional[str]
    charging_session_bill_id: Optional[str]
    invoice_number: Optional[str]

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))


class PaymentRequestUpdate(BaseModel):
    type: PaymentRequestType
    amount: str
    billing_description: str
    charging_session_bill_id: Optional[str]
    invoice_number: Optional[str]

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))


class PaymentRequestOutstandingUpdate(BaseModel):
    pre_auth_outstanding_amount: Optional[str]
    pre_auth_outstanding_capture_status: Optional[str]
    pre_auth_outstanding_order_id: Optional[str]
    pay_via_preauth_and_recurring: Optional[bool]

    @validator('pre_auth_outstanding_amount')
    def validate_amount(cls, pre_auth_outstanding_amount):
        return "{:.2f}".format(float(pre_auth_outstanding_amount))


class PaymentRequestUpdateChargePointID(BaseModel):
    connector_id: str


class PaymentRequestUpdateInvoice(BaseModel):
    invoice_number: Optional[str]


class PaymentRequestUpdateType(BaseModel):
    type: PaymentRequestType


class PaymentRequestUpdatePreAuthToRecurring(BaseModel):
    preauth_bill_via_recurring: bool


class PaymentRequestResponse(PaymentRequest):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    status: PaymentRequestStatus

    class Config:
        orm_mode = True


class PaymentRequestStatusUpdate(BaseModel):
    status: PaymentRequestStatus

    class Config:
        orm_mode = True


class PaymentRequestDeposit(BaseModel):
    type: PaymentRequestType
    amount: str
    currency: Currency = Currency.myr
    billing_description: str
    update_token: bool = False
    charge_point_id: Optional[str]
    connector_id: Optional[str]

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))


class PaymentRequestTokenCheckPreAuth(BaseModel):
    type: PaymentRequestType = PaymentRequestType.direct
    reason: PaymentRequestReason = PaymentRequestReason.token_check
    amount: str
    currency: Currency = Currency.myr
    billing_description: str
    update_token: bool = False
    charge_point_id: Optional[str]
    connector_id: Optional[str]
    payment_gateway: Optional[str]

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))


class PaymentRequestPreAuth(BaseModel):
    type: PaymentRequestType = PaymentRequestType.direct
    reason: PaymentRequestReason = PaymentRequestReason.pre_auth
    amount: str
    currency: Currency = Currency.myr
    billing_description: str
    update_token: bool = False
    charge_point_id: Optional[str]
    connector_id: Optional[str]
    payment_gateway: Optional[str]

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))


class TokenCheckPreAuthPaymentRefundCreate(BaseModel):
    transaction_id: Optional[str]
    token_check_pre_auth_payment_id: UUID


class PreAuthPaymentRefundCreate(BaseModel):
    transaction_id: Optional[str]
    pre_auth_payment_id: UUID


class TokenCheckPreAuthPaymentRefundUpdateAfterResponse(BaseModel):
    response: Optional[dict]
    domain: Optional[str]
    stat_code: Optional[str]


class PreAuthPaymentRefundUpdateAfterResponse(BaseModel):
    response: Optional[dict]
    domain: Optional[str]
    stat_code: Optional[str]


class PaymentRequestCreditCard(BaseModel):
    type: PaymentRequestType = PaymentRequestType.direct
    amount: str = '1.01'
    currency: Currency = Currency.myr
    billing_description: str = 'Subscription Fee'
    reason: PaymentRequestReason = PaymentRequestReason.payment
    update_token: bool = True
    save_credit_card: bool = True
    meta: dict = {}


class PaymentRequestPreCharging(BaseModel):
    promo_code: Optional[str]
    charge_point_id: Optional[str]
    connector_id: Optional[str]


class RecurringPayment(BaseModel):
    record_type: RecordType = RecordType.token
    sub_merchant: Optional[str]
    token: str
    pan: Optional[str]
    expired_date: Optional[str]
    order_id: str
    currency: Currency = Currency.myr
    amount: str
    bill_name: str
    bill_email: Optional[str]
    bill_mobile: Optional[str]
    bill_desc: Optional[str]
    response: dict
    payment_request_id: str

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))


class PreAuthRequest(BaseModel):
    use_3ds: Optional[bool] = False
    is_ocpi: bool
    use_wallet: bool = False
    # pre_auth_amount: Optional[float]


class StartPreAuthRequest(BaseModel):
    use_wallet: Optional[bool]
    promo_code: Optional[str]


class TokenCheckPreAuthPayment(BaseModel):
    transaction_type: Optional[str]
    transaction_channel: Optional[str]
    reference_id: Optional[str]
    expired_date: Optional[str]
    token: str
    currency: Currency = Currency.myr
    amount: str

    bill_name: str
    bill_email: Optional[str]
    bill_mobile: Optional[str]
    bill_desc: Optional[str]
    payment_request_id: str
    is_3ds: bool = False
    is_successful: Optional[bool]

    payment_type: Optional[str]
    payment_gateway: Optional[str]

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))


class PreAuthPayment(BaseModel):
    transaction_type: Optional[str]
    transaction_channel: Optional[str]
    reference_id: Optional[str]
    expired_date: Optional[str]
    token: str
    currency: Currency = Currency.myr
    amount: str

    bill_name: str
    bill_email: Optional[str]
    bill_mobile: Optional[str]
    bill_desc: Optional[str]
    payment_request_id: str
    is_3ds: bool = False
    is_successful: Optional[bool]

    payment_type: Optional[str]
    payment_gateway: Optional[str]

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))


class TokenCheckPreAuthPaymentUpdate(BaseModel):
    is_binded: Optional[bool]
    is_refunded: Optional[bool]
    failed_refund: Optional[bool]
    is_used: Optional[bool]
    used_amount: Optional[str]


class PreAuthPaymentUpdate(BaseModel):
    is_binded: Optional[bool]
    is_refunded: Optional[bool]
    failed_refund: Optional[bool]
    is_used: Optional[bool]
    used_amount: Optional[str]


class PreAuthPaymentResponse(BaseModel):
    response: Optional[dict]
    is_successful: Optional[bool]
    is_binded: Optional[bool]
    charging_session_id: Optional[str]
    transaction_id: Optional[str]
    stat_code: Optional[str]
    error_description: Optional[str]
    callback_response: Optional[dict]


class TokenCheckPreAuthPaymentResponse(BaseModel):
    response: Optional[dict]
    is_successful: Optional[bool]
    is_binded: Optional[bool]
    charging_session_id: Optional[str]
    transaction_id: Optional[str]
    stat_code: Optional[str]
    error_description: Optional[str]
    callback_response: Optional[dict]


class PreAuthPaymentResponseToMobile(BaseModel):
    id: UUID
    payment_request_id: UUID
    reference_id: str
    created_at: datetime
    updated_at: Optional[datetime]
    currency: Currency
    is_3ds: bool
    expired_date: Optional[datetime]
    is_successful: Optional[bool]
    is_binded: Optional[bool]
    is_refunded: Optional[bool]
    used_amount: Optional[str]
    amount: str
    action_url: Optional[str]
    action_method: Optional[str]
    action_data: Optional[list]
    charging_session_id: Optional[UUID]
    payment_gateway: Optional[str]

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))

    @validator('used_amount')
    def validate_used_amount(cls, used_amount):
        if used_amount:
            return "{:.2f}".format(float(used_amount))
        return None

    class Config:
        orm_mode = True
        allow_population_by_field_name = True


class PreAuthPaymentCapture(BaseModel):
    amount: str
    currency: str
    transaction_id: str
    reference_id: str

    pre_auth_payment_id: UUID
    payment_request_id: UUID
    response: Optional[dict]
    response_content: Optional[str]
    domain: Optional[str]

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))


class PreAuthPaymentCaptureResponse(BaseModel):
    response: Optional[dict]
    domain: Optional[str]


class RecurringPaymentResponse(BaseModel):
    status: str
    order_id: Optional[str] = Field(alias='orderid')
    tran_id: Optional[str] = Field(alias='tranId')
    reason: Optional[str]

    class Config:
        orm_mode = True
        allow_population_by_field_name = True


class PaymentCallback(BaseModel):
    nbcb: Optional[int]
    amount: str
    order_id: str = Field(alias='orderid')
    tran_id: str = Field(alias='tranID')
    domain: str
    status: PaymentStatus
    appcode: str
    error_code: str
    error_desc: str
    skey: Optional[str]
    currency: Optional[Currency]
    paydate: datetime
    channel: Optional[str]
    extra_p: Json = Field(alias='extraP')

    class Config:
        orm_mode = True


class PaymentCallbackViaPost(BaseModel):
    nbcb: Optional[int]
    amount: str
    order_id: str = Field(alias='order_id')
    tran_id: str = Field(alias='tran_id')
    domain: str
    status: PaymentStatus = Field(alias='stat_code')
    appcode: Optional[str]
    error_code: Optional[str]
    error_desc: Optional[str]
    skey: Optional[str]
    currency: Optional[Currency]
    paydate: datetime = Field(alias='billing_date')
    channel: Optional[str]

    extra_p: Optional[Json]

    class Config:
        orm_mode = True


class PaymentReturnURL(BaseModel):
    amount: str
    order_id: str = Field(alias='orderid')
    tran_id: str = Field(alias='tranID')
    domain: str
    status: PaymentStatus
    appcode: str
    error_code: Optional[str]
    error_desc: Optional[str]
    skey: Optional[str]
    currency: Optional[Currency]
    paydate: datetime
    channel: str
    extra_p: Optional[Json] = Field(alias='extraP')

    @validator('currency', pre=True)
    def validate_currency(cls, value):
        if value is not None:
            return value.upper()
        return value

    class Config:
        orm_mode = True


class PaymentPreAuthReturnURL(BaseModel):
    amount: str
    order_id: str = Field(alias='orderid')
    tran_id: str = Field(alias='tranID')
    domain: str
    status: PaymentStatus
    appcode: str
    error_code: Optional[str]
    error_desc: Optional[str]
    skey: Optional[str]
    currency: Optional[Currency]
    paydate: datetime
    channel: str
    extra_p: Optional[Json] = Field(alias='extraP')  # Accept JSON string, manually handle it

    @validator('currency', pre=True)
    def validate_currency(cls, value):
        if value is not None:
            return value.upper()
        return value

    class Config:
        orm_mode = True

    @validator('extra_p', pre=True)
    def parse_json(cls, v):
        if v is None:
            return v
        try:
            # Attempt to decode and parse the JSON string (incase if its encoded)
            return json.dumps(v)
        except (json.JSONDecodeError, TypeError):
            raise ValueError("extra_p must be a valid json")


class ChargingSessionBillStatus(str, Enum):
    pending = 'Pending'
    failed = 'Failed'
    paid = 'Paid'
    free = 'Free'
    rejected = 'Rejected'
    void = 'Void'


class BillingType(str, Enum):
    kwh = 'kWh'
    time = 'Time'


class ChargingSessionBill(BaseModel):
    charging_session_id: str
    charging_session_type: Optional[str]
    charge_point_id: Optional[str]
    partner_id: Optional[str]
    cpo_id: Optional[str]
    id_tag: Optional[str]
    usage_amount: float
    usage_type: BillingType
    discount: Optional[dict]
    meta: Optional[dict]
    invoice_number: Optional[str]
    tax_amount: Optional[float]
    tax_rate: Optional[float]
    total_amount: Optional[float]
    reference_number: Optional[str]
    hogging_fee: Optional[float]
    outstanding_balance: Optional[float] = 0.00
    preferred_payment_flow: Optional[str]
    preferred_payment_method: Optional[str]
    is_low_wallet_balance: Optional[bool]

    class Config:
        orm_mode = True


class ChargingSessionBillUpdate(BaseModel):
    status: ChargingSessionBillStatus


class ChargingSessionBillUpdateEInvoiceStatus(BaseModel):
    e_invoice_issued: Optional[bool]


class ChargingSessionBillResponse(ChargingSessionBill):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    status: ChargingSessionBillStatus

    class Config:
        orm_mode = True


class ChargingSession(BaseModel):
    id: UUID
    session_start: datetime
    session_end: Optional[datetime]
    duration: timedelta
    charging_usage: float
    id_tag: Optional[str]
    hogging_start: Optional[datetime]
    hogging_end: Optional[datetime]
    hogging_duration: Optional[timedelta]
    hogging_tariff: Optional[list]


class Connector(BaseModel):
    billing_type: BillingType
    billing_unit_fee: float
    billing_unit_fee_after_vat: Optional[float]
    billing_unit_fee_after_discount: Optional[float]
    billing_unit_fee_after_discount_after_vat: Optional[float]
    vat_rate: Optional[float]
    billing_cycle: int = 1
    billing_currency: str
    connector_label: Optional[str]
    connection_fee: Optional[float]
    connector_type: Optional[str]
    connector_kind: Optional[str]
    hogging_tariff: Optional[list]
    hogging_fee: Optional[float]
    connector_number: Optional[str]

    # Meant for invoice subscription info
    subscription_plan: Optional[dict]
    subscription_custom_plan: Optional[dict]

    # Whole CampaignPromoCodeUsage object from BillingInfo
    campaign_promo_code_usage: Optional[dict]


class PaymentRefundResponse(BaseModel):
    id: Optional[UUID]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
    payment_request_id: Optional[UUID]
    refund_amount: Optional[float]
    refund_type: Optional[RefundType]
    refund_status: Optional[str]
    remark: Optional[str]
    reference_id: Optional[str]

    # pg_refund_response: Optional[dict]
    # pg_refund_id: Optional[str]

    class Config:
        orm_mode = True


class Invoice(BaseModel):
    invoice_number: str
    reference_number: Optional[str]
    date: datetime
    organization: Optional[OrganizationSimpleResponse]
    user: Optional[User]
    member: Optional[ShallowMembershipResponse]
    charge_point: ChargePoint
    charging_session: ChargingSession
    connector: Connector
    amount: float
    discount: float
    subscription_discount: float
    campaign_promo_code_discount: Optional[float]
    campaign_promo_code_usage: Optional[dict]
    logo: Optional[bytes]
    status: ChargingSessionBillStatus
    logo_format_type: Optional[str]
    tax_rate: Optional[float]
    tax_amount: Optional[float]
    total_amount: Optional[float]
    payment_type: Optional[str]
    currency: Optional[str]
    hogging_fee: Optional[float]
    hogging_amount: Optional[float]
    wallet_deduct_amount: Optional[float]
    non_wallet_deduct_amount: Optional[float]

    BRANDING_NAME: Optional[str]
    BRANDING_URL: Optional[str]
    BRANDING_LOGO_URL: Optional[str]
    BRANDING_SUPPORT_EMAIL: Optional[str]
    BRANDING_SUPPORT_PHONE_NUMBER: Optional[str]
    BRANDING_ADDRESS: Optional[str]
    BRANDING_TAX_REG_NUMBER: Optional[str]

    display_connector_label: Optional[bool] = False
    total_wallet_refunded_amount: Optional[float]
    total_non_wallet_refunded_amount: Optional[float]
    payment_refund: Optional[list[PaymentRefundResponse]] = None
    refund_status: Optional[str]

    e_invoice: Optional[dict]


class InvoiceResponse(BaseModel):
    invoice_number: str
    reference_number: Optional[str]
    date: datetime
    organization: Optional[OrganizationSimpleResponse]
    user: Optional[User]
    member: Optional[ShallowMembershipResponse]
    charge_point: ChargePoint
    charging_session: ChargingSession
    connector: Connector
    amount: float
    discount: float
    subscription_discount: float
    campaign_promo_code_discount: Optional[float]
    campaign_promo_code_usage: Optional[dict]
    status: ChargingSessionBillStatus
    payment_type: Optional[str]
    tax_rate: Optional[float]
    tax_amount: Optional[float]
    total_amount: Optional[float]
    currency: Optional[str]
    hogging_fee: Optional[float]

    wallet_deduct_amount: Optional[float]
    non_wallet_deduct_amount: Optional[float]
    total_wallet_refunded_amount: Optional[float]
    total_non_wallet_refunded_amount: Optional[float]
    payment_refund: Optional[list[PaymentRefundResponse]] = None
    refund_status: Optional[str]


class ChargingSessionReportResponse(BaseModel):
    unique_users: int
    number_of_transactions: int
    avg_energy_consumption: float
    total_energy_consumed: float
    total_payments: float
    avg_operation_duration: float
    total_duration: float
    total_greenhouse_gas_saved: float
    total_charged_mileage: float


class TotalTransactionResponse(BaseModel):
    date: datetime
    total: int


class TotalTransactionType(str, Enum):
    days = 'days'
    weeks = 'weeks'
    months = 'months'
    years = 'years'


class DashboardType(str, Enum):
    Day = 'Day'
    Week = 'Week'
    Month = 'Month'
    Year = 'Year'


class DashboardEnergyResponse(BaseModel):
    date: datetime
    below_5kwh: int
    between_5kwh_to_9_9kwh: int
    between_10kwh_to_29_9kwh: int
    between_30kwh_to_39_9kwh: int
    between_40kwh_to_49_9kwh: int


class ChargePointConnectorUtilization(BaseModel):
    utilized_rate: float
    idle_rate: float
    offline_rate: float


class ChargerUtilizationFilterType(str, Enum):
    days = 'days'
    weeks = 'weeks'
    months = 'months'
    years = 'years'


class PartnerPaymentStatus(str, Enum):
    success = 'Success'
    failed = 'Failed'


class OCPICPOCdrCreate(BaseModel):
    country_code: str
    party_id: str

    partner_ocpi_cpo_cdr_id: str

    start_date_time: datetime
    end_date_time: datetime

    session_id: Optional[str]
    cdr_token: dict
    auth_method: str

    authorization_reference: Optional[str]
    cdr_location: dict
    meter_id: Optional[str]
    currency: str
    tariffs: list
    charging_periods: list
    signed_data: Optional[str]
    total_cost: dict
    total_fixed_cost: Optional[dict]
    total_energy: str
    total_energy_cost: Optional[dict]
    total_time: str
    total_time_cost: Optional[dict]
    total_parking_time: Optional[str]
    total_parking_cost: Optional[dict]
    total_reservation_cost: Optional[str]

    remark: Optional[str]
    invoice_reference_id: Optional[str]
    credit: Optional[bool] = False
    credit_reference_id: Optional[str]
    home_charging_compensation: Optional[bool] = False

    class Config:
        orm_mode = True


class OCPICPOCdrUpdate(BaseModel):
    start_date_time: Optional[datetime]
    end_date_time: Optional[datetime]

    session_id: Optional[str]
    cdr_token: Optional[dict]
    auth_method: Optional[str]

    authorization_reference: Optional[str]
    cdr_location: Optional[dict]
    meter_id: Optional[str]
    currency: Optional[str]
    tariffs: Optional[list]
    charging_periods: Optional[list]
    signed_data: Optional[str]
    total_cost: Optional[dict]
    total_fixed_cost: Optional[dict]
    total_energy: Optional[str]
    total_energy_cost: Optional[dict]
    total_time: Optional[str]
    total_time_cost: Optional[dict]
    total_parking_time: Optional[str]
    total_parking_cost: Optional[dict]
    total_reservation_cost: Optional[str]

    remark: Optional[str]
    invoice_reference_id: Optional[str]
    credit: Optional[bool]
    credit_reference_id: Optional[str]
    home_charging_compensation: Optional[bool]
    transaction_id: Optional[int]
    native_charging_session_id: Optional[str]
    operator_id: Optional[str]

    class Config:
        orm_mode = True


class OCPICPOCdrResponse(OCPICPOCdrCreate):
    id: Optional[UUID]
    updated_at: Optional[datetime]
    created_at: datetime
    duration: Optional[str]
    partner_name: Optional[str]
    charging_session_id: Optional[str]
    transaction_id: Optional[int]
    native_charging_session_id: Optional[str]
    operator_id: Optional[str]


class PartnerPaymentNotification(BaseModel):
    status: PartnerPaymentStatus
    message: str


class PartnerPaymentNotificationCreate(BaseModel):
    partner_id: UUID
    charging_session_bill_id: UUID
    status: PartnerPaymentStatus
    message: str
    charging_session_id: str


class PaymentGatewayReconcilation(BaseModel):
    billing_date: datetime
    order_id: str
    tran_id: str
    channel: str
    amount: Optional[str]
    stat_code: Optional[str]
    stat_name: Optional[str]
    billing_name: Optional[str]
    service_item: Optional[str]
    bin_number: Optional[str]
    currency: Optional[str]
    billing_email: Optional[str]
    transaction_rate: Optional[str]
    gst: Optional[str]
    net_amount: Optional[float]
    bank_name: Optional[str]
    settlement_date: Optional[datetime]
    payment_request_id: Optional[UUID]
    member_id: Optional[UUID]
    transaction_cost: Optional[str]
    billing_mobile_number: Optional[str]
    transaction_fee: Optional[str]
    expiry_date: Optional[datetime]
    status_description: Optional[str]
    paid_date: Optional[datetime]
    capture_ref_id: Optional[str]
    refund_ref_id: Optional[str]
    charging_session_bill_id: Optional[UUID]


class PaymentRequestSimple(BaseModel):
    id: UUID
    type: str
    amount: str
    currency: str
    reason: str
    status: str
    member_id: Optional[UUID]
    promo_usage_id: Optional[UUID]

    class Config:
        orm_mode = True


class InvoiceSimple(BaseModel):
    id: UUID
    charging_session_id: Optional[str]
    invoice_number: Optional[str]
    tax_amount: Optional[float]
    tax_rate: Optional[float]
    charge_point_id: Optional[str]
    discount: Optional[dict]

    class Config:
        orm_mode = True


class PaymentGatewayReconcilationResponse(BaseModel):
    billing_date: datetime
    order_id: str
    tran_id: str
    channel: str
    amount: str
    stat_code: Optional[str]
    stat_name: Optional[str]
    billing_name: Optional[str]
    service_item: Optional[str]
    bin_number: Optional[str]
    currency: Optional[str]
    billing_email: Optional[str]
    transaction_rate: Optional[str]
    gst: Optional[str]
    net_amount: Optional[str]
    bank_name: Optional[str]
    settlement_date: Optional[datetime]
    payment_request: Optional[PaymentRequestSimple]
    member: Optional[ShallowMembershipResponse]
    transaction_cost: Optional[str]
    billing_mobile_number: Optional[str]
    transaction_fee: Optional[str]
    expiry_date: Optional[datetime]
    status_description: Optional[str]
    paid_date: Optional[datetime]
    capture_ref_id: Optional[str]
    refund_ref_id: Optional[str]
    charging_session_bill: Optional[InvoiceSimple]
    discounted_price: Optional[float] = None

    class Config:
        orm_mode = True


class CyberSourceCreateToken(BaseModel):
    id: UUID
    reference_no: str
    cc_number: str


class CyberSourceTokenResponse(BaseModel):
    member_id: UUID
    user_id: UUID
    customer_token: str
    instrument_identifier_token: str


class CyberSourceBillingInfo(BaseModel):
    first_name: str
    last_name: str
    company: str
    address: str
    locality: str
    administrative_area: str
    postal_code: str
    country: str
    email: str
    phone_number: str


class CyberSourceCreditCardInfo(BaseModel):
    expired_month: str
    expired_year: str
    type: str


class CyberSourceCreateDefaultPaymentCard(BaseModel):
    customer_token: str
    instrument_identifier_token: str
    cc_info: CyberSourceCreditCardInfo
    billing_info: CyberSourceBillingInfo


class CyberSourcePreAuth(BaseModel):
    customer_token: str
    payment_instrument_token: str
    amount: float
    currency: Currency
    auth_indicator: int

    @validator('currency', pre=True)
    def validate_currency(cls, value):
        if value is not None:
            return value.upper()
        return value


class CyberSourceTokenType(str, Enum):
    transient_token = 'TRANSIENT_TOKEN'  # nosec
    tms_token = 'TMS_TOKEN'  # nosec


class HoggingCallback(BaseModel):
    member_id: Optional[str]
    ocpp_status: str
    hogging_status: str
    grace_period_seconds: Optional[int]


class CommercialInvoiceType(str, Enum):
    subscription = 'Subscrption Order'
    renewal = 'Subscrption Renewal'


class CommercialInvoice(BaseModel):
    type: CommercialInvoiceType
    amount: str
    currency: Currency = Currency.myr
    subscription_order_id: str
    invoice_number: Optional[str]

    @validator('amount')
    def validate_amount(cls, amount):
        return "{:.2f}".format(float(amount))


class CommercialInvoiceUpdateNumber(BaseModel):
    invoice_number: Optional[str]


class PaymentRefund(BaseModel):
    refund_amount: float
    refund_type: RefundType
    remark: Optional[str]


class CreatePaymentRefund(BaseModel):
    refund_amount: float
    refund_type: RefundType
    payment_request_id: UUID
    refund_status: str = RefundStatus.pending
    remark: Optional[str]
    pg_refund_response: Optional[dict] = None
    pg_refund_id: Optional[str]
    reference_id: Optional[str]
    pg_transaction_id: Optional[str]


class UpdatePaymentRefund(BaseModel):
    refund_status: Optional[str]
    pg_refund_response: Optional[dict]
    pg_refund_callback: Optional[dict]
    e_credit_note_issued: Optional[bool]


class OutlierReason(str, Enum):
    duration = 'Exceed Duration'
    kwh = 'Exceed kWh'
    amount = 'Exceed Amount'
    anomaly_kwh = 'Anomaly kWh reading'


class OutlierStatus(str, Enum):
    pending = 'Pending'
    approved = 'Approved'
    declined = 'Declined'
    voided = 'Voided'


class OutlierSession(BaseModel):
    charging_session_id: str
    charging_session_type: str
    transaction_id: Optional[str]
    id_tag: Optional[str]
    billing_currency: Currency
    billing_amount: float
    billing_kwh: float
    charging_duration: float
    reasons: Optional[list[OutlierReason]] = []
    status: Optional[OutlierStatus] = 'Pending'
    remarks: Optional[str]

    class Config:
        orm_mode = True


class OutlierSessionCSMSResponse(OutlierSession):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        orm_mode = True


class OutlierSessionUpdate(BaseModel):
    status: OutlierStatus
    remarks: Optional[str]

    class Config:
        orm_mode = True


class HoggingNotification(BaseModel):
    member_id: Optional[str]
    hogging_status: str
    hogging_tariff: dict
