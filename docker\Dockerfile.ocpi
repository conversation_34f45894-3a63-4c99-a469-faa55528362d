FROM tiangolo/uvicorn-gunicorn-fastapi:python3.9-slim

RUN pip3 install pipenv==2024.2.0

# Install dependencies required for building, but remove them after
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    gnupg \
    ca-certificates \
    procps \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
# Add a new non-root user 
RUN useradd -u 1000 apollo


# Set the ownership of the working directory to the non-root user
RUN chown -R apollo:apollo /app
RUN chown -R apollo:apollo /home



# -- Adding Pipfiles
COPY ./src/Pipfile Pipfile
COPY ./src/Pipfile.lock Pipfile.lock

# -- Install dependencies:
RUN set -ex && pipenv install --system --dev --ignore-pipfile

# Copy the custom Gunicorn configuration
COPY ./src/custom_gunicorn_conf.py /gunicorn_conf.py
COPY ./src/custom_start.sh /start.sh

RUN chmod +x /start.sh
COPY ./src /app

# Clean up unnecessary files and dependencies
RUN apt-get remove -y --auto-remove \
    build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* || true

    
# Change to non-root privilege
USER apollo

ENV MODULE_NAME=app.ocpi