import secrets
from contextlib import contextmanager
from datetime import datetime, timedelta
import uuid
import json

import jwt
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock, Mock, ANY
from app.tests.mocks.async_client import MockResponse

from app import settings, schema
from app.database import SessionLocal, create_session, Base, engine
from app.main import app, ROOT_PATH
from app.schema import MembershipType
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory, CreditCardFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory,
                                 OrganizationAuthenticationServiceFactory, WalletFactory,
                                 PaymentRequestFactory, MembershipExtendedFactory, ChargingSessionBillFactory)

client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        WalletFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PaymentRequestFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipExtendedFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ChargingSessionBillFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session

INVOICE_BASE_URL = f'{ROOT_PATH}/api/v1/invoice'

CHARGE_POINT_ID = str(uuid.uuid4())
CHARGING_SESSION_ID = str(uuid.uuid4())
OPERATOR_ID = str(uuid.uuid4())

LOCATION = {
    'id': 'b27b2389-a02d-4325-98a3-faaec01b0bc1',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'name': 'name',
    'latitude': 1.1,
    'longitude': 2.2,
    'address': 'address',
    'city': 'city',
    'state': 'state',
    'country': 'country',
}
CHARGE_POINT = {
    'id': CHARGE_POINT_ID,
    'serial_number': 'test_cp',
    'status': 'Available',
    'is_private': False,
    'is_connected': True,
    'operator_id': OPERATOR_ID,
    'location': LOCATION
}
CONNECTOR_TYPE = {
    'id': 'b27b2389-a02d-4325-98a3-faaec01b0bc1',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'name': 'test_connector_type',
    'kind': 'test_connector_type_name',
}
CONNECTOR = {
    'id': '912388d7-c48b-4de8-a826-c398bdae049b',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'number': 1,
    'info': {},
    'billing_type': schema.BillingType.time,
    'billing_unit_fee': 1.0,
    'billing_cycle': 1,
    'billing_currency': 'MYR',
    'connection_fee': 0,
    'status': 'Available',
    'ocpp_status': 'Available',
    'charge_point': CHARGE_POINT,
    'connector_type': CONNECTOR_TYPE,
    'connector_label': 'test_label'
}
CHARGING_SESSION = {
    'id': CHARGING_SESSION_ID,
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'status':  'Done',
    'meter_start': 1100,
    'meter_stop': 1120,
    'meter_values': [],
    'session_start': '2022-01-01 00:00:00',
    'session_end': '2022-01-01 00:30:00',
    'info': {},
    'id_tag': 'test_id_tag',
    'transaction_id': 10000,
    'reservation_id': None,
    'charge_point_connector': CONNECTOR,
    'ocpi_evse_connector': None,
    'meta': {
        "charger_serial_number": CHARGE_POINT['serial_number'],
        "connector_number": CONNECTOR['number'],
        "location": LOCATION,
        "id_tag": "test_id_tag",
        "billing_info": {
            "billing_type": CONNECTOR['billing_type'],
            "billing_unit_fee": CONNECTOR['billing_unit_fee'],
            "billing_cycle": CONNECTOR['billing_cycle'],
            "billing_currency": CONNECTOR['billing_currency'],
            "connection_fee": CONNECTOR['connection_fee'],
        },
        "operator": {'id': OPERATOR_ID}
    }
}


class MockAsyncClientChargingSession:
    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        pass

    def build_request(method, *args, **kwargs):
        return method

    async def send(request):
        if request == 'GET':
            return MockResponse(CHARGING_SESSION, 200)

    async def get(url, *args, **kwargs):
        return MockResponse(CHARGING_SESSION, 200)


class MockAsyncClientChargingSessionNoResult:
    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        pass

    def build_request(method, *args, **kwargs):
        return method

    async def send(request):
        if request == 'GET':
            return MockResponse({}, 404)

    async def get(url, *args, **kwargs):
        return MockResponse({}, 404)


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientChargingSession)
def test_get_invoice(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid,
            meta={
                "organization_name": str(organization.name),
                "transaction_id": 10000,
            }
        )
        db.commit()

        PaymentRequestFactory(
            charging_session_bill_id=str(charging_session_bill.id),
            amount='10.50',
            type=schema.PaymentRequestType.direct,
            billing_description='description',
            reason=schema.PaymentRequestReason.payment,
            member_id=mem.id,
            status=schema.PaymentRequestStatus.done,
            currency=schema.Currency.myr,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json())
        )

        create_res_server_and_roles(db, mem, organization_id, fr'{INVOICE_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            'exp': datetime.now() + timedelta(days=1),
            'user_id': str(user.id),
            'membership_id': str(mem.id)
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        url = f'{INVOICE_BASE_URL}/{CHARGING_SESSION_ID}'
        response = client.get(url, headers=headers)
        assert response.status_code == 200
        assert response.json()['success']
        assert response.json()['data']['invoice_number'] is not None
        assert response.json()['data']['amount'] == 10.50
        assert response.json()['data']['total_amount'] == 5.5
        assert response.json()['data']['charging_session']['id'] == CHARGING_SESSION_ID


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientChargingSession)
def test_get_invoice_cs_bill_not_found(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, fr'{INVOICE_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            'exp': datetime.now() + timedelta(days=1),
            'user_id': str(user.id),
            'membership_id': str(mem.id)
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        url = f'{INVOICE_BASE_URL}/{CHARGING_SESSION_ID}'
        response = client.get(url, headers=headers)
        assert response.status_code == 200
        assert response.json()['success'] is False
        assert response.json()['error']['message'][0] == 'ChargingSessionBill object does not exist.'


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientChargingSessionNoResult)
def test_get_invoice_mock_not_found(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid,
            meta={
                "organization_name": str(organization.name),
                "transaction_id": 10000,
            }
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, fr'{INVOICE_BASE_URL}/',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            'exp': datetime.now() + timedelta(days=1),
            'user_id': str(user.id),
            'membership_id': str(mem.id)
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        url = f'{INVOICE_BASE_URL}/{CHARGING_SESSION_ID}'
        response = client.get(url, headers=headers)
        assert response.status_code == 200
        assert response.json()['success'] is False
        assert response.json()['error']['message'][0] == 'ChargingSession object does not exist.'


def test_get_charging_histories(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, fr'{INVOICE_BASE_URL}/history/charge_histories',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            'exp': datetime.now() + timedelta(days=1),
            'user_id': str(user.id),
            'membership_id': str(mem.id)
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        url = f'{INVOICE_BASE_URL}/history/charge_histories'

        with patch('app.routers.v2.invoice.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"items": [CHARGING_SESSION]}
            mock_send.return_value = mock_response

            response = client.get(url, headers=headers)
            assert response.status_code == 200
            assert response.json()['success']
            assert response.json()['data']['items'][0]['id'] == CHARGING_SESSION_ID
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}/charging/charge_histories',
                headers=ANY,
                query_params={}
            )


def test_get_charging_histories_mock_not_found(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, fr'{INVOICE_BASE_URL}/history/charge_histories',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            'exp': datetime.now() + timedelta(days=1),
            'user_id': str(user.id),
            'membership_id': str(mem.id)
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        url = f'{INVOICE_BASE_URL}/history/charge_histories'

        with patch('app.routers.v2.invoice.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 404
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            response = client.get(url, headers=headers)
            assert response.status_code == 200
            assert response.json()['success'] is False
            assert response.json()['error']['message'][0] == 'No Record Found'
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}/charging/charge_histories',
                headers=ANY,
                query_params={}
            )


def test_ocpi_get_charging_histories(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, fr'{INVOICE_BASE_URL}/ocpi/history/charge_histories',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            'exp': datetime.now() + timedelta(days=1),
            'user_id': str(user.id),
            'membership_id': str(mem.id)
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        url = f'{INVOICE_BASE_URL}/ocpi/history/charge_histories?page=1&size=50'

        with patch('app.routers.v2.invoice.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"items": [CHARGING_SESSION]}
            mock_send.return_value = mock_response

            response = client.get(url, headers=headers)
            assert response.status_code == 200
            assert response.json()['success']
            assert response.json()['data']['items'][0]['id'] == CHARGING_SESSION_ID
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}/charging/ocpi/charge_histories',
                headers=ANY,
                query_params={'page': '1', 'size': '50'}
            )


def test_ocpi_get_charging_histories_mock_not_found(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, fr'{INVOICE_BASE_URL}/ocpi/history/charge_histories',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            'exp': datetime.now() + timedelta(days=1),
            'user_id': str(user.id),
            'membership_id': str(mem.id)
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        url = f'{INVOICE_BASE_URL}/ocpi/history/charge_histories?page=1&size=50'

        with patch('app.routers.v2.invoice.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 404
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            response = client.get(url, headers=headers)
            assert response.status_code == 200
            assert response.json()['success'] is False
            assert response.json()['error']['message'][0] == 'No Record Found'
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}/charging/ocpi/charge_histories',
                headers=ANY,
                query_params={'page': '1', 'size': '50'}
            )


def test_get_invoice_pdf(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=str(user.id),
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        create_res_server_and_roles(db, mem, organization_id, fr'{INVOICE_BASE_URL}/ocpi/pdf/url/',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            'exp': datetime.now() + timedelta(days=1),
            'user_id': str(user.id),
            'membership_id': str(mem.id)
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}
        url = f'{INVOICE_BASE_URL}/ocpi/pdf/url/{CHARGING_SESSION_ID}'
        response = client.get(url, headers=headers)
        assert response.status_code == 200
        assert response.json()['success']
        base_url = f'{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}'
        expected_url_data = f'{base_url}/api/v1/invoice/ocpi/pdf/download/?'
        assert expected_url_data in response.json()['data']
