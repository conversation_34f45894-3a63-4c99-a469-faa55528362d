"""50

Revision ID: e82eed1cf089
Revises: 83f208699819
Create Date: 2023-08-17 18:56:16.517042

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e82eed1cf089'
down_revision = '83f208699819'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_partner_payment_notification',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('charging_session_id', sa.String(), nullable=True),
    sa.Column('charging_session_bill_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('message', sa.String(), nullable=True),
    sa.Column('partner_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['charging_session_bill_id'], ['main_charging_session_bill.id'], ),
    sa.ForeignKeyConstraint(['partner_id'], ['main_organization_authentication_services.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_partner_payment_notification')
    # ### end Alembic commands ###
