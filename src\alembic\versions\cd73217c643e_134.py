"""134

Revision ID: cd73217c643e
Revises: fda37e1cffc6
Create Date: 2024-11-14 21:00:03.199639

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'cd73217c643e'
down_revision = 'fda37e1cffc6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_tax_rate', sa.Column('enforce_tax_on_ocpi', sa.<PERSON>(), nullable=True))
    op.add_column('main_tax_rate', sa.Column('ocpi_additional_tax_rate', sa.Numeric(scale=2), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_tax_rate', 'ocpi_additional_tax_rate')
    op.drop_column('main_tax_rate', 'enforce_tax_on_ocpi')
    # ### end Alembic commands ###
