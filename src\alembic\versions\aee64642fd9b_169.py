"""169

Revision ID: aee64642fd9b
Revises: e7ac535b4fff
Create Date: 2025-05-27 16:22:25.209581

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'aee64642fd9b'
down_revision = 'e7ac535b4fff'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_ocpi_cpo_cdr', sa.Column('native_charging_session_id', sa.String(), nullable=True))
    op.add_column('main_ocpi_cpo_cdr', sa.Column('transaction_id', sa.String(), nullable=True))
    op.add_column('main_ocpi_cpo_cdr', sa.Column('operator_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_ocpi_cpo_cdr', 'operator_id')
    op.drop_column('main_ocpi_cpo_cdr', 'transaction_id')
    op.drop_column('main_ocpi_cpo_cdr', 'native_charging_session_id')
    # ### end Alembic commands ###
