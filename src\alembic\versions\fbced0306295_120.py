"""120

Revision ID: fbced0306295
Revises: 674717f762c4
Create Date: 2024-09-01 15:41:48.745752

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fbced0306295'
down_revision = '674717f762c4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_id_tag_auth_histories',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id_tag', sa.String(), nullable=True),
    sa.Column('result', sa.String(), nullable=True),
    sa.Column('result_reason', sa.String(), nullable=True),
    sa.Column('connector_id', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_id_tag_auth_histories')
    # ### end Alembic commands ###
