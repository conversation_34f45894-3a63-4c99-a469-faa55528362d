from typing import List

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, NoResultFound

from app import models, schema, exceptions, settings

from .base import BaseCRUD


def xstr(s):
    return '' if s is None else str(s)


def get_billing_info_given_member_info(db_member):
    if db_member.first_name is None and db_member.last_name is None:
        bill_name = ''
    else:
        bill_name = f'{db_member.first_name} {db_member.last_name}'
    bill_email = xstr(db_member.user.email)
    bill_mobile = xstr(db_member.user.phone_number)

    return {'bill_name': bill_name, 'bill_email': bill_email, "bill_mobile": bill_mobile}


def get_billing_info_given_cc_info(db_cc):
    bill_name = ''
    bill_mobile = ''
    bill_email = ''

    if db_cc.bill_name is not None:
        bill_name = db_cc.bill_name
    if db_cc.bill_mobile is not None:
        bill_mobile = db_cc.bill_mobile
    if db_cc.bill_email is not None:
        bill_email = db_cc.bill_email
    return {'bill_name': bill_name, 'bill_email': bill_email, "bill_mobile": bill_mobile}


def get_billing_info_given_user_info(db_member):
    bill_name = f'{db_member.first_name} {db_member.last_name}'
    bill_email = db_member.user.email
    bill_mobile = db_member.user.phone_number

    return {'bill_name': bill_name, 'bill_email': bill_email, "bill_mobile": bill_mobile}


class CreditCardCRUD(BaseCRUD):
    model = models.CreditCard

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        if not check_permission:
            return super().query(dbsession, *columns)
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession, *columns).join(models.Membership).filter(
                models.Membership.organization_id.in_(allowed_orgs)
            )
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     return super().query(dbsession, *columns).filter(cls.model.member_id == membership.id)
        if membership.membership_type == schema.MembershipType.regular_user:
            return super().query(dbsession, *columns).filter(cls.model.member_id == membership.id)
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        db_cc = dbsession.query(cls.model).get(object_id)
        if membership.user.is_superuser:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_cc.member.organization_id in allowed_orgs:
                return True
        # if (membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom) and
        #     db_cc.member.organization_id == membership.organization_id):  # noqa
        #     return True
        if membership.membership_type == schema.MembershipType.regular_user and (
                db_cc.member_id == membership.id
        ):
            return True
        raise exceptions.ApolloPermissionError()


class BlacklistCreditCardCRUD(BaseCRUD):
    model = models.BlacklistCreditCard

    @classmethod
    def query(cls, dbsession: Session, *columns, check_permission=True):
        if not check_permission:
            return super().query(dbsession, *columns)
        membership = cls.membership()
        if membership.user.is_superuser:
            return super().query(dbsession, *columns)
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return super().query(dbsession, *columns).join(models.Membership).filter(
                models.Membership.organization_id.in_(allowed_orgs)
            )
        # if membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom):
        #     return super().query(dbsession, *columns).filter(cls.model.member_id == membership.id)
        if membership.membership_type == schema.MembershipType.regular_user:
            return super().query(dbsession, *columns).filter(cls.model.member_id == membership.id)
        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        membership = cls.membership()
        db_cc = dbsession.query(cls.model).get(object_id)
        if membership.user.is_superuser:
            return True
        if membership.membership_type in (schema.MembershipType.staff, schema.MembershipType.sub_staff,
                                          schema.MembershipType.custom):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_cc.member.organization_id in allowed_orgs:
                return True
        # if (membership.membership_type in (schema.MembershipType.sub_staff, schema.MembershipType.custom) and
        #     db_cc.member.organization_id == membership.organization_id):  # noqa
        #     return True
        if membership.membership_type == schema.MembershipType.regular_user and (
                db_cc.member_id == membership.id
        ):
            return True
        raise exceptions.ApolloPermissionError()


def get_cc(db: Session, cc_id: str) -> models.CreditCard:
    query = CreditCardCRUD.query(db).filter(models.CreditCard.id == cc_id)
    try:
        return query.one()
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='CreditCard')


def get_member_cc_list(db: Session, member_id: str) -> List[models.CreditCard]:
    query = CreditCardCRUD.query(db).filter(models.CreditCard.member_id == member_id).order_by(
        models.CreditCard.primary)
    return query.all()


def get_cc_by_token(db: Session, token: str) -> models.CreditCard:
    query = CreditCardCRUD.query(db).filter(models.CreditCard.token == token)
    return query.first()


def get_cc_list(db: Session, filters: dict) -> List[models.CreditCard]:
    query = CreditCardCRUD.query(db)
    membership = CreditCardCRUD.membership()
    if filters['member_id']:
        query = query.filter(models.CreditCard.member_id == filters['member_id'])
    if filters['phone_number'] or filters['email']:
        if membership.user.is_superuser:
            query = query.join(models.Membership).join(models.User)
        else:
            query = query.join(models.User)
        if filters['phone_number']:
            query = query.filter(models.User.phone_number.ilike(f'%{filters["phone_number"]}%'))
        if filters['email']:
            query = query.filter(models.User.email.ilike(f'%{filters["email"]}%'))
    if filters['credit_card_number']:
        query = query.filter(models.CreditCard.last_four_digit.ilike(f'%{filters["credit_card_number"]}%'))
    if filters['primary'] is not None:
        query = query.filter(models.CreditCard.primary.is_(filters['primary']))
    if filters['currency'] is not None:
        query = query.filter(models.CreditCard.currency.ilike(f'%{filters["currency"]}%'))
    if 'created_at' in filters and filters['created_at']:
        query = apply_created_at_filter(query, filters)
    return query


def apply_created_at_filter(query, filters):
    created_at_filter = filters['created_at']
    if 'asc' in created_at_filter:
        query = query.order_by(models.CreditCard.created_at.asc())
    elif 'desc' in created_at_filter:
        query = query.order_by(models.CreditCard.created_at.desc())
    return query


def set_cc_as_primary(db: Session, member_id: str, cc_id: str) -> models.CreditCard:
    db_cc_list = get_member_cc_list(db, member_id)
    for rec_cc in db_cc_list:
        CreditCardCRUD.update(db, rec_cc.id, {'primary': False})
    db_cc = CreditCardCRUD.update(db, cc_id, {'primary': True})
    return db_cc


def create_cc(db: Session, cc_data: schema.CreditCard, member_id: str) -> models.CreditCard:
    try:
        db_cc = CreditCardCRUD.add(db, dict(**cc_data.dict(), member_id=member_id))
        return db_cc
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloCreditCardError()


def delete_cc(db: Session, cc_id: str):
    try:
        credit_card_update = {
            'token': None,
            'last_four_digit': None,
            'primary': False,
            'status': schema.CreditCardStatus.inactive
        }
        CreditCardCRUD.update(db, cc_id, credit_card_update, check_permission=False)
        CreditCardCRUD.delete(db, cc_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='CreditCard')


def get_member_primary_cc(db: Session, member_id: str, currency: schema.Currency = None) -> models.CreditCard:
    query = CreditCardCRUD.query(db).filter(
        models.CreditCard.primary.is_(True),
        models.CreditCard.status == schema.CreditCardStatus.active,
        models.CreditCard.member_id == member_id,
        models.CreditCard.is_deleted.is_(False)
    )
    if currency:
        query = query.filter(models.CreditCard.currency == currency)
    return query.first()


def get_member_cc_by_cc_info(db: Session, member_id: str, cc_info: dict,
                             currency: schema.Currency = None) -> models.CreditCard:
    query = CreditCardCRUD.query(db).filter(
        models.CreditCard.brand == cc_info['ccbrand'],
        models.CreditCard.last_four_digit == cc_info['cclast4'],
        models.CreditCard.type == cc_info['cctype'],
        models.CreditCard.member_id == member_id
    )
    if currency:
        query = query.filter(models.CreditCard.currency == currency)
    return query.first()


def check_member_cc_token_is_not_blacklisted(db: Session, member_id: str, cc_info: dict,
                                             currency: schema.Currency, db_user: models.User):
    if check_cc_is_not_blacklisted(db, member_id, cc_info, currency, db_user.phone_number, db_user.email):
        raise exceptions.ApolloBlacklistedCreditCard


def update_member_cc_token(db: Session, member_id: str, save_credit_card: bool, cc_info: dict,
                           currency: schema.Currency, db_user: models.User,
                           db_member: models.Membership) -> models.CreditCard:
    if check_cc_is_not_blacklisted(db, member_id, cc_info, currency, db_user.phone_number, db_user.email):
        raise exceptions.ApolloBlacklistedCreditCard

    db_cc = get_member_cc_by_cc_info(db, member_id, cc_info, currency)
    db_primary_cc = get_member_primary_cc(db, member_id, currency)
    primary = True
    if save_credit_card:
        if db_cc:
            if not db_primary_cc:
                primary = True

            db_cc.token = cc_info['token']
            db_cc.is_deleted = False
            db_cc.deleted_at = None
            db.commit()
            db.refresh(db_cc)
            db.primary = primary
            return db_cc

        if db_primary_cc:
            primary = False
        billing_info = get_billing_info_given_member_info(db_member)
        if billing_info['bill_email'] == '':
            billing_info['bill_email'] = '<EMAIL>'
        if billing_info['bill_mobile'] == '':
            billing_info['bill_mobile'] = '+60322893888'

        cc_data = schema.CreditCard(
            currency=currency,
            brand=cc_info['ccbrand'],
            last_four_digit=cc_info['cclast4'],
            # V3 issue
            type=cc_info['cctype'].capitalize(),
            token=cc_info['token'],
            primary=primary,
            bill_name=billing_info['bill_name'],
            bill_email=billing_info['bill_email'],
            bill_mobile=billing_info['bill_mobile'],
            payment_gateway=schema.CreditCardPaymentGateway.fiuu
        )
        db_cc = create_cc(db, cc_data, member_id)
        return db_cc
    raise exceptions.ApolloObjectDoesNotExist(model_name='CreditCard')


def check_cc_is_not_blacklisted(db: Session, member_id: str, cc_info: dict,
                                currency: schema.Currency, phone_number: str, email: str) -> bool:
    if settings.PAYMENT_GATEWAY_TYPE == "Fiuu":
        cc_last_4 = cc_info['cclast4']
    else:
        cc_last_4 = cc_info['_embedded']['instrumentIdentifier']['card']['number'][-4:]

    conditions = []

    query = BlacklistCreditCardCRUD.query(db)
    if settings.BLACKLIST_CHECK_MEMBER_ID_LAST_4:
        conditions.append(and_(models.BlacklistCreditCard.member_id == member_id,
                               models.BlacklistCreditCard.last_four_digit == cc_last_4))

    if settings.BLACKLIST_CHECK_PHONE_NUMBER_LAST_4:
        conditions.append(and_(models.BlacklistCreditCard.phone_number == phone_number,
                               models.BlacklistCreditCard.last_four_digit == cc_last_4))

    if settings.BLACKLIST_CHECK_EMAIL_LAST_4:
        conditions.append(and_(models.BlacklistCreditCard.email == email,
                               models.BlacklistCreditCard.last_four_digit == cc_last_4))

    if conditions:
        query = query.filter(or_(*conditions))

    if settings.BLACKLIST_CHECK_CURRENCY_LAST_4:
        query = query.filter(and_(models.BlacklistCreditCard.currency == currency,
                                  models.BlacklistCreditCard.last_four_digit == cc_last_4))

    if query.first() is None:
        return False
    return True
    # raise exceptions.ApolloObjectDoesNotExist(model_name='CreditCard')


def blacklist_cc(db: Session, member_id: str, email: str, phone_number: str, currency: str, last_four_digit: str,
                 cc_id: str):
    try:
        blacklist_cc_add = {
            'currency': currency,
            'last_four_digit': last_four_digit,
            'status': "Blacklist",
            'phone_number': phone_number,
            'member_id': member_id,
            'email': email
        }
        BlacklistCreditCardCRUD.add(db, blacklist_cc_add, check_permission=False)

        credit_card_update = {
            'token': None,
            'last_four_digit': None,
            'primary': False,
            'status': schema.CreditCardStatus.blacklisted
        }
        CreditCardCRUD.update(db, cc_id, credit_card_update, check_permission=False)
        CreditCardCRUD.delete(db, cc_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='BlacklistCreditCard')
