from enum import Enum
from uuid import UUID
from typing import Optional
from datetime import datetime
from pydantic import BaseModel


class EmaidStatus(str, Enum):
    payment_pending = "Payment Pending"
    pending = 'Pending'
    active = 'Active'
    disabled = 'Disabled'  # Paused. Can be re-enabled.
    deactivated = 'Deactivated'
    failed = 'Failed'
    renewal_pending = 'Renewal Pending'
    renewal_rejected = 'Renewal Rejected'
    renewal_payment_pending = 'Renewal Payment Pending'


class Emaid(BaseModel):
    emaid: str
    status: EmaidStatus
    status_comment: str

    class Config:
        orm_mode = True


class EmaidResponse(BaseModel):
    id: UUID

    created_at: datetime
    updated_at: Optional[datetime]  # pylint:disable=unsubscriptable-object

    emaid: str
    contract_begin: Optional[datetime]  # pylint:disable=unsubscriptable-object
    contract_end: Optional[datetime]  # pylint:disable=unsubscriptable-object
    status: EmaidStatus
    status_comment: Optional[str] = ''  # pylint:disable=unsubscriptable-object

    class Config:
        orm_mode = True


class EmaidHistory(BaseModel):
    emaid_id: Optional[str]  # pylint:disable=unsubscriptable-object
    contract_begin: datetime
    contract_end: datetime
    status: EmaidStatus


class EmaidCreate(BaseModel):
    emaid: str
    status: EmaidStatus = EmaidStatus.pending
    vehicle_id: str


class EmaidUpdate(BaseModel):
    status: EmaidStatus
    status_comment: Optional[str]  # pylint:disable=unsubscriptable-object
    contract_begin: Optional[datetime]  # pylint:disable=unsubscriptable-object
    contract_end: Optional[datetime]  # pylint:disable=unsubscriptable-object
    vehicle_id: Optional[str]  # pylint:disable=unsubscriptable-object


class EmaidOEMUpdate(BaseModel):
    oem_message: dict
