"""79

Revision ID: 9ae672d69543
Revises: d8edcf650fe6
Create Date: 2024-03-23 15:50:16.801352

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '9ae672d69543'
down_revision = 'd8edcf650fe6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_charging_session_bill', sa.Column('charge_point_id', sa.String(), nullable=True))
    op.add_column('main_charging_session_bill', sa.Column('partner_id', sa.String(), nullable=True))
    op.add_column('main_charging_session_bill', sa.Column('cpo_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_charging_session_bill', 'cpo_id')
    op.drop_column('main_charging_session_bill', 'partner_id')
    op.drop_column('main_charging_session_bill', 'charge_point_id')
    # ### end Alembic commands ###
