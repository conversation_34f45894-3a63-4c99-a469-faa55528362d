"""“7”

Revision ID: 5cb4c634887d
Revises: 242a03b65174
Create Date: 2022-09-01 15:36:29.337904

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5cb4c634887d'
down_revision = '242a03b65174'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_subscription_plan',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('category', sa.Enum('fixed', 'percentage', name='subscriptionplancategory'), nullable=True),
    sa.Column('amount', sa.Float(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('is_invitation_only', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['main_organization.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_subscription_card',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id_tag', sa.String(), nullable=True),
    sa.Column('subscription_plan_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('number', sa.String(), nullable=True),
    sa.Column('linked_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['subscription_plan_id'], ['main_subscription_plan.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_subscription_custom_plan',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('amount', sa.Float(), nullable=True),
    sa.Column('connector_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('subscription_plan_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['subscription_plan_id'], ['main_subscription_plan.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_subscription_fee',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('amount', sa.Float(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('subscription_plan_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['subscription_plan_id'], ['main_subscription_plan.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_subscription_invitation',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('linked_at', sa.DateTime(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('subscription_plan_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['subscription_plan_id'], ['main_subscription_plan.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    sa.UniqueConstraint('subscription_plan_id')
    )
    op.create_table('main_subscription',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('start_date', sa.DateTime(), nullable=True),
    sa.Column('end_date', sa.DateTime(), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('subscription_plan_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('invitation_code', sa.String(), nullable=True),
    sa.Column('meta', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('status', sa.Enum('pending', 'failed', 'success', name='subscriptionstatus'), nullable=True),
    sa.Column('subscription_card_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['invitation_code'], ['main_subscription_invitation.code'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['subscription_card_id'], ['main_subscription_card.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['subscription_plan_id'], ['main_subscription_plan.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('invitation_code')
    )
    op.create_index('only_one_default_plan', 'main_subscription', ['is_default', 'member_id'], unique=True, postgresql_where=sa.text('is_default'))
    op.create_table('main_subscription_order',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('amount', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('subscription_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('status', sa.Enum('pending', 'failed', 'success', name='subscriptionstatus'), nullable=True),
    sa.Column('payable_fees', postgresql.JSON(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['subscription_id'], ['main_subscription.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('main_charging_session_bill', sa.Column('discount', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_charging_session_bill', 'discount')
    op.drop_table('main_subscription_order')
    op.drop_index('only_one_default_plan', table_name='main_subscription', postgresql_where=sa.text('is_default'))
    op.drop_table('main_subscription')
    op.drop_table('main_subscription_invitation')
    op.drop_table('main_subscription_fee')
    op.drop_table('main_subscription_custom_plan')
    op.drop_table('main_subscription_card')
    op.drop_table('main_subscription_plan')
    # ### end Alembic commands ###
