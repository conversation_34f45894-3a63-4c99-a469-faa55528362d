from datetime import datetime, timedelta
import json
import logging
import random
import string
from uuid import UUID
from zoneinfo import ZoneInfo

import pytz
import requests
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, or_

from app import models, schema, exceptions, settings
from app.crud.base import BaseCRUD
from app.utils import generate_charger_header, get_membership_segment, update_membership_segment


timezone = pytz.UTC
logger = logging.getLogger(__name__)
CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'


class CampaignCRUD(BaseCRUD):
    model = models.Campaign

    @classmethod
    def query(cls, dbsession, *columns):
        membership = cls.membership()
        query = super().query(dbsession, *columns) \
            .order_by(cls.model.updated_at.desc().nullslast(),
                      cls.model.created_at.desc())

        if membership.user.is_superuser:
            return query

        if membership.membership_type in (
                schema.MembershipType.staff,
                schema.MembershipType.sub_staff,
                schema.MembershipType.custom,
        ):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return query.filter(cls.model.organization_id.in_(allowed_orgs))

        if membership.membership_type == schema.MembershipType.regular_user:
            allowed_orgs = cls.parent_organizations(dbsession, membership) + [membership.organization_id]
            return query.filter(cls.model.organization_id.in_(allowed_orgs))

        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True

        if membership.membership_type in (
                schema.MembershipType.staff,
                schema.MembershipType.sub_staff,
                schema.MembershipType.custom,
        ):
            db_object = dbsession.query(cls.model).get(object_id)
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_object.organization_id in allowed_orgs:
                return True

        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_create(cls, dbsession, data, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True

        if membership.membership_type in (
                schema.MembershipType.staff,
                schema.MembershipType.custom,
        ):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if data['organization_id'] in allowed_orgs:
                return True

        raise exceptions.ApolloPermissionError()


class CampaignPromoCodeCRUD(BaseCRUD):
    model = models.CampaignPromoCode

    @classmethod
    def query(cls, dbsession, *columns):
        membership = cls.membership()
        query = super().query(dbsession, *columns) \
            .order_by(cls.model.updated_at.desc().nullslast(),
                      cls.model.created_at.desc())

        if membership.user.is_superuser:
            return query

        if membership.membership_type in (
                schema.MembershipType.staff,
                schema.MembershipType.sub_staff,
                schema.MembershipType.custom,
        ):
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            return query.join(models.Campaign).filter(models.Campaign.organization_id.in_(allowed_orgs))

        if membership.membership_type == schema.MembershipType.regular_user:
            allowed_orgs = cls.parent_organizations(dbsession, membership) + [membership.organization_id]
            return query.join(models.Campaign).filter(models.Campaign.organization_id.in_(allowed_orgs))

        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_write(cls, dbsession, object_id, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True

        if membership.membership_type in (
                schema.MembershipType.staff,
                schema.MembershipType.sub_staff,
                schema.MembershipType.custom,
        ):
            db_object = dbsession.query(cls.model).get(object_id)
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_object.campaign.organization_id in allowed_orgs:
                return True

        raise exceptions.ApolloPermissionError()

    @classmethod
    def can_create(cls, dbsession, data, *args, **kwargs):
        membership = cls.membership()
        if membership.user.is_superuser:
            return True

        if membership.membership_type in (
                schema.MembershipType.staff,
                schema.MembershipType.custom,
        ):
            db_campaign = dbsession.query(models.Campaign).get(data['campaign_id'])
            allowed_orgs = cls.child_organizations(dbsession, membership) + [membership.organization_id]
            if db_campaign.organization_id in allowed_orgs:
                return True

        raise exceptions.ApolloPermissionError()


class CampaignPromoCodeUsageCRUD(BaseCRUD):
    model = models.CampaignPromoCodeUsage

    @classmethod
    def can_write(cls, dbsession, object_id, *args, **kwargs):
        return True

    @classmethod
    def query(cls, dbsession, *columns):
        return super().query(dbsession, *columns) \
            .order_by(cls.model.updated_at.desc().nullslast(),
                      cls.model.created_at.desc())


def get_promo_code_count(
        db: Session,
        promo_code: str,
        is_booked: bool = True,
        is_used: bool = None,
        member_id: str = None,
) -> int:
    """
    Default: get booked promo code count (regardless used or not)
    """
    query = db.query(models.CampaignPromoCodeUsage).join(models.CampaignPromoCode).filter(
        models.CampaignPromoCode.code == promo_code,
        models.CampaignPromoCodeUsage.booked == is_booked,
    )

    if is_used is not None:
        query = query.filter(models.CampaignPromoCodeUsage.used == is_used)

    if member_id is not None:
        query = query.filter(models.CampaignPromoCodeUsage.membership_id == member_id)

    return query.count()


def get_booked_but_unused_promo_code(
        db: Session,
        promo_code: str,
        member_id: str = None,
) -> int:
    """
    Default: get booked promo code count (regardless used or not)
    """
    query = db.query(models.CampaignPromoCodeUsage).join(models.CampaignPromoCode).filter(
        models.CampaignPromoCode.code == promo_code,
        models.CampaignPromoCodeUsage.booked.is_(True),
        models.CampaignPromoCodeUsage.used.is_(False),
        models.CampaignPromoCodeUsage.membership_id == member_id,
    )

    return query.first()


def check_promo_code_exist(
        db: Session,
        promo_code: str,
) -> models.CampaignPromoCode:
    return db.query(models.CampaignPromoCode).filter(
        models.CampaignPromoCode.code == promo_code,
        models.CampaignPromoCode.is_deleted.is_(False),
    ).first()


def get_one_promo_code_usage(
        db: Session,
        membership_id: UUID,
        is_booked: bool,
        is_used: bool,
        latest: bool = True,
) -> models.CampaignPromoCodeUsage:
    query = db.query(models.CampaignPromoCodeUsage).join(models.CampaignPromoCode).filter(
        models.CampaignPromoCodeUsage.membership_id == membership_id,
        models.CampaignPromoCodeUsage.booked == is_booked,
        models.CampaignPromoCodeUsage.used == is_used,
    )

    if latest:
        query = query.order_by(models.CampaignPromoCodeUsage.created_at.desc())

    return query.first()


def invalidate_promo_codes(
        db: Session,
        membership_id: UUID,
):
    """
    Invalidate all promo codes that are booked but not used
    by setting used=True

    Invalidated promo codes are distinguished from used promo codes
    via used_at=None and charging_session_id=None
    """
    db.query(models.CampaignPromoCodeUsage).filter(
        models.CampaignPromoCodeUsage.membership_id == membership_id,
        models.CampaignPromoCodeUsage.booked.is_(True),
        models.CampaignPromoCodeUsage.used.is_(False)
    ).update({
        "used": True,
    })

    db.commit()


def unbook_other_promo_codes(
        db: Session,
        membership_id: UUID,
):
    """
    Invalidate all promo codes that are booked but not used
    by setting used=True

    Invalidated promo codes are distinguished from used promo codes
    via used_at=None and charging_session_id=None
    """
    db.query(models.CampaignPromoCodeUsage).filter(
        models.CampaignPromoCodeUsage.membership_id == membership_id,
        models.CampaignPromoCodeUsage.booked.is_(True),
        models.CampaignPromoCodeUsage.used.is_(False)
    ).update({
        "booked": False,
    })

    db.commit()


def book_promo_code(
        db: Session,
        promo_code: str,
        membership_id: UUID,
        campaign_promo_code_id: UUID = None,
) -> models.CampaignPromoCodeUsage:
    """
    Book a Promo Code Usage by creating CampaignPromoCodeUsage
    with booked=True
    """
    if campaign_promo_code_id is None:
        db_campaign_promo_code = check_promo_code_exist(db, promo_code)
        campaign_promo_code_id = db_campaign_promo_code.id

    query = db.query(models.CampaignPromoCodeUsage).filter(
        models.CampaignPromoCodeUsage.campaign_promo_code_id == campaign_promo_code_id,
        models.CampaignPromoCodeUsage.booked.is_(False),
        models.CampaignPromoCodeUsage.used.is_(False),
        models.CampaignPromoCodeUsage.membership_id == membership_id
    )
    db_campaign_promo_code_usage = query.first()
    if not db_campaign_promo_code_usage:
        data = schema.CampaignPromoCodeUsageCreate(
            booked=True,
            membership_id=membership_id,
            campaign_promo_code_id=campaign_promo_code_id,
        )

        return CampaignPromoCodeUsageCRUD.add(db, data.dict())
    db_campaign_promo_code_usage.booked = True
    db.commit()
    db.refresh(db_campaign_promo_code_usage)

    return db_campaign_promo_code_usage


def use_booked_promo_code(
        db: Session,
        promo_code_usage_id: UUID,
        charging_session_id: UUID,
        transaction_id: str = None,
):
    """
    Use a booked Promo Code Usage by updating CampaignPromoCodeUsage
    with used=True
    """
    db_promo_code_usage = db.query(models.CampaignPromoCodeUsage).get(promo_code_usage_id)

    db_promo_code_usage.used = True
    db_promo_code_usage.used_at = datetime.now()
    db_promo_code_usage.charging_session_id = charging_session_id
    db_promo_code_usage.transaction_id = transaction_id

    db.commit()
    db.refresh(db_promo_code_usage)

    return db_promo_code_usage


def validate_campaign_promo_code(  # noqa: MC0001
        db: Session,
        promo_code: str,
        location_id: UUID = None,
        member_id: str = None,
        subscription_applied: bool = False,
) -> models.CampaignPromoCode:
    """
    Validate if:
    1. Promo Code exists
    2. Campaign is active
    3. Promo Code is stackable with subscription
    4. Promo Code is within valid date range
    5. Promo Code is used within valid hours
    6. Promo Code is for new users only
    7. Promo Code is within per user usage limit
    8. Promo Code limit is not exceeded
    9. Promo Code is exclusive to location (if applicable)
    """
    db_campaign_promo_code = check_promo_code_exist(db, promo_code)
    if not db_campaign_promo_code:
        raise exceptions.ApolloObjectDoesNotExist(model_name='CampaignPromoCode')

    dt_now = datetime.now(timezone)
    out_of_date = (dt_now < db_campaign_promo_code.campaign.start_at or
                   dt_now > db_campaign_promo_code.campaign.expire_at)

    if not db_campaign_promo_code.campaign.is_active or out_of_date:
        raise exceptions.ApolloCampaignPromoCodeHasExpiredError()

    if (
        subscription_applied
        and not db_campaign_promo_code.campaign.is_stackable
    ):
        raise exceptions.ApolloCampaignPromoCodeNotStackableError()

    # if None or empty dict, assumed valid for all hours
    if db_campaign_promo_code.campaign.valid_period:
        campaign_tz = ZoneInfo(db_campaign_promo_code.campaign.valid_period.get("timezone", "UTC"))
        current_time = datetime.now(campaign_tz)
        current_day = current_time.strftime("%A").lower()

        if (
            current_day not in db_campaign_promo_code.campaign.valid_period
            or db_campaign_promo_code.campaign.valid_period[current_day] is None
        ):
            raise exceptions.ApolloCampaignPromoCodeInvalidHoursError()

        valid_hours = db_campaign_promo_code.campaign.valid_period[current_day]
        valid_hours_start = valid_hours["period_start"]
        valid_hours_end = valid_hours["period_end"]

        valid_start = __parse_valid_hours(
            dt_now=current_time,
            time_str=valid_hours_start,
        )
        # if hours_end < hours_start, it means the promo code is valid until the next day
        valid_end = __parse_valid_hours(
            dt_now=current_time if valid_hours_end > valid_hours_start else current_time + timedelta(days=1),
            time_str=valid_hours_end,
        )

        if not valid_start <= current_time <= valid_end:
            raise exceptions.ApolloCampaignPromoCodeInvalidHoursError()

    if db_campaign_promo_code.campaign.valid_membership_segments_only:
        charging_session_count = __get_charging_sessions_total_count(db, member_id)
        membership_segment = get_membership_segment(charging_session_count)
        db_member = update_membership_segment(db, member_id, membership_segment)

        if db_member.membership_segment not in db_campaign_promo_code.campaign.valid_membership_segments_only:
            raise exceptions.ApolloCampaignPromoCodeInvalidMembershipSegmentError(
                db_campaign_promo_code.campaign.valid_membership_segments_only
            )

    if db_campaign_promo_code.campaign.per_user_usage_limit:
        user_usage_count = get_promo_code_count(
            db=db,
            promo_code=promo_code,
            is_booked=True,
            is_used=True,
            member_id=member_id,
        )
        if user_usage_count >= db_campaign_promo_code.campaign.per_user_usage_limit:
            raise exceptions.ApolloCampaignPromoCodeLimitExceededError()

    booked_count = get_promo_code_count(db, promo_code)

    if booked_count >= db_campaign_promo_code.limit:
        is_booked_but_unused_same_member = get_booked_but_unused_promo_code(db, promo_code, member_id)
        if is_booked_but_unused_same_member is None:
            raise exceptions.ApolloCampaignPromoCodeLimitExceededError()

    if db_campaign_promo_code.campaign.only_exclusive_locations:
        if not location_id:
            raise exceptions.ApolloCampaignPromoCodeMissingLocationError()

        if location_id not in [str(uuid) for uuid in db_campaign_promo_code.campaign.exclusive_location_ids]:
            raise exceptions.ApolloCampaignPromoCodeLocationError()

    return db_campaign_promo_code


def generate_campaign_promo_code(length, prefix: str = None):
    letters_and_digits = string.ascii_uppercase + string.digits

    if prefix is not None:
        number_of_length = length - len(prefix) - 1
        code = ''.join(random.choice(letters_and_digits) for _ in range(number_of_length))  # nosec
        # code = prefix.upper() + '-' + code
        code = prefix.upper() + '' + code
        return code
    return ''.join(random.choice(letters_and_digits) for _ in range(length))  # nosec


def get_all_campaign_promo(dbsession, filters: schema.CampaignFiltersParam = None):  # noqa mccabe: MC0001
    db_campaign_list = CampaignCRUD.query(dbsession).join(
        models.Organization,
        models.Organization.id == models.Campaign.organization_id
    )
    if filters:
        if filters.name:
            db_campaign_list = db_campaign_list.filter(
                models.Campaign.name.ilike(f'%{filters.name}%')
            )
        if filters.description:
            db_campaign_list = db_campaign_list.filter(
                models.Campaign.description.ilike(f'%{filters.description}%')
            )
        if filters.organization:
            db_campaign_list = db_campaign_list.filter(
                models.Organization.name.ilike(f'%{filters.organization}%')
            )
        if filters.currency:
            db_campaign_list = db_campaign_list.filter(
                models.Campaign.currency.ilike(f'%{filters.currency}%')
            )
        if filters.discount_category:
            db_campaign_list = db_campaign_list.filter(
                models.Campaign.discount_category.ilike(f'%{filters.discount_category}%')
            )
        if filters.active is not None:
            db_campaign_list = db_campaign_list.filter(
                models.Campaign.is_active.is_(filters.active)
            )
        if filters.reusable is not None:
            db_campaign_list = db_campaign_list.filter(
                models.Campaign.is_reusable.is_(filters.reusable)
            )
        if filters.ocpi_reusable is not None:
            db_campaign_list = db_campaign_list.filter(
                models.Campaign.ocpi_usable.is_(filters.ocpi_reusable)
            )
        if filters.exclusive_locations is not None:
            db_campaign_list = db_campaign_list.filter(
                models.Campaign.only_exclusive_locations.is_(filters.exclusive_locations)
            )
        if filters.promotion_code_type:
            db_campaign_list = db_campaign_list.filter(
                func.lower(models.Campaign.promo_code_type) == filters.promotion_code_type.lower()
            )
        if filters.date_from and filters.date_to:
            db_campaign_list = db_campaign_list.filter(
                models.Campaign.created_at >= filters.date_from,
                models.Campaign.created_at <= filters.date_to,
            )

    db_campaign_list = db_campaign_list.order_by(
        desc(models.Campaign.created_at))
    return db_campaign_list


def get_all_campaign_promo_code(dbsession, campaign_id, is_unique: bool = False,  # noqa mccabe: MC0001
                                filters: schema.CampaignPromoCodeUsageFiltersParam = None):  # noqa mccabe: MC0001
    # db_campaign_promo_code = CampaignPromoCodeCRUD.query(dbsession).filter(
    #     models.CampaignPromoCode.campaign_id == campaign_id)
    if is_unique:
        db_campaign_promo_code = CampaignPromoCodeCRUD.query(dbsession).filter(
            models.CampaignPromoCode.campaign_id == campaign_id)
        db_campaign_promo_code = db_campaign_promo_code.outerjoin(
            models.CampaignPromoCodeUsage,
            models.CampaignPromoCodeUsage.campaign_promo_code_id == models.CampaignPromoCode.id
        ).outerjoin(
            models.Membership,
            models.Membership.id == models.CampaignPromoCodeUsage.membership_id
        ).outerjoin(
            models.User,
            models.User.id == models.Membership.user_id
        )
        if filters:
            if filters.code:
                db_campaign_promo_code = db_campaign_promo_code.filter(
                    models.CampaignPromoCode.code.ilike(f'%{filters.code}%')
                )
            if filters.booked is not None:
                if filters.booked:
                    db_campaign_promo_code = db_campaign_promo_code.filter(
                        models.CampaignPromoCodeUsage.booked.is_(filters.booked)
                    )
                else:
                    db_campaign_promo_code = db_campaign_promo_code.filter(
                        or_(
                            models.CampaignPromoCodeUsage.booked.is_(filters.booked),
                            models.CampaignPromoCodeUsage.booked.is_(None)
                        )
                    )
            if filters.used is not None:
                if filters.used:
                    db_campaign_promo_code = db_campaign_promo_code.filter(
                        models.CampaignPromoCodeUsage.used.is_(filters.used)
                    )
                else:
                    db_campaign_promo_code = db_campaign_promo_code.filter(
                        or_(
                            models.CampaignPromoCodeUsage.used.is_(filters.used),
                            models.CampaignPromoCodeUsage.used.is_(None)
                        )
                    )
            if filters.date_from and filters.date_to:
                db_campaign_promo_code = db_campaign_promo_code.filter(
                    models.CampaignPromoCodeUsage.used_at >= filters.date_from,
                    models.CampaignPromoCodeUsage.used_at <= filters.date_to,
                )
            if filters.user_name:
                db_campaign_promo_code = db_campaign_promo_code.filter(
                    func.concat(models.Membership.first_name, ' ',
                                models.Membership.last_name).ilike(f'%{filters.user_name}%')
                )
            if filters.id_tag:
                db_campaign_promo_code = db_campaign_promo_code.filter(
                    func.lower(models.Membership.user_id_tag) == str(filters.id_tag).lower()
                )
            if filters.phone_number:
                db_campaign_promo_code = db_campaign_promo_code.filter(
                    models.User.phone_number.ilike(f'%{filters.phone_number}%')
                )
            if filters.transaction_id:
                db_campaign_promo_code = db_campaign_promo_code.filter(
                    models.CampaignPromoCodeUsage.transaction_id.ilike(f'%{filters.transaction_id}%'))
    else:
        subquery = (
            dbsession.query(
                models.CampaignPromoCodeUsage.campaign_promo_code_id,
                func.count(models.CampaignPromoCodeUsage.id).label("claimed")
            )
            .group_by(models.CampaignPromoCodeUsage.campaign_promo_code_id)
            .subquery()
        )
        db_campaign_promo_code = (
            dbsession.query(
                models.CampaignPromoCode,
                func.coalesce(subquery.c.claimed, 0).label("claimed")
            )
            .outerjoin(subquery, models.CampaignPromoCode.id == subquery.c.campaign_promo_code_id)
            .filter(
                models.CampaignPromoCode.campaign_id == campaign_id,
                models.CampaignPromoCode.is_deleted.is_(False),
            )
        )
        if filters:
            if filters.code:
                db_campaign_promo_code = db_campaign_promo_code.filter(
                    models.CampaignPromoCode.code.ilike(f'%{filters.code}%')
                )
            if filters.date_from and filters.date_to:
                db_campaign_promo_code = db_campaign_promo_code.filter(
                    models.CampaignPromoCode.created_at >= filters.date_from,
                    models.CampaignPromoCode.created_at <= filters.date_to,
                )
    return db_campaign_promo_code


def get_all_campaign_promo_code_usage(dbsession, campaign_id,  # noqa mccabe: MC0001
                                      filters: schema.CampaignPromoCodeUsageFiltersParam = None):  # noqa mccabe: MC0001
    db_campaign_usage = CampaignPromoCodeUsageCRUD.query(dbsession).join(
        models.CampaignPromoCode,
        models.CampaignPromoCode.id == models.CampaignPromoCodeUsage.campaign_promo_code_id
    ).join(
        models.Campaign,
        models.Campaign.id == models.CampaignPromoCode.campaign_id
    ).join(
        models.Membership,
        models.Membership.id == models.CampaignPromoCodeUsage.membership_id
    ).join(
        models.User,
        models.User.id == models.Membership.user_id
    ).filter(
        models.Campaign.id == campaign_id
    )
    if filters:
        if filters.code:
            db_campaign_usage = db_campaign_usage.filter(
                models.CampaignPromoCode.code.ilike(f'%{filters.code}%')
            )
        if filters.user_name:
            db_campaign_usage = db_campaign_usage.filter(
                func.concat(models.Membership.first_name, ' ',
                            models.Membership.last_name).ilike(f'%{filters.user_name}%')
            )
        if filters.booked is not None:
            if filters.booked:
                db_campaign_usage = db_campaign_usage.filter(
                    models.CampaignPromoCodeUsage.booked.is_(filters.booked)
                )
            else:
                db_campaign_usage = db_campaign_usage.filter(
                    or_(
                        models.CampaignPromoCodeUsage.booked.is_(filters.booked),
                        models.CampaignPromoCodeUsage.booked.is_(None)
                    )
                )
        if filters.used is not None:
            if filters.used:
                db_campaign_usage = db_campaign_usage.filter(
                    models.CampaignPromoCodeUsage.used.is_(filters.used)
                )
            else:
                db_campaign_usage = db_campaign_usage.filter(
                    or_(
                        models.CampaignPromoCodeUsage.used.is_(filters.used),
                        models.CampaignPromoCodeUsage.used.is_(None)
                    )
                )
        if filters.id_tag:
            db_campaign_usage = db_campaign_usage.filter(
                func.lower(models.Membership.user_id_tag) == str(filters.id_tag).lower()
            )
        if filters.transaction_id:
            db_campaign_usage = db_campaign_usage.filter(
                models.CampaignPromoCodeUsage.transaction_id.ilike(f'%{filters.transaction_id}%'))
        if filters.date_from and filters.date_to:
            db_campaign_usage = db_campaign_usage.filter(
                models.CampaignPromoCodeUsage.used_at >= filters.date_from,
                models.CampaignPromoCodeUsage.used_at <= filters.date_to,
            )
        if filters.phone_number:
            db_campaign_usage = db_campaign_usage.filter(
                models.User.phone_number.ilike(f'%{filters.phone_number}%')
            )
    return db_campaign_usage


def __parse_valid_hours(dt_now: datetime, time_str: str):  # noqa: N802
    """
    Given NOW timestamp and time string, combine to valid start/end datetime object
    """
    # Parse time string into a timedelta
    time_obj = datetime.strptime(time_str, "%H:%M:%S").time()
    delta = timedelta(hours=time_obj.hour, minutes=time_obj.minute, seconds=time_obj.second)

    # Get current date and add timedelta
    now = dt_now.date()
    result = datetime.combine(now, datetime.min.time()) + delta
    result = result.replace(tzinfo=dt_now.tzinfo)

    return result


def __get_charging_sessions_total_count(db: Session, member_id: UUID) -> int:  # noqa: N802
    url = f"{CHARGER_URL_PREFIX}/charging/total_count"
    charger_headers = generate_charger_header(db, member_id)
    max_retries = 3
    timeout = 3

    id_tags = json.loads(charger_headers["id_tags_list"])
    params = {"id_tags": id_tags} if id_tags else {}

    for attempt in range(max_retries):
        try:
            res = requests.get(url=url, headers=charger_headers, params=params, timeout=timeout)
            if 200 <= res.status_code < 300:
                break
        except Exception as e:  # pylint: disable=broad-except
            logger.error("Request error. Retrying (Attempt %d of %d)... Error: %s", attempt + 1, max_retries, e)

        timeout = timeout * 1.5

    total_count = res.json()
    return total_count
