from unittest.mock import patch

from faker import Faker

from app.mail import Send<PERSON>ridMail, RecipientsSchema


fake = Faker()


@patch('app.mail.SendGridMail._send_mail')
def test_sendgrid_mail(_send_email_mock):
    """
    Tests the mail functionality
    """
    _send_email_mock.return_value = {
        'status': 'status',
        'body': 'response.body',
        'headers': 'response.headers',
    }
    sendgrid_mail = SendGridMail(f'TSA Test {fake.pystr()}',
                                 RecipientsSchema(emails=[fake.email(), fake.email(), ]),
                                 fake.email())
    response = sendgrid_mail.send_text_mail(fake.sentence())

    assert response['status'] is not None
    assert response['body'] is not None
    assert response['headers'] is not None
