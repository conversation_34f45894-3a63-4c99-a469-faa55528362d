from enum import Enum
from uuid import UUID
from typing import Optional
from datetime import datetime
from pydantic import BaseModel


class AutochargeStatus(str, Enum):
    # Make sure the status field in Autocharge model defaults to pending state string
    pending = 'Pending'
    active = 'Active'
    disabled = 'Disabled'  # Paused. Can be re-enabled.
    deactivated = 'Deactivated'


class Autocharge(BaseModel):
    mac_address: str
    status: AutochargeStatus

    class Config:
        orm_mode = True


class AutochargeResponse(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]  # pylint:disable=unsubscriptable-object
    mac_address: Optional[str]  # pylint:disable=unsubscriptable-object
    status: AutochargeStatus

    class Config:
        orm_mode = True


class AutochargeHistory(BaseModel):
    mac_address: Optional[str]  # pylint:disable=unsubscriptable-object
    status: AutochargeStatus


class AutochargeCreate(BaseModel):
    mac_address: Optional[str]  # pylint:disable=unsubscriptable-object
    status: AutochargeStatus = AutochargeStatus.pending
    vehicle_id: str


class AutochargeUpdate(BaseModel):
    mac_address: Optional[str]  # pylint:disable=unsubscriptable-object
    status: Optional[AutochargeStatus]  # pylint:disable=unsubscriptable-object
    vehicle_id: Optional[str]  # pylint:disable=unsubscriptable-object
