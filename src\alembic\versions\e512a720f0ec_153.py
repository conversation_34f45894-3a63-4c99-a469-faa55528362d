"""153

Revision ID: e512a720f0ec
Revises: 0cf3fc33723a
Create Date: 2025-03-13 14:33:07.018915

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e512a720f0ec'
down_revision = '0cf3fc33723a'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('main_wallet_package', sa.Column('package_category', sa.String(), nullable=True))


def downgrade():
    op.drop_column('main_wallet_package', 'package_category')