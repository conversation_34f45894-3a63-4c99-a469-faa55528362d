"""164

Revision ID: 22cee48f8ba6
Revises: c1234196b6fd
Create Date: 2025-05-13 07:52:05.732496

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '22cee48f8ba6'
down_revision = 'c1234196b6fd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_auth_user', sa.Column('mfa_enabled', sa.<PERSON>(), nullable=True))
    op.add_column('main_auth_user', sa.Column('mfa_secret', sa.String(), nullable=True))
    op.add_column('main_auth_user', sa.Column('temp_mfa_secret', sa.String(), nullable=True))
    op.add_column('main_auth_user', sa.Column('temp_mfa_secret_created_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_auth_user', 'temp_mfa_secret_created_at')
    op.drop_column('main_auth_user', 'temp_mfa_secret')
    op.drop_column('main_auth_user', 'mfa_secret')
    op.drop_column('main_auth_user', 'mfa_enabled')
    # ### end Alembic commands ###
