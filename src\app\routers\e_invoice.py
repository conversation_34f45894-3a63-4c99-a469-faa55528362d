import logging
from datetime import datetime

from fastapi import APIRouter, Depends, Request, HTTPException, status
from fastapi.responses import HTMLResponse
from jose import jwt

from app import settings, crud, exceptions, schema
from app.crud import get_e_invoice_by_charging_session_id, update_e_invoice, get_e_credit_note_by_payment_refund_id, \
    update_e_credit_note
# from app.crud import UserCRUD, BillingCRUD
from app.database import create_session, SessionLocal
from app.e_invoice_utils import construct_and_submit_invoice_to_rmp, construct_and_submit_credit_note_to_rmp
from app.e_invoice_utils import get_e_invoice_callback_template, make_status_timeline_entry
from app.permissions import x_api_key
from app.utils import (decode_auth_token_from_headers, generate_charger_header)

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/e_invoice",
    tags=['e_invoice', ],
    # dependencies=[Depends(permission)],
)
CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'


@router.get("/{charging_session_id}", status_code=status.HTTP_200_OK)
async def get_and_send_e_invoice(request: Request,  # noqa: MC0001,# pylint: disable=too-many-nested-blocks
                                 charging_session_id: str,
                                 dbsession: SessionLocal = Depends(create_session)):
    """
    Get invoice and submit to 3rd party.

    :param str charging_session_id: Target Invoice ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    # user_id = auth_token_data.get('user_id')
    # login_user = UserCRUD.get(dbsession, user_id)
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    try:
        charging_session_bill = crud.get_charging_session_bill_by_charging_session(dbsession, charging_session_id)
        if not charging_session_bill:
            raise HTTPException(400, 'ChargingSessionBill does not exists')
        db_cs = await crud.get_raw_charging_session(dbsession, charging_session_id, headers)

        result = await construct_and_submit_invoice_to_rmp(dbsession,
                                                           db_cs,
                                                           charging_session_bill,
                                                           membership_id)
        return result

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/callback", dependencies=[Depends(x_api_key)], status_code=status.HTTP_200_OK)
async def callback_e_invoice(request: Request,
                             callback_data: dict,
                             dbsession: SessionLocal = Depends(create_session)):
    """
    Callback endpoint for RMP to return submission results.
    """
    logger.info("RMP Callback headers: %s", str(request.headers))
    request_body = await request.json()
    logger.info("RMP Callback content %s", str(request_body))
    logger.info('RMP CALLBACK: %s', callback_data)
    invoice_numbers = []

    accepted = callback_data.get("acceptedDocuments", [])
    for doc in accepted:
        invoice_number = doc.get("invoiceCodeNumber")
        if invoice_number:
            invoice_numbers.append(invoice_number)

    rejected = callback_data.get("rejectedDocuments", [])
    for doc in rejected:
        invoice_number = doc.get("invoiceCodeNumber")
        if invoice_number:
            invoice_numbers.append(invoice_number)

    await crud.update_invoices_with_callback(dbsession, invoice_numbers, callback_data)

    return {"status": "success", "updated_invoice_numbers": invoice_numbers}


@router.get("/cn/{payment_refund_id}", status_code=status.HTTP_200_OK)
async def get_and_send_cn_e_invoice(request: Request,  # noqa: MC0001,# pylint: disable=too-many-nested-blocks
                                    payment_refund_id: str,
                                    dbsession: SessionLocal = Depends(create_session)):
    """
    Get CN and submit to 3rd party.

    :param str payment_refund_id: Target Payment Refund ID ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    # user_id = auth_token_data.get('user_id')
    # login_user = UserCRUD.get(dbsession, user_id)
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    try:
        payment_refund = crud.get_payment_refund_by_refund_id(dbsession, payment_refund_id)
        db_pr = crud.get_payment_request(dbsession, payment_refund.payment_request_id)
        charging_session_bill = crud.get_charging_session_bill(dbsession, db_pr.charging_session_bill_id)
        charging_session_id = charging_session_bill.charging_session_id
        if not charging_session_bill:
            raise HTTPException(400, 'ChargingSessionBill does not exists')
        db_cs = await crud.get_raw_charging_session(dbsession, charging_session_id, headers)

        result = await construct_and_submit_credit_note_to_rmp(dbsession,
                                                               db_cs,
                                                               charging_session_bill,
                                                               membership_id, payment_refund_id)
        return result

    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.post("/cn/callback", dependencies=[Depends(x_api_key)], status_code=status.HTTP_200_OK)
async def callback_e_cn(request: Request,
                        callback_data: dict,
                        dbsession: SessionLocal = Depends(create_session)):
    """
    Callback endpoint for RMP to return submission results for credit notes
    """
    logger.info("RMP Callback headers: %s", str(request.headers))
    request_body = await request.json()
    logger.info("RMP Callback content %s", str(request_body))
    logger.info('RMP CALLBACK: %s', callback_data)
    invoice_numbers = []

    accepted = callback_data.get("acceptedDocuments", [])
    for doc in accepted:
        invoice_number = doc.get("invoiceCodeNumber")
        if invoice_number:
            invoice_numbers.append(invoice_number)

    rejected = callback_data.get("rejectedDocuments", [])
    for doc in rejected:
        invoice_number = doc.get("invoiceCodeNumber")
        if invoice_number:
            invoice_numbers.append(invoice_number)

    await crud.update_cn_with_callback(dbsession, invoice_numbers, callback_data)

    return {"status": "success", "updated_invoice_numbers": invoice_numbers}


@router.get('/redirect/', include_in_schema=True)
async def e_invoice_redirect(request: Request, key: str,
                             dbsession: SessionLocal = Depends(create_session)):  # noqa
    """
    Payment Return URL, return from Webform
    """
    if key:
        try:
            e_invoice_data = jwt.decode(key, key=settings.E_INVOICE_JWT_SECRET,
                                        algorithms=[schema.JWT_ALGORITHM, ],
                                        options={"verify_signature": True, "verify_exp": True})
            charging_session_id = e_invoice_data['charging_session_id']

            db_e_invoice = get_e_invoice_by_charging_session_id(dbsession, charging_session_id)
            status_timeline = db_e_invoice.status_timeline or []
            new_timeline = list(status_timeline)
            e_invoice_update = schema.EInvoiceUpdate(
                status=schema.EInvoiceStatus.requested,
                last_submission_date=datetime.now(),
            )
            new_timeline.append(make_status_timeline_entry(schema.EInvoiceStatus.requested,
                                                           schema.EInvoiceAction.requested)
                                )
            e_invoice_update.status_timeline = new_timeline
            update_e_invoice(dbsession, str(db_e_invoice.id), e_invoice_update)

            return HTMLResponse(content=get_e_invoice_callback_template(True), status_code=200)
        except jwt.JWTError:
            return HTMLResponse(content=get_e_invoice_callback_template(False), status_code=200)
    return HTMLResponse(content=get_e_invoice_callback_template(False), status_code=200)


@router.get('/cn/redirect/', include_in_schema=True)
async def e_cn_redirect(request: Request, key: str,
                        dbsession: SessionLocal = Depends(create_session)):  # noqa
    """
    Payment Return URL, return from Webform
    """
    if key:
        try:
            e_cn_data = jwt.decode(key, key=settings.E_INVOICE_JWT_SECRET,
                                   algorithms=[schema.JWT_ALGORITHM, ],
                                   options={"verify_signature": True, "verify_exp": True})
            payment_refund_id = e_cn_data['payment_refund_id']

            db_e_invoice = get_e_credit_note_by_payment_refund_id(dbsession, payment_refund_id)
            status_timeline = db_e_invoice.status_timeline or []
            new_timeline = list(status_timeline)
            e_cn_update = schema.ECreditNoteUpdate(
                status=schema.ECreditNoteStatus.requested,
                last_submission_date=datetime.now(),
            )
            new_timeline.append(make_status_timeline_entry(schema.ECreditNoteStatus.requested,
                                                           schema.ECreditNoteAction.requested)
                                )
            e_cn_update.status_timeline = new_timeline
            update_e_credit_note(dbsession, str(db_e_invoice.id), e_cn_update)

            return HTMLResponse(content=get_e_invoice_callback_template(True), status_code=200)
        except jwt.JWTError:
            return HTMLResponse(content=get_e_invoice_callback_template(False), status_code=200)
    return HTMLResponse(content=get_e_invoice_callback_template(False), status_code=200)
