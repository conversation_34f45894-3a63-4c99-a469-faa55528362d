from typing import List

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, NoResultFound

from app import models, exceptions
from app.schema.v2 import EmaidCreate
from app.schema import ContractCertificateFeeCreate, ContractCertificateOrderCreate, ContractCertificateOrderUpdate, \
    PaymentRequest, PaymentRequestType, PaymentRequestReason, PaymentRequestUpdateInvoice
from .payment import create_payment_request, update_payment_request
from .base import BaseCRUD


class EmaidCRUD(BaseCRUD):
    model = models.Emaid

    # @classmethod
    # def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
    #     membership = cls.membership()
    #     if any([
    #         membership.user.is_superuser,
    #         membership.membership_type == schema.MembershipType.staff,
    #         membership.membership_type == schema.MembershipType.sub_staff,
    #         membership.membership_type == schema.MembershipType.custom,
    #     ]):
    #         return True
    #     raise exceptions.ApolloPermissionError()

    # @classmethod
    # def can_create(cls, dbsession: Session, data: dict, *args, **kwargs):
    #     membership = cls.membership()
    #     if any([
    #         membership.user.is_superuser,
    #         membership.membership_type == schema.MembershipType.staff,
    #         membership.membership_type == schema.MembershipType.sub_staff,
    #         membership.membership_type == schema.MembershipType.custom,
    #     ]):
    #         return True
    #     raise exceptions.ApolloPermissionError()


def get_emaid(db: Session, emaid_id: str) -> models.Emaid:
    try:
        db_emaid = EmaidCRUD.get(db, emaid_id)
        return db_emaid
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Emaid')


def get_emaid_by_emaid(db: Session, emaid) -> models.Emaid:
    '''Get Emaid object for a specific EMAID'''
    try:
        db_emaid = EmaidCRUD.query(db).filter(models.Emaid.emaid == emaid).first()
        return db_emaid
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Emaid')


def get_emaid_list(db: Session) -> List[models.Emaid]:
    db_emaid_list = EmaidCRUD.query(db).all()
    return db_emaid_list


def create_emaid(db: Session, emaid_data: EmaidCreate) -> models.Emaid:
    try:
        db_emaid = EmaidCRUD.add(db, emaid_data.dict())
        return db_emaid
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloEmaidError()


def delete_emaid(db: Session, emaid_id: str) -> str:
    try:
        EmaidCRUD.delete(db, emaid_id)
        return emaid_id
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Emaid')


def update_emaid(db: Session, emaid_data: dict, emaid_id: str) -> models.Emaid:
    try:
        db_emaid = EmaidCRUD.update(db, emaid_id, emaid_data.dict(exclude_unset=True, exclude_defaults=True))
        return db_emaid
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='Emaid')


class ContractCertificateFeeCRUD(BaseCRUD):
    model = models.ContractCertificateFee


def get_contract_certificate_fee_list(db: Session) -> List[models.ContractCertificateFee]:
    db_cc_fee_list = ContractCertificateFeeCRUD.query(db).all()
    return db_cc_fee_list


def get_contract_certificate_fee(db: Session, cc_fee_id: str) -> models.ContractCertificateFee:
    try:
        db_cc_fee_obj = ContractCertificateFeeCRUD.get(db, cc_fee_id)
        return db_cc_fee_obj
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ContractCertificateFee')


def get_contract_certificate_fee_by_country(db: Session, iso3166_country_code: str) -> models.ContractCertificateFee:
    try:
        query = ContractCertificateFeeCRUD.query(db, models.ContractCertificateFee)
        db_cc_fee_obj = query.filter(
            models.ContractCertificateFee.iso3166_country_code == iso3166_country_code).first()
        return db_cc_fee_obj
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ContractCertificateFee')


def create_contract_certificate_fee(db: Session,
                                    fee_data: ContractCertificateFeeCreate)-> models.ContractCertificateFee:
    try:
        db_cc_fee = ContractCertificateFeeCRUD.add(db, fee_data.dict())
        return db_cc_fee
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloContractCertificateFeeError()


def delete_contract_certificate_fee(db: Session, cc_fee_id: str) -> str:
    try:
        return ContractCertificateFeeCRUD.delete(db, cc_fee_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ContractCertificateFee')


def delete_contract_certificate_fee_by_country(db: Session, iso3166_country_code: str) -> str:
    try:
        query = ContractCertificateFeeCRUD.query(db, models.ContractCertificateFee)
        db_cc_fee_obj = query.filter(
            models.ContractCertificateFee.iso3166_country_code == iso3166_country_code).first()
        if db_cc_fee_obj is not None:
            return ContractCertificateFeeCRUD.delete(db, db_cc_fee_obj.id)
        return None
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ContractCertificateFee')


def update_contract_certificate_fee(db: Session, cc_fee_data: dict, cc_fee_id: str) -> models.ContractCertificateFee:
    try:
        db_cc_fee_obj = ContractCertificateFeeCRUD.update(db, cc_fee_id,
                                                          cc_fee_data.dict(exclude_unset=True, exclude_defaults=True))
        if db_cc_fee_obj is not None:
            return db_cc_fee_obj
        return None
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ContractCertificateFee')


def update_contract_certificate_fee_by_country(db: Session, cc_fee_data: dict,
                                               iso3166_country_code: str) -> models.ContractCertificateFee:
    try:
        query = ContractCertificateFeeCRUD.query(db, models.ContractCertificateFee)
        db_cc_fee_obj = query.filter(
            models.ContractCertificateFee.iso3166_country_code == iso3166_country_code).first()
        if db_cc_fee_obj is not None:
            return ContractCertificateFeeCRUD.update(db, db_cc_fee_obj.id,
                                                     cc_fee_data.dict(exclude_unset=True, exclude_defaults=True))
        return None
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ContractCertificateFee')


class ContractCertificateOrderCRUD(BaseCRUD):
    model = models.ContractCertificateOrder


def create_contract_certificate_order(db: Session,
                                      order_data: ContractCertificateOrderCreate) -> models.ContractCertificateOrder:
    try:
        db_cc_order = ContractCertificateOrderCRUD.add(db, order_data.dict())
        return db_cc_order
    except IntegrityError:
        db.rollback()
        raise exceptions.ApolloContractCertificateOrderError()


def update_contract_certificate_order(db: Session, order_id: str,
                                      order_data: ContractCertificateOrderUpdate) -> models.ContractCertificateOrder:
    try:
        db_cc_order = ContractCertificateOrderCRUD.update(db, order_id,
                                                          order_data.dict(exclude_unset=True, exclude_defaults=True))
        return db_cc_order
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ContractCertificateOrder')


def get_contract_certificate_order(db: Session, contract_certificate_order_id) -> models.ContractCertificateOrder:
    db_contract_certificate_order = ContractCertificateOrderCRUD.query(db).filter(
        models.ContractCertificateOrder.id == contract_certificate_order_id).first()

    return db_contract_certificate_order


def create_contract_certificate_payment_request(db: Session, membership_id: str,
                                                contract_certificate_order_amount: float,
                                                currency: str, contract_certificate_order_id: str,
                                                meta: dict, is_renewal=False
                                                ) -> models.PaymentRequest:
    # Create direct payment request to pay for Contract Certificate
    pr = PaymentRequest(
        type=PaymentRequestType.direct,
        amount=contract_certificate_order_amount,
        currency=currency,
        billing_description=f'contract_certificate_order-{contract_certificate_order_id}',
        reason=PaymentRequestReason.contract_certificate,
        meta=meta,
    )

    if is_renewal:
        pr.reason = PaymentRequestReason.contract_certificate_renewal

    contract_certificate_pr = create_payment_request(db, pr, membership_id)

    # Using CT as the prefix for contract certificates
    pr_update = PaymentRequestUpdateInvoice(
        invoice_number='CT-' + str(contract_certificate_pr.pr_running_number)
    )
    contract_certificate_pr = update_payment_request(db, pr_update, contract_certificate_pr.id)
    return contract_certificate_pr


def create_contract_certificate_renewal_payment_request(db: Session, membership_id: str,
                                                        cc_renewal_order_amount: float,
                                                        currency: str,
                                                        cc_renewal_order_id: str) -> models.PaymentRequest:
    # Create direct payment request to pay for Contract Certificate Renewal
    pr = PaymentRequest(
        type=PaymentRequestType.direct,
        amount=cc_renewal_order_amount,
        currency=currency,
        billing_description=f'contract_certificate_renewal_order-{cc_renewal_order_id}',
        reason=PaymentRequestReason.contract_certificate_renewal
    )
    cc_renewal_pr = create_payment_request(db, pr, membership_id)
    pr_update = PaymentRequestUpdateInvoice(
        invoice_number='CTR-' + str(cc_renewal_pr.pr_running_number)
    )
    cc_renewal_pr = update_payment_request(db, pr_update, cc_renewal_pr.id)
    return cc_renewal_pr
