"""175

Revision ID: 01eb6c926deb
Revises: fbb1c466ccb3
Create Date: 2025-05-29 09:48:02.472343

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '01eb6c926deb'
down_revision = 'fbb1c466ccb3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('unique_vehicle_emaid', 'main_emaid', ['emaid', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('unique_vehicle_emaid', table_name='main_emaid', postgresql_where=sa.text('NOT is_deleted'))
    # ### end Alembic commands ###
