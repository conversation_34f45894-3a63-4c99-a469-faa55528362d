import random
from datetime import timed<PERSON><PERSON>, datetime
from contextlib import contextmanager
from unittest.mock import patch, Mock
import secrets
from uuid import UUI<PERSON>, uuid4

from faker import Faker
from fastapi.testclient import TestClient
import jwt
import pytest

from app import settings, schema
from app.main import app, ROOT_PATH
from app.schema import MembershipType, argon_ph
from app.tests.factories import UserFactory, VerificationTokenFactory, OrganizationFactory, WalletFactory, \
    GlobalRoleFactory, MembershipFactory, MembershipExtendedFactory, OrganizationAuthenticationServiceFactory
from app.database import SessionLocal, create_session, Base, engine
from app.models import MembershipExtended

fake = Faker()
client = TestClient(app)


class TwilioVerifyResponse:
    status = 200

    def create(self, status_code):
        self.status = status_code
        return self


def faker_phone_number(fake: Faker) -> str:
    random_number = ''.join(random.choice('123456789') for _ in range(8))
    random_number = '+601' + random_number
    return random_number


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        WalletFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VerificationTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        GlobalRoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipExtendedFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


@patch('app.routers.v2.auth.send_otp')
def test_post_signup_with_valid_data_succeeds(send_otp_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup'

    valid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': fake.first_name(),
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'IamAPassword123!',
    }

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        org = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        organization_id = str(org.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        admn = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=f'{organization_id}',
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{admn.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        GlobalRoleFactory(
            name='Regular User',
            is_global=True,
            organization_id=None
        )
        db.commit()

        UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        send_otp_mock.return_value = {
            'status': 'success',
        }

        valid_data['organization_id'] = str(org.id)

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id},
                           json=valid_data)
    assert response.json()['message'] == 'Verification code has been sent to your phone number.'
    assert response.status_code == 200
    assert send_otp_mock.called is True


@patch('app.routers.v2.auth.send_otp')
def test_post_signup_with_valid_data_succeeds_with_sms(send_otp_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup'

    valid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': fake.first_name(),
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'IamAPassword123!',
    }

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        org = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        organization_id = str(org.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        admn = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=f'{organization_id}',
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{admn.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        GlobalRoleFactory(
            name='Regular User',
            is_global=True,
            organization_id=None
        )
        db.commit()

        UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        send_otp_mock.return_value = {
            'status': 'success',
        }

        valid_data['organization_id'] = str(org.id)

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id},
                           params={'channel': 'sms'}, json=valid_data)
    assert response.json()['message'] == 'Verification code has been sent to your phone number.'
    assert response.status_code == 200
    assert send_otp_mock.called is True


@patch('app.routers.v2.auth.send_otp')
@patch('app.settings.OTP_ENABLE_WHATSAPP', True)
def test_post_signup_with_valid_data_succeeds_with_whatsapp(send_otp_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup'

    valid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': fake.first_name(),
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'IamAPassword123!',
    }

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        org = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        organization_id = str(org.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        admn = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=f'{organization_id}',
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{admn.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        GlobalRoleFactory(
            name='Regular User',
            is_global=True,
            organization_id=None
        )
        db.commit()

        UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        send_otp_mock.return_value = {
            'status': 'success',
        }

        valid_data['organization_id'] = str(org.id)

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id},
                           params={'channel': 'whatsapp'}, json=valid_data)
    assert response.json()['message'] == 'Verification code has been sent to your phone number.'
    assert response.status_code == 200
    assert send_otp_mock.called is True


@patch('app.routers.v2.auth.send_otp')
@patch('app.settings.OTP_ENABLE_WHATSAPP', False)
def test_post_signup_with_whatsapp_channel_with_whatsapp_disabled(send_otp_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup'

    valid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': fake.first_name(),
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'IamAPassword123!',
    }

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        org = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        organization_id = str(org.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        admn = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=f'{organization_id}',
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{admn.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        GlobalRoleFactory(
            name='Regular User',
            is_global=True,
            organization_id=None
        )
        db.commit()

        UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        send_otp_mock.return_value = {
            'status': 'success',
        }

        valid_data['organization_id'] = str(org.id)

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id},
                           params={'channel': 'whatsapp'}, json=valid_data)

    assert response.json()['error']['message'][0] == 'Invalid Channel selected. Please contact customer support'
    assert response.status_code == 200
    assert send_otp_mock.called is False


@patch('app.routers.v2.auth.send_otp')
def test_post_signup_with_valid_data_fails_with_invalid_channel(send_otp_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup'

    valid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': fake.first_name(),
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'IamAPassword123!',
    }

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        org = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        organization_id = str(org.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        admn = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=f'{organization_id}',
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{admn.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        GlobalRoleFactory(
            name='Regular User',
            is_global=True,
            organization_id=None
        )
        db.commit()

        UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        send_otp_mock.return_value = {
            'status': 'success',
        }

        valid_data['organization_id'] = str(org.id)

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id},
                           params={'channel': 'dummy_channel'}, json=valid_data)
    assert response.json()['error']['message'][0] == 'Invalid Channel selected. Please contact customer support'
    assert response.status_code == 200
    assert send_otp_mock.called is False


@patch('app.routers.v2.auth.send_otp')
def test_post_signup_with_invalid_data_fails(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }

    url = f'{ROOT_PATH}/api/v1/auth/signup'

    invalid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': '',
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'IamAPassword123!',
    }

    with contextmanager(override_create_session)() as db:
        org = OrganizationFactory()
        db.commit()

        organization_id = str(org.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        invalid_data['organization_id'] = str(org.id)

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=invalid_data)

    assert response.status_code == 200
    assert send_otp_mock.called is False


@patch('app.routers.v2.auth.send_otp')
def test_post_signup_with_unmatched_passwords_fails(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }

    url = f'{ROOT_PATH}/api/v1/auth/signup'

    invalid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': fake.first_name(),
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'i_am_clearly_unmatched',
    }

    with contextmanager(override_create_session)() as db:
        org = OrganizationFactory()
        db.commit()

        organization_id = str(org.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        invalid_data['organization_id'] = str(org.id)

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=invalid_data)
    assert send_otp_mock.called is False
    assert response.json()['error']['message'][0] == 'password_confirmation; Passwords must match.'


@patch('app.routers.v2.auth.verify_otp')
def test_post_verify_with_valid_data_succeeds(verify_otp_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/verify'

    verify_otp_mock.return_value = TwilioVerifyResponse().create('approved')
    valid_data = {
        'phone_number': '',
        'password': 'password',
        'organization_id': ''
    }
    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization_id = str(parent_organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(user_id=str(user.id), organization_id=str(parent_organization.id))
        db.commit()

        membership_extended = MembershipExtendedFactory(membership_id=str(membership.id))
        db.commit()

        vtoken = VerificationTokenFactory(user_id=str(user.id))
        db.commit()

        valid_data['phone_number'] = user.phone_number
        valid_data['token'] = vtoken.token
        valid_data['organization_id'] = str(parent_organization.id)

        response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)

        assert response.status_code == 200
        print(response.json())
        assert len(response.json()['data']['auth_token']) > 0

        membership_extended = db.query(MembershipExtended).get(membership_extended.id)
        assert membership_extended.phone_verified


@patch('app.routers.v2.auth.verify_otp')
def test_post_verify_with_invalid_data_fails(verify_otp_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/verify'

    verify_otp_mock.return_value = TwilioVerifyResponse().create('declined')

    invalid_data = {
    }

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization_id = str(parent_organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(user_id=str(user.id), organization_id=str(parent_organization.id))
        db.commit()

        membership_extended = MembershipExtendedFactory(membership_id=str(membership.id))
        db.commit()

        invalid_data['phone_number'] = user.phone_number
        invalid_data['token'] = 'random'
        invalid_data['organization_id'] = str(parent_organization.id)

        response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=invalid_data)
        assert response.json()['error']['code'] == 2001
        assert response.json()['error']['message'] == ['Invalid token.']
        assert response.status_code == 200

        membership_extended = db.query(MembershipExtended).get(membership_extended.id)
        assert membership_extended.phone_verified is False
        assert membership_extended.email_verified is False


@patch('app.routers.v2.auth.send_otp')
def test_post_resend_otp_with_valid_data_succeeds(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }
    url = f'{ROOT_PATH}/api/v1/auth/signup/resend-token'

    valid_data = {}

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization_id = str(parent_organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(user_id=str(user.id), organization_id=str(parent_organization.id))
        db.commit()

        membership_extended = MembershipExtendedFactory(membership_id=str(membership.id))
        db.commit()

        valid_data['phone_number'] = user.phone_number
        valid_data['organization_id'] = str(parent_organization.id)

        response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
        assert response.status_code == 200
        membership_extended = db.query(MembershipExtended).get(membership_extended.id)
        assert membership_extended.verification_token is not None


@patch('app.routers.v2.auth.send_otp')
@patch('app.settings.OTP_ENABLE_WHATSAPP', True)
def test_post_resend_otp_with_valid_data_succeeds_with_whatsapp(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }
    url = f'{ROOT_PATH}/api/v1/auth/signup/resend-token'

    valid_data = {}

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization_id = str(parent_organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(user_id=str(user.id), organization_id=str(parent_organization.id))
        db.commit()

        membership_extended = MembershipExtendedFactory(membership_id=str(membership.id))
        db.commit()

        valid_data['phone_number'] = user.phone_number
        valid_data['organization_id'] = str(parent_organization.id)

        response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id},
                               params={'channel': 'whatsapp'}, json=valid_data)
        assert response.status_code == 200
        membership_extended = db.query(MembershipExtended).get(membership_extended.id)
        assert membership_extended.verification_token is not None


@patch('app.routers.v2.auth.send_otp')
@patch('app.settings.OTP_ENABLE_WHATSAPP', False)
def test_post_resend_otp_via_whatsapp_with_whatsapp_disabled(send_otp_mock, test_db):
    send_otp_mock.return_value = {
        'status': 'success',
    }
    url = f'{ROOT_PATH}/api/v1/auth/signup/resend-token'

    valid_data = {}

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization_id = str(parent_organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(user_id=str(user.id), organization_id=str(parent_organization.id))
        db.commit()

        membership_extended = MembershipExtendedFactory(membership_id=str(membership.id))
        db.commit()

        valid_data['phone_number'] = user.phone_number
        valid_data['organization_id'] = str(parent_organization.id)

        response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id},
                               params={'channel': 'whatsapp'}, json=valid_data)
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == 'Invalid Channel selected. Please contact customer support'
        assert membership_extended.verification_token is None


def test_signup_user_resend_not_exist(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/resend-token'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)
        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        valid_data = {
            'phone_number': faker_phone_number(fake),
            'organization_id': organization_id
        }

        response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == 'User does not exist.'


def test_signup_user_resend_no_membership(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/resend-token'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)
        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        MembershipFactory(
            user_id=str(user.id),
            organization_id=organization_id
        )
        db.commit()

        valid_data = {
            'phone_number': user.phone_number,
            'organization_id': organization_id
        }

        response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == 'Membership not found.'


def test_signup_user_resend_verified(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/resend-token'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization_id = str(parent_organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(user_id=str(user.id), organization_id=str(parent_organization.id))
        db.commit()

        MembershipExtendedFactory(
            membership_id=str(membership.id),
            email_verified=True,
            phone_verified=False
        )
        db.commit()

        valid_data = {
            'phone_number': user.phone_number,
            'organization_id': organization_id
        }

        response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == 'User already verified.'


def test_post_resend_otp_with_less_than_permitted_fails(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/resend-token'

    valid_data = {}

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization_id = str(parent_organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(user_id=str(user.id), organization_id=str(parent_organization.id))
        db.commit()

        vtoken = VerificationTokenFactory(user_id=str(user.id), created_at=datetime.now() + timedelta(hours=5))
        db.commit()

        MembershipExtendedFactory(membership_id=str(membership.id),
                                  verification_token_id=str(vtoken.id))
        db.commit()

        valid_data['phone_number'] = user.phone_number
        valid_data['organization_id'] = str(parent_organization.id)

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
    assert response.json()['error']['message'] == ['Verification token has not expired yet.']


def test_post_resend_otp_with_invalid_data_fails(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/resend-token'

    valid_data = {}

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        org = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        organization_id = str(org.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        GlobalRoleFactory(
            name='Regular User',
            is_global=True,
        )
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=False,
            verification_method='phone',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        MembershipFactory(
            organization_id=org.id,
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )

        db.commit()

        valid_data['phone_number'] = str(user.phone_number) + '0000000000000000'
        valid_data['organization_id'] = str(org.id)

    response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id}, json=valid_data)
    assert response.status_code == 200


@patch('app.routers.v2.auth.SendGridMail')
def test_migration_email_verify_request(mock_send_grid_mail, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/request-email'

    mock_instance = Mock()
    mock_instance.send_html_mail.return_value = {"status": "success"}
    mock_send_grid_mail.return_value = mock_instance

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id,
            is_migrated=True,
            migration_status=False,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{user.id}'
        )
        db.commit()

        MembershipExtendedFactory(
            membership_id=str(membership.id),
            phone_verified=True
        )
        db.commit()

        valid_data = {
            'email': user.email,
            'organization_id': organization_id
        }
        headers = {'x-api-key': secret_key, 'x-api-sid': organization_id}

        response = client.post(url, headers=headers, json=valid_data)
        assert response.json()['message'] == 'Verification code sent to your email.'
        assert response.status_code == 200
        assert mock_send_grid_mail.call_count == 1
        assert mock_instance.send_html_mail.call_count == 1


@patch('app.routers.v2.auth.SendGridMail')
def test_migration_email_verify_request_not_migrated(mock_send_grid_mail, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/request-email'

    mock_instance = Mock()
    mock_instance.send_html_mail.return_value = {"status": "success"}
    mock_send_grid_mail.return_value = mock_instance

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id,
            is_migrated=False,
            user_type=schema.UserType.regular
        )
        db.commit()

        valid_data = {
            'email': user.email,
            'organization_id': organization_id
        }
        headers = {'x-api-key': secret_key, 'x-api-sid': organization_id}

        response = client.post(url, headers=headers, json=valid_data)
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == 'Email Not Found.'


@patch('app.routers.v2.auth.SendGridMail')
def test_migration_email_verify_request_no_mem(mock_send_grid_mail, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/request-email'

    mock_instance = Mock()
    mock_instance.send_html_mail.return_value = {"status": "success"}
    mock_send_grid_mail.return_value = mock_instance

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id,
            is_migrated=True,
            migration_status=False,
            user_type=schema.UserType.regular
        )
        db.commit()

        MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{user.id}'
        )
        db.commit()

        valid_data = {
            'email': user.email,
            'organization_id': organization_id
        }
        headers = {'x-api-key': secret_key, 'x-api-sid': organization_id}

        response = client.post(url, headers=headers, json=valid_data)
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == 'Membership not found.'


@patch('app.routers.v2.auth.SendGridMail')
def test_migration_email_verify_request_no_pv(mock_send_grid_mail, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/request-email'

    mock_instance = Mock()
    mock_instance.send_html_mail.return_value = {"status": "success"}
    mock_send_grid_mail.return_value = mock_instance

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id,
            is_migrated=True,
            migration_status=False,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{user.id}'
        )
        db.commit()

        MembershipExtendedFactory(
            membership_id=str(membership.id)
        )
        db.commit()

        valid_data = {
            'email': user.email,
            'organization_id': organization_id
        }
        headers = {'x-api-key': secret_key, 'x-api-sid': organization_id}

        response = client.post(url, headers=headers, json=valid_data)
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == ('Please Make Sure Phone Number is '
                                                          'Verified First Before Continuing.')


@patch('app.routers.v2.auth.SendGridMail')
def test_migration_email_verify_request_email_verified(mock_send_grid_mail, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/request-email'

    mock_instance = Mock()
    mock_instance.send_html_mail.return_value = {"status": "success"}
    mock_send_grid_mail.return_value = mock_instance

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id,
            is_migrated=True,
            migration_status=False,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{user.id}'
        )
        db.commit()

        MembershipExtendedFactory(
            membership_id=str(membership.id),
            phone_verified=True,
            email_verified=True
        )
        db.commit()

        valid_data = {
            'email': user.email,
            'organization_id': organization_id
        }
        headers = {'x-api-key': secret_key, 'x-api-sid': organization_id}

        response = client.post(url, headers=headers, json=valid_data)
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == 'Email already verified.'


def test_signup_user_verify_email(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/verify-email'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id,
            is_migrated=True,
            migration_status=False,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{user.id}'
        )
        db.commit()

        vtoken = VerificationTokenFactory(user_id=str(user.id))
        db.commit()

        MembershipExtendedFactory(
            membership_id=str(membership.id),
            phone_verified=True,
            verification_token=vtoken
        )
        db.commit()

        valid_data = {
            'token': vtoken.token,
            'email': user.email,
            'organization_id': organization_id
        }
        headers = {'x-api-key': secret_key, 'x-api-sid': organization_id}

        response = client.post(url, headers=headers, json=valid_data)
        assert response.status_code == 200
        assert response.json()['error'] is None
        response_data = response.json()['data']
        assert response_data['auth_token'] is not None
        assert UUID(response_data['member_id']) == UUID(str(membership.id))
        assert response_data['is_migrated_user'] is True


def test_signup_user_verify_email_not_migrated(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/verify-email'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        valid_data = {
            'token': 'test',
            'email': user.email,
            'organization_id': organization_id
        }
        headers = {'x-api-key': secret_key, 'x-api-sid': organization_id}

        response = client.post(url, headers=headers, json=valid_data)
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == 'User does not exist.'


def test_signup_user_verify_email_invalid(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/signup/verify-email'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id,
            is_migrated=True,
            migration_status=False,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{user.id}'
        )
        db.commit()

        vtoken = VerificationTokenFactory(user_id=str(user.id))
        db.commit()

        MembershipExtendedFactory(
            membership_id=str(membership.id),
            phone_verified=True,
            verification_token=vtoken
        )
        db.commit()

        valid_data = {
            'token': 'invalid',
            'email': user.email,
            'organization_id': organization_id
        }
        headers = {'x-api-key': secret_key, 'x-api-sid': organization_id}

        response = client.post(url, headers=headers, json=valid_data)
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == 'Invalid token.'


@patch('app.routers.v2.auth.SendGridMail')
def test_send_verification_email(mock_send_grid_mail, test_db):
    url = f'{ROOT_PATH}/api/v1/auth/send-verify-email'

    mock_instance = Mock()
    mock_instance.send_html_mail.return_value = {"status": "success"}
    mock_send_grid_mail.return_value = mock_instance

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)
        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            verification_method='email',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{user.id}'
        )
        db.commit()

        MembershipExtendedFactory(
            membership_id=str(membership.id)
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.now() + timedelta(days=1),
                "user_id": f'{user.id}',
                "membership_id": f'{membership.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}

        response = client.post(url, headers=headers)
        assert response.json()['message'] == 'Verification code sent.'
        assert response.status_code == 200
        assert mock_send_grid_mail.call_count == 1
        assert mock_instance.send_html_mail.call_count == 1


def test_send_verification_email_not_found(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/send-verify-email'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)
        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        response = client.post(url, headers={'x-api-key': secret_key, 'x-api-sid': organization_id})
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == 'User not found.'


def test_send_verification_email_verified(test_db):
    url = f'{ROOT_PATH}/api/v1/auth/send-verify-email'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)
        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            verification_method='email',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization_id}',
            user_id=f'{user.id}'
        )
        db.commit()

        MembershipExtendedFactory(
            membership_id=str(membership.id),
            email_verified=True
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.now() + timedelta(days=1),
                "user_id": f'{user.id}',
                "membership_id": f'{membership.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        headers = {'authorization': token, 'x-api-key': secret_key, 'x-api-sid': organization_id}

        response = client.post(url, headers=headers)
        assert response.status_code == 200
        assert response.json()['error']['message'][0] == 'User already verified.'
