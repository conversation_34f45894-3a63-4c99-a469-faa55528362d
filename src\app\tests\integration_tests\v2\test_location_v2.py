from contextlib import contextmanager
from datetime import datetime, timedelta, timezone
from unittest.mock import patch, ANY
import uuid
import secrets

import jwt
import pytest
from fastapi.testclient import TestClient

from app import schema, settings
from app.main import app, ROOT_PATH
from app.tests.factories import (UserFactory, OrganizationFactory, MembershipFactory, ResourceServerFactory,
                                 ResourceFactory, RoleFactory, OrganizationAuthenticationServiceFactory)
from app.tests.mocks.async_client import MockResponse
from app.database import SessionLocal, create_session, Base, engine

client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationAuthenticationServiceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session

LOCATION_BASE_URL = f'{ROOT_PATH}/api/v1/location'
CHARGER_URL_V2_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'

LOCATION_ID = str(uuid.uuid4())
OPERATOR_ID = str(uuid.uuid4())
CHARGE_POINT_ID = str(uuid.uuid4())


@patch('app.routers.v2.location.send_request')
def test_get_native_location_by_distance(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{LOCATION_BASE_URL}/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = 'sort_by=distance&longitude=1.0&latitude=2.0&distance=10.0'
        url = f'{LOCATION_BASE_URL}/?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/distance/?{params}&{params}',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.location.send_request')
def test_get_native_location_by_distance_discount_not_found(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{LOCATION_BASE_URL}/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = 'sort_by=distance&longitude=1.0&latitude=2.0&distance=10.0'
        url = f'{LOCATION_BASE_URL}/?{params}&discounted=True'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        expected_params = f'{params}&discount_type=no_subscription_member&{params}&discounted=True'
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/distance/?{expected_params}',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.location.send_request')
def test_get_native_location_simplified(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{LOCATION_BASE_URL}/marker/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = 'sort_by=distance&longitude=1.0&latitude=2.0&distance=10.0'
        url = f'{LOCATION_BASE_URL}/marker/?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/marker/?{params}&{params}',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.location.send_request')
def test_get_native_location_simplified_discount_not_found(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{LOCATION_BASE_URL}/marker/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = 'sort_by=distance&longitude=1.0&latitude=2.0&distance=10.0'
        url = f'{LOCATION_BASE_URL}/marker/?{params}&discounted=True'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        expected_params = f'{params}&discount_type=no_subscription_member&{params}&discounted=True'
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/marker/?{expected_params}',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.location.send_request')
def test_get_all_location_by_distance(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{LOCATION_BASE_URL}/ocpi.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = 'sort_by=distance&longitude=1.0&latitude=2.0&distance=10.0'
        url = f'{LOCATION_BASE_URL}/ocpi?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/ocpi/?{params}&{params}',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.location.send_request')
def test_get_all_location_by_distance_discount_not_found(send_request_mock, test_db):
    send_request_mock.return_value = MockResponse({}, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{LOCATION_BASE_URL}/ocpi.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = 'sort_by=distance&longitude=1.0&latitude=2.0&distance=10.0'
        url = f'{LOCATION_BASE_URL}/ocpi?{params}&discounted=True'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        expected_params = f'{params}&discount_type=no_subscription_member&{params}&discounted=True'
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/ocpi/?{expected_params}',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.location.send_request')
def test_get_all_location_by_distance_mock_data(send_request_mock, test_db):
    mock_data = {
        'items': [{
            'charge_points': [
                {
                    'id': CHARGE_POINT_ID,
                    'serial_number': 'test_sn',
                    'is_private': True,
                    'location_id': LOCATION_ID,
                    'operator_id': OPERATOR_ID
                }
            ]
        }]
    }
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{LOCATION_BASE_URL}/ocpi.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = 'sort_by=distance&longitude=1.0&latitude=2.0&distance=10.0'
        url = f'{LOCATION_BASE_URL}/ocpi?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/ocpi/?{params}&{params}',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.location.send_request')
def test_get_location_simplified_mock_data(send_request_mock, test_db):
    mock_data = [{
        'charge_points': [
            {
                'id': CHARGE_POINT_ID,
                'serial_number': 'test_sn',
                'is_private': True,
                'location_id': LOCATION_ID,
                'operator_id': OPERATOR_ID
            }
        ]
    }]
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{LOCATION_BASE_URL}/ocpi/marker.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        params = 'sort_by=distance&longitude=1.0&latitude=2.0&distance=10.0'
        url = f'{LOCATION_BASE_URL}/ocpi/marker?{params}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/ocpi/marker/?{params}&{params}',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.location.send_request')
def test_get_ocpi_location_mock_data(send_request_mock, test_db):
    mock_data = {
        'charge_points': [
            {
                'id': CHARGE_POINT_ID,
                'serial_number': 'test_sn',
                'is_private': True,
                'location_id': LOCATION_ID,
                'operator_id': OPERATOR_ID
            }
        ]
    }
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{LOCATION_BASE_URL}/ocpi/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{LOCATION_BASE_URL}/ocpi/{LOCATION_ID}'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/location/ocpi/{LOCATION_ID}',
            headers=ANY
        )
        assert response.status_code == 200


@patch('app.routers.v2.location.send_request')
def test_get_location_by_serial_number_mock_data(send_request_mock, test_db):
    mock_data = {
        'charge_points': [
            {
                'id': CHARGE_POINT_ID,
                'serial_number': 'test_sn',
                'is_private': True,
                'location_id': LOCATION_ID,
                'operator_id': OPERATOR_ID
            }
        ]
    }
    send_request_mock.return_value = MockResponse(mock_data, 200)

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        secret_key = secrets.token_hex(32)

        OrganizationAuthenticationServiceFactory(
            id=organization_id,
            organization_id=organization_id,
            secret_key=schema.argon_ph.hash(secret_key)
        )
        db.commit()

        user = UserFactory(
            is_verified=False,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=(user.id)
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(
            db, mem, str(organization.id), fr'{LOCATION_BASE_URL}/ocpi/serial/.*',
            'get,patch,post,delete', 'apollo-main'
        )

        token = jwt.encode(
            {
                'exp': datetime.now(timezone.utc) + timedelta(days=1),
                'user_id': str(user.id),
                'membership_id': membership_id
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{LOCATION_BASE_URL}/ocpi/serial/test_sn'
        headers = {
            'authorization': token,
            'x-api-key': secret_key,
            'x-api-sid': organization_id
        }
        response = client.get(url, headers=headers)
        assert send_request_mock.call_count == 1
        send_request_mock.assert_called_with(
            'GET',
            f'{CHARGER_URL_V2_PREFIX}/charge_point/ocpi/serial/test_sn',
            headers=ANY
        )
        assert response.status_code == 200
