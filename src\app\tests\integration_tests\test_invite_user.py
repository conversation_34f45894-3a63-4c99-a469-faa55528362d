import random
from contextlib import contextmanager
from unittest.mock import patch
import pytest

from faker import Faker
from sqlalchemy.exc import IntegrityError
from fastapi.testclient import TestClient

from app import crud, schema
from app.main import app, ROOT_PATH
from app.tests.factories import (GlobalRoleFactory, UserFactory, VerificationTokenFactory, OrganizationFactory,
                                 ResourceServerFactory, ResourceFactory,
                                 RoleFactory, MembershipFactory, InviteFactory)
from app.database import SessionLocal, create_session, Base, engine
from app.schema import MembershipType, MembershipResponse

fake = Faker()
client = TestClient(app)


def faker_phone_number(fake: Faker) -> str:
    random_number = ''.join(random.choice('123456789') for _ in range(8))
    random_number = '+601' + random_number
    return random_number


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VerificationTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        InviteFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        GlobalRoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


def test_list_invite_code_returns_only_own_invite_code(test_db):
    organization_id = None
    auth_token = None

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        InviteFactory(user_id=f'{user.id}')

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/invites/?',
                                    'get,patch,post,delete', 'apollo-main')

        # login user and attach auth token to client.get call's header
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/password'

        login_data = {
            'phone_number': user.phone_number,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        assert login_response.status_code == 200
        auth_token = login_response.json().get('auth_token')

    url = f'{ROOT_PATH}/api/v1/csms/invites'
    response = client.get(url, headers={'authorization': auth_token})

    assert response.status_code == 200
    assert len(response.json()) == 1


def test_create_valid_invite_succeeds(test_db):
    organization_id = None
    auth_token = None

    data = {
        'token': fake.color()
    }
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/invites/?',
                                    'get,patch,post,delete', 'apollo-main')

        # login user and attach auth token to client.get call's header
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/password'

        login_data = {
            'phone_number': user.phone_number,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        assert login_response.status_code == 200
        auth_token = login_response.json().get('auth_token')

    url = f'{ROOT_PATH}/api/v1/csms/invites'
    response = client.post(url, json=data, headers={'authorization': auth_token})

    assert response.status_code == 201
    assert len(response.json()['id']) > 1
    assert response.json()['token'] == data['token']


def test_create_own_invite_code_with_already_existing_invite_fails(test_db):
    organization_id = None
    auth_token = None

    data = {
        'token': fake.color()
    }
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        # create existing invite
        InviteFactory(user_id=f'{user.id}')

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/invites/?',
                                    'get,patch,post,delete', 'apollo-main')

        # login user and attach auth token to client.get call's header
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/password'

        login_data = {
            'phone_number': user.phone_number,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        assert login_response.status_code == 200
        auth_token = login_response.json().get('auth_token')

    url = f'{ROOT_PATH}/api/v1/csms/invites'
    response = client.post(url, json=data, headers={'authorization': auth_token})

    assert response.status_code == 400


def test_update_invite_code_succeeds(test_db):
    invite_id = None
    organization_id = None
    auth_token = None

    data = {
        'token': fake.color()
    }
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        # create existing invite
        inv = InviteFactory(user_id=f'{user.id}')

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        invite_id = str(inv.id)

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/invites/.+',
                                    'get,patch,post,delete', 'apollo-main')

        # login user and attach auth token to client.get call's header
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/password'

        login_data = {
            'phone_number': user.phone_number,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        assert login_response.status_code == 200
        auth_token = login_response.json().get('auth_token')

    url = f'{ROOT_PATH}/api/v1/csms/invites/{invite_id}'
    response = client.patch(url, json=data, headers={'authorization': auth_token})

    assert response.status_code == 200
    assert response.json()['token'] == data['token']


def test_update_other_user_invite_code_fails(test_db):
    invite_id = None
    organization_id = None
    auth_token = None

    data = {
        'token': fake.color()
    }
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        other_user = UserFactory(
            is_verified=True,
            verification_method='phone',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        # create existing invite
        inv = InviteFactory(user_id=f'{other_user.id}')

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        invite_id = str(inv.id)

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/invites/.+',
                                    'get,patch,post,delete', 'apollo-main')

        # login user and attach auth token to client.get call's header
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/password'

        login_data = {
            'phone_number': user.phone_number,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        assert login_response.status_code == 200
        auth_token = login_response.json().get('auth_token')

    url = f'{ROOT_PATH}/api/v1/csms/invites/{invite_id}'
    response = client.patch(url, json=data, headers={'authorization': auth_token})

    assert response.status_code == 400


def test_delete_invite_code_succeeds(test_db):
    invite_id = None
    organization_id = None
    auth_token = None

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        # create existing invite
        inv = InviteFactory(user_id=f'{user.id}')

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        invite_id = str(inv.id)

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/invites/.+',
                                    'get,patch,post,delete', 'apollo-main')

        # login user and attach auth token to client.get call's header
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/password'

        login_data = {
            'phone_number': user.phone_number,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        assert login_response.status_code == 200
        auth_token = login_response.json().get('auth_token')

    url = f'{ROOT_PATH}/api/v1/csms/invites/{invite_id}'
    response = client.delete(url, headers={'authorization': auth_token})

    assert response.status_code == 204


def test_delete_other_user_invite_fails(test_db):
    invite_id = None
    organization_id = None
    auth_token = None

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        organization_id = str(organization.id)

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        other_user = UserFactory(
            is_verified=True,
            verification_method='phone',
            organization_id=organization_id,
            user_type=schema.UserType.regular
        )
        db.commit()

        # create existing invite
        inv = InviteFactory(user_id=f'{other_user.id}')

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        invite_id = str(inv.id)

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/invites/.+',
                                    'get,patch,post,delete', 'apollo-main')

        # login user and attach auth token to client.get call's header
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/user/password'

        login_data = {
            'phone_number': user.phone_number,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        assert login_response.status_code == 200
        auth_token = login_response.json().get('auth_token')

    url = f'{ROOT_PATH}/api/v1/csms/invites/{invite_id}'
    response = client.delete(url, headers={'authorization': auth_token})

    assert response.status_code == 400


@patch('app.routers.auth.send_otp')
def test_register_use_valid_invite_code_succeeds(send_otp_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/signup'

    valid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': fake.first_name(),
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'IamAPassword123!',
        'invite_token': ''
    }

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=str(organization.id),
            user_type=schema.UserType.regular
        )
        db.commit()

        # create existing invite
        inv = InviteFactory(user_id=f'{user.id}')

        MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        send_otp_mock.return_value = {
            'status': 'success',
        }

        GlobalRoleFactory(
            name='Regular User',
            is_global=True,
            organization_id=None
        )
        db.commit()

        valid_data['organization_id'] = str(organization.id)
        valid_data['invite_token'] = str(inv.token)

    response = client.post(url, json=valid_data)
    assert response.json()['detail'] == 'Verification code has been sent to your phone number.'
    assert response.status_code == 200
    assert send_otp_mock.called is True


@patch('app.routers.auth.send_otp')
def test_register_use_non_existent_invite_code_fails(send_otp_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/signup'

    valid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': fake.first_name(),
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'IamAPassword123!',
        'invite_token': '',
    }

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=str(organization.id)
        )
        db.commit()

        # create existing invite
        inv = InviteFactory(user_id=f'{user.id}')

        MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        send_otp_mock.return_value = {
            'status': 'success',
        }

        valid_data['organization_id'] = str(organization.id)
        valid_data['invite_token'] = str(inv.token)

        db.delete(inv)
        db.commit()

    response = client.post(url, json=valid_data)

    assert response.status_code == 400


@patch('app.routers.auth.send_otp')
def test_register_use_valid_invite_code_increments_count(send_otp_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/csms/auth/signup'

    valid_data = {
        'email': fake.email(),
        'phone_number': faker_phone_number(fake),
        'first_name': fake.first_name(),
        'last_name': fake.last_name(),
        'password': 'IamAPassword123!',
        'password_confirmation': 'IamAPassword123!',
        'invite_token': ''
    }

    invite_owner_id = None

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()

        user = UserFactory(
            phone_number=faker_phone_number(fake),
            is_verified=True,
            verification_method='phone',
            organization_id=str(organization.id),
            user_type=schema.UserType.regular
        )
        db.commit()

        # create existing invite
        inv = InviteFactory(user_id=f'{user.id}')

        MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        GlobalRoleFactory(
            name='Regular User',
            is_global=True,
            organization_id=None
        )
        db.commit()

        send_otp_mock.return_value = {
            'status': 'success',
        }

        invite_owner_id = str(user.id)

        valid_data['organization_id'] = str(organization.id)
        valid_data['invite_token'] = str(inv.token)

    response = client.post(url, json=valid_data)

    assert response.status_code == 200
    assert send_otp_mock.called is True

    with contextmanager(override_create_session)() as db:
        inv = crud.get_user_invites(db, invite_owner_id)

        assert inv[0].accepted_invite_count == 1
