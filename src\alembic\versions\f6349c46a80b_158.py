"""158

Revision ID: f6349c46a80b
Revises: ead63b185733
Create Date: 2025-04-03 15:04:15.277267

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f6349c46a80b'
down_revision = 'ead63b185733'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_membership_extended', sa.Column('failed_otp_count', sa.Integer(), nullable=True, default=0))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_membership_extended', 'failed_otp_count')
    # ### end Alembic commands ###
