"""154

Revision ID: f3f188fde3f8
Revises: e512a720f0ec
Create Date: 2025-03-25 16:57:14.318015

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f3f188fde3f8'
down_revision = 'e512a720f0ec'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_membership', sa.Column('pdpa_accepted', sa.<PERSON>(), nullable=True, server_default=sa.text('false')))
    op.add_column('main_membership', sa.Column('pdpa_acceptance_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('main_membership', sa.Column('marketing_consent_accepted', sa.<PERSON>an(), nullable=True, server_default=sa.text('false')))
    op.add_column('main_membership', sa.Column('marketing_consent_acceptance_date', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_membership', 'marketing_consent_acceptance_date')
    op.drop_column('main_membership', 'marketing_consent_accepted')
    op.drop_column('main_membership', 'pdpa_acceptance_date')
    op.drop_column('main_membership', 'pdpa_accepted')
    # ### end Alembic commands ###
