"""31

Revision ID: d35249bb1a60
Revises: 71da65405283
Create Date: 2023-03-28 23:57:29.006964

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd35249bb1a60'
down_revision = '71da65405283'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_credit_card', sa.Column('country', sa.String(), nullable=True))
    op.drop_index('only_one_primary_credit_card', table_name='main_credit_card')
    op.create_index('only_one_primary_credit_card', 'main_credit_card', ['primary', 'member_id', 'currency'], unique=True, postgresql_where=sa.text('NOT is_deleted AND "primary"'))
    op.add_column('main_payment_request', sa.Column('country', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_payment_request', 'country')
    op.drop_index('only_one_primary_credit_card', table_name='main_credit_card', postgresql_where=sa.text('NOT is_deleted AND "primary"'))
    op.create_index('only_one_primary_credit_card', 'main_credit_card', ['primary', 'member_id'], unique=True, postgresql_where=sa.text('"primary"'))
    op.drop_column('main_credit_card', 'country')
    # ### end Alembic commands ###
