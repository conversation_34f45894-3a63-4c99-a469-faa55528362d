import uuid

import sqlalchemy as db
from sqlalchemy import and_
from sqlalchemy.orm import validates
from sqlalchemy.dialects.postgresql import UUID, ARRAY, JSONB

from app.models.base import BaseModel, RelationBaseModel
from app.models.vehicle import Vehicle
from app.models.association import membership_vehicle_association_table
from app.schema import MembershipType, ActivityLogType, VerificationMethodEnum, AuditLogType, \
    DunningBlockStatusEnum, DunningAdminBlockStatusEnum


metadata = BaseModel.metadata


class Organization(BaseModel):
    __tablename__ = 'main_organization'
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # https://docs.sqlalchemy.org/en/14/orm/self_referential.html
    parent_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_organization.id'), nullable=True)
    parent = db.orm.relationship('Organization', backref='children', remote_side=[id])

    name = db.Column(db.String)

    logo = db.Column(db.LargeBinary, nullable=True)
    logo_format_type = db.Column(db.String, nullable=True)


db.Index('unique_organization_name', Organization.name, Organization.is_deleted,
         unique=True, postgresql_where=~Organization.is_deleted)

cpo_membership_association_table = db.Table(
    'cpo_membership_association',
    BaseModel.metadata,
    db.Column('main_operator_id', db.ForeignKey('main_operator.id', ondelete='CASCADE')),
    db.Column('main_membership_id', db.ForeignKey('main_membership.id', ondelete='CASCADE')),
)


class Operator(BaseModel):
    __tablename__ = 'main_operator'

    organization_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_organization.id'), nullable=True)
    organization = db.orm.relationship('Organization', backref='operators')

    memberships = db.orm.relationship('Membership', secondary=cpo_membership_association_table,
                                      back_populates='operators')

    name = db.Column(db.String)
    email = db.Column(db.String)
    description = db.Column(db.String)
    phone_number = db.Column(db.String)
    website = db.Column(db.String)
    meta = db.Column(JSONB)
    whatsapp_number = db.Column(db.String)
    images = db.Column(JSONB)
    party_id = db.Column(db.String)
    country_code = db.Column(db.String)
    is_taxable = db.Column(db.Boolean, default=False)
    is_private = db.Column(db.Boolean, default=False)
    ac_default_price = db.Column(db.Numeric(scale=4), default=0)
    dc_default_price = db.Column(db.Numeric(scale=4), default=0)


role_resource_association_table = db.Table(
    'role_resource_association',
    BaseModel.metadata,
    db.Column('main_role_id', db.ForeignKey('main_role.id', ondelete='CASCADE')),
    db.Column('main_resource_id', db.ForeignKey('main_resource.id', ondelete='CASCADE')),
)

membership_role_association_table = db.Table(
    'membership_role_association',
    BaseModel.metadata,
    db.Column('main_membership_id', db.ForeignKey('main_membership.id', ondelete='CASCADE')),
    db.Column('main_role_id', db.ForeignKey('main_role.id', ondelete='CASCADE')),
)


class OperatorChargepoint(RelationBaseModel):
    __tablename__ = 'main_operator_charge_point'

    charge_point_id = db.Column(UUID(as_uuid=True), nullable=False)
    operator_id = db.Column(UUID(as_uuid=True),
                            db.ForeignKey('main_operator.id', ondelete='CASCADE'), nullable=False)

    operator = db.orm.relationship('Operator', backref='charge_points')

    __table_args__ = (
        db.schema.UniqueConstraint('charge_point_id', 'operator_id', name='_unique_operator_charge_point'),
    )


class OperatorPowercable(RelationBaseModel):
    __tablename__ = 'main_operator_power_cable'

    power_cable_id = db.Column(UUID(as_uuid=True), nullable=False)
    operator_id = db.Column(UUID(as_uuid=True),
                            db.ForeignKey('main_operator.id', ondelete='CASCADE'), nullable=False)

    operator = db.orm.relationship('Operator', backref='power_cables')

    __table_args__ = (
        db.schema.UniqueConstraint('power_cable_id', 'operator_id', name='_unique_operator_power_cable'),
    )


class PartnerChargepoint(RelationBaseModel):
    __tablename__ = 'main_partner_charge_point'

    charge_point_id = db.Column(UUID(as_uuid=True), nullable=False)
    partner_id = db.Column(UUID(as_uuid=True),
                           db.ForeignKey('main_external_organization.id', ondelete='CASCADE'), nullable=False)

    partner = db.orm.relationship('ExternalOrganization', backref='charge_points')

    __table_args__ = (
        db.schema.UniqueConstraint('charge_point_id', 'partner_id', name='_unique_partner_charge_point'),
    )


class SharedOperator(RelationBaseModel):
    __tablename__ = 'main_shared_operator'
    type = db.Column(db.String)
    organization_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_organization.id'), nullable=False)
    operator_id = db.Column(UUID(as_uuid=True),
                            db.ForeignKey('main_operator.id', ondelete='CASCADE'), nullable=False)

    organization = db.orm.relationship('Organization', backref='shared_operators')
    operator = db.orm.relationship('Operator', backref='shared_organizations')

    __table_args__ = (
        db.schema.UniqueConstraint('organization_id', 'operator_id', name='_unique_operator_organization'),
    )


class Role(BaseModel):
    """
    Role Model

    Can be created by Staff to be assigned to Users' Membership.
    Each Organization can create and customize their own set of Roles.
    A Membership can have multiple Roles.
    A Role is a collection of Resources.
    """
    __tablename__ = 'main_role'

    organization_id = db.Column(UUID(as_uuid=True),
                                db.ForeignKey('main_organization.id',
                                              ondelete='CASCADE'), nullable=True)
    organization = db.orm.relationship('Organization', passive_deletes=True)

    name = db.Column(db.String)
    is_global = db.Column(db.Boolean, default=False)

    resources = db.orm.relationship('Resource', secondary=role_resource_association_table,
                                    back_populates='roles')
    memberships = db.orm.relationship('Membership', secondary=membership_role_association_table,
                                      back_populates='roles')


class ResourceServer(BaseModel):
    __tablename__ = 'main_resourceserver'

    url = db.Column(db.String)
    name = db.Column(db.String)
    root_path = db.Column(db.String)

    resources = db.orm.relationship('Resource', back_populates='resourceserver')


db.Index('unique_resource_server_url', ResourceServer.url, ResourceServer.is_deleted,
         unique=True, postgresql_where=~ResourceServer.is_deleted)


class Resource(BaseModel):
    __tablename__ = 'main_resource'

    name = db.Column(db.String, nullable=True)
    path = db.Column(db.String)
    scope = db.Column(db.String)  # allowed methods e.g.: get,update,post,delete

    require_auth_token = db.Column(db.Boolean)

    resourceserver_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_resourceserver.id'))
    resourceserver = db.orm.relationship('ResourceServer', back_populates='resources')

    roles = db.orm.relationship('Role', secondary=role_resource_association_table,
                                back_populates='resources', passive_deletes=True)

    __table_args__ = (
        db.schema.UniqueConstraint('path', 'scope', name='_path_scope_uc'),
    )


class Membership(BaseModel):
    __tablename__ = 'main_membership'

    first_name = db.Column(db.String)
    last_name = db.Column(db.String)

    labels = db.Column(ARRAY(db.Text), default=[])

    organization_id = db.Column(UUID(as_uuid=True),
                                db.ForeignKey('main_organization.id', ondelete='CASCADE'))
    organization = db.orm.relationship('Organization', passive_deletes=True)

    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_auth_user.id'))

    user = db.orm.relationship('User', passive_deletes=True)

    preferred_payment_method = db.Column(db.String, default='Credit-Card')
    pre_auth_info = db.orm.relationship('MemberPreAuthInfo', back_populates='member')

    preferred_payment_flow = db.Column(db.String, default='Credit-Card')
    password = db.Column(db.String)

    roles = db.orm.relationship('Role', secondary=membership_role_association_table, passive_deletes=True)

    membership_type = db.Column(db.Enum(MembershipType), default=MembershipType.regular_user)

    operators = db.orm.relationship('Operator', secondary=cpo_membership_association_table,
                                    back_populates='memberships', passive_deletes=True)

    favorite_charge_points = db.Column(ARRAY(db.Text), default=[])

    user_id_tag = db.Column(db.String)

    dunning_details = db.orm.relationship(
        'MembershipDunning',
        uselist=False,
        back_populates='membership',
        cascade="all, delete-orphan"
    )

    @validates('user_id_tag')
    def validate_user_id_tag(self, key, value):
        if value:
            # Replace spaces with underscores
            value = value.replace(' ', '_')
        return value

    vehicles = db.orm.relationship(
        "Vehicle",
        secondary=membership_vehicle_association_table,
        secondaryjoin=and_(
            membership_vehicle_association_table.c.main_vehicle_id == Vehicle.id,
            Vehicle.is_deleted.is_(False)
        ),
        # viewonly=True  # Add this if the relationship is meant to be read-only
    )
    vehicle_model = db.Column(db.String)
    allow_marketing = db.Column(db.Boolean, default=False)

    favorite_locations = db.Column(ARRAY(db.Text), default=[])
    ruby_user_id = db.Column(db.BigInteger)

    # Enums: New (no session), Recent (1-5 sessions), Premium (more than X sessions)
    membership_segment = db.Column(db.String)

    pdpa_accepted = db.Column(db.Boolean, default=False)
    pdpa_acceptance_date = db.Column(db.DateTime(timezone=True))
    marketing_consent_accepted = db.Column(db.Boolean, default=False)
    marketing_consent_acceptance_date = db.Column(db.DateTime(timezone=True))

    __table_args__ = (
        db.schema.UniqueConstraint('user_id', 'organization_id', 'membership_type', name='_org_user_type_uc'),
    )


db.Index('unique_membership_id_tag', db.func.lower(Membership.user_id_tag), Membership.is_deleted,
         unique=True, postgresql_where=~Membership.is_deleted)


class MemberPreAuthInfo(BaseModel):
    __tablename__ = 'main_pre_auth_info'

    member_id = db.Column(UUID(as_uuid=True),
                          db.ForeignKey('main_membership.id', ondelete='SET NULL'),
                          nullable=True)
    member = db.orm.relationship('Membership', back_populates='pre_auth_info')
    pre_auth_amount = db.Column(db.Numeric(scale=2))
    pre_auth_currency = db.Column(db.String)
    pre_auth_type = db.Column(db.String)
    charger_type = db.Column(db.String, default='AC')


class ActivityLog(BaseModel):
    __tablename__ = 'main_activity_log'

    user_id = db.Column(UUID(as_uuid=True),
                        db.ForeignKey('main_auth_user.id', ondelete='SET NULL'),
                        nullable=True)
    user = db.orm.relationship('User', passive_deletes=True)

    table_name = db.Column(db.String)
    data = db.Column(JSONB)
    type = db.Column(db.Enum(ActivityLogType))


class MembershipExtended(BaseModel):
    # To-prevent more and more table from being created, this use an extended methodology
    # For regular user, the user will have extended functionality for example both verification checking
    # Also, the verification instead of bound to user, will be bound to membership
    # Further processing are to handled in the controller level

    __tablename__ = 'main_membership_extended'

    membership_id = db.Column(UUID(as_uuid=True),
                              db.ForeignKey('main_membership.id', ondelete='CASCADE'),
                              nullable=False)

    membership = db.orm.relationship('Membership', passive_deletes=True)

    verification_method = db.Column(db.String, default=VerificationMethodEnum.phone)

    email_verified = db.Column(db.Boolean, default=False)

    phone_verified = db.Column(db.Boolean, default=False)

    verification_token_id = db.Column(UUID(as_uuid=True),
                                      db.ForeignKey('main_auth_token.id', ondelete='CASCADE'), nullable=True)

    verification_token = db.orm.relationship('VerificationToken', passive_deletes=True)

    reset_password_token_id = db.Column(UUID(as_uuid=True),
                                        db.ForeignKey('main_reset_password_token.id', ondelete='CASCADE'),
                                        nullable=True)

    reset_password_token = db.orm.relationship('ResetPasswordToken', passive_deletes=True)
    failed_otp_count = db.Column(db.Integer, default=0)


class MembershipDunning(BaseModel):
    # This table is created to keep track of the member's dunning detail statuses

    __tablename__ = 'main_membership_dunning'

    membership_id = db.Column(UUID(as_uuid=True),
                              db.ForeignKey('main_membership.id', ondelete='CASCADE'),
                              nullable=False)

    membership = db.orm.relationship('Membership', back_populates='dunning_details')

    block_date = db.Column(db.DateTime(timezone=True))

    block_status = db.Column(db.String, default=DunningBlockStatusEnum.available)

    block_reason = db.Column(db.String)

    admin_block_date = db.Column(db.DateTime(timezone=True))

    admin_block_status = db.Column(db.String, default=DunningAdminBlockStatusEnum.no_status)

    admin_block_remark = db.Column(db.String)

    dunnings = db.Column(JSONB, default={})

    all_outstanding_bill_ids = db.Column(JSONB)

    total_outstanding_amount = db.Column(db.Numeric(scale=2))


class DunningRepaymentLog(BaseModel):
    __tablename__ = 'main_dunning_repayment_log'

    member_id = db.Column(UUID(as_uuid=True),
                          db.ForeignKey('main_membership.id', ondelete='CASCADE'),
                          nullable=False)
    member = db.orm.relationship('Membership', passive_deletes=True)

    bill_id = db.Column(UUID(as_uuid=True),
                        db.ForeignKey('main_charging_session_bill.id', ondelete='CASCADE'),
                        nullable=False)
    charging_session_bill = db.orm.relationship('ChargingSessionBill', passive_deletes=True)
    charging_session_id = db.Column(db.String)
    payment_amount = db.Column(db.Numeric(scale=2))
    payment_payload = db.Column(JSONB, default={})
    payment_response = db.Column(JSONB, default={})


class ManualLinkingToken(BaseModel):
    # To store email verification token for targeted user, and migrated user

    __tablename__ = 'main_manual_linking_token'

    migrated_member_id = db.Column(UUID(as_uuid=True),
                                   db.ForeignKey('main_membership.id', ondelete='CASCADE'),
                                   nullable=False)

    migrated_member = db.orm.relationship('Membership', foreign_keys=[migrated_member_id], passive_deletes=True)

    target_member_id = db.Column(UUID(as_uuid=True),
                                 db.ForeignKey('main_membership.id', ondelete='CASCADE'),
                                 nullable=False)

    target_member = db.orm.relationship('Membership', foreign_keys=[target_member_id], passive_deletes=True)

    token = db.Column(db.String)
    expiration = db.Column(db.DateTime(timezone=True))
    is_used = db.Column(db.Boolean, default=False)


class OrganizationAuthenticationService(BaseModel):
    """
    Organization Authentication Services Model
    This table is created to store the hashed API Key associated to
    each organization upon organization creation.
    An organization can only have one active API Key.
    Existing API Key of an organization will be replaced upon regeneration.
    """
    __tablename__ = 'main_organization_authentication_services'

    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    organization_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_organization.id'))
    organization = db.orm.relationship('Organization', backref='organization')

    name = db.Column(db.String)
    secret_key = db.Column(db.String, unique=True)
    meta = db.Column(JSONB, default={})
    is_editable = db.Column(db.Boolean, default=False)
    expiry_date = db.Column(db.DateTime(timezone=True))

    partner_id = db.Column(UUID(as_uuid=True),
                           db.ForeignKey('main_external_organization.id', ondelete='CASCADE'), nullable=True)
    partner = db.orm.relationship('ExternalOrganization', backref='external_organization')


class ExternalOrganization(BaseModel):
    __tablename__ = 'main_external_organization'
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    party_id = db.Column(db.String(3), nullable=False)
    country_code = db.Column(db.String(2), nullable=False)
    images = db.Column(JSONB)
    website = db.Column(db.String)
    description = db.Column(db.String, nullable=True)
    meta = db.Column(JSONB)
    phone_number = db.Column(db.String)
    whatsapp_number = db.Column(db.String)
    email = db.Column(db.String)
    friendly_name = db.Column(db.String, nullable=True)
    organization_type = db.Column(db.String, nullable=True, default='OCPI')
    roaming_organization = db.Column(UUID(as_uuid=True))
    name = db.Column(db.String)

    # This display_as_operator is meant for special handling, where, when we publish to OCPI partner
    # we will publish as operator's info
    # Lets say, if display_as_operator is set as TRUE, and the friendly_name is "Ali",
    # we will push the chargepoint operator as "Ali" instead of null
    # The charge point will push as - "charge_point['operator'] - BusinessDetails"

    display_as_operator = db.Column(db.Boolean, default=False)
    # ac_default_price = db.Column(db.Numeric(scale=4), default=0)
    # dc_default_price = db.Column(db.Numeric(scale=4), default=0)
    party_prefix_trim_count = db.Column(db.Integer, default=7)
    party_suffix_trim_count = db.Column(db.Integer, default=1)


class TaxRate(BaseModel):
    __tablename__ = 'main_tax_rate'
    country = db.Column(db.String)
    tax_rate = db.Column(db.Numeric(scale=2), default=0.0)
    enforce_tax_on_ocpi = db.Column(db.Boolean, default=False)
    ocpi_additional_tax_rate = db.Column(db.Numeric(scale=2), default=0.0)


class PageResource(BaseModel):
    __tablename__ = 'main_page_resource'

    name = db.Column(db.String)
    view = db.Column(JSONB)
    create = db.Column(JSONB)
    edit = db.Column(JSONB)
    delete = db.Column(JSONB)
    manage = db.Column(JSONB)


class UserAuditLog(BaseModel):
    __tablename__ = 'main_user_audit_log'

    user_id = db.Column(UUID(as_uuid=True),
                        db.ForeignKey('main_auth_user.id', ondelete='SET NULL'))
    user = db.orm.relationship('User')

    membership_id = db.Column(UUID(as_uuid=True),
                              db.ForeignKey('main_membership.id', ondelete='SET NULL'))
    membership = db.orm.relationship('Membership')

    api = db.Column(db.String)
    table_name = db.Column(db.String)
    data_before = db.Column(JSONB)
    data_after = db.Column(JSONB)
    action = db.Column(db.Enum(AuditLogType))
    group_id = db.Column(UUID(as_uuid=True))
    module = db.Column(db.String)
    object_id = db.Column(JSONB)
    remarks = db.Column(JSONB)


class UserAccessOperator(BaseModel):
    __tablename__ = 'main_user_access_operator'
    operator_id = db.Column(UUID(as_uuid=True),
                            db.ForeignKey('main_operator.id', ondelete='CASCADE'), nullable=False)
    membership_id = db.Column(UUID(as_uuid=True),
                              db.ForeignKey('main_membership.id', ondelete='CASCADE'),
                              nullable=False)
    member = db.orm.relationship('Membership')
