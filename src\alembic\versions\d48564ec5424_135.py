"""135

Revision ID: d48564ec5424
Revises: cd73217c643e
Create Date: 2024-11-27 16:14:47.589673

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd48564ec5424'
down_revision = 'cd73217c643e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_auth_user', sa.Column('migrated_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('main_auth_user', sa.Column('link_date', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_auth_user', 'link_date')
    op.drop_column('main_auth_user', 'migrated_date')
    # ### end Alembic commands ###
