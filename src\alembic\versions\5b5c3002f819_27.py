"""27

Revision ID: 5b5c3002f819
Revises: 31e9ff528e55
Create Date: 2023-01-11 10:00:36.405584

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5b5c3002f819'
down_revision = '31e9ff528e55'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('only_one_default_plan', table_name='main_subscription', postgresql_where=sa.text('is_default'))
    op.create_index('only_one_default_plan', 'main_subscription', ['is_default', 'member_id'], unique=True, postgresql_where=sa.text('is_default AND NOT is_deleted'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('only_one_default_plan', table_name='main_subscription', postgresql_where=sa.text('is_default AND NOT is_deleted'))
    op.create_index('only_one_default_plan', 'main_subscription', ['is_default', 'member_id'], unique=True, postgresql_where=sa.text('is_default'))
    # ### end Alembic commands ###
