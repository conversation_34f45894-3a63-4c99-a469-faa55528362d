"""100

Revision ID: 55187f5a67df
Revises: 2da260f521b8
Create Date: 2024-06-03 23:10:29.609957

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '55187f5a67df'
down_revision = '2da260f521b8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_cybersource_enroll_step', sa.Column('screen_width', sa.String(), nullable=True))
    op.add_column('main_cybersource_enroll_step', sa.Column('screen_height', sa.String(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_cybersource_enroll_step', 'screen_height')
    op.drop_column('main_cybersource_enroll_step', 'screen_width')
    # ### end Alembic commands ###
