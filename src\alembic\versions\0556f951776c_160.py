"""160”

Revision ID: 0556f951776c
Revises: 14a739421ad0
Create Date: 2025-04-23 13:04:45.992082

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0556f951776c'
down_revision = '14a739421ad0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_payment_refund',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>an(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('payment_request_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('refund_amount', sa.String(), nullable=True),
    sa.Column('refund_status', sa.String(), nullable=True),
    sa.Column('refund_type', sa.String(), nullable=True),
    sa.Column('remark', sa.String(), nullable=True),
    sa.Column('meta', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('reference_id', sa.String(), nullable=True),
    sa.Column('pg_refund_id', sa.String(), nullable=True),
    sa.Column('pg_refund_response', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('pg_refund_callback', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['payment_request_id'], ['main_payment_request.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('reference_id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_payment_refund')
    # ### end Alembic commands ###
