from datetime import datetime
from typing import Optional, List
from uuid import UUID

from pydantic import BaseModel, constr

from .emaid import EmaidHistory, EmaidResponse
from .autocharge import AutochargeResponse


class VehicleBase(BaseModel):
    '''Vehicle Base Schema'''
    name: Optional[str]  # pylint:disable=unsubscriptable-object
    model: Optional[str]  # pylint:disable=unsubscriptable-object
    brand: Optional[str]  # pylint:disable=unsubscriptable-object
    registration_number: Optional[str]  # pylint:disable=unsubscriptable-object


class Vehicle(VehicleBase):
    pcid: Optional[str]  # pylint:disable=unsubscriptable-object


class VehicleRegistration(BaseModel):
    name: Optional[str]  # pylint:disable=unsubscriptable-object
    model: Optional[str]  # pylint:disable=unsubscriptable-object
    brand: Optional[str]  # pylint:disable=unsubscriptable-object
    pcid: constr(regex=r'^[a-zA-Z0-9]{17,18}$')
    registration_number: str


class VehicleUpdate(BaseModel):
    '''Vehicle Update Schema'''
    name: Optional[str]  # pylint: disable=unsubscriptable-object
    model: Optional[str]  # pylint: disable=unsubscriptable-object
    brand: Optional[str]  # pylint: disable=unsubscriptable-object
    connector_type_id: Optional[UUID]  # pylint: disable=unsubscriptable-object
    acceptance_rate: Optional[float]  # pylint: disable=unsubscriptable-object
    battery_capacity: Optional[float]  # pylint: disable=unsubscriptable-object
    suspend_timer: Optional[int]  # pylint: disable=unsubscriptable-object
    pcid: Optional[constr(regex=r'^(|[a-zA-Z0-9]{17,18})$')]   # type: ignore  # pylint: disable=unsubscriptable-object
    registration_number: Optional[str]  # pylint: disable=unsubscriptable-object
    mac_address: Optional[str]  # pylint: disable=unsubscriptable-object
    ac_status: Optional[str]  # pylint: disable=unsubscriptable-object


class VehicleHistory(Vehicle):
    emaid: EmaidHistory

    class Config:
        orm_mode = True


class VehicleHistoryUpdate(BaseModel):
    vehicle_histories: list[dict]


class CreateVehicleResponse(BaseModel):
    detail: str
    vehicle_id: UUID

    class Config:
        orm_mode = True


class DeleteVehicleResponse(BaseModel):
    detail: str

    class Config:
        orm_mode = True


class VehicleResponse(Vehicle):
    emaid: Optional[List[EmaidResponse]]  # pylint:disable=unsubscriptable-object
    vehicle_histories: Optional[list] = []  # pylint:disable=unsubscriptable-object
    autocharge: Optional[AutochargeResponse]  # pylint:disable=unsubscriptable-object

    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]  # pylint:disable=unsubscriptable-object

    class Config:
        orm_mode = True


class AutochargeVehicleResponse(BaseModel):
    '''Autocharge Response Schema'''
    id: UUID
    mac_address: Optional[str]  # pylint: disable=unsubscriptable-object
    status: str
    vehicle_id: UUID
    expiry: Optional[str]  # pylint: disable=unsubscriptable-object

    class Config:
        orm_mode = True


class VehicleResponseNoHistories(Vehicle):
    emaid: List[EmaidResponse]
    autocharge: Optional[AutochargeVehicleResponse] = {}  # pylint: disable=unsubscriptable-object

    id: UUID
    created_at: Optional[datetime]  # pylint:disable=unsubscriptable-object
    updated_at: Optional[datetime]  # pylint:disable=unsubscriptable-object

    class Config:
        orm_mode = True
