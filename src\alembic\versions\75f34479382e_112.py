"""112

Revision ID: 75f34479382e
Revises: f3b1fc4e2cf4
Create Date: 2024-07-29 22:58:36.009004

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '75f34479382e'
down_revision = 'f3b1fc4e2cf4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_trace_malloc', sa.Column('mem_allocated', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_trace_malloc', 'mem_allocated')
    # ### end Alembic commands ###
