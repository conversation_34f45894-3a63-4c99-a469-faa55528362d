import datetime
import ssl
from pathlib import Path
import pytz
from kombu import Queue
from celery import Celery
from celery.signals import heartbeat_sent, worker_ready, worker_shutdown
from celery.schedules import crontab

from app.settings import CELERY_BROKER_URL, LTA_MAIN_WORKER_EXCHANGE, WORKER_EXCHANGE_TYPE, RABBITMQ_IS_SECURE, \
    LTA_TASK_SPLIT_MODE

LTA_HEARTBEAT_FILE = Path("/tmp/lta_worker_heartbeat")  # nosec
LTA_READINESS_FILE = Path("/tmp/lta_worker_ready")  # nosec


# Liveness prob for celery/beat workers
@heartbeat_sent.connect
def heartbeat(**_):
    LTA_HEARTBEAT_FILE.touch()


@worker_ready.connect
def worker_ready(**_):
    LTA_READINESS_FILE.touch()


@worker_shutdown.connect
def worker_shutdown(**_):
    for f in (LTA_HEARTBEAT_FILE, LTA_READINESS_FILE):
        f.unlink()


if RABBITMQ_IS_SECURE:
    app = Celery('apollo-lta-charger', broker=CELERY_BROKER_URL, include='app.lta_tasks', broker_use_ssl={
        'ssl_version': ssl.PROTOCOL_TLS_CLIENT,

    }, backend='rpc://')
else:
    app = Celery('apollo-lta-charger', broker=CELERY_BROKER_URL, include='app.lta_tasks', backend='rpc://')

app.conf.task_default_queue = 'default'
app.conf.task_always_eager = False
app.conf.task_default_exchange = LTA_MAIN_WORKER_EXCHANGE
app.conf.task_default_exchange_type = WORKER_EXCHANGE_TYPE
app.conf.task_default_queue = 'default'
app.conf.task_default_routing_key = 'default'

if LTA_TASK_SPLIT_MODE == "SPLIT_REPORTING":
    app.conf.task_queues = (
        Queue('default', routing_key='default'),
        Queue('main_reporting_worker', routing_key='main_reporting_worker'),
    )
elif LTA_TASK_SPLIT_MODE == 'SPLIT_LTA':
    app.conf.task_queues = (
        Queue('default', routing_key='default'),
        Queue('main_lta_worker', routing_key='main_lta_worker'),
    )
else:
    app.conf.task_queues = (
        Queue('default', routing_key='default'),
        Queue('main_lta_worker', routing_key='main_lta_worker'),
        Queue('main_reporting_worker', routing_key='main_reporting_worker'),
    )

# app.conf.task_reject_on_worker_lost = True
# app.conf.task_acks_late = True


def now_func():
    return datetime.datetime.now(pytz.timezone('Asia/Kuala_Lumpur'))


app.conf.beat_schedule = {
    'apollo.main.tasks.generate_lta_static_excel.monthly': {
        'task': 'apollo.main.lta_main_tasks.generate_lta_static_excel',
        'schedule': crontab(hour=19, minute=30, day_of_month='1'),  # 3:30 AM GMT+8 is 19:30 the previous day in UTC
        'options': {
            'queue': 'main_lta_worker',
            'routing_key': 'main_lta_worker'
        },
    }
}
app.conf.timezone = 'UTC'
