import pytest
import uuid
from datetime import datetime, timedelta

from app.database import Base, engine
from app.routers.payments import (is_valid_uuid, filter_dashboard_energy, filter_emsp
                                  )


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.mark.asyncio
async def test_is_valid_uuid():
    valid_uuid = uuid.uuid4()

    result = is_valid_uuid(valid_uuid)
    assert result is True


@pytest.mark.asyncio
async def test_is_valid_uuid_invalid():
    result = is_valid_uuid('invalid')
    assert result is False


@pytest.mark.asyncio
async def test_filter_dashboard_energy():
    type = 'Day'
    result = await filter_dashboard_energy(type)
    assert result.get('type') == 'Day'
    assert result['date_start'] is not None
    assert result['date_end'] is not None
