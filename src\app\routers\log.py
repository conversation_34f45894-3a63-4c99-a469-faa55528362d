"""Module serving logs"""

import logging
from datetime import datetime
from fastapi import APIRouter, HTTPException, Request, Depends, status
from fastapi_pagination import Params, Page
from app import settings, crud, exceptions, schema
from app.database import create_session, SessionLocal


logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/log",
    tags=['log', ],
)


async def log_filters(
        from_: datetime = None,
        to: datetime = None,
) -> dict:
    '''Filter for Provider logs'''
    filter_dict = {}
    if from_:
        filter_dict['from_'] = from_
    if to:
        filter_dict['to'] = to
    return filter_dict


@router.get("/pnc_logs",
            status_code=status.HTTP_200_OK)
async def get_pnc_logs(
    request: Request,
    dbsession: SessionLocal = Depends(create_session)
):
    """
    Get PnC logs
    """
    try:
        pnclogs = crud.get_provider_log(dbsession)
        return pnclogs
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/pnc_logs_data", response_model=Page[schema.ProviderLogResponse],
            status_code=status.HTTP_200_OK)
async def get_pnc_logs_data(request: Request, params: Params = Depends(),
                            log_filters: dict = Depends(log_filters),
                            db: SessionLocal = Depends(create_session)):
    """
    Get PnC logs
    """
    try:
        logs = await crud.get_provider_log_data(
            db, params=params, filters=log_filters)

        return logs
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())
