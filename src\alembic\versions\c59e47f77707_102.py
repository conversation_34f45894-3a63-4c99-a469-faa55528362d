"""102

Revision ID: c59e47f77707
Revises: 0a939349e661
Create Date: 2024-06-06 11:19:55.423070

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c59e47f77707'
down_revision = '0a939349e661'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_subscription_plan', sa.Column('payment_currency', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_subscription_plan', 'payment_currency')
    # ### end Alembic commands ###
