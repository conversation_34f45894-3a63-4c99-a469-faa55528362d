from contextlib import contextmanager
import uuid
from attr import Factory
import pytest
from unittest.mock import patch
from datetime import datetime, timed<PERSON>ta
from faker import Faker
from fastapi.testclient import TestClient
from jose import jwt
from app import schema, settings
from xml.etree.ElementInclude import include
from app.constants import AUTHORIZATION_METHOD_ERRORS, AUTHORIZATION_METHOD_SUCCESS_MSG
from app.main import app, ROOT_PATH
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory, )
from app.database import SessionLocal, create_session, Base, engine
from app.schema import MembershipType, ActivityLogType
from app.models import ActivityLog
from app.tests.mocks.async_client import MockAsyncClientGeneratorChargepoint

fake = Faker()
client = TestClient(app)

SUCCESS_MSG = AUTHORIZATION_METHOD_SUCCESS_MSG
ERRORS = AUTHORIZATION_METHOD_ERRORS


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


# Operator(CPO) tests

def test_list_operator_succeeds(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/operator'

    auth_token = None
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=str(organization.id)
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/operator/?',
                                    'get,patch,post,delete', 'apollo-main')

        organization_id = str(organization.id)
        OperatorFactory(organization_id=organization_id)
        db.commit()

        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': user.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    response = client.get(url, headers={'authorization': auth_token})

    assert response.status_code == 200
    assert len(response.json()['items']) == 1


def test_get_operator_succeeds(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/operator'

    auth_token = None
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=str(organization.id)
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/operator/.+',
                                    'get,patch,post,delete', 'apollo-main')

        organization_id = str(organization.id)
        operator = OperatorFactory(organization_id=organization_id)
        db.commit()
        operator_id = str(operator.id)

        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': user.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    response = client.get(url + f'/{operator_id}', headers={'authorization': auth_token})

    assert response.status_code == 200
    assert response.json()['id'] == operator_id


def test_create_operator_succeeds(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/operator'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization_id = str(organization.id)

        data = {
            'name': 'name',
            'organization_id': organization_id,
            'email': '<EMAIL>',
            'description': 'Operator 123',
            'website': 'https://www.yinson.com',
            'phone_number': '+60123456789',
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/operator/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    response = client.post(url, json=data, headers={'authorization': auth_token})
    assert response.status_code == 201
    assert response.json()['organization_id'] == organization_id
    assert response.json()['id'] is not None

    with contextmanager(override_create_session)() as db:
        db_activity = db.query(ActivityLog).order_by(ActivityLog.created_at).first()
        assert db_activity.type == ActivityLogType.create


def test_create_operator_fails(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/operator'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        data = {
            'name': 'name',
            'organization_id': organization_id,
            'email': '<EMAIL>',
            'description': 'Operator 123',
            'website': 'https://www.yinson.com',
            'phone_number': '+60123456789',
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization.id), f'{ROOT_PATH}/api/v1/csms/operator/?',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()

        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    response = client.post(url, json=data, headers={'authorization': auth_token})
    assert response.status_code == 400


def test_update_operator_succeeds(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/operator'

    with contextmanager(override_create_session)() as db:
        parent_organization = OrganizationFactory()
        db.commit()

        organization1 = OrganizationFactory(parent_id=f'{parent_organization.id}')
        organization2 = OrganizationFactory(parent_id=f'{parent_organization.id}')
        db.commit()
        organization1_id = str(organization1.id)
        organization2_id = str(organization2.id)
        operator = OperatorFactory(organization_id=organization1_id)
        db.commit()
        operator_id = str(operator.id)

        data = {
            'organization_id': organization2_id
        }

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization1_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization1_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization1.id), f'{ROOT_PATH}/api/v1/csms/operator/.+',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization1_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    response = client.patch(url + f'/{operator_id}', json=data, headers={'authorization': auth_token})
    assert response.status_code == 200
    assert response.json()['organization_id'] == organization2_id


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorChargepoint)
def test_add_charge_popint_to_opeartor(async_client_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/csms/operator'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        operator = OperatorFactory(organization_id=organization_id)
        db.commit()
        operator_id = str(operator.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=str(organization.id)
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, organization_id, f'{ROOT_PATH}/api/v1/csms/operator/.+',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    response = client.post(url + f'/{operator_id}/charge_point', params={'charge_point_id': str(uuid.uuid4())},
                           headers={'authorization': auth_token})
    assert response.status_code == 200
    assert len(response.json()['charge_points']) == 1


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorChargepoint)
def test_remove_charge_popint_from_opeartor(async_client_mock, test_db):
    url = f'{ROOT_PATH}/api/v1/csms/operator'

    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        operator = OperatorFactory(organization_id=organization_id)
        db.commit()
        operator_id = str(operator.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, organization_id, f'{ROOT_PATH}/api/v1/csms/operator/.+',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')
    charge_point_id = str(uuid.uuid4())
    response = client.post(url + f'/{operator_id}/charge_point', params={'charge_point_id': charge_point_id},
                           headers={'authorization': auth_token})
    assert response.status_code == 200
    assert len(response.json()['charge_points']) == 1

    response = client.delete(url + f'/{operator_id}/charge_point', params={'charge_point_id': charge_point_id},
                             headers={'authorization': auth_token})
    assert response.status_code == 200
    assert len(response.json()['charge_points']) == 0


def test_share_operator(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/operator'

    with contextmanager(override_create_session)() as db:
        organization1 = OrganizationFactory()
        organization2 = OrganizationFactory()
        db.commit()
        organization1_id = str(organization1.id)
        organization2_id = str(organization2.id)
        operator = OperatorFactory(organization_id=organization1_id)
        db.commit()
        operator_id = str(operator.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization1_id
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization1_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization1.id), f'{ROOT_PATH}/api/v1/csms/operator/.+',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization1_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    response = client.post(url + f'/{operator_id}/organization', params={'organization_id': organization2_id},
                           headers={'authorization': auth_token})
    assert response.status_code == 200
    assert len(response.json()['shared_organizations']) == 1


def test_withhold_shared_operator(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/operator'

    with contextmanager(override_create_session)() as db:
        organization1 = OrganizationFactory()
        organization2 = OrganizationFactory()
        db.commit()
        organization1_id = str(organization1.id)
        organization2_id = str(organization2.id)
        operator = OperatorFactory(organization_id=organization1_id)
        db.commit()
        operator_id = str(operator.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization1_id,
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization1_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization1.id), f'{ROOT_PATH}/api/v1/csms/operator/.+',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization1_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    response = client.post(url + f'/{operator_id}/organization', params={'organization_id': organization2_id},
                           headers={'authorization': auth_token})
    assert response.status_code == 200
    assert len(response.json()['shared_organizations']) == 1

    response = client.delete(url + f'/{operator_id}/organization', params={'organization_id': organization2_id},
                             headers={'authorization': auth_token})
    assert response.status_code == 200
    assert len(response.json()['shared_organizations']) == 0


def test_update_operator_fails(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/operator'

    with contextmanager(override_create_session)() as db:
        organization1 = OrganizationFactory()
        organization2 = OrganizationFactory()
        db.commit()
        organization1_id = str(organization1.id)
        organization2_id = str(organization2.id)
        operator = OperatorFactory(organization_id=organization1_id)
        db.commit()
        operator_id = str(operator.id)

        data = {
            'organization_id': organization2_id
        }
        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization1_id,
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization1_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization1.id), f'{ROOT_PATH}/api/v1/csms/operator/.+',
                                    'get,patch,post,delete', 'apollo-main')

        db.commit()
        login_url = f'{ROOT_PATH}/api/v1/csms/auth/signin/staff'
        login_data = {
            'email': staff.email,
            'password': 'password',
            'organization_id': organization1_id,
        }
        login_response = client.post(login_url, json=login_data)
        auth_token = login_response.json().get('auth_token')

    response = client.patch(url + f'/{operator_id}', json=data, headers={'authorization': auth_token})
    assert response.status_code == 400


def test_get_list_of_operator_with_org_id(test_db):
    url = f'{ROOT_PATH}/api/v1/csms/operator'

    with contextmanager(override_create_session)() as db:

        organization1 = OrganizationFactory()
        organization2 = OrganizationFactory()
        db.commit()
        organization1_id = str(organization1.id)
        organization2_id = str(organization2.id)

        for _item in range(2):
            OperatorFactory(id=uuid.uuid4(), organization_id=organization1_id)
            db.commit()

        for _item in range(2):
            OperatorFactory(id=uuid.uuid4(), organization_id=organization2_id)
            db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization2_id,
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization2_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization2.id), f'{ROOT_PATH}/api/v1/csms/operator/?',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": f'{staff.id}',
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    response = client.get(url, headers={'authorization': token}, params={'organization_id': organization2_id})
    assert response.status_code == 200
    assert len(response.json()['items']) == 2
    assert response.json()['items'][0]['organization_id'] == organization2_id
    assert response.json()['items'][1]['organization_id'] == organization2_id


def test_get_list_of_operator_without_org_id(test_db):
    with contextmanager(override_create_session)() as db:
        organization1 = OrganizationFactory()
        organization2 = OrganizationFactory()
        db.commit()
        organization1_id = str(organization1.id)
        organization2_id = str(organization2.id)

        for _item in range(2):
            OperatorFactory(id=uuid.uuid4(), organization_id=organization1_id)
            db.commit()

        OperatorFactory(id=uuid.uuid4(), organization_id=organization2_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization1_id,
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization1_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization1.id), f'{ROOT_PATH}/api/v1/csms/operator/?',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": f'{staff.id}',
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    url = f'{ROOT_PATH}/api/v1/csms/operator'
    response = client.get(url, headers={'authorization': token}, params={'organization_id': ""})
    assert response.status_code == 200
    assert len(response.json()['items']) == 2
    assert response.json()['items'][0]['organization_id'] == organization1_id
    assert response.json()['items'][1]['organization_id'] == organization1_id


def test_get_list_of_operator_without_org_id_superuser(test_db):
    with contextmanager(override_create_session)() as db:
        organization1 = OrganizationFactory()
        organization2 = OrganizationFactory()
        db.commit()
        organization1_id = str(organization1.id)
        organization2_id = str(organization2.id)

        for _item in range(2):
            OperatorFactory(id=uuid.uuid4(), organization_id=organization1_id)
            db.commit()

        OperatorFactory(id=uuid.uuid4(), organization_id=organization2_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization1_id,
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization1_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization1.id), f'{ROOT_PATH}/api/v1/csms/operator/?',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": f'{staff.id}',
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    url = f'{ROOT_PATH}/api/v1/csms/operator'
    response = client.get(url, headers={'authorization': token}, params={'organization_id': ""})
    assert response.status_code == 200
    assert len(response.json()['items']) == 3


def test_get_list_of_operator_with_org_id_other_organization(test_db):
    with contextmanager(override_create_session)() as db:
        organization1 = OrganizationFactory()
        organization2 = OrganizationFactory()
        db.commit()
        organization1_id = str(organization1.id)
        organization2_id = str(organization2.id)

        for _item in range(2):
            OperatorFactory(id=uuid.uuid4(), organization_id=organization1_id)
            db.commit()

        OperatorFactory(id=uuid.uuid4(), organization_id=organization2_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=False,
            verification_method='email',
            organization_id=organization1_id,
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization1_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization1.id), f'{ROOT_PATH}/api/v1/csms/operator/?',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": f'{staff.id}',
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    url = f'{ROOT_PATH}/api/v1/csms/operator'
    response = client.get(url, headers={'authorization': token}, params={'organization_id': organization2_id})
    assert response.status_code == 403
    assert response.json() == {'detail': 'Access to resource denied, User has no assigned role for this resource.'}
    assert len(response.json()) == 1


def test_get_list_of_operator_with_org_id_not_found(test_db):
    with contextmanager(override_create_session)() as db:
        organization1 = OrganizationFactory()
        organization2 = OrganizationFactory()
        db.commit()
        organization1_id = str(organization1.id)
        organization2_id = str(organization2.id)

        for _item in range(2):
            OperatorFactory(id=uuid.uuid4(), organization_id=organization1_id)
            db.commit()

        OperatorFactory(id=uuid.uuid4(), organization_id=organization2_id)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization1_id,
        )
        db.commit()
        staff_id = str(staff.id)

        mem = MembershipFactory(
            organization_id=organization1_id,
            user_id=staff_id,
            membership_type=MembershipType.staff,
        )

        create_res_server_and_roles(db, mem, str(organization1.id), f'{ROOT_PATH}/api/v1/csms/operator/?',
                                    'get,patch,post,delete', 'apollo-main')
        db.commit()

        token = jwt.encode({
            "exp": datetime.utcnow() + timedelta(days=1),
            "user_id": f'{staff.id}',
            "membership_id": f'{mem.id}',
        },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        dummy_id = str(uuid.uuid4())

    url = f'{ROOT_PATH}/api/v1/csms/operator'
    response = client.get(url, headers={'authorization': token}, params={'organization_id': dummy_id})
    assert response.status_code == 400
    assert response.json() == {'detail': 'Organization not found'}
    assert len(response.json()) == 1
