import logging
# import os
# import os
import ssl

import pydantic
from kombu import Connection, Exchange
from kombu.messaging import Producer

from app.settings import (RABBITMQ_USER, RABBITMQ_PASS, RABBITMQ_HOST, RABBITMQ_PORT,
                          EXCHANGE, EXCHANGE_TYPE, RABBITMQ_IS_SECURE)

logger = logging.getLogger(__name__)


def create_connection():
    # mq_tls = os.path.join(BASE_DIR, 'app/amazon_mq_ca.pem')
    # ssl_options = {
    #     'ca_certs': mq_tls,
    #     'cert_reqs': ssl.CERT_REQUIRED
    # }
    # return Connection(CELERY_BROKER_URL)
    if pydantic.parse_obj_as(bool, RABBITMQ_IS_SECURE):
        connection_broker = Connection(
            transport='amqps',
            hostname=RABBITMQ_HOST,
            port=RABBITMQ_PORT,
            virtual_host='/',
            userid=RABBITMQ_USER,
            password=RABBITMQ_PASS,
            ssl={
                'ssl_version': ssl.PROTOCOL_TLSv1_2,
            }
            # ssl=ssl_options
        )
    else:
        connection_broker = Connection(
            transport='amqp',
            hostname=RABBITMQ_HOST,
            port=RABBITMQ_PORT,
            virtual_host='/',
            userid=RABBITMQ_USER,
            password=RABBITMQ_PASS,
            # ssl=ssl_options
        )

    connection_broker.connect()

    logger.info("---- KOMBU CONNECTION ----")
    # logger.info("Kombu connection last updated by Sam at 06-05-2023, justified with AMQPS hosted on AmazonMQ")
    # logger.info("Backend is secured: %s", str(RABBITMQ_IS_SECURE))
    # logger.info("Connection Status: %s", str(connection_broker.connected))
    # logger.info("Transport Used: %s", str(connection_broker.transport_cls))
    # logger.info("SSL: %s", str(connection_broker.ssl))
    # logger.info("---- KOMBU CONNECTION ----")

    return connection_broker


def define_exchange():
    return Exchange(EXCHANGE, type=EXCHANGE_TYPE)


class ChargerServicePublisher:
    """This is charger service publisher
    """

    def __init__(self):
        """Setup the publisher object
        """
        self._connection = create_connection()
        self._channel = self._connection.channel()
        self.exchange = define_exchange()
        self.producer = Producer(self._channel)

    def publish_message(self, message, routing_key, headers=None, compression=None):
        """Publish the message using the json serializer (which is the default),
        and zlib compression.  The kombu consumer will automatically detect
        encoding, serialization and compression used and decode accordingly.
        """
        try:
            self.producer.publish(
                message,
                headers=headers,
                exchange=self.exchange,
                routing_key=routing_key,
                serializer='json',
                compression=compression,
            )
        except (OSError, ConnectionResetError, ssl.SSLError):
            logger.exception('Error publishing message to RabbitMQ. Reconnecting and retrying...')
            self._connection.release()  # Release current connection
            self._connection = create_connection()  # Re-establish connection
            self._channel = self._connection.channel()  # Re-create channel
            self.producer = Producer(self._channel)  # Re-create producer
            self.producer.publish(
                message,
                headers=headers,
                exchange=self.exchange,
                routing_key=routing_key,
                serializer='json',
                compression=compression,
            )

    def get_connection_status(self):
        logger.info("---- KOMBU CONNECTION ----")
        logger.info("Kombu connection last updated by Sam at 06-05-2023, justified with AMQPS hosted on AmazonMQ")
        logger.info("Backend is secured: %s", str(RABBITMQ_IS_SECURE))
        logger.info("Connection Status: %s", str(self._connection.connected))
        logger.info("Transport Used: %s", str(self._connection.transport_cls))
        logger.info("INFO: %s", str(self._connection.ssl))
        logger.info("---- KOMBU CONNECTION ----")

    def stop(self):
        self._connection.close()
