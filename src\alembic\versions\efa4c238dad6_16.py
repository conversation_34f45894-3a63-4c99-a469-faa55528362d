"""16

Revision ID: efa4c238dad6
Revises: 4267b210e978
Create Date: 2022-10-31 08:57:46.648541

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'efa4c238dad6'
down_revision = '4267b210e978'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_subscription_custom_plan', 'charger_name', existing_type=sa.String(), existing_nullable=True, new_column_name='charger_point_id', nullable=True, type_=sa.String())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_subscription_custom_plan', 'charger_point_id', existing_type=sa.String(), existing_nullable=True, new_column_name='charger_name', nullable=True, type_=sa.String())
    # ### end Alembic commands ###
