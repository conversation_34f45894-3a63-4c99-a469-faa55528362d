from datetime import datetime, timed<PERSON><PERSON>
from contextlib import contextmanager
from unittest.mock import patch
import pytest

import jwt
from faker import Faker
from fastapi.testclient import TestClient

from app import settings
from app.main import app, ROOT_PATH
from app.tests.factories import (UserFactory, VerificationTokenFactory, OrganizationFactory,
                                 ResourceServerFactory, ResourceFactory,
                                 RoleFactory, MembershipFactory, IDTagFactory, )
from app.database import SessionLocal, create_session, Base, engine
from app.models import Membership, ActivityLog
from app.schema import MembershipType, JWT_ALGORITHM, ActivityLogType
from app.tests.mocks.async_client import (
    MockAsyncClientGeneratorConnectorType
)


fake = Faker()
client = TestClient(app)


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        VerificationTokenFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        IDTagFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session


def create_user_with_auth_token(root_path: str = '', res_path: str = '', res_scope: str = ''):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()

        user = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization.id
        )
        db.commit()

        membership = MembershipFactory(
            organization_id=f'{organization.id}',
            user_id=f'{user.id}',
            membership_type=MembershipType.regular_user,
        )
        db.commit()

        IDTagFactory(
            member_id=f'{membership.id}',
            expiration=datetime.now() + timedelta(weeks=10),
        )
        db.commit()

        res_server = ResourceServerFactory(root_path=root_path)

        res = ResourceFactory(
            path=res_path,
            scope=res_scope,
        )

        # create Role with the newly created resource attached to it
        role = RoleFactory(organization_id=f'{organization.id}')

        res_server.resources.append(res)
        role.resources.append(res)

        # attach Role to Membership
        membership = db.query(Membership).get(f'{membership.id}')
        membership.roles.append(role)
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(user.id),
                "membership_id": f'{membership.id}',
            },
            settings.JWT_SECRET,
            algorithm=JWT_ALGORITHM,
        )

    return token


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorConnectorType)
def test_list_connectortype_succeeds(mocked_get, test_db):
    token = create_user_with_auth_token(root_path='apollo-main',
                                        res_path=f'{ROOT_PATH}/api/v1/csms/charger/connectortype/?',
                                        res_scope='get,patch,delete,post')
    url = f'{ROOT_PATH}/api/v1/csms/charger/connectortype/'
    response = client.get(url, headers={'authorization': token})

    assert response.status_code == 200


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorConnectorType)
def test_create_connectortype_with_valid_data_succeeds(mocked_post, test_db):
    token = create_user_with_auth_token(root_path='apollo-main',
                                        res_path=f'{ROOT_PATH}/api/v1/csms/charger/connectortype/?',
                                        res_scope='get,patch,delete,post')
    url = f'{ROOT_PATH}/api/v1/csms/charger/connectortype/'
    data = {
        'created_at': fake.iso8601(),
        'updated_at': fake.iso8601(),
        'name': fake.pystr(),
        'kind': fake.pystr(),
    }
    response = client.post(url, json=data, headers={'authorization': token})

    assert response.status_code == 201

    with contextmanager(override_create_session)() as db:
        db_activity = db.query(ActivityLog).order_by(ActivityLog.created_at).first()
        assert db_activity.type == ActivityLogType.create


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorConnectorType)
def test_get_connectortype_succeeds(mocked_get, test_db):
    token = create_user_with_auth_token(root_path='apollo-main',
                                        res_path=f'{ROOT_PATH}/api/v1/csms/charger/connectortype/.+',
                                        res_scope='get,patch,delete,post')
    url = f'{ROOT_PATH}/api/v1/csms/charger/connectortype/123'
    response = client.get(url, headers={'authorization': token})

    assert response.status_code == 200


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorConnectorType)
def test_patch_connectortype_with_valid_data_succeeds(mocked_patch, test_db):
    token = create_user_with_auth_token(root_path='apollo-main',
                                        res_path=f'{ROOT_PATH}/api/v1/csms/charger/connectortype/.+',
                                        res_scope='get,patch,delete,post')
    url = f'{ROOT_PATH}/api/v1/csms/charger/connectortype/123'
    data = {
        'name': fake.pystr(),
    }
    response = client.patch(url, json=data, headers={'authorization': token})

    assert response.status_code == 200

    with contextmanager(override_create_session)() as db:
        db_activity = db.query(ActivityLog).order_by(ActivityLog.created_at).first()
        assert db_activity.type == ActivityLogType.update


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorConnectorType)
def test_delete_connectortype_succeeds(mocked_delete, test_db):
    token = create_user_with_auth_token(root_path='apollo-main',
                                        res_path=f'{ROOT_PATH}/api/v1/csms/charger/connectortype/.+',
                                        res_scope='get,patch,delete,post')
    url = f'{ROOT_PATH}/api/v1/csms/charger/connectortype/123'
    response = client.delete(url, headers={'authorization': token})

    assert response.status_code == 204

    with contextmanager(override_create_session)() as db:
        db_activity = db.query(ActivityLog).order_by(ActivityLog.created_at).first()
        assert db_activity.type == ActivityLogType.delete
