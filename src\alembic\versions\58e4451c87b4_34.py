"""34

Revision ID: 58e4451c87b4
Revises: a7fe05bc3ced
Create Date: 2023-04-19 17:55:53.062586

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '58e4451c87b4'
down_revision = 'a7fe05bc3ced'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_auth_external_token', sa.Column('version', sa.String(), nullable=True))
    op.add_column('main_auth_external_token', sa.Column('push_notification', sa.<PERSON>an(), nullable=True))
    op.drop_column('main_auth_external_token', 'versions')
    op.add_column('main_operator', sa.Column('whatsapp_number', sa.String(), nullable=True))
    op.add_column('main_operator', sa.Column('images', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_operator', sa.Column('party_id', sa.String(), nullable=True))
    op.add_column('main_operator', sa.Column('country_code', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_operator', 'country_code')
    op.drop_column('main_operator', 'party_id')
    op.drop_column('main_operator', 'images')
    op.drop_column('main_operator', 'whatsapp_number')
    op.add_column('main_auth_external_token', sa.Column('versions', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('main_auth_external_token', 'push_notification')
    op.drop_column('main_auth_external_token', 'version')
    # ### end Alembic commands ###
