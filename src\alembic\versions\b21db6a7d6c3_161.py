"""161

Revision ID: b21db6a7d6c3
Revises: 0556f951776c
Create Date: 2025-04-28 00:20:43.244239

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b21db6a7d6c3'
down_revision = '0556f951776c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_commercial_invoice',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('invoice_number', sa.String(), nullable=True),
    sa.Column('amount', sa.String(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('invoice_running_number', sa.Integer(), nullable=True),
    sa.Column('subscription_order_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ),
    sa.ForeignKeyConstraint(['subscription_order_id'], ['main_subscription_order.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('invoice_running_number')
    )
    op.execute(sa.schema.CreateSequence(sa.schema.Sequence('commercial_invoice_seq', start=60000, increment=1)))
    op.alter_column("main_commercial_invoice", "invoice_running_number",
                    server_default=sa.text("nextval('commercial_invoice_seq'::regclass)"))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_commercial_invoice')
    # ### end Alembic commands ###
