"""142

Revision ID: ab0e16f7ab1b
Revises: c860eabec770
Create Date: 2025-01-08 16:51:01.413173

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ab0e16f7ab1b'
down_revision = 'c860eabec770'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_contract_certificate_fee',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>an(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('iso3166_country_code', sa.String(), nullable=True),
    sa.Column('payment_currency', sa.String(), nullable=True),
    sa.Column('activation_fee', sa.Numeric(scale=5), nullable=True),
    sa.Column('renewal_fee', sa.Numeric(scale=5), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_contract_certificate_order',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('emaid_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['emaid_id'], ['main_emaid.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_contract_certificate_order')
    op.drop_table('main_contract_certificate_fee')
    # ### end Alembic commands ###
