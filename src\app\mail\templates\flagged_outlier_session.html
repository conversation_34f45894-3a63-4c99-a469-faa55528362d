<html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Outlier Charging Session Detected - [Session ID: {{charging_session_id}}]</title>
    <!-- CSS only -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-gH2yIJqKdNHPEq0n4Mqa/HGKIhSkIHeL5AyhkYV8i59U5AR6csBvApHHNl/vI1Bx" crossorigin="anonymous">
    <style>
        @media only screen and (max-width: 600px) {
            .card-body {
                width: 100% !important;
            }
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        td,
        th {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
        }

        tr:nth-child(even) {
            background-color: #dddddd;
        }
    </style>
</head>

<body>
    <div class="card card-body" width="570"
    style="width: 80%; margin-left: auto; margin-right: auto; overflow-x: hidden;">
    <div style="margin:5px 5%;">
        <p class="mx-5 mb-5 mt-1">Hi Ops Team,</p>
        <p class="mx-5 mb-5 mt-1">A charging session has been flagged as an outlier based on one or more of the following parameters:</p>

        <table class="mx-5 mb-5 mt-1" border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
            <tr style="background-color: #f2f2f2;">
                <th>Charging Session ID</th>
                <th>Transaction ID</th>
                <th>Flagged At</th>
                <th>Session Type</th>
                <th>ID Tag</th>
                <th>Billed Amount</th>
                <th>kWh Delivered</th>
                <th>Session Duration</th>
                <th>Reasons</th>
            </tr>
            <tr>
                <td>{{ charging_session_id }}</td>
                <td>{{ transaction_id }}</td>
                <td>{{ created_at }}</td>
                <td>{{ charging_session_type }}</td>
                <td>{{ id_tag }}</td>
                <td>{{ billing_currency }} {{ "{:.2f}".format(billing_amount) }}</td>
                <td>{{ billing_kwh }}</td>
                <td>{{ billing_duration }}</td>
                <td>{{ reasons }}</td>
            </tr>
        </table>

        <br />
        <p class="m-5">Kindly review the session(s) and take action if necessary. Thank you.</p>

        <div class="m-t-30" style="margin:30px 0px; font-size: 1.5vh;">
            <p class="mx-5 mb-5 mt-1">Thank you.<br />Apollo/CSMS</p>
        </div>

        <div class="m-t-30" style="margin:30px 0px; font-size: 1.5vh;">
            <p class="mx-5 mb-5 mt-1"><i>Please do not reply to this email as it is auto generated.</i></p>
        </div>
    </div>
</div>


</body>

</html>