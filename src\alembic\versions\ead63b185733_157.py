"""157

Revision ID: ead63b185733
Revises: e3ecb0b65b93
Create Date: 2025-02-28 09:54:29.803444

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ead63b185733'
down_revision = 'e3ecb0b65b93'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_prepaid_wallet_plan',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('plan_type', sa.String(), nullable=True),
    sa.Column('interval', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('unique_prepaid_wallet_plan_name', 'main_prepaid_wallet_plan', ['name', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted'))

    op.create_table('main_prepaid_wallet_plan_batch',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('prepaid_wallet_plan_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('plan_type', sa.String(), nullable=True),
    sa.Column('interval', sa.String(), nullable=True),
    sa.Column('start_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('end_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['prepaid_wallet_plan_id'], ['main_prepaid_wallet_plan.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )

    op.create_table('main_prepaid_wallet_subscription',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('prepaid_wallet_plan_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['prepaid_wallet_plan_id'], ['main_prepaid_wallet_plan.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('unique_prepaid_wallet_subscription_member_plan', 'main_prepaid_wallet_subscription', ['member_id', 'prepaid_wallet_plan_id', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted'))

    op.create_table('main_prepaid_wallet_transaction',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('transaction_type', sa.String(), nullable=True),
    sa.Column('remarks', sa.String(), nullable=True),
    sa.Column('transaction_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('prepaid_wallet_transaction_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('prepaid_wallet_subscription_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('charging_session_bill_ids', postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True),
    sa.Column('prepaid_wallet_plan_batch_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['prepaid_wallet_plan_batch_id'], ['main_prepaid_wallet_plan_batch.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['prepaid_wallet_subscription_id'], ['main_prepaid_wallet_subscription.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['prepaid_wallet_transaction_id'], ['main_prepaid_wallet_transaction.id'], ),
    sa.PrimaryKeyConstraint('id')
    )

    op.add_column('main_payment_request', sa.Column('prepaid_wallet_transaction_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key('main_payment_request_fk_prepaid_wallet_transaction_id', 'main_payment_request', 'main_prepaid_wallet_transaction', ['prepaid_wallet_transaction_id'], ['id'], ondelete='SET NULL')

    op.add_column('main_wallet_transaction', sa.Column('prepaid_wallet_transaction_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key('main_wallet_transaction_fk_prepaid_wallet_transaction_id', 'main_wallet_transaction', 'main_prepaid_wallet_transaction', ['prepaid_wallet_transaction_id'], ['id'], ondelete='SET NULL')

    # have to add separately due to circular dependency of foreign keys (
    # main_prepaid_wallet_plan_batch.prepaid_wallet_plan_id -> main_prepaid_wallet_plan.latest_batch_id)
    op.add_column('main_prepaid_wallet_plan', sa.Column('latest_batch_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key('main_prepaid_wallet_plan_latest_batch_id_fkey', 'main_prepaid_wallet_plan', 'main_prepaid_wallet_plan_batch', ['latest_batch_id'], ['id'], ondelete='CASCADE')

    op.add_column('main_prepaid_wallet_plan_batch', sa.Column('next_batch_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key('main_prepaid_wallet_plan_batch_next_batch_id_fkey', 'main_prepaid_wallet_plan_batch', 'main_prepaid_wallet_plan_batch', ['next_batch_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('main_prepaid_wallet_plan_batch_next_batch_id_fkey', 'main_prepaid_wallet_plan_batch', type_='foreignkey')
    op.drop_column('main_prepaid_wallet_plan_batch', 'next_batch_id')

    op.drop_constraint('main_prepaid_wallet_plan_latest_batch_id_fkey', 'main_prepaid_wallet_plan', type_='foreignkey')
    op.drop_column('main_prepaid_wallet_plan', 'latest_batch_id')

    op.drop_constraint('main_wallet_transaction_fk_prepaid_wallet_transaction_id', 'main_wallet_transaction', type_='foreignkey')
    op.drop_column('main_wallet_transaction', 'prepaid_wallet_transaction_id')

    op.drop_constraint('main_payment_request_fk_prepaid_wallet_transaction_id', 'main_payment_request', type_='foreignkey')
    op.drop_column('main_payment_request', 'prepaid_wallet_transaction_id')

    op.drop_table('main_prepaid_wallet_transaction')

    op.drop_index('unique_prepaid_wallet_subscription_member_plan', table_name='main_prepaid_wallet_subscription', postgresql_where=sa.text('NOT is_deleted'))
    op.drop_table('main_prepaid_wallet_subscription')

    op.drop_table('main_prepaid_wallet_plan_batch')

    op.drop_index('unique_prepaid_wallet_plan_name', table_name='main_prepaid_wallet_plan', postgresql_where=sa.text('NOT is_deleted'))
    op.drop_table('main_prepaid_wallet_plan')
    # ### end Alembic commands ###
