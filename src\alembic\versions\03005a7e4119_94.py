"""94

Revision ID: 03005a7e4119
Revises: 8cc0248f2df0
Create Date: 2024-05-09 14:05:24.240848

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '03005a7e4119'
down_revision = '8cc0248f2df0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_pre_auth_payment', sa.Column('failed_refund', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_pre_auth_payment', 'failed_refund')
    # ### end Alembic commands ###
