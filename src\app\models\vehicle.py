from enum import Enum
import sqlalchemy as db
from sqlalchemy.dialects.postgresql import UUID, JSONB
from app.models.base import BaseModel
from app.database import Base
from app.schema import EmaidStatus


class Vehicle(BaseModel):
    __tablename__ = 'main_vehicle'

    name = db.Column(db.String)
    model = db.Column(db.String)
    brand = db.Column(db.String)
    connector_type_id = db.Column(UUID(as_uuid=True))
    acceptance_rate = db.Column(db.Numeric(scale=2))
    battery_capacity = db.Column(db.Numeric(scale=2))
    suspend_timer = db.Column(db.Integer, default=15)
    pcid = db.Column(db.String)
    registration_number = db.Column(db.String)
    vehicle_histories = db.Column(JSONB, default=[])

    emaid = db.orm.relationship("Emaid", back_populates="vehicle", uselist=True)
    autocharge = db.orm.relationship("Autocharge", back_populates="vehicle", uselist=False, lazy="joined")
    membership = db.orm.relationship("Membership", secondary="membership_vehicle_association",
                                     back_populates="vehicles", lazy="joined", uselist=False)


# Index the PCID of a vehicle. It needn't be unique due to multi-tenancy
db.Index('unique_vehicle_pcid', Vehicle.pcid, Vehicle.is_deleted,
         unique=False, postgresql_where=~Vehicle.is_deleted)


class Emaid(BaseModel):
    __tablename__ = 'main_emaid'

    emaid = db.Column(db.String)
    contract_begin = db.Column(db.DateTime(timezone=True))
    contract_end = db.Column(db.DateTime(timezone=True))
    status = db.Column(db.String, default=EmaidStatus.payment_pending)
    status_comment = db.Column(db.String)
    oem_message = db.Column(JSONB, default={})

    vehicle = db.orm.relationship('Vehicle')
    vehicle_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_vehicle.id'))
    id_tag = db.orm.relationship("IDTag", back_populates="emaid", uselist=False)
    contract_order = db.orm.relationship("ContractCertificateOrder", back_populates="emaid", uselist=True)


# Index the PCID of a vehicle. It needn't be unique due to multi-tenancy
db.Index('unique_vehicle_emaid', Emaid.emaid, Emaid.is_deleted,
         unique=True, postgresql_where=~Emaid.is_deleted)


class ContractCertificateFee(BaseModel):
    __tablename__ = 'main_contract_certificate_fee'

    iso3166_country_code = db.Column(db.String)
    payment_currency = db.Column(db.String)
    activation_fee = db.Column(db.Numeric(scale=5), default=0.0)
    renewal_fee = db.Column(db.Numeric(scale=5), default=0.0)


class ContractCertificateOrder(BaseModel):
    __tablename__ = 'main_contract_certificate_order'

    amount = db.Column(db.Numeric(scale=2), default=0.0)
    currency = db.Column(db.String)
    emaid = db.orm.relationship('Emaid', lazy="joined")
    emaid_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_emaid.id', ondelete='CASCADE'))
    status = db.Column(db.String)


class HubjectAuthTokenType(str, Enum):
    cpo = 'CPO'
    mo = 'MO'


# This DB is mainly a place holder for the Hubject Auth Token.
# it is not something we need to soft delete. So just inheriting from Base
class HubjectToken(Base):
    __tablename__ = 'hubject_token'

    role = db.Column(db.String, primary_key=True)
    token = db.Column(db.Text)
    expiry = db.Column(db.DateTime(timezone=True))


class Autocharge(BaseModel):
    __tablename__ = 'main_autocharge'

    mac_address = db.Column(db.String)
    status = db.Column(db.String, default='Pending')
    # Should we have an expiry just in case a user forgot to delete the car?
    expiry = db.Column(db.DateTime(timezone=True))
    vehicle = db.orm.relationship('Vehicle')
    vehicle_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_vehicle.id'))
    id_tag = db.orm.relationship("IDTag", back_populates="autocharge", uselist=False)


db.Index('unique_autocharge_mac_address', Autocharge.mac_address, Autocharge.is_deleted,
         unique=True, postgresql_where=~Autocharge.is_deleted)
