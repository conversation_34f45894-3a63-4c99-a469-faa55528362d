"""68

Revision ID: bf963a62059d
Revises: 167f07cb4348
Create Date: 2024-01-30 10:42:45.509747

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'bf963a62059d'
down_revision = '167f07cb4348'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_emaid',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>an(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('emaid', sa.String(), nullable=True),
    sa.Column('contract_begin', sa.DateTime(timezone=True), nullable=True),
    sa.Column('contract_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('status_comment', sa.String(), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('vehicle_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ),
    sa.ForeignKeyConstraint(['vehicle_id'], ['main_vehicle.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('main_vehicle', sa.Column('pcid', sa.String(), nullable=True))
    op.add_column('main_vehicle', sa.Column('registration_number', sa.String(), nullable=True))
    op.add_column('main_vehicle', sa.Column('vehicle_histories', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_vehicle', 'vehicle_histories')
    op.drop_column('main_vehicle', 'registration_number')
    op.drop_column('main_vehicle', 'pcid')
    op.drop_table('main_emaid')
    # ### end Alembic commands ###
