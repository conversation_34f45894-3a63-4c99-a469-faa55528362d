"""130

Revision ID: 50b438a03081
Revises: a2bdefb4bd84
Create Date: 2024-10-28 10:06:16.850790

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '50b438a03081'
down_revision = 'a2bdefb4bd84'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_charging_session_bill', sa.Column('preferred_payment_flow', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_charging_session_bill', 'preferred_payment_flow')
    # ### end Alembic commands ###
