"""179

Revision ID: 979346a8f7b9
Revises: d165f383410e
Create Date: 2025-06-23 14:43:38.739980

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '979346a8f7b9'
down_revision = 'd165f383410e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_e_credit_note', sa.Column('error_message_timeline', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('main_e_invoice', sa.Column('error_message_timeline', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_e_invoice', 'error_message_timeline')
    op.drop_column('main_e_credit_note', 'error_message_timeline')
    # ### end Alembic commands ###
