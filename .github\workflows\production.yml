name: apollo-main.production

on:
  push:
    branches:
      - 'production'

jobs:
  push_to_ecr:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - uses: actions/checkout@v2
        with:
          persist-credentials: false
      - name: Get version number from version.txt
        id: get_version
        run: |
          echo "version_number=$(head -1 version.txt)" >> $GITHUB_ENV
          echo "version_number=$(head -1 version.txt)" >> $GITHUB_OUTPUT
      - uses: whoan/docker-build-with-cache-action@v5
        with:
          username: '${{ secrets.AWS_ACCESS_KEY_ID }}'
          password: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
          registry: ${{ vars.AWS_ACCOUNT_ID }}.dkr.ecr.ap-southeast-1.amazonaws.com
          dockerfile: ./docker/Dockerfile.main
          image_name: ${{ vars.MAIN_IMAGE_NAME }}
          image_tag: production-latest,22.1.${{ github.run_number }},${{ steps.get_version.outputs.version_number }}

      - uses: tibdex/github-app-token@v1
        id: get_installation_token
        with:
          app_id: 465705
          installation_id: ********
          private_key: ${{ secrets.CONFIG_REPO_PRIVATE_KEY }}
          
      - name: Update Variable for Production environment in Config Repo
        run: |
          # Set the required variables
          repo_owner="yinsondigital" 
          repo_name="${{ vars.CONFIG_REPO_NAME }}"  

          curl \
          -X PATCH \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer ${{ steps.get_installation_token.outputs.token }}" \
          https://api.github.com/repos/$repo_owner/$repo_name/environments/production/variables/MAIN_VERSION_NUMBER \
          -d '{"name":"MAIN_VERSION_NUMBER","value":"'"$version_number"'"}'
  
      - name: Trigger Deployment Workflow in Config Repo
        run: |
          # Set the required variables
          repo_owner="yinsondigital" 
          repo_name="${{ vars.CONFIG_REPO_NAME }}"  
          workflow_id="production.yml"
          branch='production'
          event_type="trigger-deployment-production" 

          curl \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer ${{ steps.get_installation_token.outputs.token }}" \
          https://api.github.com/repos/$repo_owner/$repo_name/actions/workflows/$workflow_id/dispatches \
          -d "{\"ref\": \"$branch\", \"inputs\": {\"branch\": \"$branch\"}}"