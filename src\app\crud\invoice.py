import asyncio
import base64
from decimal import ROUND_HALF_UP, Decimal
import json
import logging
import math
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo
from typing import List, Optional, Union

import httpx
import requests
import tenacity
from celery.result import allow_join_result

from sqlalchemy import desc, func, or_, select, cast, Numeric, and_
from sqlalchemy.exc import NoR<PERSON>ultFound, IntegrityError
from sqlalchemy.orm import Session, joinedload, contains_eager

from fastapi_pagination.ext.sqlalchemy import paginate
from fastapi_pagination import Page

from app import models, exceptions, settings, schema, crud, utils
from .auth import MembershipCRUD
from .organization import IDTagCRUD, get_operator_by_organization_id_include_child, get_organization_by_id, \
    get_external_organization_by_operator_name
from .base import filter_user

from .payment import PaymentRequestCRUD
from .billing import BillingCRUD, get_charging_session_bill, get_charging_session_bill_by_charging_session
from ..models import User
from ..schema import PaymentRequestType
# from app.lta_celery import app as lta_celery_app
# from app.celery import app
from app.lta_celery import app as lta_app
from ..settings import BRANDING_NAME

branding_attributes = [
    "BRANDING_NAME",
    "BRANDING_URL",
    "BRANDING_LOGO_URL",
    "BRANDING_SUPPORT_EMAIL",
    "BRANDING_SUPPORT_PHONE_NUMBER",
    "BRANDING_ADDRESS",
    "BRANDING_TAX_REG_NUMBER"
]


def safe_get(d, *keys):
    try:
        for key in keys:
            if isinstance(d, list):
                key = int(key)  # Assuming the key can be converted to an integer index
            d = d[key] if d is not None else None
            if d is None:
                return None
        return d
    except (KeyError, TypeError, IndexError, ValueError):  # More specific exceptions
        return None


logger = logging.getLogger(__name__)

CHARGER_URL_PREFIX = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH}'
CHARGER_URL_PREFIX_V2 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'


@tenacity.retry(
    stop=tenacity.stop_after_attempt(3),
    wait=tenacity.wait_fixed(5),
    retry=tenacity.retry_if_exception_type(httpx.RequestError)
)
async def get_charging_session(db: Session, charging_session_id: str,  # pylint: disable-all  # noqa
                               headers: dict):
    path = f'charging/{charging_session_id}'
    async with httpx.AsyncClient() as client:
        response = await client.get(f'{CHARGER_URL_PREFIX}/{path}', headers=headers)
    if response.status_code == 200:
        cs = response.json()
        meta = cs['meta']  # charging session static data
        billing_info = meta['billing_info']
        connector = cs['charge_point_connector']
        connector_evse = cs['ocpi_evse_connector']

        operator = db.query(models.Operator).get(meta['operator']['id'])
        if operator:
            operator_name = operator.name
        else:
            operator = db.query(models.ExternalOrganization).get(meta['operator']['id'])
            if operator:
                operator_name = operator.friendly_name
            else:
                operator_name = None
                if connector_evse:
                    evse_id = connector_evse['ocpi_evse']['ocpi_partner_evse_id']
                    evse = evse_id.split('*')
                    operator_name = f"{evse[0].upper()}-{evse[1].upper()}"

        charge_box_serial_number = meta['charger_serial_number']
        try:
            charge_box_serial_number = meta['charge_box_serial_number']
        except KeyError:
            _ = ''

        charge_point = schema.ChargePoint(
            serial_number=meta['charger_serial_number'],
            charge_box_serial_number=charge_box_serial_number,
            location=meta['location'],
            operator=operator_name
        )
        try:
            operator_name = safe_get(meta, 'location', 'ocpi_partner_operator', 'name')
            if operator_name is not None:
                operators_by_name = get_external_organization_by_operator_name(db,
                                                                               [operator_name])
                operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                                     operators_by_name]

                if len(operators_by_name) >= 1:
                    charge_point.operator = operators_by_name[0].original_name
        except (KeyError, IndexError):
            _ = ''

        session_end = cs['session_end']
        session_start = cs['session_start']

        if session_start and session_end:
            duration = datetime.fromisoformat(cs['session_end']) - datetime.fromisoformat(cs['session_start'])
        else:
            duration = 0

        hogging_duration = 0.00
        if 'hogging_end' in cs and 'hogging_start' in cs:
            hogging_end = cs['hogging_end']
            hogging_start = cs['hogging_start']
            if hogging_start and hogging_end:
                hogging_duration = datetime.fromisoformat(cs['hogging_end']) - datetime.fromisoformat(
                    cs['hogging_start'])
                total_seconds = hogging_duration.total_seconds()
                rounded_hogging_duration = timedelta(seconds=total_seconds)
                hogging_duration = str(rounded_hogging_duration).split('.')[0]
            else:
                hogging_duration = 0

        charging_session = schema.ChargingSession(
            id=cs['id'],
            session_start=cs['session_start'],
            session_end=cs['session_end'],
            duration=duration,
            charging_usage=round(((cs['meter_stop'] - cs['meter_start']) / 1000), 3),
            id_tag=cs['id_tag'],
            hogging_start=cs.get('hogging_start', None),
            hogging_end=cs.get('hogging_end', None),
            hogging_duration=hogging_duration
        )
        connector_type = None
        connector_label = None
        connector_number = None

        if connector:
            connector_type = connector['connector_type']['name']
            connector_label = connector.get('connector_label', None)
            connector_number = connector.get('number', None)
        elif connector_evse:
            connector_type = connector_evse['connector_type']['name']
            connector_label = connector_evse.get('ocpi_evse', {}).get('physical_reference', None)
            connector_number = connector_evse.get('number', None)

        subscription_info = billing_info.get('subscription_info') or {}

        vat_rate = billing_info.get('vat_rate', 0) or 0
        try:
            billing_unit_fee_after_discount = utils.calculate_new_amount_upon_discount(
                billing_info['billing_discounted_type'],
                billing_info['billing_unit_fee'],
                billing_info['billing_discounted_amount'], 1, rounding_number=6)
        except KeyError:
            billing_unit_fee_after_discount = billing_info['billing_unit_fee']

        billing_unit_fee_after_discount_after_vat = billing_unit_fee_after_discount * (1 + vat_rate / 100)

        connector = schema.Connector(
            billing_type=billing_info['billing_type'],
            billing_unit_fee=billing_info['billing_unit_fee'],
            billing_unit_fee_after_vat=billing_info.get('billing_unit_fee_after_vat', None),
            billing_unit_fee_after_discount=billing_unit_fee_after_discount,
            billing_unit_fee_after_discount_after_vat=billing_unit_fee_after_discount_after_vat,
            vat_rate=billing_info.get('vat_rate', None),
            billing_cycle=billing_info['billing_cycle'],
            billing_currency=billing_info['billing_currency'],
            connection_fee=billing_info['connection_fee'],
            connector_type=connector_type,
            connector_label=connector_label,
            hogging_tariff=billing_info.get('hogging_tariff', None),
            connector_number=connector_number,
            subscription_plan=subscription_info.get('subscription_plan', None),
            subscription_custom_plan=subscription_info.get('subscription_custom_plan', None),
            campaign_promo_code_usage=billing_info.get("promo_code_usage"),
        )

        return {
            'id': cs['id'],
            'connector': connector,
            'charging_session': charging_session,
            'charge_point': charge_point,
            'transaction_id': cs['transaction_id']
        }

    logger.debug('charger response: %s', response.json())
    raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSession')


@tenacity.retry(
    stop=tenacity.stop_after_attempt(3),
    wait=tenacity.wait_fixed(5),
    retry=tenacity.retry_if_exception_type(httpx.RequestError)
)
async def get_raw_charging_session(db: Session, charging_session_id: str,  # pylint: disable-all  # noqa
                                   headers: dict):
    path = f'charging/{charging_session_id}'
    async with httpx.AsyncClient() as client:
        response = await client.get(f'{CHARGER_URL_PREFIX}/{path}', headers=headers)
    if response.status_code == 200:
        cs = response.json()
        return cs

    logger.debug('charger response: %s', response.json())
    raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSession')


# pylint: disable=too-many-locals  # noqa
async def get_charging_session_list(db: Session, charging_session_ids: list[str], headers: dict,
                                    allow_ocpi: bool, params=None, use_task: bool = False):
    url = f'{CHARGER_URL_PREFIX}/charging/'
    query_params = {'ids': charging_session_ids, 'allow_ocpi': allow_ocpi}
    if params:
        params = dict(params)
        query_params['size'] = params['size']
        if settings.USE_CSB_INSTEAD_OF_PR_FOR_INVOICES:
            query_params['query_with_deleted'] = True
    if use_task:
        request_message = {
            'headers': headers,
            'query_params': query_params
        }
        async_id = lta_app.send_task(
            'apollo.charger.reporting_tasks.get_charging_history_data',
            kwargs={'message': request_message},
            queue=settings.CHARGER_REPORTING_SERVICE_WORKER_QUEUE,
            routing_key=settings.CHARGER_REPORTING_SERVICE_WORKER_QUEUE
        )
        async_result = lta_app.AsyncResult(async_id.id)
        while not async_result.ready():
            async_result = lta_app.AsyncResult(async_id.id)
            await asyncio.sleep(1)
        #
        response_non_json = async_result.result

        queue_name = async_result.backend.oid
        correlation_id = async_result.id

        utils.ack_rpc_reply_by_correlation(queue_name, settings.CELERY_BROKER_URL, correlation_id)
        # async_result.forget()

        class DummyResponse:
            def __init__(self, status_code, json_data):
                self.status_code = status_code
                self.json_data = json_data

            def json(self):
                return self.json_data

        # Creating a custom response object
        response = DummyResponse(200, response_non_json)

    else:
        async with httpx.AsyncClient() as client:
            req = client.build_request('GET', url, headers=headers, params=query_params)
            response = await client.send(req)
    if response.status_code == 200:
        result = {}
        operators = db.query(models.Operator).all()
        ext_org = db.query(models.ExternalOrganization).all()
        operators_name = {str(op.id): op.name for op in operators}
        ext_name = {str(ex.id): ex.friendly_name for ex in ext_org}
        cs_list = response.json()
        for cs in cs_list['items']:
            meta = cs['meta']  # charging session static data
            billing_info = meta['billing_info']
            connector = cs['charge_point_connector']
            connector_evse = cs['ocpi_evse_connector']
            operator_name = None
            if connector:
                operator_name = operators_name.get(meta['operator']['id'])
            else:
                operator_name = ext_name.get(meta['operator']['id'])

            charge_box_serial_number = meta['charger_serial_number']
            try:
                charge_box_serial_number = meta['charge_box_serial_number']
            except KeyError:
                _ = ''

            charge_point = schema.ChargePoint(
                serial_number=meta['charger_serial_number'],
                charge_box_serial_number=charge_box_serial_number,
                location=meta['location'],
                operator=operator_name
            )
            session_end = cs['session_end']
            session_start = cs['session_start']

            if session_start and session_end:
                duration = datetime.fromisoformat(cs['session_end']) - datetime.fromisoformat(cs['session_start'])
            else:
                duration = 0
            charging_session = schema.ChargingSession(
                id=cs['id'],
                session_start=cs['session_start'],
                session_end=cs['session_end'],
                duration=duration,
                charging_usage=round(((cs['meter_stop'] - cs['meter_start']) / 1000), 3),
                id_tag=cs['id_tag']
            )

            connector_type = None
            connector_label = None
            connector_number = None
            connector_kind = None

            if connector:
                connector_type = connector['connector_type']['name']
                connector_label = connector.get('connector_label', None)
                connector_number = connector.get('number', None)
                connector_kind = connector['connector_type']['kind']
            elif connector_evse:
                connector_type = connector_evse['connector_type']['name']
                connector_label = connector_evse.get('ocpi_evse', {}).get('physical_reference', None)
                connector_number = connector_evse.get('number', None)
                connector_kind = connector_evse['connector_type']['kind']
            subscription_info = billing_info.get('subscription_info') or {}

            vat_rate = billing_info.get('vat_rate', 0) or 0
            try:
                billing_unit_fee_after_discount = utils.calculate_new_amount_upon_discount(
                    billing_info['billing_discounted_type'],
                    billing_info['billing_unit_fee'],
                    billing_info['billing_discounted_amount'], 1, rounding_number=6)
            except KeyError:
                billing_unit_fee_after_discount = billing_info['billing_unit_fee']
            billing_unit_fee_after_discount_after_vat = billing_unit_fee_after_discount * (1 + vat_rate / 100)

            connector = schema.Connector(
                billing_type=billing_info['billing_type'],
                billing_unit_fee=billing_info['billing_unit_fee'],
                billing_unit_fee_after_vat=billing_info.get('billing_unit_fee_after_vat', None),
                billing_unit_fee_after_discount=billing_unit_fee_after_discount,
                billing_unit_fee_after_discount_after_vat=billing_unit_fee_after_discount_after_vat,
                vat_rate=billing_info.get('vat_rate', None),
                billing_cycle=billing_info['billing_cycle'],
                billing_currency=billing_info['billing_currency'],
                connection_fee=billing_info['connection_fee'],
                connector_label=connector_label,
                connector_type=connector_type,
                connector_kind=connector_kind,
                connector_number=connector_number,
                subscription_plan=subscription_info.get('subscription_plan', None),
                subscription_custom_plan=subscription_info.get('subscription_custom_plan', None),
                campaign_promo_code_usage=billing_info.get("promo_code_usage"),
            )

            res = {
                'id': cs['id'],
                'connector': connector,
                'charging_session': charging_session,
                'charge_point': charge_point,
                'transaction_id': cs['transaction_id']
            }
            result[str(cs['id'])] = res
        return result
    logger.debug('charger response: %s', response.json())
    raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSession')


async def get_invoice(db: Session, charging_session_id: str, headers: dict,
                      login_user: Optional[User] = None, is_credit_note: bool = False) -> schema.Invoice:
    charging_session_bill = get_charging_session_bill_by_charging_session(db, charging_session_id)
    if not charging_session_bill:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')

    query = PaymentRequestCRUD.query(db, check_permission=False).filter(
        models.PaymentRequest.charging_session_bill_id == str(charging_session_bill.id),
    ).outerjoin(models.PaymentRefund).order_by(desc(models.PaymentRequest.created_at))
    pr = query.first()

    sub_discount = 0
    campaign_discount = 0
    if charging_session_bill.discount:
        sub_discount = charging_session_bill.discount.get('subscription_discount') or 0
        campaign_discount = charging_session_bill.discount.get('campaign_promo_code_discount') or 0

    cs_dict = await get_charging_session(db, charging_session_id, headers)
    cs_data = cs_dict.pop('charging_session')
    payment_type = '-'
    invoice_currency = None
    if not pr:
        promo_usage = 0
        try:
            db_member = MembershipCRUD.query(db).filter(
                func.lower(models.Membership.user_id_tag) == str(cs_data.id_tag).lower()
            ).one()
            member = db_member
            organization = db_member.organization
        except (NoResultFound, IntegrityError):
            try:
                db_member = IDTagCRUD.query(db).filter(
                    func.lower(models.IDTag.id_tag) == str(cs_data.id_tag).lower()
                ).one()
                db_member = db_member.member
                member = db_member
                organization = db_member.organization
            except (NoResultFound, IntegrityError) as e:
                logger.error(e)
                member = None
                organization = None
    #     raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRequest')
    else:
        promo_usage = pr.promo_usage
        invoice_currency = pr.currency
        if pr.type == schema.PaymentRequestType.payment_terminal:
            member = None
            payment_type = 'Payment Terminal'
            organization = None
        else:
            member = schema.ShallowMembershipResponse(**pr.meta)
            organization = pr.member.organization
            if pr.type == schema.PaymentRequestType.wallet:
                payment_type = 'Wallet'
            elif pr.type == schema.PaymentRequestType.recurring:
                payment_type = 'Credit-Card'
            elif pr.type == schema.PaymentRequestType.direct:
                payment_type = 'Credit-Card'
            elif pr.type == schema.PaymentRequestType.partial:
                payment_type = 'Partial'
            elif pr.type == schema.PaymentRequestType.partial_direct:
                payment_type = 'Partial'

    cs_data.session_start = cs_data.session_start.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))
    cs_data.session_end = cs_data.session_end.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))

    if hasattr(cs_data, 'hogging_start') and cs_data.hogging_start is not None:
        cs_data.hogging_start = cs_data.hogging_start.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))
    if hasattr(cs_data, 'hogging_end') and cs_data.hogging_end is not None:
        cs_data.hogging_end = cs_data.hogging_end.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))

    invoice_date = charging_session_bill.created_at.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))

    if invoice_currency is not None and invoice_currency in ['KHR']:
        cs_data.session_start = cs_data.session_start.astimezone(ZoneInfo('Asia/Bangkok'))
        cs_data.session_end = cs_data.session_end.astimezone(ZoneInfo('Asia/Bangkok'))

        if hasattr(cs_data, 'hogging_start') and cs_data.hogging_start is not None:
            cs_data.hogging_start = cs_data.hogging_start.astimezone(ZoneInfo('Asia/Bangkok'))
        if hasattr(cs_data, 'hogging_end') and cs_data.hogging_end is not None:
            cs_data.hogging_end = cs_data.hogging_end.astimezone(ZoneInfo('Asia/Bangkok'))

        invoice_date = charging_session_bill.created_at.astimezone(ZoneInfo('Asia/Bangkok'))

    cs_dict['charging_session'] = cs_data
    organization_logo = organization.logo if organization else None
    organization_logo_format_type = organization.logo_format_type if organization else None
    invoice_number = f'{charging_session_bill.meta["transaction_id"]}'
    if not is_credit_note:
        reference_number = f'{charging_session_bill.reference_number}'
    else:
        reference_number = f'{charging_session_bill.invoice_number}'

    display_connector_label = False
    if str(settings.OCPI_PARTY_ID).upper() != 'CEV':
        display_connector_label = True

    wallet_deduct_amount = 0
    non_wallet_deduct_amount = 0
    total_wallet_refunded_amount = 0
    total_non_wallet_refunded_amount = 0
    if pr:
        if pr.wallet_deduct_amount is not None:
            wallet_deduct_amount = float(pr.wallet_deduct_amount)
        if pr.non_wallet_deduct_amount is not None:
            non_wallet_deduct_amount = float(pr.non_wallet_deduct_amount)

        for refund in pr.payment_refund:
            if refund.refund_status == 'Success':
                if refund.refund_type == 'Credit Card':
                    total_wallet_refunded_amount += float(str(refund.refund_amount))
                else:
                    total_non_wallet_refunded_amount += float(str(refund.refund_amount))
    e_invoice = None
    try:
        if charging_session_bill.e_invoice is not None:
            e_invoice = schema.EInvoiceResponse.from_orm(charging_session_bill.e_invoice).dict()
    except Exception as e:
        logger.error("Error mapping e-invoice, ignoring with error as %s", str(e))

    invoice = schema.Invoice(
        invoice_number=invoice_number,
        reference_number=reference_number,
        date=invoice_date,
        organization=schema.OrganizationResponse.from_orm(organization) if organization else None,
        logo=base64.b64encode(organization_logo) if organization_logo else None,
        user=member.user if member else None,
        member=member if member else None,
        charge_point=cs_dict['charge_point'],
        charging_session=cs_dict['charging_session'],
        connector=cs_dict['connector'],
        display_connector_label=display_connector_label,
        amount=float(charging_session_bill.usage_amount),
        discount=float(promo_usage.amount) if promo_usage else 0,
        subscription_discount=sub_discount,
        campaign_promo_code_discount=campaign_discount,
        campaign_promo_code_usage=cs_dict["connector"].campaign_promo_code_usage,
        status=charging_session_bill.status,
        logo_format_type=organization_logo_format_type if organization_logo_format_type else None,
        tax_amount=charging_session_bill.tax_amount if charging_session_bill.tax_amount else 0.00,
        tax_rate=charging_session_bill.tax_rate if charging_session_bill.tax_rate else 0,
        total_amount=(
            charging_session_bill.total_amount 
            if charging_session_bill.total_amount is not None 
            else (
                float(charging_session_bill.usage_amount) - float(sub_discount) - float(campaign_discount) +
                float(charging_session_bill.tax_amount if charging_session_bill.tax_amount else 0.00) +
                float(charging_session_bill.hogging_fee if charging_session_bill.hogging_fee else 0.00)
            )
        ),
        payment_type=payment_type,
        currency=invoice_currency,
        hogging_fee=charging_session_bill.hogging_fee if charging_session_bill.hogging_fee else 0.00,
        hogging_amount=charging_session_bill.hogging_fee if charging_session_bill.hogging_fee else 0.00,
        wallet_deduct_amount=wallet_deduct_amount,
        non_wallet_deduct_amount=non_wallet_deduct_amount,
        total_wallet_refunded_amount = total_wallet_refunded_amount,
        total_non_wallet_refunded_amount=total_non_wallet_refunded_amount,
        e_invoice=e_invoice,
    )

    for attribute in branding_attributes:
        setattr(invoice, attribute, getattr(settings, attribute))

    if not settings.CAN_VIEW_SUB_ORG and login_user and not login_user.is_superuser:
        if member:
            cs_mem_org_id = member.organization_id
            mem_org_id = crud.MembershipCRUD.membership().organization_id
            all_child_org = [str(child) for child in utils.get_all_child_organizations(db, mem_org_id)]
            if str(cs_mem_org_id) != str(mem_org_id) and str(cs_mem_org_id) not in all_child_org:
                # Clear sensitive user information
                for field in ['phone_number', 'email', 'first_name', 'last_name']:
                    setattr(invoice.member.user, field, '')

    return invoice


async def get_single_credit_note(db: Session, refund_id: str, headers: dict,
                                 login_user: Optional[User] = None) -> schema.Invoice:
    try:
        db_refund = crud.get_payment_refund_by_refund_id(db, refund_id)
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='PaymentRefund')

    query = PaymentRequestCRUD.query(db, check_permission=False).filter(
        models.PaymentRequest.id == db_refund.payment_request_id
    ).join(
        models.PaymentRefund, 
        and_(
            models.PaymentRequest.id == models.PaymentRefund.payment_request_id,
            models.PaymentRefund.id == refund_id
        )
    ).options(
        contains_eager(models.PaymentRequest.payment_refund),
        joinedload(models.PaymentRequest.charging_session_bill)
    )
    pr = query.first()

    sub_discount = 0
    campaign_discount = 0
    if pr.charging_session_bill.discount:
        sub_discount = pr.charging_session_bill.discount.get('subscription_discount') or 0
        campaign_discount = pr.charging_session_bill.discount.get('campaign_promo_code_discount') or 0

    cs_dict = await get_charging_session(db, pr.charging_session_bill.charging_session_id, headers)
    cs_data = cs_dict.pop('charging_session')
    payment_type = '-'
    invoice_currency = None
    if not pr:
        promo_usage = 0
        try:
            db_member = MembershipCRUD.query(db).filter(
                func.lower(models.Membership.user_id_tag) == str(cs_data.id_tag).lower()
            ).one()
            member = db_member
            organization = db_member.organization
        except (NoResultFound, IntegrityError):
            try:
                db_member = IDTagCRUD.query(db).filter(
                    func.lower(models.IDTag.id_tag) == str(cs_data.id_tag).lower()
                ).one()
                db_member = db_member.member
                member = db_member
                organization = db_member.organization
            except (NoResultFound, IntegrityError) as e:
                logger.error(e)
                member = None
                organization = None
    else:
        promo_usage = pr.promo_usage
        invoice_currency = pr.currency
        if pr.type == schema.PaymentRequestType.payment_terminal:
            member = None
            payment_type = 'Payment Terminal'
            organization = None
        else:
            member = schema.ShallowMembershipResponse(**pr.meta)
            organization = pr.member.organization
            if pr.type == schema.PaymentRequestType.wallet:
                payment_type = 'Wallet'
            elif pr.type == schema.PaymentRequestType.recurring:
                payment_type = 'Credit-Card'
            elif pr.type == schema.PaymentRequestType.direct:
                payment_type = 'Credit-Card'
            elif pr.type == schema.PaymentRequestType.partial:
                payment_type = 'Partial'
            elif pr.type == schema.PaymentRequestType.partial_direct:
                payment_type = 'Partial'

    cs_data.session_start = cs_data.session_start.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))
    cs_data.session_end = cs_data.session_end.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))

    if hasattr(cs_data, 'hogging_start') and cs_data.hogging_start is not None:
        cs_data.hogging_start = cs_data.hogging_start.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))
    if hasattr(cs_data, 'hogging_end') and cs_data.hogging_end is not None:
        cs_data.hogging_end = cs_data.hogging_end.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))

    invoice_date = pr.charging_session_bill.created_at.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))

    if invoice_currency is not None and invoice_currency in ['KHR']:
        cs_data.session_start = cs_data.session_start.astimezone(ZoneInfo('Asia/Bangkok'))
        cs_data.session_end = cs_data.session_end.astimezone(ZoneInfo('Asia/Bangkok'))

        if hasattr(cs_data, 'hogging_start') and cs_data.hogging_start is not None:
            cs_data.hogging_start = cs_data.hogging_start.astimezone(ZoneInfo('Asia/Bangkok'))
        if hasattr(cs_data, 'hogging_end') and cs_data.hogging_end is not None:
            cs_data.hogging_end = cs_data.hogging_end.astimezone(ZoneInfo('Asia/Bangkok'))

        invoice_date = pr.charging_session_bill.created_at.astimezone(ZoneInfo('Asia/Bangkok'))

    cs_dict['charging_session'] = cs_data
    organization_logo = organization.logo if organization else None
    organization_logo_format_type = organization.logo_format_type if organization else None
    credit_note_number = f"{pr.payment_refund[0].reference_id}"
    reference_number = f'{pr.invoice_number}'
    try:
        reference_number = f'{pr.charging_session_bill.meta["transaction_id"]}'
    except Exception as e:  # pylint: disable=broad-except
        logger.error("Failing to get transaction from charging session bill with error as: %s", str(e))

    display_connector_label = False
    if str(settings.OCPI_PARTY_ID).upper() != 'CEV':
        display_connector_label = True

    wallet_deduct_amount = 0
    non_wallet_deduct_amount = 0
    total_wallet_refunded_amount = 0
    total_non_wallet_refunded_amount = 0
    if pr:
        if pr.wallet_deduct_amount is not None:
            wallet_deduct_amount = float(pr.wallet_deduct_amount)
        if pr.non_wallet_deduct_amount is not None:
            non_wallet_deduct_amount = float(pr.non_wallet_deduct_amount)

        for refund in pr.payment_refund:
            if refund.refund_status == 'Success':
                if refund.refund_type == 'Credit Card':
                    total_wallet_refunded_amount += float(str(refund.refund_amount))
                else:
                    total_non_wallet_refunded_amount += float(str(refund.refund_amount))

    invoice = schema.Invoice(
        invoice_number=credit_note_number,
        reference_number=reference_number,
        date=invoice_date,
        organization=schema.OrganizationResponse.from_orm(organization) if organization else None,
        logo=base64.b64encode(organization_logo) if organization_logo else None,
        user=member.user if member else None,
        member=member if member else None,
        charge_point=cs_dict['charge_point'],
        charging_session=cs_dict['charging_session'],
        connector=cs_dict['connector'],
        display_connector_label=display_connector_label,
        amount=float(pr.charging_session_bill.usage_amount),
        discount=float(promo_usage.amount) if promo_usage else 0,
        subscription_discount=sub_discount,
        campaign_promo_code_discount=campaign_discount,
        campaign_promo_code_usage=cs_dict["connector"].campaign_promo_code_usage,
        status=pr.charging_session_bill.status,
        logo_format_type=organization_logo_format_type if organization_logo_format_type else None,
        tax_amount=pr.charging_session_bill.tax_amount if pr.charging_session_bill.tax_amount else 0.00,
        tax_rate=pr.charging_session_bill.tax_rate if pr.charging_session_bill.tax_rate else 0,
        total_amount=(
            pr.charging_session_bill.total_amount 
            if pr.charging_session_bill.total_amount is not None 
            else (
                float(pr.charging_session_bill.usage_amount) - float(sub_discount) - float(campaign_discount) +
                float(pr.charging_session_bill.tax_amount if pr.charging_session_bill.tax_amount else 0.00) +
                float(pr.charging_session_bill.hogging_fee if pr.charging_session_bill.hogging_fee else 0.00)
            )
        ),
        payment_type=payment_type,
        currency=invoice_currency,
        hogging_fee=pr.charging_session_bill.hogging_fee if pr.charging_session_bill.hogging_fee else 0.00,
        hogging_amount=pr.charging_session_bill.hogging_fee if pr.charging_session_bill.hogging_fee else 0.00,
        wallet_deduct_amount=wallet_deduct_amount,
        non_wallet_deduct_amount=non_wallet_deduct_amount,
        total_wallet_refunded_amount = total_wallet_refunded_amount,
        total_non_wallet_refunded_amount=total_non_wallet_refunded_amount,
    )

    for attribute in branding_attributes:
        setattr(invoice, attribute, getattr(settings, attribute))

    if not settings.CAN_VIEW_SUB_ORG and login_user and not login_user.is_superuser:
        if member:
            cs_mem_org_id = member.organization_id
            mem_org_id = crud.MembershipCRUD.membership().organization_id
            all_child_org = [str(child) for child in utils.get_all_child_organizations(db, mem_org_id)]
            if str(cs_mem_org_id) != str(mem_org_id) and str(cs_mem_org_id) not in all_child_org:
                # Clear sensitive user information
                for field in ['phone_number', 'email', 'first_name', 'last_name']:
                    setattr(invoice.member.user, field, '')

    return invoice


async def get_charging_session_invoice_list(db: Session, filter_params: dict = {},  # noqa: MC0001
                                            user_filters: dict = {}, params=None, operator_filter=True,
                                            filter_ocpi=True) -> dict:  # noqa
    query = BillingCRUD.query(db).options(
        joinedload(models.ChargingSessionBill.payment_requests)
        .joinedload(models.PaymentRequest.payment_refund))
    query = query.order_by(desc(models.ChargingSessionBill.created_at))
    query = query.outerjoin(models.PaymentRequest).outerjoin(models.Membership).outerjoin(models.User)
    
    if 'refund_status' in filter_params and filter_params['refund_status']:
        has_refund_subquery = db.query(
            models.PaymentRefund.payment_request_id
        ).group_by(
            models.PaymentRefund.payment_request_id
        ).subquery()

        refund_subquery = db.query(
            models.PaymentRefund.payment_request_id,
            func.sum(cast(models.PaymentRefund.refund_amount, Numeric)).label('total_refund')
        ).filter(
            models.PaymentRefund.refund_status == schema.RefundStatus.success
        ).group_by(
            models.PaymentRefund.payment_request_id
        ).subquery()

        query = query.join(
            has_refund_subquery,
            models.PaymentRequest.id == has_refund_subquery.c.payment_request_id
        ).outerjoin(
            refund_subquery,
            models.PaymentRequest.id == refund_subquery.c.payment_request_id
        )
        
        if filter_params['refund_status'] == 'Partial':
            query = query.filter(
                cast(models.PaymentRequest.amount, Numeric) - func.coalesce(refund_subquery.c.total_refund, 0) > 0,
                models.PaymentRequest.status == schema.PaymentRequestStatus.done
            )
        elif filter_params['refund_status'] == 'Full':
            query = query.filter(
                cast(models.PaymentRequest.amount, Numeric) - func.coalesce(refund_subquery.c.total_refund, 0) == 0,
                models.PaymentRequest.status == schema.PaymentRequestStatus.done
            )

    if filter_ocpi:
        query = query.filter(or_(models.ChargingSessionBill.charging_session_type != 'OCPI-CPO',
                                 models.ChargingSessionBill.charging_session_type.is_(None)))

    if 'date_from' in filter_params and filter_params['date_from']:
        query = query.filter(models.ChargingSessionBill.created_at >= filter_params['date_from'])
    if 'date_to' in filter_params and filter_params['date_to']:
        query = query.filter(models.ChargingSessionBill.created_at < filter_params['date_to'])
    if 'status' in filter_params and filter_params['status']:
        query = query.filter(func.lower(models.ChargingSessionBill.status) == str(filter_params['status']).lower())
    if 'cs_ids' in filter_params and filter_params['cs_ids']:
        query = query.filter(models.ChargingSessionBill.charging_session_id.in_(filter_params['cs_ids']))

    if 'reference_number' in filter_params and filter_params['reference_number']:
        reference_number = filter_params['reference_number']
        query = query.filter(models.ChargingSessionBill.reference_number.ilike(f'%{reference_number}%'))

    if 'invoice_number' in filter_params and filter_params['invoice_number']:
        invoice_number = filter_params['invoice_number']
        invoice_number = invoice_number.split('-')
        prefix = None
        transaction_id = None
        if len(invoice_number) == 1:
            if invoice_number[0].isdigit():
                transaction_id = invoice_number[0]
            else:
                prefix = invoice_number[0]
        if len(invoice_number) == 2:
            prefix = invoice_number[0]
            transaction_id = invoice_number[1]

        if prefix:
            query = query.filter(models.ChargingSessionBill.meta['organization_name'].astext.ilike(f'%{prefix}%'))
        if transaction_id:
            query = query.filter(models.ChargingSessionBill.meta['transaction_id'].astext.ilike(f'%{transaction_id}%'))
    filter_params['username'] = filter_params['username'] if 'username' in filter_params else None
    if filter_params['username']:
        username = filter_params['username']
        query = query.filter(
            (models.Membership.first_name + ' ' + models.Membership.last_name).ilike(username)
        )
        if user_filters:
            query = filter_user(query, user_filters)
    elif user_filters:
        query = filter_user(query, user_filters)
    if operator_filter:
        membership = BillingCRUD.membership()
        operators = get_operator_by_organization_id_include_child(db, membership)
        operator = []
        if 'operator' in filter_params and filter_params['operator']:
            if isinstance(filter_params['operator'], list):
                operator_id_list = filter_params['operator']
            else:
                operator_id_list = [id.strip() for id in filter_params['operator'].split(',')]
            operator = db.query(models.Operator).filter(models.Operator.id.in_(operator_id_list)) \
                .with_entities(models.Operator.id).all()
            operator = [op[0] for op in operator]
            operators = [op for op in operators if op in operator]
            if membership.user.is_superuser:
                operators = operator
        chargepoint = db.query(models.OperatorChargepoint.charge_point_id) \
            .filter(models.OperatorChargepoint.operator_id.in_(operators)).all()
        chargepoint_ids = [str(cp[0]) for cp in chargepoint]
        chargepoint_op = chargepoint_ids
        filter_params['charge_point_id'] = filter_params[
            'charge_point_id'] if 'charge_point_id' in filter_params else None
        filter_params['operator'] = filter_params['operator'] if 'operator' in filter_params else None
        if filter_params['charge_point_id']:
            chargepoint_ids = [cp for cp in chargepoint_ids if cp in filter_params['charge_point_id']]
            if membership.user.is_superuser:
                cps = filter_params['charge_point_id']
                if isinstance(cps, str):
                    chargepoint_ids = [cps]
                else:
                    chargepoint_ids = filter_params['charge_point_id']
        evse_connectors_ops = filter_params['evse_connectors_ops'] if 'evse_connectors_ops' in filter_params else None
        if filter_params['charge_point_id'] and filter_params['operator'] and filter_params['evse_connectors_ids']:
            cps_ops = [cp for cp in chargepoint_ids if cp in set(chargepoint_op)]
            query = query.filter(
                models.PaymentRequest.connector_id.in_(list(set(cps_ops + filter_params['evse_connectors_ids']))))
        elif filter_params['charge_point_id'] and filter_params['evse_connectors_ids']:
            query = query.filter(models.PaymentRequest.connector_id.in_(
                list(set(chargepoint_ids + filter_params['evse_connectors_ids']))))
        elif filter_params['charge_point_id']:
            query = query.filter(models.PaymentRequest.connector_id.in_(chargepoint_ids))
        elif 'evse_connectors_ids' in filter_params and filter_params['evse_connectors_ids']:
            query = query.filter(models.PaymentRequest.connector_id.in_(filter_params['evse_connectors_ids']))
        if filter_params.get('payment_type'):
            if filter_params['payment_type'] == 'Wallet':
                query = query.filter(models.PaymentRequest.type == PaymentRequestType.wallet)
            elif filter_params['payment_type'] == 'Credit':
                # query = query.filter(models.PaymentRequest.type == PaymentRequestType.recurring)
                query = query.filter(or_(models.PaymentRequest.type == PaymentRequestType.recurring,
                                         models.PaymentRequest.type == PaymentRequestType.direct))
            elif filter_params['payment_type'] == 'Direct':
                query = query.filter(models.PaymentRequest.type == PaymentRequestType.direct)
            elif filter_params['payment_type'] == 'Partial':
                # Add in filter for partial direct
                query = query.filter(or_(models.PaymentRequest.type == PaymentRequestType.partial,
                                         models.PaymentRequest.type == PaymentRequestType.partial_direct))
            else:
                query = query.filter(models.PaymentRequest.type == PaymentRequestType.payment_terminal)
        if filter_params.get('currency'):
            query = query.filter(func.lower(models.PaymentRequest.currency) == str(filter_params['currency']).lower())
        if operator and evse_connectors_ops:
            ext_cp_ids = evse_connectors_ops + chargepoint_ids
            query = query.filter(models.PaymentRequest.connector_id.in_(ext_cp_ids))
        elif evse_connectors_ops:
            query = query.filter(models.PaymentRequest.connector_id.in_(evse_connectors_ops))
        elif operator:
            if not filter_params.get('is_charging_history'):
                query = query.filter(models.PaymentRequest.connector_id.in_(chargepoint_ids))

        mem_org = get_organization_by_id(db, membership.organization_id)
        parent_org = mem_org.parent
        if parent_org and not filter_params.get('is_charging_history'):
            if filter_params.get('allowed_ocpi_connectors'):
                try:
                    chargepoint_ids = (list(set(chargepoint_ids + filter_params['allowed_ocpi_connectors'])))
                except Exception as e:  # pylint: disable=broad-except
                    print("Error adding native cps to ocpi cps on invoice: %s", str(e))
            if settings.USE_CSB_INSTEAD_OF_PR_FOR_INVOICES:
                # query = query.filter(models.ChargingSessionBill.charge_point_id.in_(chargepoint_ids))
                query = query.filter(
                    or_(
                        models.PaymentRequest.connector_id.in_(chargepoint_ids),
                        models.ChargingSessionBill.charge_point_id.in_(chargepoint_ids),
                    )
                )
            else:
                query = query.filter(models.PaymentRequest.connector_id.in_(chargepoint_ids))

    if params:
        cs_paginate = paginate(query, params)
        charging_session_bill = cs_paginate.items
    else:
        charging_session_bill = query.all()

    bill_dict = {bill.charging_session_id: bill.__dict__ for bill in charging_session_bill}
    bill_cs_mapping = {bill.id: bill.charging_session_id for bill in charging_session_bill}
    bill_cs_list = [str(bill.id) for bill in charging_session_bill]
    # print(bill_cs_mapping)

    payment_request = PaymentRequestCRUD.query(db, check_permission=False) \
        .options(joinedload(models.PaymentRequest.member).joinedload(models.Membership.user),
                 joinedload(models.PaymentRequest.member).joinedload(models.Membership.organization),
                 joinedload(models.PaymentRequest.payment_refund)) \
        .order_by(desc(models.PaymentRequest.created_at)) \
        .filter(~models.PaymentRequest.billing_description.ilike('%subscription%')).join(
        models.ChargingSessionBill).filter(models.ChargingSessionBill.id.in_(bill_cs_list)).all()

    for pr in payment_request:
        if pr.charging_session_bill_id in bill_cs_mapping:
            cs_id = bill_cs_mapping[pr.charging_session_bill_id]
            if pr.member is None:
                bill_dict[cs_id]['member'] = None
                bill_dict[cs_id]['user'] = None
                bill_dict[cs_id]['user_name'] = None
                bill_dict[cs_id]['user_phone'] = None
                bill_dict[cs_id]['user_email'] = None
                bill_dict[cs_id]['organization'] = None
            else:
                bill_dict[cs_id]['member'] = pr.member
                bill_dict[cs_id]['user'] = pr.member.user if pr.member.user is not None else None
                bill_dict[cs_id]['user_name'] = f'{pr.member.first_name} {pr.member.last_name}' \
                    if pr.member.first_name else None
                bill_dict[cs_id]['user_phone'] = pr.member.user.phone_number \
                    if pr.member.user and pr.member.user.phone_number else None
                bill_dict[cs_id]['user_email'] = pr.member.user.email \
                    if pr.member.user and pr.member.user.email else None
                bill_dict[cs_id][
                    'organization'] = pr.member.organization if pr.member.organization is not None else None
            # bill_dict[cs_id]['member'] = pr.member
            # bill_dict[cs_id]['user'] = pr.member.user
            # bill_dict[cs_id]['user_name'] = f'{pr.member.first_name} {pr.member.last_name}'
            bill_dict[cs_id]['promo_usage'] = pr.promo_usage
            bill_dict[cs_id]['payment_type'] = pr.type
            bill_dict[cs_id]['currency'] = pr.currency
            bill_dict[cs_id]['wallet_deduct_amount'] = pr.wallet_deduct_amount
            bill_dict[cs_id]['non_wallet_deduct_amount'] = pr.non_wallet_deduct_amount
            bill_dict[cs_id]['payment_refund'] = pr.payment_refund
    if params:
        return {'items': bill_dict, 'total': cs_paginate.total, 'size': cs_paginate.size, 'page': cs_paginate.page}
    return bill_dict


async def get_charging_session_invoice_list_optimized(db: Session, filter_params: dict = {},  # noqa: MC0001
                                                      user_filters: dict = {}, params=None) -> dict:  # noqa
    query = BillingCRUD.query(db).outerjoin(models.PaymentRequest).order_by(desc(models.ChargingSessionBill.created_at))
    if 'date_from' in filter_params and filter_params['date_from']:
        query = query.filter(models.ChargingSessionBill.created_at >= filter_params['date_from'])
    if 'date_to' in filter_params and filter_params['date_to']:
        query = query.filter(models.ChargingSessionBill.created_at < filter_params['date_to'])
    if 'status' in filter_params and filter_params['status']:
        query = query.filter(func.lower(models.ChargingSessionBill.status) == str(filter_params['status']).lower())
    if 'cs_ids' in filter_params and filter_params['cs_ids']:
        query = query.filter(models.ChargingSessionBill.charging_session_id.in_(filter_params['cs_ids']))
    if 'invoice_number' in filter_params and filter_params['invoice_number']:
        invoice_number = filter_params['invoice_number']
        invoice_number = invoice_number.split('-')
        prefix = None
        transaction_id = None
        if len(invoice_number) == 1:
            if invoice_number[0].isdigit():
                transaction_id = invoice_number[0]
            else:
                prefix = invoice_number[0]
        if len(invoice_number) == 2:
            prefix = invoice_number[0]
            transaction_id = invoice_number[1]

        if prefix:
            query = query.filter(models.ChargingSessionBill.meta['organization_name'].astext.ilike(f'%{prefix}%'))
        if transaction_id:
            query = query.filter(models.ChargingSessionBill.meta['transaction_id'].astext.ilike(f'%{transaction_id}%'))

    if 'username' in filter_params and filter_params['username']:
        username = filter_params['username']
        query = query.join(models.Membership).filter(
            (models.Membership.first_name + ' ' + models.Membership.last_name).ilike(username)
        )
        if user_filters:
            query = query.join(models.User)
            query = filter_user(query, user_filters)
    else:
        query = query.join(models.Membership).join(models.User)
        query = filter_user(query, user_filters)
    membership = BillingCRUD.membership()
    operators = get_operator_by_organization_id_include_child(db, membership)
    if 'operator' in filter_params and filter_params['operator']:
        operator = db.query(models.Operator).filter(models.Operator.name.ilike(f'%{filter_params["operator"]}%')) \
            .with_entities(models.Operator.id).all()
        operator = [op[0] for op in operator]
        operators = [op for op in operators if op in operator]
        if membership.user.is_superuser:
            operators = operator
    chargepoint = db.query(models.OperatorChargepoint.charge_point_id) \
        .filter(models.OperatorChargepoint.operator_id.in_(operators)).all()
    chargepoint_ids = [str(cp[0]) for cp in chargepoint]
    if 'charge_point_id' in filter_params and filter_params['charge_point_id']:
        chargepoint_ids = [cp for cp in chargepoint_ids if cp in filter_params['charge_point_id']]
        if membership.user.is_superuser:
            chargepoint_ids = [filter_params['charge_point_id']]
    if 'charge_point_id' in filter_params or 'operator' in filter_params:
        query = query.filter(models.PaymentRequest.connector_id.in_(chargepoint_ids))

    if 'usage_amount' in filter_params and filter_params['usage_amount']:
        if filter_params['usage_amount'] == 0:
            query = query.filter(or_(models.ChargingSessionBill.usage_amount == filter_params['usage_amount'],
                                     models.ChargingSessionBill.usage_amount.is_(None)))
        query = query.filter(models.ChargingSessionBill.usage_amount == filter_params['usage_amount'])
    if params:
        cs_paginate = paginate(query, params)
        charging_session_bill = cs_paginate.items
    else:
        charging_session_bill = query.all()
    bill_dict = {bill.charging_session_id: bill.__dict__ for bill in charging_session_bill}

    if params:
        return {'items': bill_dict, 'total': cs_paginate.total, 'size': cs_paginate.size, 'page': cs_paginate.page}
    return bill_dict


def simplified_invoice_reconcilation(db: Session, cs_ids: dict = {}) -> tuple[dict, dict]:  # noqa
    query = BillingCRUD.query(db) \
        .filter(models.ChargingSessionBill.charging_session_id.in_(cs_ids)) \
        .order_by(desc(models.ChargingSessionBill.created_at)).all()
    # if 'date_from' in filter_params and filter_params['date_from']:
    #     query = query.filter(models.ChargingSessionBill.created_at >= filter_params['date_from'])
    # if 'date_to' in filter_params and filter_params['date_to']:
    #     query = query.filter(models.ChargingSessionBill.created_at < filter_params['date_to'])

    bill_dict = {}
    bill_ids = []
    for bill in query:
        bill_dict[bill.charging_session_id] = bill
        bill_ids.append(str(bill.id))

    query_payment = PaymentRequestCRUD.query(db) \
        .filter(models.PaymentRequest.charging_session_bill_id.in_(bill_ids)).all()
    pr_data = {pr.charging_session_bill.charging_session_id: pr for pr in query_payment}
    return bill_dict, pr_data


def reconcilation_invoice_filtered(db: Session, filters, params, cp_ids, partner_ids):
    query = BillingCRUD.query(db, models.ChargingSessionBill.charging_session_id)
    if filters['invoice_generated'] is not None:
        subquery = PaymentRequestCRUD.query(db, models.PaymentRequest.charging_session_bill_id) \
            .filter(models.PaymentRequest.charging_session_bill_id.is_not(None)).subquery()
        if not filters['invoice_generated']:
            query = query.filter(models.ChargingSessionBill.id.notin_(select([subquery])))
        else:
            query = query.filter(models.ChargingSessionBill.id.in_(select([subquery])))
    type_ = filters.get('type')
    if type_ is not None:
        if type_ == 'OCPI-EMSP':
            query = query.filter(models.ChargingSessionBill.charging_session_type.in_(('PARTNER',
                                                                                       'OCPI-EMSP')))
        elif type_ == 'Native':
            query = query.filter(models.ChargingSessionBill.charging_session_type == 'Local')
        else:
            query = query.filter(models.ChargingSessionBill.charging_session_type == type_)
    if filters['order_id']:
        query = query.filter(models.ChargingSessionBill.meta['transaction_id'] \
                             .astext.ilike(f'%{filters["order_id"]}%'))
    if filters['date_from']:
        query = query.filter(models.ChargingSessionBill.created_at >= filters['date_from'])
    if filters['date_to']:
        query = query.filter(models.ChargingSessionBill.created_at < filters['date_to'])

    if filters['username']:
        subquery = PaymentRequestCRUD.query(db, models.PaymentRequest.charging_session_bill_id)
        subquery = subquery.join(models.Membership)
        subquery = subquery.filter(
            func.concat(models.Membership.first_name, ' ',
                        models.Membership.last_name).ilike(f'%{filters["username"]}%')
        ).subquery()
        query = query.filter(or_(models.ChargingSessionBill.id_tag.ilike(f'%{filters["username"]}%'),
                                 models.ChargingSessionBill.charge_point_id.in_(subquery)))
    if cp_ids:
        query = query.filter(models.ChargingSessionBill.charge_point_id.in_(cp_ids))
    if partner_ids:
        query = query.filter(models.ChargingSessionBill.partner_id.in_(partner_ids))
    subquery_all = reconcilation_invoice_filtered_subquery(db, filters)
    if subquery_all is not None:
        query = query.filter(models.ChargingSessionBill.id.in_(subquery_all))
    data = paginate(query, params)
    ids = [item.get('charging_session_id') for item in data.items]
    data.items = ids
    return data


def reconcilation_invoice_filtered_subquery(db: Session, filters):
    keys_to_check = ['payment_type', 'currency']
    have_query = any(filters.get(key) is not None for key in keys_to_check)
    subquery = PaymentRequestCRUD.query(db, models.PaymentRequest.charging_session_bill_id)
    # if filters['payment_type']:
    #     subquery = subquery.filter(models.PaymentRequest.type.ilike(filters['payment_type']))
    if filters.get('payment_type'):
        if filters['payment_type'] == 'Wallet':
            subquery = subquery.filter(models.PaymentRequest.type == PaymentRequestType.wallet)
        elif filters['payment_type'] == 'Credit-card':
            subquery = subquery.filter(models.PaymentRequest.type == PaymentRequestType.recurring)
        elif filters['payment_type'] == 'Direct':
            subquery = subquery.filter(models.PaymentRequest.type == PaymentRequestType.direct)
        else:
            subquery = subquery.filter(models.PaymentRequest.type == PaymentRequestType.payment_terminal)
    if filters['currency']:
        subquery = subquery.filter(models.PaymentRequest.currency.ilike(filters['currency']))
    if filters['username']:
        subquery = subquery.join(models.Membership)
        subquery = subquery.filter(
            func.concat(models.Membership.first_name, ' ',
                        models.Membership.last_name).ilike(f'%{filters["username"]}%')
        )
    if have_query:
        return subquery.subquery()
    else:
        return None


def transform_schema(cs: dict, operators: dict):
    cp = cs['charge_point_connector']['charge_point']
    charge_point = schema.ChargePoint(
        serial_number=cs['meta']['charger_serial_number'],
        charge_box_serial_number=cp['charge_box_serial_number'] if 'charge_box_serial_number' in cp else None,
        location=cs['meta']['location'],
        operator=operators.get(cs['meta']['operator']['id'], None)
    )

    charging_session = schema.ChargingSession(
        id=cs['id'],
        session_start=cs['session_start'],
        session_end=cs['session_end'],
        duration=datetime.fromisoformat(cs['session_end']) - datetime.fromisoformat(cs['session_start']),
        charging_usage=round(((cs['meter_stop'] - cs['meter_start']) / 1000), 3)
    )

    billing_info = cs['meta']['billing_info']
    connector = schema.Connector(
        billing_type=billing_info['billing_type'],
        billing_unit_fee=billing_info['billing_unit_fee'],
        billing_cycle=billing_info['billing_cycle'],
        billing_currency=billing_info['billing_currency'],
        connection_fee=billing_info['connection_fee'],
        connector_type=cs['charge_point_connector']['connector_type']['name'],
    )

    return {'charge_point': charge_point, 'charging_session': charging_session, 'connector': connector}


async def get_member_invoice_list_optimized(db: Session, filter_params: dict, request,  # noqa: MC0001
                                            user_filters: dict, params, headers, use_task: bool = False) -> Page[
    schema.Invoice]:
    invoice_list = []
    bills = await get_charging_session_invoice_list(db, filter_params=filter_params,
                                                    user_filters=user_filters, params=params)
    logger.debug('header for request to charger service: %s', headers)
    bills_items = bills['items'].items()
    cs_ids = [k for k, _ in bills_items]
    mem_org_id = BillingCRUD.membership().organization_id
    mem_org = get_organization_by_id(db, mem_org_id)
    allow_ocpi = True
    if mem_org.parent and mem_org.parent.parent:
        allow_ocpi = False
    cs_dict = await get_charging_session_list(db, cs_ids, headers, allow_ocpi, params, use_task)
    for cs_id, bill in bills_items:
        promo_usage = bill['promo_usage'] if 'promo_usage' in bill else None
        cs = cs_dict.get(cs_id, None)
        if 'member' in bill:
            member = bill['member']
            user = bill['user']
            organization = bill['organization']
            logo_type = organization.logo_format_type if organization else None
        else:
            try:
                db_member = MembershipCRUD.query(db).options(joinedload(models.Membership.user)).filter(
                    func.lower(models.Membership.user_id_tag) == str(cs['charging_session'].id_tag).lower()
                ).one()
                member = db_member
                user = db_member.user
                organization = db_member.organization
                logo_type = db_member.organization.logo_format_type
            except (NoResultFound, IntegrityError):
                try:
                    db_member = IDTagCRUD.query(db).options(joinedload(models.IDTag.member)
                                                            .joinedload(models.Membership.user)).filter(
                        func.lower(models.IDTag.id_tag) == str(cs['charging_session'].id_tag).lower()
                    ).one()
                    db_member = db_member.member
                    member = db_member
                    user = db_member.user
                    organization = db_member.organization
                    logo_type = db_member.organization.logo_format_type
                except (NoResultFound, IntegrityError) as e:
                    logger.error(e)
                    member = None
                    user = None
                    organization = None
                    logo_type = None

        # invoice_number_prefix = None
        # if organization:
        #     invoice_number_prefix = organization.name[:3].upper() if len(organization.name) >= 3 \
        #         else organization.name.upper()
        promo_discount = float(promo_usage.amount) if promo_usage else 0
        campaign_discount = 0
        # sub_discount = bill['discount']['subscription_discount'] \
        #     if bill['discount'] else 0
        if bill['discount']:
            if isinstance(bill['discount'], dict):
                sub_discount = bill['discount']['subscription_discount']
                campaign_discount = bill['discount'].get('campaign_promo_code_discount', 0)
            elif isinstance(bill['discount'], float):
                sub_discount = float(bill['discount'])
        invoice_number = f'{bill["meta"]["organization_name"]}-{bill["meta"]["transaction_id"]}'
        reference_number = f'{bill["reference_number"]}'
        try:
            if bill['payment_type'] == schema.PaymentRequestType.wallet:
                payment_type = "Wallet"
            elif bill['payment_type'] == schema.PaymentRequestType.recurring:
                payment_type = 'Credit-Card'
            elif bill['payment_type'] == schema.PaymentRequestType.payment_terminal:
                payment_type = 'Payment Terminal'
            elif bill['payment_type'] == schema.PaymentRequestType.direct:
                payment_type = 'Credit-Card'
            elif bill['payment_type'] == schema.PaymentRequestType.partial:
                payment_type = 'Partial'
            elif bill['payment_type'] == schema.PaymentRequestType.partial_direct:
                payment_type = 'Partial'
            else:
                payment_type = None
        except KeyError:
            payment_type = None
        try:
            if bill['currency'] in [schema.Currency.myr, schema.Currency.rm]:
                currency = 'MYR'
            elif bill['currency'] == schema.Currency.sgd:
                currency = 'SGD'
            elif bill['currency'] == schema.Currency.bnd:
                currency = 'BND'
            elif bill['currency'] == schema.Currency.khr:
                currency = 'KHR'
            elif bill['currency'] == schema.Currency.thb:
                currency = 'THB'
            elif bill['currency'] == schema.Currency.idr:
                currency = 'IDR'
            else:
                currency = None
        except KeyError:
            currency = None
        if cs is not None:
            display_connector_label = False
            if str(settings.OCPI_PARTY_ID).upper() != 'CEV':
                display_connector_label = True

            wallet_deduct_amount = 0
            non_wallet_deduct_amount = 0

            total_amount = (bill['total_amount']
                            if bill['total_amount'] is not None
                            else float(bill['usage_amount']) - float(sub_discount) +
                                 float(bill['tax_amount'] if bill['tax_amount']
                                       else 0.00) + float(bill['hogging_fee'] if bill['hogging_fee'] else 0.00))
            if bill:
                wallet_deduct_amount = bill.get('wallet_deduct_amount', 0.0)
                non_wallet_deduct_amount = bill.get('non_wallet_deduct_amount', 0.0)
                if wallet_deduct_amount is not None:
                    wallet_deduct_amount = float(wallet_deduct_amount)
                if non_wallet_deduct_amount is not None:
                    non_wallet_deduct_amount = float(non_wallet_deduct_amount)
                if payment_type:
                    if payment_type == 'Credit-Card':
                        non_wallet_deduct_amount = total_amount
                    elif payment_type == 'Wallet':
                        wallet_deduct_amount = total_amount

            payment_refund_list = []
            total_non_wallet_refunded_amount = 0
            total_wallet_refunded_amount = 0
            if bill:
                for refund in bill.get('payment_refund', []):
                    if refund.refund_status == 'Success':
                        if refund.refund_type == 'Credit Card':
                            total_non_wallet_refunded_amount += Decimal(str(refund.refund_amount))\
                                .quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                        else:
                            total_wallet_refunded_amount += Decimal(str(refund.refund_amount))\
                                .quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                    payment_refund_list.append(schema.PaymentRefundResponse(
                        id=refund.id,
                        payment_request_id=refund.payment_request_id,
                        refund_amount=refund.refund_amount,
                        refund_type=refund.refund_type,
                        refund_status=refund.refund_status,
                        remark=refund.remark,
                        reference_id=refund.reference_id
                    ))
            refund_status = None
            if bill['status'] == 'Paid':
                if total_amount - (total_non_wallet_refunded_amount + total_wallet_refunded_amount) == 0 and bill.get('payment_refund', []):
                    refund_status = 'Full Refund'
                elif total_amount - (total_non_wallet_refunded_amount + total_wallet_refunded_amount) > 0 and bill.get('payment_refund', []):
                    refund_status = 'Partial Refund'
                else:
                    refund_status = 'Non Applicable'

            invoice_list.append(
                schema.Invoice(
                    invoice_number=invoice_number,
                    reference_number=reference_number,
                    date=bill['created_at'],
                    organization=schema.OrganizationSimpleResponse.from_orm(organization) if organization else None,
                    logo=base64.b64encode(organization.logo) if organization and organization.logo else None,
                    user=user,
                    member=member,
                    charge_point=cs['charge_point'],
                    charging_session=cs['charging_session'],
                    connector=cs['connector'],
                    display_connector_label=display_connector_label,
                    amount=float(bill['usage_amount']),
                    discount=promo_discount,
                    subscription_discount=sub_discount,
                    campaign_promo_code_discount=campaign_discount,
                    campaign_promo_code_usage=cs["connector"].campaign_promo_code_usage,
                    status=bill['status'],
                    currency=currency,
                    payment_type=payment_type,
                    logo_format_type=organization.logo_format_type if logo_type else None,
                    tax_amount=bill['tax_amount'] if bill['tax_amount'] else 0.00,
                    tax_rate=bill['tax_rate'] if bill['tax_rate'] else 0,
                    total_amount=total_amount,
                    hogging_fee=bill['hogging_fee'] if bill['hogging_fee'] else 0.00,
                    hoggin_amount=bill['hogging_fee'] if bill['hogging_fee'] else 0.00,
                    wallet_deduct_amount=wallet_deduct_amount,
                    non_wallet_deduct_amount=non_wallet_deduct_amount,
                    total_non_wallet_refunded_amount=total_non_wallet_refunded_amount,
                    total_wallet_refunded_amount=total_wallet_refunded_amount,
                    payment_refund=payment_refund_list,
                    refund_status=refund_status
                )
            )
        else:
            invoice_list = []
            bills['page'] = 1
            bills['size'] = 50
            bills['total'] = 0

    return {'items': invoice_list, 'page': bills['page'], 'size': bills['size'], 'total': bills['total']}


async def get_member_invoice_list(db: Session, headers: dict, filter_params: dict) -> List[schema.Invoice]:
    invoice_list = []

    query = PaymentRequestCRUD.query(db).order_by(desc(models.PaymentRequest.created_at))

    # Apply date filters
    if filter_params['date_from']:
        query = query.filter(models.PaymentRequest.created_at >= filter_params['date_from'])
    if filter_params['date_to']:
        query = query.filter(models.PaymentRequest.created_at < filter_params['date_to'])
    query = query.filter(~models.PaymentRequest.billing_description.ilike('%subscription%'))
    for pr in query.all():
        promo_usage = pr.promo_usage
        member = schema.ShallowMembershipResponse(**pr.meta)
        organization = pr.member.organization

        try:
            charging_session_bill = get_charging_session_bill(db, pr.charging_session_bill_id)
        except exceptions.ApolloObjectDoesNotExist:
            continue

        cs_dict = await get_charging_session(db, charging_session_bill.charging_session_id, headers)
        sub_discount = charging_session_bill.discount['subscription_discount'] if charging_session_bill.discount else 0
        invoice_number_prefix = organization.name[:3].upper() if len(organization.name) >= 3 \
            else organization.name.upper()
        invoice_list.append(
            schema.Invoice(
                invoice_number=f'{invoice_number_prefix}-{cs_dict["transaction_id"]}',
                date=pr.created_at,
                organization=schema.OrganizationResponse.from_orm(organization),
                logo=base64.b64encode(organization.logo) if organization.logo is not None else None,
                user=member.user,
                member=member,
                charge_point=cs_dict['charge_point'],
                charging_session=cs_dict['charging_session'],
                connector=cs_dict['connector'],
                amount=float(charging_session_bill.usage_amount),
                discount=float(promo_usage.amount) if promo_usage else 0,
                subscription_discount=sub_discount,
                status=charging_session_bill.status,
                logo_format_type=organization.logo_format_type if organization.logo_format_type is not None else None
            )
        )
    return invoice_list


def ocpi_session_invoice_mapper(db, cs, start_time, end_time):
    meta = cs['meta']  # charging session static data
    billing_info = meta['billing_info']
    if cs['charge_point_connector']:
        connector = cs['charge_point_connector']
    else:
        connector = cs['ocpi_evse_connector']
        connector_evse = cs['ocpi_evse_connector']

    operator = db.query(models.Operator).get(meta['operator']['id'])
    if operator:
        operator_name = operator.name
    else:
        operator = db.query(models.ExternalOrganization).get(meta['operator']['id'])
        if operator:
            operator_name = operator.friendly_name
        else:
            operator_name = None

    try:
        operator_name_in_cs = safe_get(meta, 'location', 'ocpi_partner_operator', 'name')
        if operator_name_in_cs is not None:
            operator_name_in_cs = get_external_organization_by_operator_name(db,
                                                                             [operator_name_in_cs])
            operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                                 operator_name_in_cs]

            if len(operators_by_name) >= 1:
                operator_name = operators_by_name[0].original_name
    except Exception as e:  # pylint: disable=broad-except
        logger.error('Operator ocpi name mapping error with error as %s', str(e))
        operator_name = None
        _ = ''

    charge_box_serial_number = meta['charger_serial_number']
    try:
        charge_box_serial_number = meta['charge_box_serial_number']
    except KeyError:
        _ = ''

    charge_point = schema.ChargePoint(
        serial_number=meta['charger_serial_number'],
        charge_box_serial_number=charge_box_serial_number,
        location=meta['location'],
        operator=operator_name
    )
    session_end = end_time
    session_start = start_time

    if session_start and session_end:
        duration = datetime.fromisoformat(str(session_end)) - datetime.fromisoformat(str(session_start))
    else:
        duration = 0
    charging_session = schema.ChargingSession(
        id=cs['id'],
        session_start=session_start,
        session_end=session_end,
        duration=duration,
        charging_usage=round(((cs['meter_stop'] - cs['meter_start']) / 1000), 3),
        id_tag=cs['id_tag']
    )

    connector_type = None
    connector_label = None
    connector_number = None

    if connector:
        connector_type = connector['connector_type']['name']
        connector_label = connector.get('connector_label', None)
        connector_number = connector.get('number', None)
    elif connector_evse:
        connector_type = connector_evse['connector_type']['name']
        connector_label = connector_evse.get('ocpi_evse', {}).get('physical_reference', None)
        connector_number = connector_evse.get('number', None)

    # subscription_info = billing_info.get('subscription_info') or {}

    vat_rate = billing_info.get('vat_rate', 0) or 0
    try:
        billing_unit_fee_after_discount = utils.calculate_new_amount_upon_discount(
            billing_info['billing_discounted_type'],
            billing_info['billing_unit_fee'],
            billing_info['billing_discounted_amount'], 1, rounding_number=6)
    except KeyError:
        billing_unit_fee_after_discount = billing_info['billing_unit_fee']
    billing_unit_fee_after_discount_after_vat = billing_unit_fee_after_discount * (1 + vat_rate / 100)

    connector = schema.Connector(
        billing_type=billing_info['billing_type'],
        billing_unit_fee=billing_info['billing_unit_fee'],
        billing_unit_fee_after_vat=billing_info.get('billing_unit_fee_after_vat', None),
        billing_unit_fee_after_discount=billing_unit_fee_after_discount,
        billing_unit_fee_after_discount_after_vat=billing_unit_fee_after_discount_after_vat,
        vat_rate=billing_info.get('vat_rate', None),
        billing_cycle=billing_info['billing_cycle'],
        billing_currency=billing_info['billing_currency'],
        connection_fee=billing_info['connection_fee'],
        connector_type=connector_type,
        connector_number=connector_number,
        connector_label=connector_label,
        subscription_plan=None,
        subscription_custom_plan=None
    )

    return {
        'id': cs['id'],
        'connector': connector,
        'charging_session': charging_session,
        'charge_point': charge_point,
        'transaction_id': cs['transaction_id']
    }


def create_invoice(cs_dict, charging_session_bill, pr, db, sub_discount):
    cs_data = cs_dict.pop('charging_session')
    payment_type = '-'
    invoice_currency = None
    if not pr:
        promo_usage = 0
        member, organization = get_member_and_organization_by_id_tag(db, cs_data.id_tag)
    else:
        promo_usage = pr.promo_usage
        invoice_currency = pr.currency
        if pr.type == schema.PaymentRequestType.payment_terminal:
            member = None
            payment_type = 'Payment Terminal'
            organization = None
        else:
            member = schema.ShallowMembershipResponse(**pr.meta)
            organization = pr.member.organization
            if pr.type == schema.PaymentRequestType.wallet:
                payment_type = 'Wallet'
            elif pr.type == schema.PaymentRequestType.recurring:
                payment_type = 'Credit-Card'
            elif pr.type == schema.PaymentRequestType.direct:
                payment_type = 'Credit-Card'
            elif pr.type == schema.PaymentRequestType.partial:
                payment_type = 'Partial'
            elif pr.type == schema.PaymentRequestType.partial_direct:
                payment_type = 'Partial'

    wallet_deduct_amount = 0
    non_wallet_deduct_amount = 0
    if pr:
        if pr.wallet_deduct_amount is not None:
            wallet_deduct_amount = float(pr.wallet_deduct_amount)
        if pr.non_wallet_deduct_amount is not None:
            non_wallet_deduct_amount = float(pr.non_wallet_deduct_amount)

    cs_data = adjust_session_times(cs_data)

    if hasattr(cs_data, 'hogging_start') and cs_data.hogging_start is not None:
        cs_data.hogging_start = cs_data.hogging_start.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))
    if hasattr(cs_data, 'hogging_end') and cs_data.hogging_end is not None:
        cs_data.hogging_end = cs_data.hogging_end.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))

    invoice_date = charging_session_bill.created_at.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))
    if invoice_currency is not None and invoice_currency in ['KHR']:
        cs_data.session_start = cs_data.session_start.astimezone(ZoneInfo('Asia/Bangkok'))
        cs_data.session_end = cs_data.session_end.astimezone(ZoneInfo('Asia/Bangkok'))

        if hasattr(cs_data, 'hogging_start') and cs_data.hogging_start is not None:
            cs_data.hogging_start = cs_data.hogging_start.astimezone(ZoneInfo('Asia/Bangkok'))
        if hasattr(cs_data, 'hogging_end') and cs_data.hogging_end is not None:
            cs_data.hogging_end = cs_data.hogging_end.astimezone(ZoneInfo('Asia/Bangkok'))

        invoice_date = charging_session_bill.created_at.astimezone(ZoneInfo('Asia/Bangkok'))

    cs_dict['charging_session'] = cs_data
    organization_logo = organization.logo if organization else None
    organization_logo_format_type = organization.logo_format_type if organization else None
    invoice_number = f'{charging_session_bill.meta["transaction_id"]}'
    reference_number = f'{charging_session_bill.reference_number}'
    print('charging_session_bill', charging_session_bill)
    display_connector_label = False
    if str(settings.OCPI_PARTY_ID).upper() != 'CEV':
        display_connector_label = True

    invoice = schema.Invoice(
        invoice_number=invoice_number,
        reference_number=reference_number,
        date=invoice_date,
        organization=schema.OrganizationResponse.from_orm(organization) if organization else None,
        logo=base64.b64encode(organization_logo) if organization_logo else None,
        user=member.user if member else None,
        member=member if member else None,
        charge_point=cs_dict['charge_point'],
        charging_session=cs_dict['charging_session'],
        display_connector_label=display_connector_label,
        connector=cs_dict['connector'],
        amount=float(charging_session_bill.usage_amount),
        discount=float(promo_usage.amount) if promo_usage else 0,
        subscription_discount=sub_discount,
        status=charging_session_bill.status,
        logo_format_type=organization_logo_format_type if organization_logo_format_type else None,
        tax_amount=charging_session_bill.tax_amount if charging_session_bill.tax_amount else 0.00,
        tax_rate=charging_session_bill.tax_rate if charging_session_bill.tax_rate else 0,
        total_amount=charging_session_bill.total_amount if charging_session_bill.total_amount is not None else
        float(charging_session_bill.usage_amount) - float(sub_discount) +
        float(charging_session_bill.tax_amount if charging_session_bill.tax_amount else 0.00) +
        float(charging_session_bill.hogging_fee if charging_session_bill.hogging_fee else 0.00),
        payment_type=payment_type,
        currency=invoice_currency,
        hogging_fee=charging_session_bill.hogging_fee if charging_session_bill.hogging_fee else 0.00,
        hogging_amount=charging_session_bill.hogging_fee if charging_session_bill.hogging_fee else 0.00,
        wallet_deduct_amount=wallet_deduct_amount,
        non_wallet_deduct_amount=non_wallet_deduct_amount,
        campaign_promo_code_discount=0.00,
    )

    for attribute in branding_attributes:
        setattr(invoice, attribute, getattr(settings, attribute))
    return invoice


def get_member_and_organization_by_id_tag(db: Session, id_tag: str):
    try:
        db_member = MembershipCRUD.query(db).filter(
            func.lower(models.Membership.user_id_tag) == id_tag.lower()
        ).one()
        return db_member, db_member.organization
    except (NoResultFound, IntegrityError):
        try:
            db_member = IDTagCRUD.query(db).filter(
                func.lower(models.IDTag.id_tag) == id_tag.lower()
            ).one()
            return db_member.member, db_member.member.organization
        except (NoResultFound, IntegrityError) as e:
            logger.error(e)
            return None, None


def adjust_session_times(session_data):
    session_data.session_start = session_data.session_start.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))
    session_data.session_end = session_data.session_end.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))

    if 'hogging_start' in session_data:
        session_data.hogging_start = session_data.hogging_start.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))
    if 'hogging_end' in session_data:
        session_data.hogging_end = session_data.hogging_end.astimezone(ZoneInfo('Asia/Kuala_Lumpur'))

    return session_data


def calculate_total_amount(charging_session_bill, sub_discount):
    return (charging_session_bill.total_amount if charging_session_bill.total_amount is not None else
            float(charging_session_bill.usage_amount) - float(sub_discount) +
            float(charging_session_bill.tax_amount if charging_session_bill.tax_amount else 0.00) +
            float(charging_session_bill.hogging_fee if charging_session_bill.hogging_fee else 0.00))


def get_charging_session_ocpi_sync(db: Session, charging_session_id: str, start_time: datetime,
                                   end_time: datetime, headers: dict):
    path = f'charging/{charging_session_id}'
    response = requests.get(f'{CHARGER_URL_PREFIX_V2}/{path}', headers=headers, timeout=25)

    if response.status_code == 200:
        cs = response.json()
        return ocpi_session_invoice_mapper(db, cs, start_time, end_time)

    logger.debug('charger response: %s', response.json())
    raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSession')


async def get_charging_session_ocpi(db: Session, charging_session_id: str, start_time: datetime,
                                    end_time: datetime, headers: dict):
    path = f'charging/{charging_session_id}'
    async with httpx.AsyncClient() as client:
        response = await client.get(f'{CHARGER_URL_PREFIX_V2}/{path}', headers=headers)
    if response.status_code == 200:
        cs = response.json()
        return ocpi_session_invoice_mapper(db, cs, start_time, end_time)

    logger.debug('charger response: %s', response.json())
    raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSession')


async def get_invoice_ocpi(db: Session, charging_session_id: str, start_time: datetime,
                           end_time: datetime, headers: dict) -> schema.Invoice:
    charging_session_bill = get_charging_session_bill_by_charging_session(db, charging_session_id)
    if not charging_session_bill:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')

    query = PaymentRequestCRUD.query(db, check_permission=False).filter(
        models.PaymentRequest.charging_session_bill_id == str(charging_session_bill.id),
    ).order_by(desc(models.PaymentRequest.created_at))
    pr = query.first()
    sub_discount = charging_session_bill.discount['subscription_discount'] if charging_session_bill.discount else 0

    cs_dict = await get_charging_session_ocpi(db, charging_session_id, start_time, end_time, headers)

    return create_invoice(cs_dict, charging_session_bill, pr, db, sub_discount)


def get_invoice_ocpi_sync(db: Session, charging_session_id: str, start_time: datetime,
                          end_time: datetime, headers: dict) -> schema.Invoice:
    charging_session_bill = get_charging_session_bill_by_charging_session(db, charging_session_id)
    if not charging_session_bill:
        raise exceptions.ApolloObjectDoesNotExist(model_name='ChargingSessionBill')

    query = PaymentRequestCRUD.query(db, check_permission=False).filter(
        models.PaymentRequest.charging_session_bill_id == str(charging_session_bill.id),
    ).order_by(desc(models.PaymentRequest.created_at))
    pr = query.first()
    sub_discount = charging_session_bill.discount['subscription_discount'] if charging_session_bill.discount else 0

    cs_dict = get_charging_session_ocpi_sync(db, charging_session_id, start_time, end_time, headers)

    return create_invoice(cs_dict, charging_session_bill, pr, db, sub_discount)


def get_outlier_list(db: Session, filters: {}, params=None) \
        -> Union[list[schema.OutlierSessionCSMSResponse], Page[schema.OutlierSessionCSMSResponse]]:
    query = crud.OutlierSessionCRUD.query(db)
    if filters.get('status'):
        query = query.filter(models.OutlierSession.status.ilike(f'%{filters["status"]}%'))
    if filters.get('transaction_id'):
        query = query.filter(models.OutlierSession.transaction_id.ilike(f'%{filters["transaction_id"]}%'))
    if filters.get('charging_session_id'):
        query = query.filter(models.OutlierSession.charging_session_id.ilike(f'%{filters["charging_session_id"]}%'))
    if filters.get('charging_session_type'):
        query = query.filter(models.OutlierSession.charging_session_type.ilike(f'%{filters["charging_session_type"]}%'))
    if filters.get('id_tag'):
        query = query.filter(models.OutlierSession.id_tag.ilike(f'%{filters["id_tag"]}%'))
    if filters.get('billing_currency'):
        query = query.filter(models.OutlierSession.billing_currency.ilike(f'%{filters["billing_currency"]}%'))
    if filters.get('charging_session_id_list'):
        query = query.filter(models.OutlierSession.charging_session_id.in_(filters['charging_session_id_list']))

    query = query.order_by(models.OutlierSession.created_at.desc())
    if params:
        outlier_list = paginate(query, params)
    else:
        outlier_list = query.all()
    return outlier_list


def get_outlier_by_id(db: Session, outlier_id: str) -> schema.OutlierSessionCSMSResponse:
    try:
        outlier_session = crud.OutlierSessionCRUD.get(db, outlier_id)
        return outlier_session
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OutlierSession')


def update_outlier(db: Session, outlier_session: schema.OutlierSessionUpdate,
                   outlier_id: str) -> schema.OutlierSessionCSMSResponse:
    try:
        outlier_session = crud.OutlierSessionCRUD.update(db, outlier_id,
                                                         outlier_session.dict(exclude_unset=True,
                                                                              exclude_defaults=True)
                                                         )
        return outlier_session
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OutlierSession')


def get_outlier_by_charging_session_id(db: Session, charging_session_id: str) -> schema.OutlierSessionCSMSResponse:
    try:
        query = crud.OutlierSessionCRUD.query(db)
        query = query.filter(models.OutlierSession.charging_session_id == charging_session_id)
        outlier_session = query.one_or_none()
        return outlier_session
    except NoResultFound:
        raise exceptions.ApolloObjectDoesNotExist(model_name='OutlierSession')
