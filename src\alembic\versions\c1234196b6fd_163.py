"""163

Revision ID: c1234196b6fd
Revises: 35078e5b1a5c
Create Date: 2025-05-09 15:27:53.233422

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c1234196b6fd'
down_revision = '35078e5b1a5c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_outlier_session', sa.Column('status', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_outlier_session', 'status')
    # ### end Alembic commands ###
