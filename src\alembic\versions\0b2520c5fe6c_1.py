"""1

Revision ID: 0b2520c5fe6c
Revises: 
Create Date: 2022-06-15 16:56:38.262812

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0b2520c5fe6c'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_auth_user',
    sa.<PERSON>umn('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('is_superuser', sa.<PERSON>(), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=True),
    sa.Column('verification_method', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('phone_number')
    )
    op.create_table('main_charging_session_bill',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('charging_session_id', sa.String(), nullable=True),
    sa.Column('usage_amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('usage_type', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_label',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_organization',
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('parent_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['main_organization.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('main_promo_code',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('value', sa.Numeric(scale=2), nullable=False),
    sa.Column('min_amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('start_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expire_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('labels', postgresql.ARRAY(sa.Text()), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('main_resourceserver',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('url', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('root_path', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('url')
    )
    op.create_table('main_vehicle',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('model', sa.String(), nullable=True),
    sa.Column('brand', sa.String(), nullable=True),
    sa.Column('connector_type_id', postgresql.UUID(), nullable=True),
    sa.Column('acceptance_rate', sa.Numeric(scale=2), nullable=True),
    sa.Column('battery_capacity', sa.Numeric(scale=2), nullable=True),
    sa.Column('suspend_timer', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_auth_token',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('token', sa.String(), nullable=True),
    sa.Column('expiration', sa.DateTime(), nullable=True),
    sa.Column('is_used', sa.Boolean(), nullable=True),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['main_auth_user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_charge_point_promo_code',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('charge_point_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('promo_code_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('is_excluded', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['promo_code_id'], ['main_promo_code.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('charge_point_id', 'promo_code_id', name='unique_charge_point_promo_code')
    )
    op.create_table('main_invite',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('token', sa.String(), nullable=True),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('accepted_invite_count', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['main_auth_user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('token'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('main_membership',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('labels', postgresql.ARRAY(sa.Text()), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('password', sa.String(), nullable=True),
    sa.Column('membership_type', sa.Enum('staff', 'sub_staff', 'regular_user', name='membershiptype'), nullable=True),
    sa.Column('favorite_charge_points', postgresql.ARRAY(sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['main_organization.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['main_auth_user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'organization_id', 'membership_type', name='_org_user_type_uc')
    )
    op.create_table('main_operator',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('meta', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['main_organization.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_organization_promo_code',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('promo_code_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['main_organization.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['promo_code_id'], ['main_promo_code.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('organization_id', 'promo_code_id', name='unique_organization_promo_code')
    )
    op.create_table('main_reset_password_token',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('token', sa.String(), nullable=True),
    sa.Column('expiration', sa.DateTime(), nullable=True),
    sa.Column('is_used', sa.Boolean(), nullable=True),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['main_auth_user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_resource',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('path', sa.String(), nullable=True),
    sa.Column('scope', sa.String(), nullable=True),
    sa.Column('require_auth_token', sa.Boolean(), nullable=True),
    sa.Column('resourceserver_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['resourceserver_id'], ['main_resourceserver.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('path', 'scope', name='_path_scope_uc')
    )
    op.create_table('main_role',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('is_global', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['main_organization.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_user_vehicle',
    sa.Column('main_auth_user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('main_vehicle_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['main_auth_user_id'], ['main_auth_user.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['main_vehicle_id'], ['main_vehicle.id'], ondelete='CASCADE')
    )
    op.create_table('cpo_membership_association',
    sa.Column('main_operator_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('main_membership_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['main_membership_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['main_operator_id'], ['main_operator.id'], ondelete='CASCADE')
    )
    op.create_table('main_accepted_invite',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('invite_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['invite_id'], ['main_invite.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['main_auth_user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_credit_card',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('brand', sa.String(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('token', sa.String(), nullable=True),
    sa.Column('last_four_digit', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('primary', sa.Boolean(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('brand', 'last_four_digit', 'type', 'member_id', name='unique_member_credit_card')
    )
    op.create_index('only_one_primary_credit_card', 'main_credit_card', ['primary', 'member_id'], unique=True, postgresql_where=sa.text('"primary"'))
    op.create_table('main_id_tag',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id_tag', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('expiration', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('parent_id_tag_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['parent_id_tag_id'], ['main_id_tag.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_membership_promo_code',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('promo_code_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('is_excluded', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['promo_code_id'], ['main_promo_code.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('member_id', 'promo_code_id', name='unique_membership_promo_code')
    )
    op.create_table('main_operator_charge_point',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('charge_point_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('operator_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['operator_id'], ['main_operator.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('charge_point_id', 'operator_id', name='_unique_operator_charge_point')
    )
    op.create_table('main_operator_promo_code',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('operator_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('promo_code_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['operator_id'], ['main_operator.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['promo_code_id'], ['main_promo_code.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('operator_id', 'promo_code_id', name='unique_operator_promo_code')
    )
    op.create_table('main_promo_code_usage',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('amount', sa.Numeric(scale=2), nullable=False),
    sa.Column('association_type', sa.String(), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('promo_code_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ),
    sa.ForeignKeyConstraint(['promo_code_id'], ['main_promo_code.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_wallet',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('balance', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('member_id')
    )
    op.create_table('membership_role_association',
    sa.Column('main_membership_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('main_role_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['main_membership_id'], ['main_membership.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['main_role_id'], ['main_role.id'], ondelete='CASCADE')
    )
    op.create_table('role_resource_association',
    sa.Column('main_role_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('main_resource_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['main_resource_id'], ['main_resource.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['main_role_id'], ['main_role.id'], ondelete='CASCADE')
    )
    op.create_table('main_payment_request',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('amount', sa.String(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('billing_description', sa.String(), nullable=True),
    sa.Column('reason', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('update_token', sa.Boolean(), nullable=True),
    sa.Column('save_credit_card', sa.Boolean(), nullable=True),
    sa.Column('charge_point_id', sa.String(), nullable=True),
    sa.Column('member_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('promo_usage_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('charging_session_bill_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['charging_session_bill_id'], ['main_charging_session_bill.id'], ),
    sa.ForeignKeyConstraint(['member_id'], ['main_membership.id'], ),
    sa.ForeignKeyConstraint(['promo_usage_id'], ['main_promo_code_usage.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_payment_callback',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('nbcb', sa.Integer(), nullable=True),
    sa.Column('amount', sa.Numeric(scale=2), nullable=True),
    sa.Column('tran_id', sa.String(), nullable=True),
    sa.Column('domain', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('appcode', sa.String(), nullable=True),
    sa.Column('error_code', sa.String(), nullable=True),
    sa.Column('error_desc', sa.String(), nullable=True),
    sa.Column('skey', sa.String(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('paydate', sa.DateTime(), nullable=True),
    sa.Column('channel', sa.String(), nullable=True),
    sa.Column('extra_p', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('order_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['order_id'], ['main_payment_request.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('main_recurring_payment',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('record_type', sa.String(), nullable=True),
    sa.Column('sub_merchant', sa.String(), nullable=True),
    sa.Column('token', sa.String(), nullable=True),
    sa.Column('pan', sa.String(), nullable=True),
    sa.Column('expired_date', sa.String(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('amount', sa.String(), nullable=True),
    sa.Column('bill_name', sa.String(), nullable=True),
    sa.Column('bill_email', sa.String(), nullable=True),
    sa.Column('bill_mobile', sa.String(), nullable=True),
    sa.Column('bill_desc', sa.String(), nullable=True),
    sa.Column('response', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('order_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['order_id'], ['main_payment_request.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_recurring_payment')
    op.drop_table('main_payment_callback')
    op.drop_table('main_payment_request')
    op.drop_table('role_resource_association')
    op.drop_table('membership_role_association')
    op.drop_table('main_wallet')
    op.drop_table('main_promo_code_usage')
    op.drop_table('main_operator_promo_code')
    op.drop_table('main_operator_charge_point')
    op.drop_table('main_membership_promo_code')
    op.drop_table('main_id_tag')
    op.drop_index('only_one_primary_credit_card', table_name='main_credit_card', postgresql_where=sa.text('"primary"'))
    op.drop_table('main_credit_card')
    op.drop_table('main_accepted_invite')
    op.drop_table('cpo_membership_association')
    op.drop_table('main_user_vehicle')
    op.drop_table('main_role')
    op.drop_table('main_resource')
    op.drop_table('main_reset_password_token')
    op.drop_table('main_organization_promo_code')
    op.drop_table('main_operator')
    op.drop_table('main_membership')
    op.drop_table('main_invite')
    op.drop_table('main_charge_point_promo_code')
    op.drop_table('main_auth_token')
    op.drop_table('main_vehicle')
    op.drop_table('main_resourceserver')
    op.drop_table('main_promo_code')
    op.drop_table('main_organization')
    op.drop_table('main_label')
    op.drop_table('main_charging_session_bill')
    op.drop_table('main_auth_user')
    # ### end Alembic commands ###
