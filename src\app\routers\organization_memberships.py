import logging

from typing import List
from uuid import UUID
from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate

from app import settings, schema, crud, exceptions
from app.database import create_session, SessionLocal
from app.permissions import permission
from app.utils import get_dunning_bills, calculate_and_update_member_dunning

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/organization",
    tags=['organization', ],
    dependencies=[Depends(permission)]
)


async def membership_filters(name: str = None, id_tag: str = None, organization: str = None,
                             first_name: str = None, last_name: str = None):
    return {'name': name, 'user_id_tag': id_tag, 'organization': organization,
            'first_name': first_name, 'last_name': last_name}


async def user_filters(email: str = None, phone_number: str = None, is_verified: bool = None,
                       migrated_only: bool = False):
    return {'email': email, 'phone_number': phone_number, 'is_verified': is_verified,
            'migrated_only': migrated_only}


async def dunning_filters(block_status: str = None, block_reason: str = None, admin_block_status: str = None,
                          admin_block_remark: str = None):
    return {'block_status': block_status, 'block_reason': block_reason, 'admin_block_status': admin_block_status,
            'admin_block_remark': admin_block_remark}


async def non_migrated_user_filters(email: str = None):
    return {'email': email}


@router.get("/{organization_id}/membership/{membership_id}/charge-points", response_model=List)
async def membership_charger_points(organization_id: UUID, membership_id: UUID,
                                    dbsession: SessionLocal = Depends(create_session)):
    """
    List membership organization operators charge points

    :param membership_id:
    :param organization_id:
    :param dbsession:
    """
    try:
        res = crud.list_membership_operator_charge_points(dbsession, organization_id, membership_id)
        return res
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/{organization_id}/membership/{membership_type}", response_model=Page[schema.MembershipResponse])
async def list_all_organization_members(organization_id: UUID, membership_type: str = None,
                                        include_child_orgs: bool = False, params: Params = Depends(),
                                        user_filter: dict = Depends(user_filters),
                                        mem_filter: dict = Depends(membership_filters),
                                        dunning_filter: dict = Depends(dunning_filters),
                                        dbsession: SessionLocal = Depends(create_session)):
    """
    Lists Membership objects related to Organization based on membership type

    :param membership_type:
    :param organization_id:
    :param dbsession:
    :return:
    """
    query = crud.get_organization_membership_list(dbsession, organization_id, membership_type,
                                                  include_child_orgs, user_filter, mem_filter,
                                                  dunning_filter)
    membership_list = paginate(query, params)
    for member in membership_list.items:
        if not member.dunning_details:
            membership_dunning_data = schema.InitiateMembershipDunning(
                membership_id=str(member.id),
            )
            member.dunning_details = crud.create_membership_dunning(dbsession, membership_dunning_data)
        _, _ = calculate_and_update_member_dunning(dbsession, member.id)

        # member.dunning_details.total_outstanding_amount = dunning_bills['total_outstanding_amount']
        # member.dunning_details.all_outstanding_bill_ids = dunning_bills['all_outstanding_bill_ids']
        # member.dunning_details.dunnings = dunning_bills['dunnings']

    return membership_list


@router.get("/{organization_id}/membership/{membership_type}/manual-link",
            response_model=Page[schema.NonMigratedMembershipEmailResponse])
async def list_manual_link_member_email(organization_id: UUID, membership_type: str = None,
                                        include_child_orgs: bool = False, migrated_user: bool = False,
                                        params: Params = Depends(),
                                        user_filter: dict = Depends(non_migrated_user_filters),
                                        mem_filter: dict = Depends(membership_filters),
                                        dbsession: SessionLocal = Depends(create_session)):
    """
    Lists Membership objects related to Organization based on membership type

    :param membership_type:
    :param organization_id:
    :param dbsession:
    :return:
    """
    query = crud.get_organization_membership_list(dbsession, organization_id, membership_type, include_child_orgs,
                                                  user_filter, mem_filter, migrated_user=migrated_user,
                                                  return_email=True)
    membership_list = paginate(query, params)
    return membership_list


@router.get("/{organization_id}/membership/{membership_type}/create-dunning")
async def all_member_initialize_dunning(dbsession: SessionLocal = Depends(create_session)):
    """
    Initiate Dunning for all regular usuer

    :param membership_type:
    :param organization_id:
    :param dbsession:
    :return:
    """
    message = crud.create_dunning_rc_for_all_member(dbsession)
    return message


# Staff


@router.post("/{organization_id}/membership/staff", response_model=schema.MembershipResponse, status_code=201)
async def create_organization_staff(data: schema.StaffMembership, organization_id: UUID, request: Request,
                                    dbsession: SessionLocal = Depends(create_session)):
    """
    Creates User and Membership (Staff level) objects for a user in the Organization

    :param str organization_id: Target Organization ID
    """
    try:
        data.organization_id = organization_id

        if data.user_id_tag:
            if crud.user_id_tag_exists(dbsession, data.user_id_tag):
                raise exceptions.ApolloDuplicateUserIdTagError()
        else:
            data.user_id_tag = crud.create_user_id_tag(dbsession)

        db_member = crud.create_organization_membership(dbsession, data, schema.MembershipType.staff)
        staff_role = crud.get_staff_role(dbsession)
        crud.attach_roles_to_membership(dbsession,
                                        schema.MembershipRoles(roles=[str(staff_role.id)]),
                                        str(db_member.id))

        return db_member
    except (exceptions.ApolloIDTagError,
            exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloUserDataError,
            exceptions.ApolloDuplicateUserIdTagError,
            exceptions.ApolloDuplicateMembershipError,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloRootOrganizationMembershipCreation,
            exceptions.ApolloDuplicateMembershipParentError) as e:
        raise HTTPException(400, str(e))


@router.patch("/{organization_id}/membership/staff/{membership_id}", response_model=schema.MembershipResponse)
async def update_organization_staff(data: schema.MembershipUpdate, organization_id: UUID, membership_id: UUID,
                                    request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Partially updates User and Membership (Staff level) objects for a user in the Organization

    :param str organization_id: Target Organization ID
    """
    try:
        res = crud.update_organization_membership(dbsession, data, organization_id, membership_id)
        return res
    except (exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloEmailPhoneNumberAlreadyAssigned) as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{organization_id}/membership/staff/{membership_id}", status_code=204)
async def delete_organization_staff(organization_id: UUID, membership_id: UUID,
                                    request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Deletes Membership (Staff level) object for a user in the Organization

    :param str organization_id: Target Organization ID
    :param str membership_id: Target Membership ID
    """
    try:
        crud.delete_organization_membership(dbsession, membership_id)
        return None
    except (exceptions.ApolloObjectDoesNotExist, exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloUserDataError) as e:
        raise HTTPException(400, e.__str__())


#  Substaff


@router.post("/{organization_id}/membership/sub_staff", response_model=schema.MembershipResponse, status_code=201)
async def create_organization_sub_staff(data: schema.StaffMembership, organization_id: UUID, request: Request,
                                        dbsession: SessionLocal = Depends(create_session)):
    """
    Creates User and Membership (Sub-Staff level) objects for a user in the Organization

    :param str organization_id: Target Organization ID
    """
    try:
        data.organization_id = organization_id

        if data.user_id_tag:
            if crud.user_id_tag_exists(dbsession, data.user_id_tag):
                raise exceptions.ApolloDuplicateUserIdTagError()
        else:
            data.user_id_tag = crud.create_user_id_tag(dbsession)

        db_member = crud.create_organization_membership(dbsession, data, schema.MembershipType.sub_staff)
        sub_staff_role = crud.get_sub_staff_role(dbsession)
        crud.attach_roles_to_membership(dbsession,
                                        schema.MembershipRoles(roles=[str(sub_staff_role.id)]),
                                        str(db_member.id))

        return db_member
    except (exceptions.ApolloIDTagError,
            exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloUserDataError,
            exceptions.ApolloDuplicateUserIdTagError,
            exceptions.ApolloDuplicateMembershipError,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloRootOrganizationMembershipCreation,
            exceptions.ApolloDuplicateMembershipParentError) as e:
        raise HTTPException(400, str(e))


@router.patch("/{organization_id}/membership/sub_staff/{membership_id}", response_model=schema.MembershipResponse)
async def update_organization_sub_staff(data: schema.MembershipUpdate, organization_id: UUID, membership_id: UUID,
                                        request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Partially updates User and Membership (Sub-Staff level) objects for a user in the Organization

    :param str organization_id: Target Organization ID
    """
    try:
        res = crud.update_organization_membership(dbsession, data, organization_id, membership_id)
        return res
    except (exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloEmailPhoneNumberAlreadyAssigned) as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{organization_id}/membership/sub_staff/{membership_id}", status_code=204)
async def delete_organization_sub_staff(organization_id: UUID, membership_id: UUID,
                                        request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Deletes Membership (SubStaff level) object for a user in the Organization

    :param str organization_id: Target Organization ID
    :param str membership_id: Target Membership ID
    """
    try:
        crud.delete_organization_membership(dbsession, membership_id)
        return None
    except (exceptions.ApolloObjectDoesNotExist, exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloUserDataError) as e:
        raise HTTPException(400, e.__str__())


#  Regular Users


@router.post("/{organization_id}/membership/regular", response_model=schema.MembershipResponse, status_code=201)
async def create_organization_regular(data: schema.StaffMembership, organization_id: UUID, request: Request,
                                      dbsession: SessionLocal = Depends(create_session)):
    """
    Creates User and Membership (Regular/Non-staff level) objects for a user in the Organization

    :param str organization_id: Target Organization ID
    """
    try:
        data.organization_id = organization_id

        if data.user_id_tag:
            if crud.user_id_tag_exists(dbsession, data.user_id_tag):
                raise exceptions.ApolloDuplicateUserIdTagError()
        else:
            data.user_id_tag = crud.create_user_id_tag(dbsession)

        db_member = crud.create_organization_membership(dbsession, data, schema.MembershipType.regular_user)
        extended_membership_data = schema.MembershipExtended(
            membership_id=str(db_member.id),
        )

        membership_dunning_data = schema.InitiateMembershipDunning(
            membership_id=str(db_member.id),
        )
        crud.create_membership_dunning(dbsession, membership_dunning_data)

        membership_extended = crud.create_user_membership_extended(dbsession, extended_membership_data)
        regular_role = crud.get_regular_role(dbsession)

        membership_extended_status_update_data = {
            'verification_method': schema.VerificationMethodEnum.phone,
            'phone_verified': True,
            'id': str(membership_extended.id),
            'verification_token_id': None,
        }
        user_status_update_data = {
            'verification_method': schema.VerificationMethodEnum.phone,
            'is_verified': True,
            'user_id': str(db_member.user.id)
        }

        crud.update_membership_extended_verification(dbsession, membership_extended_status_update_data)

        crud.update_user_verification_status(dbsession, schema.VerificationMethodUpdate(**user_status_update_data))
        crud.attach_roles_to_membership(dbsession,
                                        schema.MembershipRoles(roles=[str(regular_role.id)]),
                                        str(db_member.id))

        return db_member
    except (exceptions.ApolloIDTagError,
            exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloUserDataError,
            exceptions.ApolloDuplicateUserIdTagError,
            exceptions.ApolloDuplicateMembershipError,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloRootOrganizationMembershipCreation,
            exceptions.ApolloDuplicateMembershipParentError,
            exceptions.ApolloDuplicateUser) as e:
        raise HTTPException(400, str(e))


@router.patch("/{organization_id}/membership/regular/{membership_id}", response_model=schema.MembershipResponse)
async def update_organization_regular(data: schema.MembershipUpdate, organization_id: UUID, membership_id: UUID,
                                      request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Partially updates User and Membership (Regular/Non-staff level) objects for a user in the Organization

    :param str organization_id: Target Organization ID
    """
    try:
        res = crud.update_organization_membership(dbsession, data, organization_id, membership_id)
        return res
    except (exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloEmailPhoneNumberAlreadyAssigned) as e:
        raise HTTPException(400, e.__str__())


@router.patch("/{organization_id}/membership/regular/{membership_id}/dunning-block",
              response_model=schema.MembershipDunningResponse)
async def dunning_block_membership(data: schema.DunningAdminBlockBody, organization_id: UUID, membership_id: UUID,
                                   request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Admin trigger and block an user manually for dunning flow

    :param str organization_id: Target Organization ID
    :param str membership_id: Target Membership ID
    """
    try:
        dunning_bills = get_dunning_bills(dbsession, membership_id)
        num_dunning_bills = len(dunning_bills['all_outstanding_bill_ids'])

        if num_dunning_bills > 0:
            res = crud.admin_update_membership_dunning(dbsession, membership_id, 'block', data)
            return res
        raise HTTPException(400, 'You cannot block user with 0 outstanding bills.')
    except (exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloEmailPhoneNumberAlreadyAssigned) as e:
        raise HTTPException(400, e.__str__())


@router.patch("/{organization_id}/membership/regular/{membership_id}/dunning-unblock",
              response_model=schema.MembershipDunningResponse)
async def dunning_unblock_membership(data: schema.DunningAdminUnblockBody, organization_id: UUID, membership_id: UUID,
                                     request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Admin trigger and unblock an user manually for dunning flow

    :param str organization_id: Target Organization ID
    """
    try:
        member_dunning = crud.admin_update_membership_dunning(dbsession, membership_id, 'unblock', data)
        return member_dunning
    except (exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloEmailPhoneNumberAlreadyAssigned) as e:
        raise HTTPException(400, e.__str__())


@router.patch("/{organization_id}/membership/regular/{membership_id}/dunning-clear",
              response_model=schema.MembershipDunningResponse)
async def dunning_clear_status(organization_id: UUID, membership_id: UUID,
                               request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Admin trigger and clear admin status for an user manually for dunning flow

    :param str organization_id: Target Organization ID
    """
    try:
        member_dunning = crud.admin_update_membership_dunning(dbsession, membership_id, 'clear')
        return member_dunning
    except (exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloEmailPhoneNumberAlreadyAssigned) as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{organization_id}/membership/regular/{membership_id}", status_code=204)
async def delete_organization_regular(organization_id: UUID, membership_id: UUID,
                                      request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Deletes Membership (Regular/Non-staff level) object for a user in the Organization

    :param str organization_id: Target Organization ID
    :param str membership_id: Target Membership ID
    """
    try:
        crud.delete_organization_membership(dbsession, membership_id)
        return None
    except (exceptions.ApolloObjectDoesNotExist, exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloUserDataError) as e:
        raise HTTPException(400, e.__str__())


# Custom


@router.post("/{organization_id}/membership/custom", response_model=schema.MembershipResponse, status_code=201)
async def create_organization_custom(data: schema.CustomMembership, organization_id: UUID, request: Request,
                                     dbsession: SessionLocal = Depends(create_session)):
    """
    Creates User and Membership (Custom) objects for a user in the Organization

    :param str organization_id: Target Organization ID
    """
    try:
        data.organization_id = organization_id

        if data.user_id_tag:
            if crud.user_id_tag_exists(dbsession, data.user_id_tag):
                raise exceptions.ApolloDuplicateUserIdTagError()
        else:
            data.user_id_tag = crud.create_user_id_tag(dbsession)

        db_member = crud.create_organization_membership(dbsession, data, schema.MembershipType.custom)
        crud.attach_roles_to_membership(dbsession,
                                        schema.MembershipRoles(roles=[str(data.role_id)]),
                                        str(db_member.id))
        return db_member
    except (exceptions.ApolloIDTagError,
            exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloUserDataError,
            exceptions.ApolloDuplicateUserIdTagError,
            exceptions.ApolloDuplicateMembershipError,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloRootOrganizationMembershipCreation,
            exceptions.ApolloDuplicateMembershipParentError) as e:
        raise HTTPException(400, str(e))


@router.patch("/{organization_id}/membership/custom/{membership_id}", response_model=schema.MembershipResponse)
async def update_organization_custom(data: schema.CustomMembershipUpdate, organization_id: UUID, membership_id: UUID,
                                     request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Partially updates User and Membership (Custom) objects for a user in the Organization

    :param str organization_id: Target Organization ID
    """
    try:
        res = crud.update_organization_membership(dbsession, data, organization_id, membership_id)
        role_before = None
        if res.roles:
            role_before = res.roles[0]
            crud.remove_roles_from_membership(dbsession,
                                              schema.MembershipRoles(roles=[str(res.roles[0].id)]),
                                              str(res.id))

        crud.attach_roles_to_membership(dbsession,
                                        schema.MembershipRoles(roles=[str(data.role_id)]),
                                        str(res.id))
        # LOG Role Changes
# membership_role_association_table
        if role_before and data.role_id != role_before.id:
            data_before = {
                'role_name': role_before.name,
                'organization': res.organization.name,
                'user_first_name': res.first_name,
                'user_last_name': res.last_name,
                'user_email': res.user.email,
                'user_id_tag': res.user_id_tag,
            }
            data_after = {**data_before, 'role_name': res.roles[0].name}
            crud.log_audit_trail(crud.RoleCRUD, dbsession, schema.AuditLogType.update, data_before,
                                 data_after, res.id, remarks={})
        return res
    except (exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloEmailPhoneNumberAlreadyAssigned) as e:
        raise HTTPException(400, e.__str__())


@router.delete("/{organization_id}/membership/custom/{membership_id}", status_code=204)
async def delete_organization_custom(organization_id: UUID, membership_id: UUID,
                                     request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Deletes Membership (Custom) object for a user in the Organization

    :param str organization_id: Target Organization ID
    :param str membership_id: Target Membership ID
    """
    try:
        crud.delete_organization_membership(dbsession, membership_id)
        return None
    except (exceptions.ApolloObjectDoesNotExist, exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloUserDataError) as e:
        raise HTTPException(400, e.__str__())


# Membership Roles


@router.post("/{organization_id}/membership/roles/{membership_id}/add", response_model=schema.MembershipResponse)
async def attach_roles_to_membership(data: schema.MembershipRoles, organization_id: UUID, membership_id: UUID,
                                     request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Attaches Role objects into a target Membership

    :param str organization_id: Target Organization ID
    :param str membership_id: Target Membership ID
    """
    try:
        res = crud.attach_roles_to_membership(dbsession, data, membership_id)
        return res
    except (exceptions.ApolloObjectDoesNotExist, exceptions.ApolloOperationOnRootOrganization) as e:
        raise HTTPException(400, e.__str__())


@router.post("/{organization_id}/membership/roles/{membership_id}/remove", response_model=schema.MembershipResponse)
async def remove_roles_from_membership(data: schema.MembershipRoles, organization_id: UUID, membership_id: UUID,
                                       request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Removes attached Role objects from a target Membership

    :param str organization_id: Target Organization ID
    :param str membership_id: Target Membership ID
    """
    try:
        res = crud.remove_roles_from_membership(dbsession, data, membership_id)
        return res
    except (exceptions.ApolloObjectDoesNotExist, exceptions.ApolloOperationOnRootOrganization) as e:
        raise HTTPException(400, e.__str__())


# Membership CPO/Operators


@router.post("/{organization_id}/membership/operators/{membership_id}/add", response_model=schema.MembershipResponse)
async def attach_operators_to_membership(data: schema.MembershipOperator, organization_id: UUID, membership_id: UUID,
                                         request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Attaches Operator object into a target Membership

    :param str organization_id: Target Organization ID
    :param str membership_id: Target Membership ID
    """
    try:
        res = crud.attach_operator_to_membership(dbsession, data, membership_id)
        return res
    except (exceptions.ApolloObjectDoesNotExist, exceptions.ApolloOperationOnRootOrganization) as e:
        raise HTTPException(400, e.__str__())


@router.post("/{organization_id}/membership/operators/{membership_id}/remove", response_model=schema.MembershipResponse)
async def remove_operators_to_membership(data: schema.MembershipOperator, organization_id: UUID, membership_id: UUID,
                                         request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Attaches Operator object into a target Membership

    :param str organization_id: Target Organization ID
    :param str membership_id: Target Membership ID
    """
    try:
        res = crud.remove_operator_from_membership(dbsession, data, membership_id)
        return res
    except (exceptions.ApolloObjectDoesNotExist, exceptions.ApolloOperationOnRootOrganization) as e:
        raise HTTPException(400, e.__str__())


@router.patch("/manual-verify-user/{membership_id}",
              response_model=schema.MembershipResponse)
async def manual_user_verify(membership_id: str,
                             dbsession: SessionLocal = Depends(create_session)):
    """
    Endpoint for manually verify user from CSMS without OTP token
    """
    try:
        membership = crud.get_membership_by_id(dbsession, membership_id)
        user = crud.get_user_by_id(dbsession, membership.user_id)
        membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, membership_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=404, detail='User does not exist.')

    membership_extended_status_update_data = {
        'verification_method': schema.VerificationMethodEnum.phone,
        'phone_verified': True,
        'id': str(membership_extended.id),
        'verification_token_id': None,
    }
    user_status_update_data = {
        'verification_method': schema.VerificationMethodEnum.phone,
        'is_verified': True,
        'user_id': str(user.id)
    }
    user_dict = schema.User.from_orm(user)
    is_migrated_user = user_dict.is_migrated

    crud.update_membership_extended_verification(dbsession, membership_extended_status_update_data)
    if not is_migrated_user:
        crud.update_user_verification_status(dbsession,
                                             schema.VerificationMethodUpdate(**user_status_update_data))
    return membership


@router.patch("/payment-method/{membership_id}", response_model=schema.MembershipResponse)
async def update_user(data: schema.MembershipUpdatePreferredPayment,
                      membership_id: str,
                      request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Update Preferred Payment Method for specific Member

    :param str membership_id: Target Membership ID
    """
    try:
        res = crud.update_preferred_payment_flow(dbsession, data, membership_id)
        return res
    except (exceptions.ApolloObjectDoesNotExist,
            exceptions.ApolloOperationOnRootOrganization,
            exceptions.ApolloRootUserCreation,
            exceptions.ApolloEmailPhoneNumberAlreadyAssigned) as e:
        raise HTTPException(400, e.__str__())
