import sqlalchemy as db
from sqlalchemy.dialects.postgresql import UUID, ARRAY

from app.models.base import BaseModel, RelationBaseModel


class PromoCode(BaseModel):
    __tablename__ = 'main_promo_code'

    code = db.Column(db.String, nullable=False)
    type = db.Column(db.String, nullable=False)
    start_at = db.Column(db.DateTime(timezone=True))
    expire_at = db.Column(db.DateTime(timezone=True))
    is_active = db.Column(db.Boolean, default=True)
    labels = db.Column(ARRAY(db.Text), default=[])
    limit = db.Column(db.Integer, default=1)
    amount = db.Column(db.Numeric(scale=2), default=0)


db.Index('unique_promo_code_code', PromoCode.code, PromoCode.is_deleted,
         unique=True, postgresql_where=~PromoCode.is_deleted)


class ChargePointPromoCode(RelationBaseModel):
    __tablename__ = 'main_charge_point_promo_code'

    charge_point_id = db.Column(UUID(as_uuid=True), nullable=False)
    promo_code_id = db.Column(UUID(as_uuid=True),
                              db.ForeignKey('main_promo_code.id', ondelete='CASCADE'), nullable=False)

    promo_code = db.orm.relationship('PromoCode', backref='charge_points')

    is_excluded = db.Column(db.Boolean, default=False)

    __table_args__ = (
        db.schema.UniqueConstraint('charge_point_id', 'promo_code_id', name='unique_charge_point_promo_code'),
    )


class MembershipPromoCode(RelationBaseModel):
    __tablename__ = 'main_membership_promo_code'

    member_id = db.Column(UUID(as_uuid=True),
                          db.ForeignKey('main_membership.id', ondelete='CASCADE'), nullable=False)
    promo_code_id = db.Column(UUID(as_uuid=True),
                              db.ForeignKey('main_promo_code.id', ondelete='CASCADE'), nullable=False)

    member = db.orm.relationship('Membership', backref='promo_codes')
    promo_code = db.orm.relationship('PromoCode', backref='members')

    is_excluded = db.Column(db.Boolean, default=False)

    __table_args__ = (
        db.schema.UniqueConstraint('member_id', 'promo_code_id', name='unique_membership_promo_code'),
    )


class OrganizationPromoCode(RelationBaseModel):
    __tablename__ = 'main_organization_promo_code'

    organization_id = db.Column(UUID(as_uuid=True),
                                db.ForeignKey('main_organization.id', ondelete='CASCADE'), nullable=False)
    promo_code_id = db.Column(UUID(as_uuid=True),
                              db.ForeignKey('main_promo_code.id', ondelete='CASCADE'), nullable=False)

    organization = db.orm.relationship('Organization', backref='promo_codes')
    promo_code = db.orm.relationship('PromoCode', backref='organizations')

    __table_args__ = (
        db.schema.UniqueConstraint('organization_id', 'promo_code_id', name='unique_organization_promo_code'),
    )


class OperatorPromoCode(RelationBaseModel):
    __tablename__ = 'main_operator_promo_code'

    operator_id = db.Column(UUID(as_uuid=True),
                            db.ForeignKey('main_operator.id', ondelete='CASCADE'), nullable=False)
    promo_code_id = db.Column(UUID(as_uuid=True),
                              db.ForeignKey('main_promo_code.id', ondelete='CASCADE'), nullable=False)

    operator = db.orm.relationship('Operator', backref='promo_codes')
    promo_code = db.orm.relationship('PromoCode', backref='operators')

    __table_args__ = (
        db.schema.UniqueConstraint('operator_id', 'promo_code_id', name='unique_operator_promo_code'),
    )


class PromoCodeUsage(BaseModel):
    __tablename__ = 'main_promo_code_usage'

    amount = db.Column(db.Numeric(scale=2), nullable=False)
    association_type = db.Column(db.String)

    member_id = db.Column(UUID(as_uuid=True), db.ForeignKey('main_membership.id'), nullable=False)
    member = db.orm.relationship('Membership', backref='promo_usages')

    promo_code_id = db.Column(UUID(as_uuid=True),
                              db.ForeignKey('main_promo_code.id'), nullable=False)
    promo_code = db.orm.relationship('PromoCode')
