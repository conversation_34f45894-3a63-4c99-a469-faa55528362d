"""150

Revision ID: aa0c192f7e1d
Revises: 79653997ed0e
Create Date: 2025-02-24 05:57:01.079466

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'aa0c192f7e1d'
down_revision = '79653997ed0e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_tariff_tag',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('name', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    )
    op.create_index('unique_tariff_tag_name', 'main_tariff_tag', ['name', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted'))

    op.create_unique_constraint('unique_operator_subscription_plan', 'main_subscription_operator_plan', ['operator_id', 'subscription_plan_id'])

    op.add_column('main_subscription_tariff_plan', sa.Column('tariff_tag_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_unique_constraint('unique_tariff_tag_subscription_plan', 'main_subscription_tariff_plan', ['tariff_tag_id', 'subscription_plan_id'])
    op.create_foreign_key('subscription_tariff_plan_tariff_tag', 'main_subscription_tariff_plan', 'main_tariff_tag', ['tariff_tag_id'], ['id'], ondelete='CASCADE')
    op.drop_column('main_subscription_tariff_plan', 'tariff_tag')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_subscription_tariff_plan', sa.Column('tariff_tag', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint('subscription_tariff_plan_tariff_tag', 'main_subscription_tariff_plan', type_='foreignkey')
    op.drop_constraint('unique_tariff_tag_subscription_plan', 'main_subscription_tariff_plan', type_='unique')
    op.drop_column('main_subscription_tariff_plan', 'tariff_tag_id')

    op.drop_constraint('unique_operator_subscription_plan', 'main_subscription_operator_plan', type_='unique')

    op.drop_index('unique_tariff_tag_name', table_name='main_tariff_tag', postgresql_where=sa.text('NOT is_deleted'))
    op.drop_table('main_tariff_tag')
    # ### end Alembic commands ###
