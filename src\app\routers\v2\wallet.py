import logging
from typing import List
from typing import Union

from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi_pagination import Params, Page
from fastapi_pagination.ext.sqlalchemy import paginate
from twilio.rest import Client

from app import settings, models, schema
from app.crud import get_or_create_pre_auth_info_cc, get_membership_by_id, update_preferred_payment_method, \
    get_user_membership_extended_by_membership_id, get_member_primary_cc, update_preferred_payment_flow
from app.crud.wallet import WalletTransactionCRUD, get_or_create_wallet
from app.database import create_session, SessionLocal
from app.permissions import permission, x_api_key
from app.schema.v2 import UpdatePreferredPaymentMethod, UpdatePreferredPaymentFlow
from app.settings import APOLLO_MAIN_DOMAIN, MAIN_ROOT_PATH
from app.utils import (
    decode_auth_token_from_headers,
    RouteErrorHandler, update_wallet_balance
)

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

main_path = f"{APOLLO_MAIN_DOMAIN}/{MAIN_ROOT_PATH}/api/v1"

twilio_client = Client(settings.TWILIO['ACCOUNT_SID'], settings.TWILIO['AUTH_TOKEN'])

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/wallet",
    tags=['v2 wallet', ],
    dependencies=[Depends(permission), Depends(x_api_key)],
    route_class=RouteErrorHandler,
)


@router.get('/balance', response_model=schema.WalletResponseSmall,
            tags=['wallet', ])
async def get_wallet(request: Request, dbsession: SessionLocal = Depends(create_session),
                     currency: schema.Currency = schema.Currency.myr):
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')

        db_wallet = get_or_create_wallet(dbsession, membership_id, currency)
        db_wallet = update_wallet_balance(dbsession, db_wallet.id)
        return db_wallet
    except Exception:
        raise HTTPException(401, 'Authorization token is missing.')


@router.get('/pre-auth-info', tags=['wallet', ])
async def get_pre_auth_info(request: Request, dbsession: SessionLocal = Depends(create_session),
                            currency: schema.Currency = schema.Currency.myr):
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')

        db_member = get_membership_by_id(dbsession, membership_id)
        db_wallet = get_or_create_wallet(dbsession, membership_id, currency)
        # db_wallet = update_wallet_balance(dbsession, db_wallet.id)

        db_pre_auth_info = get_or_create_pre_auth_info_cc(dbsession, membership_id, currency)

        db_cc = get_member_primary_cc(dbsession, membership_id, currency)
        # if there is no default cc, try to get w/o currency, as cybs do not have multi-currency
        if not db_cc:
            # if there is a cc found, we check if its cybersource
            db_cc = get_member_primary_cc(dbsession, membership_id)
            if db_cc:
                # if its not cybersource, we mark them as None
                if db_cc.payment_gateway != schema.CreditCardPaymentGateway.cybersource:
                    db_cc = None

        joint_info = {
            'wallet': db_wallet,
            'pre_auth_info': db_pre_auth_info,
            'preferred_payment_method': db_member.preferred_payment_method,
            'credit_card': db_cc,
        }
        return joint_info
    except Exception as e:
        print(e)
        raise HTTPException(401, 'Authorization token is missing.')


@router.patch('/pre-auth-preferred', tags=['wallet', ])
async def update_pre_auth_preferred(request: Request, preferred_payment_method: UpdatePreferredPaymentMethod,
                                    dbsession: SessionLocal = Depends(create_session),
                                    ):
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
        db_member = update_preferred_payment_method(dbsession, preferred_payment_method, membership_id)
        membership_extended = get_user_membership_extended_by_membership_id(dbsession, db_member.id)
        return schema.MembershipExtendedResponse.from_orm(membership_extended)
    except Exception as e:
        print(e)
        raise HTTPException(401, 'Authorization token is missing.')


@router.patch('/preferred-payment-flow', tags=['wallet', ])
async def update_normal_preferred_payment_flow(request: Request, preferred_payment_method: UpdatePreferredPaymentFlow,
                                               dbsession: SessionLocal = Depends(create_session),
                                               ):
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
        db_member = update_preferred_payment_flow(dbsession, preferred_payment_method, membership_id)
        membership_extended = get_user_membership_extended_by_membership_id(dbsession, db_member.id)
        return schema.MembershipExtendedResponse.from_orm(membership_extended)
    except Exception as e:
        print(e)
        raise HTTPException(401, 'Authorization token is missing.')


@router.get('/histories', response_model=Union[List[schema.WalletTransactionResponse],  # noqa
Page[schema.WalletTransactionResponse]], tags=['wallet', ])
async def get_histories(request: Request, params: Params = Depends(), dbsession: SessionLocal = Depends(create_session),
                        pagination: bool = True, currency: schema.Currency = schema.Currency.myr):
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
    except Exception:
        raise HTTPException(401, 'Authorization token is missing.')

    # We define currency as MYR now as only MYR wallet is used
    # currency = schema.Currency = schema.Currency.myr
    db_wallet = get_or_create_wallet(dbsession, membership_id, currency)

    db_wallet_history = WalletTransactionCRUD.query(dbsession).filter(
        models.WalletTransaction.wallet_id == db_wallet.id).order_by(models.WalletTransaction.created_at.desc())

    if pagination:
        return paginate(db_wallet_history, params)
    # else:
    return db_wallet_history.all()
