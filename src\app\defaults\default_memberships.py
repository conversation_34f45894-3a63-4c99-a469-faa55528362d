import bcrypt

from app import schema
from app.settings import ROOT_PASSWORD

sys_admin_mem = {
    'organization_id': None,
    'user_id': None,
    'membership_type': schema.MembershipType.staff,
    'password': bcrypt.hashpw(ROOT_PASSWORD.encode('utf-8'), bcrypt.gensalt(11, b'2a')).decode('utf-8'),  # nosec
    'favorite_charge_points': [],
    'favorite_locations': [],
    'first_name': 'Apollo',
    'last_name': 'Admin'
}
