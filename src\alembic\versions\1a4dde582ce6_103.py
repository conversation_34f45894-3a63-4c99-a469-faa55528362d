"""103

Revision ID: 1a4dde582ce6
Revises: c59e47f77707
Create Date: 2024-06-06 11:44:37.992669

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1a4dde582ce6'
down_revision = 'c59e47f77707'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_credit_card', sa.Column('network_transaction_id', sa.String(), nullable=True))
    op.add_column('main_credit_card', sa.Column('response', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_credit_card', 'response')
    op.drop_column('main_credit_card', 'network_transaction_id')
    # ### end Alembic commands ###
