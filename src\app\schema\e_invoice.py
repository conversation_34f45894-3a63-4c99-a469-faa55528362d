# pylint:disable=too-many-lines
from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field, EmailStr

from app.schema.payment import PaymentRefundResponse
from app import settings

RMP_RETURN_URL = f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/csms/e_invoice/callback"


# pylint:disable=unsubscriptable-object
class EInvoiceStatus(str, Enum):
    pending = 'Pending'
    requested = 'Requested'
    validated = 'Validated'
    rejected = 'Rejected'
    expired = 'Expired'
    not_applicable = 'Not-Applicable'


class ECreditNoteStatus(str, Enum):
    pending = 'Pending'
    requested = 'Requested'
    validated = 'Validated'
    rejected = 'Rejected'
    expired = 'Expired'
    not_applicable = 'Not-Applicable'


class SubmissionType(str, Enum):
    invoice = 'INVOICE'
    credit_note = 'CREDITNOTE'
    debit_note = 'DEBITNOTE'
    refund_note = 'REFUNDNOTE'


class SubmissionCurrencyCode(str, Enum):
    myr = 'MYR'


class IdentificationType(str, Enum):
    tin = "TIN"
    nric = "NRIC"
    brn = "BRN"
    passport = "PASSPORT"
    army = "ARMY"
    sst = "SST"
    ttx = "TTX"


class Measurement(str, Enum):
    min = "MIN"
    kwh = "KWH"


class SubmissionPaymentType(str, Enum):
    cash = "01"
    cheque = "02"
    bank_transfer = "03"
    credit_card = "04"
    debit_card = "05"
    e_wallet = "06"
    digital_bank = "07"
    others = "08"


class EInvoiceAction(str, Enum):
    pre_submission = 'Pre-submission'
    post_submission = 'Post-submission'
    requested = 'Requested'
    callback = 'Callback from E-Invoice'
    other = 'Others'


class ECreditNoteAction(str, Enum):
    pre_submission = 'Pre-submission'
    post_submission = 'Post-submission'
    requested = 'Requested'
    callback = 'Callback from E-Invoice'
    other = 'Others'


class EInvoice(BaseModel):
    charging_session_id: Optional[str]
    charging_session_bill_id: Optional[UUID]
    status: EInvoiceStatus
    submission_payload: dict = {}
    submission_response: dict = {}
    submission_callback: dict = {}
    last_submission_date: Optional[datetime]
    last_rejection_date: Optional[datetime]
    status_timeline: Optional[list]
    error_message_timeline: Optional[list]

    class Config:
        orm_mode = True


class EInvoiceCreate(BaseModel):
    e_invoice_date_time: datetime
    e_invoice_number: str
    charging_session_id: str
    charging_session_bill_id: UUID
    status: EInvoiceStatus
    submission_payload: dict = {}
    submission_response: dict = {}
    submission_callback: dict = {}
    last_submission_date: Optional[datetime]
    last_rejection_date: Optional[datetime]
    status_timeline: Optional[list]

    currency_code: Optional[str]
    payment_terms: Optional[str]
    bill_reference_number: Optional[str]
    is_self_issued: Optional[bool]
    is_consolidated_invoice: Optional[bool]

    total_net_amount: Optional[str]
    total_tax_amount: Optional[str]
    total_charge_amount: Optional[str]
    total_discount_value: Optional[str]
    total_payable_amount: Optional[str]
    total_tax_excluded_amount: Optional[str]
    total_tax_included_amount: Optional[str]

    invoice_tax: Optional[dict]
    invoice_discount: Optional[dict]
    invoice_total_excluding_tax: Optional[str]
    quantity: Optional[str]
    measurement: Optional[str]
    unit_price: Optional[str]
    sub_total: Optional[str]

    class Config:
        orm_mode = True


class EInvoiceResponse(EInvoice):
    id: UUID

    created_at: datetime
    updated_at: Optional[datetime]
    e_invoice_number: Optional[str]

    class Config:
        orm_mode = True


class EInvoiceWithDownloadURLResponse(EInvoiceResponse):
    action_url: Optional[str]
    action_method: Optional[str]
    action_data: Optional[list]

    class Config:
        orm_mode = True


class EInvoiceStatusTimeline(BaseModel):
    status: Optional[EInvoiceStatus]
    action: Optional[EInvoiceAction]
    status_date: Optional[datetime]

    class Config:
        orm_mode = True


class EInvoiceUpdate(BaseModel):
    status: Optional[EInvoiceStatus]
    last_submission_date: Optional[datetime]
    last_rejection_date: Optional[datetime]
    submission_response: Optional[dict]
    submission_callback: Optional[dict]
    lhdn_uid: Optional[str]
    lhdn_qr: Optional[str]
    lhdn_validated_date_time: Optional[datetime]
    status_timeline: Optional[list[dict]]
    error_message_timeline: Optional[list[dict]]
    e_invoice_number: Optional[str]
    submission_payload: dict = {}

    class Config:
        orm_mode = True


class ECreditNote(BaseModel):
    payment_refund_id: UUID
    status: EInvoiceStatus
    last_submission_date: Optional[datetime]
    last_rejection_date: Optional[datetime]
    submission_payload: dict = {}
    submission_response: dict = {}
    submission_callback: dict = {}
    status_timeline: Optional[list]
    error_message_timeline: Optional[list]
    e_invoice_id: Optional[UUID]

    class Config:
        orm_mode = True


class ECreditNoteResponse(ECreditNote):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    e_credit_note_number: Optional[str]

    class Config:
        orm_mode = True


class ECreditNoteStatusTimeline(BaseModel):
    status: Optional[EInvoiceStatus]
    action: Optional[EInvoiceAction]
    status_date: Optional[datetime]

    class Config:
        orm_mode = True


class ECreditNoteUpdate(BaseModel):
    status: Optional[EInvoiceStatus]
    last_submission_date: Optional[datetime]
    last_rejection_date: Optional[datetime]

    lhdn_uid: Optional[str]
    lhdn_qr: Optional[str]
    lhdn_validated_date_time: Optional[datetime]
    status_timeline: Optional[list[dict]]
    error_message_timeline: Optional[list[dict]]

    e_credit_note_number: Optional[str]
    submission_payload: dict = {}

    class Config:
        orm_mode = True


class ECreditNoteWithDownloadURLResponse(ECreditNoteResponse):
    action_url: Optional[str]
    action_method: Optional[str]
    action_data: Optional[list]

    class Config:
        orm_mode = True


class PaymentRefundResponseWithECreditNoteInfo(PaymentRefundResponse):
    e_credit_note: Optional[ECreditNoteWithDownloadURLResponse]

    class Config:
        orm_mode = True


class Identification(BaseModel):
    value: str
    schemeID: IdentificationType


class Address(BaseModel):
    address: list[str]
    postalZone: Optional[str] = None
    cityName: Optional[str]
    state: str = '14'
    country: str = 'MYS'


class SubmissionSupplier(BaseModel):
    name: Optional[str] = None
    identification: Optional[list[Identification]] = None
    msicCode: Optional[str] = '01111'
    businessActivityDescription: Optional[str] = None
    address: Optional[Address] = None
    email: Optional[EmailStr] = None
    contactNumber: Optional[str] = None
    internalCode: Optional[str] = None


class SubmissionBuyer(BaseModel):
    name: Optional[str] = None
    identification: Optional[list[Identification]] = None
    address: Optional[Address] = None
    email: Optional[EmailStr] = None
    contactNumber: Optional[str] = None
    internalCode: Optional[str] = None


class Tax(BaseModel):
    type: str = '06'
    rate: str = '0.00%'
    amount: str = '0.00'


class TaxExemption(BaseModel):
    details: str = 'NA'
    amount: str = '0.00'


class Discount(BaseModel):
    rate: str
    amount: str


class Fee(BaseModel):
    rate: str = '0.00'
    amount: str = '0.00'


class InvoiceLineItem(BaseModel):
    classification: list[str] = ["029"]
    description: str = "PAY PER USE"
    unitPrice: str
    tax: Tax
    taxExemption: Optional[TaxExemption] = None
    subtotal: str
    totalExcludingTax: str
    quantity: Optional[str] = None
    measurement: Optional[Measurement] = None
    discount: Optional[Discount] = None
    fee: Optional[Fee] = None
    productTariffCode: Optional[str] = None
    countryOfOrigin: Optional[str] = "MYS"


class TaxAmountPerType(BaseModel):
    type: str = '06'
    amount: str = '0.00'


class TaxableAmountPerType(BaseModel):
    type: str
    amount: str


class SubmissionTotal(BaseModel):
    taxIncludedAmount: str = '0.00'
    taxExcludedAmount: str = '0.00'
    payableAmount: str = '0.00'

    netAmount: Optional[str] = '0.00'
    discountValue: str = '0.00'
    chargeAmount: Optional[str] = '0.00'

    taxAmount: str = '0.00'

    taxableAmountPerTaxType: Optional[list[TaxableAmountPerType]] = None
    taxAmountPerTaxType: list[TaxAmountPerType]


class SubmissionDocument(BaseModel):
    eInvoiceNumber: str
    eInvoiceDate: str = Field(..., regex=r"^\d{4}-\d{2}-\d{2}$")
    eInvoiceTime: str = Field(..., regex=r"^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)Z$")
    currencyCode: SubmissionCurrencyCode = SubmissionCurrencyCode.myr
    currencyExchangeRate: str = "1.00"
    frequencyOfBilling: str = "Others / Not Applicable"
    billReferenceNumber: str
    taxExemption: list = []
    supplier: Optional[SubmissionSupplier]
    buyer: Optional[SubmissionBuyer]
    invoiceLineItems: list[InvoiceLineItem]
    paymentMode: Optional[SubmissionPaymentType] = SubmissionPaymentType.others
    paymentTerms: Optional[str] = 'CASH'
    total: SubmissionTotal


class SubmissionDocuments(BaseModel):
    type: SubmissionType
    isSelfIssued: bool = False
    isConsolidatedInvoice: bool = True
    document: SubmissionDocument


class EInvoiceSubmission(BaseModel):
    documents: list[SubmissionDocuments]
    returnUrl: str = RMP_RETURN_URL


class CreditNoteSubmissionDocument(SubmissionDocument):
    originalEInvoiceNumber: str


class CreditNoteDocuments(BaseModel):
    type: SubmissionType
    isSelfIssued: bool = False
    isConsolidatedInvoice: bool = True
    document: CreditNoteSubmissionDocument


class ECreditNoteSubmission(BaseModel):
    documents: list[CreditNoteDocuments]
    returnUrl: str = RMP_RETURN_URL


class ECreditNoteCreate(BaseModel):
    e_invoice_id: UUID
    e_credit_note_date_time: datetime
    e_credit_note_number: str
    payment_refund_id: UUID
    status: EInvoiceStatus
    submission_payload: dict = {}
    submission_response: dict = {}
    submission_callback: dict = {}
    last_submission_date: Optional[datetime]
    last_rejection_date: Optional[datetime]
    status_timeline: Optional[list]

    currency_code: Optional[str]
    payment_terms: Optional[str]
    bill_reference_number: Optional[str]
    is_self_issued: Optional[bool]
    is_consolidated_invoice: Optional[bool]

    total_net_amount: Optional[str]
    total_tax_amount: Optional[str]
    total_charge_amount: Optional[str]
    total_discount_value: Optional[str]
    total_payable_amount: Optional[str]
    total_tax_excluded_amount: Optional[str]
    total_tax_included_amount: Optional[str]

    invoice_tax: Optional[dict]
    invoice_discount: Optional[dict]
    invoice_total_excluding_tax: Optional[str]
    quantity: Optional[str]
    measurement: Optional[str]
    unit_price: Optional[str]
    sub_total: Optional[str]

    class Config:
        orm_mode = True
