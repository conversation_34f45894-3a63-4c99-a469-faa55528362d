import uuid
from datetime import timed<PERSON><PERSON>, datetime

import bcrypt
import factory
from faker import Faker

from app import models, schema

fake = Faker()


def make_password(password: str):
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt(11, b'2a')).decode('utf-8')


class UserFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.User
        sqlalchemy_get_or_create = ('email', 'phone_number', 'is_superuser', 'organization_id', 'user_type')

    email = factory.Faker('email')
    phone_number = factory.Faker('phone_number')
    is_superuser = False
    user_type = schema.UserType.staff


class VerificationTokenFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.VerificationToken

    token = factory.Faker('pystr')
    expiration = factory.Faker('date_time_this_month', after_now=True)
    user_id = uuid.uuid4()


class ResetPasswordTokenFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.ResetPasswordToken

    token = factory.Faker('pystr')
    expiration = factory.Faker('date_time_this_month', after_now=True)
    is_used = False


class OrganizationFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Organization
        sqlalchemy_get_or_create = ('name', 'parent_id',)

    name = factory.Faker('company')
    parent_id = None


class MembershipFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Membership
        sqlalchemy_get_or_create = ('first_name', 'last_name', 'organization_id', 'user_id', 'user_id_tag',
                                    'membership_type', 'favorite_charge_points', 'favorite_locations', 'labels',)

    first_name = factory.Faker('pystr')
    last_name = factory.Faker('pystr')
    user_id_tag = factory.Faker('pystr')
    password = bcrypt.hashpw(b'password', bcrypt.gensalt(11, b'2a')).decode('utf-8')
    membership_type = schema.MembershipType.regular_user
    favorite_charge_points = []
    favorite_locations = []
    labels = ['test', ]


class RoleFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Role

    name = factory.Faker('job')


class GlobalRoleFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Role
        sqlalchemy_get_or_create = ('name', 'is_global',)

    name = factory.Faker('job')


class SysAdminRoleFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Role
        sqlalchemy_get_or_create = ('name', 'organization_id',)

    name = factory.Faker('job')


class ResourceServerFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.ResourceServer
        sqlalchemy_get_or_create = ('url',)

    name = factory.Faker('pystr')
    url = factory.Faker('domain_name')


class ResourceFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Resource
        sqlalchemy_get_or_create = ('path', 'scope', 'require_auth_token')

    name = factory.Faker('pystr')
    path = factory.Faker('file_path')
    require_auth_token = True
    scope = ['get', 'post', 'patch', ][fake.random_int(min=0, max=2)]


class InviteFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Invite
        sqlalchemy_get_or_create = ('user_id', 'token',)

    token = factory.Faker('color')


class OperatorFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Operator
        sqlalchemy_get_or_create = (
            'id', 'organization_id', 'name', 'meta', 'email', 'phone_number',
            'description', 'website')

    id = uuid.uuid4()
    name = factory.Faker('pystr')
    meta = {'email': '<EMAIL>'}
    email = factory.Faker('email')
    phone_number = factory.Faker('phone_number')
    description = factory.Faker('pystr')
    website = factory.Faker('url')


class IDTagFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.IDTag
        sqlalchemy_get_or_create = ('name', 'member_id', 'id_tag', 'type', 'expiration', 'is_active',)

    name = factory.Faker('pystr')
    id_tag = factory.Faker('pystr')
    is_active = True
    expiration = datetime.now() + timedelta(days=365)
    type = schema.IDTagType.rfid
    description = 'test id_tag'


class CreditCardFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.CreditCard
        sqlalchemy_get_or_create = ('member_id', 'last_four_digit')

    brand = 'Visa'
    currency = schema.Currency.myr
    token = factory.Faker('pystr')
    last_four_digit = '1111'
    type = schema.CreditCardType.credit
    primary = True


class BlacklistCreditCardFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.BlacklistCreditCard
        sqlalchemy_get_or_create = ('member_id', 'last_four_digit')

    currency = schema.Currency.myr
    last_four_digit = '1111'
    status = 'Blacklist'
    phone_number = factory.Faker('pystr')
    email = factory.Faker('pystr')


class WalletFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Wallet
        sqlalchemy_get_or_create = ('member_id', 'currency', 'balance')

    currency = schema.Currency.myr
    balance = '0'


class PromoCodeFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.PromoCode
        sqlalchemy_get_or_create = ('start_at', 'expire_at', 'type', 'amount', 'labels', 'limit')

    code = factory.Faker('pystr')
    labels = ['test', ]
    limit = 3


class OrganizationPromoCodeFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.OrganizationPromoCode
        model = models.OrganizationPromoCode
        sqlalchemy_get_or_create = ('organization_id', 'promo_code_id')


class PromoCodeUsageFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.PromoCodeUsage
        sqlalchemy_get_or_create = ('amount', 'association_type', 'member_id', 'promo_code_id')

    amount = 0
    association_type = schema.PromoUsageAssociationType.direct


class PaymentRequestFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.PaymentRequest
        sqlalchemy_get_or_create = ('type', 'amount', 'reason', 'status', 'connector_id',
                                    'charging_session_bill_id', 'member_id', 'promo_usage_id', 'meta')

    type = schema.PaymentRequestType.recurring
    amount = 10
    currency = 'MYR'
    billing_description = 'test'
    reason = schema.PaymentRequestReason.payment
    status = schema.PaymentRequestStatus.pending
    connector_id = None
    meta = {}
    charging_session_bill_id = None
    promo_usage_id = None


class PreAuthPaymentFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.PreAuthPayment
        sqlalchemy_get_or_create = (
            'is_successful', 'bill_name', 'bill_email', 'bill_mobile', 'bill_desc', 'token', 'amount', 'currency',
            'payment_request_id', 'payment_type', 'response')

    is_successful = False
    bill_name = factory.Faker('pystr')
    bill_email = factory.Faker('pystr')
    bill_mobile = factory.Faker('pystr')
    bill_desc = 'Pre-authorization'
    token = factory.Faker('pystr')

    amount = '10'
    currency = 'MYR'
    payment_request_id = None
    payment_type = None
    response = None


class PreAuthPaymentCaptureFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.PreAuthPaymentCapture
        sqlalchemy_get_or_create = (
            'transaction_id', 'amount', 'currency', 'reference_id', 'pre_auth_payment_id', 'payment_request_id')

    reference_id = factory.Faker('pystr')
    amount = '10'
    currency = 'MYR'
    payment_request_id = None


class RecurringPaymentFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.RecurringPayment
        sqlalchemy_get_or_create = ('id', 'payment_request_id', 'record_type')

    id = uuid.uuid4()
    record_type = schema.RecordType.recurring
    payment_request_id = None


class ChargingSessionBillFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.ChargingSessionBill
        sqlalchemy_get_or_create = ('id', 'charging_session_id', 'usage_amount', 'usage_type', 'status', 'discount')

    id = uuid.uuid4()
    charging_session_id = uuid.uuid4()
    usage_amount = 10.5
    usage_type = schema.BillingType.kwh
    status = schema.ChargingSessionBillStatus.pending
    discount = {'subscription_discount': 5.0}


class VehicleFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Vehicle
        sqlalchemy_get_or_create = ('name', 'model', 'brand', 'connector_type_id', 'acceptance_rate',
                                    'battery_capacity', 'suspend_timer')

    name = factory.Faker('pystr')
    model = factory.Faker('pystr')
    brand = factory.Faker('pystr')
    connector_type_id = uuid.uuid4()
    acceptance_rate = 10.0
    battery_capacity = 40.0
    suspend_timer = 15
    pcid = 'HUBOpenProvCert001'
    registration_number = factory.Faker('pystr')


class EmaidFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Emaid
        sqlalchemy_get_or_create = ('emaid', 'contract_begin', 'contract_end', 'status', 'vehicle_id')

    emaid = factory.Faker('pystr')
    contract_begin = datetime.now()
    contract_end = datetime.now() + timedelta(days=365)
    status = schema.EmaidStatus.active
    vehicle_id = uuid.uuid4()


class SubscriptionFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Subscription
        sqlalchemy_get_or_create = ('start_date', 'end_date', 'is_default', 'member_id', 'subscription_plan_id',
                                    'status', 'number', 'invitation_code')

    start_date = datetime.now()
    end_date = datetime.now() + timedelta(days=2)
    is_default = False
    status = schema.SubscriptionStatus.success
    number = factory.Faker('pystr')
    invitation_code = None


class SubscriptionPlanFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.SubscriptionPlan
        sqlalchemy_get_or_create = (
            'name', 'description', 'category', 'amount', 'is_active', 'organization_id', 'allow_invitation_code',
            'is_private')

    name = factory.Faker('pystr')
    description = factory.Faker('pystr')
    category = 'Fixed'
    amount = 10.0
    is_active = False
    is_invitation_only = False
    allow_invitation_code = False
    is_private = False


class SubscriptionFeeFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.SubscriptionFee
        sqlalchemy_get_or_create = ('name', 'amount', 'subscription_plan_id')

    name = factory.Faker('pystr')
    amount = 10.0


class SubscriptionCustomPlanFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.SubscriptionCustomPlan
        sqlalchemy_get_or_create = ('amount', 'connector_id', 'charger_point_id', 'subscription_plan_id')

    amount = 10.0
    connector_id = str(uuid.uuid4())
    charger_point_id = factory.Faker('pystr')


class SubscriptionCardFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.SubscriptionCard
        sqlalchemy_get_or_create = ('number', 'id_tag', 'subscription_plan_id')

    number = factory.Faker('pystr')
    id_tag = factory.Faker('pystr')


class SubscriptionInvitationFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.SubscriptionInvitation
        sqlalchemy_get_or_create = ('code', 'subscription_plan_id', 'member_id', 'custom_number', 'custom_id_tag')

    code = factory.Faker('pystr')
    member_id = None
    custom_id_tag = None
    custom_number = None


class OperatorChargepointFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.OperatorChargepoint
        sqlalchemy_get_or_create = ('charge_point_id', 'operator_id')

    charge_point_id = uuid.uuid4()


class MembershipExtendedFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.MembershipExtended
        sqlalchemy_get_or_create = ('id', 'phone_verified', 'email_verified', 'verification_method')

    id = uuid.uuid4()
    phone_verified = False
    email_verified = False
    verification_method = 'phone'


class OrganizationAuthenticationServiceFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.OrganizationAuthenticationService
        sqlalchemy_get_or_create = ('id', 'organization_id', 'secret_key')

    id = uuid.uuid4()
    organization_id = uuid.uuid4()
    secret_key = schema.argon_ph.hash('9IPqNlBclJqdBUfP07IJVu8xrI2ObkbA')


class WalletPackageFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.WalletPackage
        sqlalchemy_get_or_create = ('name', 'currency')

    name = factory.Faker('pystr')
    description = factory.Faker('pystr')
    currency = schema.Currency.myr
    price = 100
    credit_amount = 120
    is_redeemable = True
    is_active = True
    is_private = False


class WalletPackageInvitationFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.WalletPackageInvitation
        sqlalchemy_get_or_create = ('code', 'wallet_package_id')

    code = factory.Faker('pystr')
    discount_amount = 120


class AutochargeFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Autocharge
        sqlalchemy_get_or_create = ('status', 'vehicle_id', 'mac_address')

    mac_address = factory.Faker('pystr')
    status = 'Pending'
    vehicle_id = uuid.uuid4()


class OCPICPOTokenFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.OCPICPOToken
        sqlalchemy_get_or_create = ('partner_ocpi_cpo_token_id', 'valid')

    partner_ocpi_cpo_token_id = str(uuid.uuid4())
    country_code = 'MY'
    party_id = 'test'
    type = 'test'
    contract_id = 'test'
    issuer = 'CHARGEV'
    valid = True
    whitelist = 'test'


class ExternalOrganizationFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.ExternalOrganization
        sqlalchemy_get_or_create = ('country_code', 'party_id', 'friendly_name')

    country_code = 'MY'
    party_id = 'abc'
    friendly_name = None
    meta = {}


class TaxRateFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.TaxRate
        sqlalchemy_get_or_create = ('country', 'tax_rate')

    country = 'Singapore'
    tax_rate = 0.00
    enforce_tax_on_ocpi = False
    ocpi_additional_tax_rate = 0.0


class ExternalTokenFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.ExternalToken
        sqlalchemy_get_or_create = ('token', 'is_temporary', 'endpoints', 'external_service_token')

    token = str(uuid.uuid4())
    is_temporary = True
    endpoints = {}
    external_service_token = None


class OCPITokenFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.OCPIToken
        sqlalchemy_get_or_create = ('contract_id', 'valid', 'member', 'type')

    country_code = 'MY'
    party_id = 'test'
    type = 'test'
    contract_id = 'test'
    issuer = 'CHARGEV'
    valid = True
    whitelist = 'test'


class PrepaidWalletPlanFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.PrepaidWalletPlan
        sqlalchemy_get_or_create = ('name', 'currency', 'amount', 'is_active', 'plan_type', 'interval')

    name = 'test-prepaid-wallet-plan'
    amount = 100
    currency = schema.PrepaidWalletCurrency.myr
    is_active = True
    # plan_type = schema.PrepaidWalletPlanType.carry_over
    # interval = schema.PrepaidWalletInterval.monthly


class PrepaidWalletPlanBatchFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.PrepaidWalletPlanBatch
        sqlalchemy_get_or_create = ('prepaid_wallet_plan_id', 'amount', 'currency', 'plan_type', 'interval',
                                    'start_time', 'end_time', 'status')

    amount = 100
    currency = schema.PrepaidWalletCurrency.myr


class PrepaidWalletSubscriptionFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.PrepaidWalletSubscription
        sqlalchemy_get_or_create = ('member_id', 'prepaid_wallet_plan_id', 'is_active')

    is_active = True


class MembershipPromoCodeFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.MembershipPromoCode
        sqlalchemy_get_or_create = ('member_id', 'promo_code_id', 'is_excluded')

    is_excluded = False


class ChargePointPromoCodeFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.ChargePointPromoCode
        sqlalchemy_get_or_create = ('charge_point_id', 'promo_code_id', 'is_excluded')

    is_excluded = False
