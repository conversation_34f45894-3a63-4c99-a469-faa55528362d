# pylint:disable=too-many-lines
import logging
import re
from contextlib import contextmanager
from typing import List, Union
from uuid import UUID
import urllib
from datetime import datetime, timedelta, timezone
import macaddress
from fastapi import APIRouter, Depends, Request, HTTPException, BackgroundTasks, status
from fastapi.responses import JSONResponse
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate
from phone_iso3166.country import phone_country
from app.settings import AUTOCHARGE_VALIDITY_DURATION_YEARS
from app import settings, crud, exceptions, models
from app.crud import create_random_emaid_tag, update_vehicle_history, create_id_tag, \
    AutochargeCRUD, IDTagCRUD, EmaidCRUD
from app.database import create_session, SessionLocal
from app.schema.v2 import EmaidCreate, EmaidStatus, EmaidUpdate, \
    DeleteVehicleResponse, VehicleResponse, VehicleBase, \
    AutochargeCreate, AutochargeStatus, AutochargeUpdate, VehicleUpdate
from app.schema import IDTag, IDTagType, IDTagUpdate, ContractCertificateOrder<PERSON><PERSON>, Currency, \
    ContractCertificateFeePaymentStatus
from app.permissions import permission, x_api_key
from app.utils import RouteErrorHandler, decode_auth_token_from_headers, callback_vehicle_registration, \
    provision_contract_certificate, lookup_pcid, delete_contract_certificate, calculate_md5_hash, MAIN_URL_PREFIX
from app.middlewares import set_admin_as_context_user
from app.constants import PAYMENT_DIRECT_API_URL

ROOT_PATH = settings.MAIN_ROOT_PATH

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/vehicle",
    tags=['v2 vehicle', ],
    dependencies=[Depends(permission), Depends(x_api_key)],
    route_class=RouteErrorHandler,
)


async def pcid_and_callback(db_vehicle, vehicle_id, pcid_contract_type):
    with contextmanager(create_session)() as dbsession:
        set_admin_as_context_user(dbsession)
        await provision_contract_certificate(db_vehicle, dbsession, pcid_contract_type)
        db_membership = crud.get_membership_by_vehicle_id(dbsession, vehicle_id, check_permission=False)
        # Refresh the db_vehicle object after the contract has been created.
        db_vehicle = crud.get_vehicle(dbsession, vehicle_id)
        await callback_vehicle_registration(
            dbsession, db_membership, db_vehicle, pcid_contract_type
        )


@router.get('/vehicles_per_user_limit', status_code=status.HTTP_200_OK)
async def get_max_user_vehicle_count():
    """
    Get the maximum vehicles a user can add
    """
    return settings.VEHICLES_PER_USER_LIMIT


@router.post('', status_code=status.HTTP_201_CREATED)
async def add_vehicle(request: Request, vehicle: VehicleBase,
                      dbsession: SessionLocal = Depends(create_session)):
    """
    Add a vehicle and link to a member
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
        try:
            db_vehicle_list = crud.get_membership_vehicle_list(dbsession, membership_id)
            if len(db_vehicle_list) >= settings.VEHICLES_PER_USER_LIMIT:
                raise HTTPException(409, 'Vehicle Limit Reached')
        except exceptions.ApolloObjectDoesNotExist:
            raise HTTPException(404, 'Member not found')

        db_vehicle = crud.create_vehicle_v2(dbsession, vehicle)
        crud.add_membership_vehicle(dbsession, db_vehicle.id, membership_id)
        return db_vehicle.id
    except Exception as e:
        logger.info('Exception while adding Vehicle: %s', str(e))
        raise HTTPException(400, 'Unable to add vehicle')


# pylint: disable=too-many-branches
@router.delete("/{vehicle_id}", response_model=DeleteVehicleResponse, status_code=status.HTTP_200_OK)
async def delete_vehicle(request: Request, vehicle_id: UUID,  # noqa: MC0001
                         dbsession: SessionLocal = Depends(create_session)):
    """
    Delete an Vehicle
    :param str vehicle_id: Target Vehicle ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_membership = crud.get_membership_by_vehicle_id(dbsession, vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')

        vehicle_record = crud.VehicleCRUD.query(dbsession).filter(models.Vehicle.id == vehicle_id).first()
        if vehicle_record is None:
            raise HTTPException(404, 'Vehicle Not Found')
        emaid_records = vehicle_record.emaid
        autocharge_record = vehicle_record.autocharge

        for emaid_record in emaid_records:
            # If EMAID status is active or disabled, clear the cetificate in Hubject
            if emaid_record.status in (EmaidStatus.active, EmaidStatus.disabled):
                hubject_delete_status = await delete_contract_certificate(dbsession, emaid_record.emaid)
                if not hubject_delete_status:
                    delete_response = DeleteVehicleResponse(
                        detail='Failed to removed vehicle.'
                    )
                    return delete_response
            id_tag_record = emaid_record.id_tag
            if id_tag_record is not None:
                # Decouple the id tag record from the emaid record
                # before soft deleting the id tag object
                id_tag_update = IDTagUpdate(id_tag=id_tag_record.id_tag, name=id_tag_record.name,
                                            type=id_tag_record.type, is_active=False,
                                            member_id=membership_id,
                                            expiration=datetime.now(timezone.utc))
                id_tag_record = IDTagCRUD.update(dbsession, id_tag_record.id,
                                                 id_tag_update.dict(exclude_unset=False, exclude_defaults=False),
                                                 check_permission=False)
                if id_tag_record is not None:
                    logger.info('id_tag_record is not None. id is %s', str(id_tag_record.id))
                    crud.delete_id_tag(dbsession, id_tag_record.id)
            # Decouple the emaid object from the vehicle record before soft deleting it
            status_comment = 'Delete Contract'
            emaid_update = EmaidUpdate(
                status=EmaidStatus.deactivated,
                status_comment=status_comment,
                contract_begin=emaid_record.contract_begin,
                contract_end=emaid_record.contract_begin
            )
            EmaidCRUD.update(dbsession, emaid_record.id,
                             emaid_update.dict(exclude_unset=False, exclude_defaults=False))
            crud.delete_emaid(dbsession, emaid_record.id)

        if autocharge_record is not None:
            # Soft delete the corresponding id tag as well as the autocharge record.
            if autocharge_record.id_tag is not None:
                id_tag_record = autocharge_record.id_tag
                if id_tag_record is not None:
                    # Decouple the id tag record from the autocharge record
                    # before soft deleting the id tag object
                    id_tag_update = IDTagUpdate(id_tag=id_tag_record.id_tag,
                                                name=id_tag_record.name,
                                                type=id_tag_record.type, is_active=False,
                                                member_id=membership_id,
                                                expiration=datetime.now(timezone.utc))
                    # id_tag_update = IDTagUpdate(autocharge_id=None)
                    id_tag_record = IDTagCRUD.update(dbsession, id_tag_record.id,
                                                     id_tag_update.dict(exclude_unset=False,
                                                                        exclude_defaults=False),
                                                     check_permission=False)
                    if id_tag_record is not None:
                        logger.info('id_tag_record is not None. id is %s', str(id_tag_record.id))
                        crud.delete_id_tag(dbsession, id_tag_record.id)

            # Decouple the autocharge object from the vehicle object
            # before soft deleting the autocharge object
            autocharge_update = AutochargeUpdate(status=AutochargeStatus.deactivated,
                                                 mac_address=autocharge_record.mac_address)
            AutochargeCRUD.update(dbsession, autocharge_record.id,
                                  autocharge_update.dict(exclude_unset=False,
                                                         exclude_defaults=False))
            crud.delete_autocharge(dbsession, autocharge_record.id)

        # Delete the Vehicle, Delete the emaid and then delete the ID Tag
        crud.remove_membership_vehicle(dbsession, vehicle_id, membership_id)
        crud.delete_vehicle(dbsession, vehicle_id)

        delete_response = DeleteVehicleResponse(
            detail='Successfully removed vehicle.'
        )
        return delete_response
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(4040, 'No vehicle or PCID found.')


@router.get("/member/{member_id}", status_code=status.HTTP_200_OK, response_model=List[VehicleResponse])
async def get_vehicle_by_member_id(request: Request,
                                   member_id: UUID,
                                   dbsession: SessionLocal = Depends(create_session)):
    """
    Get Vehicle by Member ID, return a List
    """

    auth_token_data = decode_auth_token_from_headers(request.headers)
    _ = auth_token_data.get('membership_id')
    # vehicles = crud.get_membership_vehicle_list(dbsession, member_id, check_permission=False)
    # return vehicles
    vehicle_query = crud.get_membership_vehicle_list_query(dbsession, member_id, check_permission=False)
    return vehicle_query.order_by(models.Vehicle.created_at.asc()).all()


@router.get(
    "/member_v2/{member_id}",
    response_model=Union[List[VehicleResponse], Page[VehicleResponse]],    # pylint:disable=unsubscriptable-object
    status_code=status.HTTP_200_OK
)
async def get_vehicle_by_member_id_v2(request: Request,
                                      member_id: UUID,
                                      dbsession: SessionLocal = Depends(create_session),
                                      pagination: bool = True,
                                      params: Params = Depends()):
    """
    Get Vehicle by Member ID, return a List, v2 supporting pagination by default
    """

    auth_token_data = decode_auth_token_from_headers(request.headers)
    _ = auth_token_data.get('membership_id')

    query = crud.get_membership_vehicle_list_query(
        dbsession, member_id, check_permission=False).order_by(
        models.Vehicle.created_at.asc())

    if pagination:
        return paginate(query, params)

    return query.all()


@router.get("/{vehicle_id}", status_code=status.HTTP_200_OK, response_model=VehicleResponse)
async def get_vehicle_by_vehicle_id(vehicle_id: UUID, request: Request,
                                    dbsession: SessionLocal = Depends(create_session)):
    """
    Get Vehicle List
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_membership = crud.get_membership_by_vehicle_id(dbsession,
                                                          vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')

        db_vehicle = crud.get_vehicle(dbsession, vehicle_id)
        return db_vehicle
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(4040, 'No vehicle or PCID found.')


@router.patch("/{vehicle_id}", status_code=status.HTTP_200_OK, response_model=VehicleResponse)
async def update_vehicle(vehicle_data: VehicleUpdate, vehicle_id: UUID, request: Request,
                         dbsession: SessionLocal = Depends(create_session)):
    """
    Update Vehicle
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_membership = crud.get_membership_by_vehicle_id(dbsession,
                                                          vehicle_id,
                                                          check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')

        db_vehicle = crud.get_vehicle(dbsession, vehicle_id)

        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')

        # Check if autocharge is registered
        if db_vehicle.autocharge is not None:
            if vehicle_data.registration_number is None:
                raise HTTPException(
                    400,
                    'Vehicle is registered for Autocharge.Cannot update the Vehicle.'
                )
            logger.info('Updating only registration number field as vehicle is registered for autocharge')
            vehicle_data = VehicleUpdate(registration_number=vehicle_data.registration_number)

        # Check if emaid array is registered
        if db_vehicle.emaid is not None and len(db_vehicle.emaid) > 0:
            if vehicle_data.registration_number is None:
                raise HTTPException(
                    400,
                    'Vehicle is registered for Plug and Charge. Cannot update the Vehicle.'
                )
            logger.info('Updating only registration number field as vehicle is registered for PnC')
            vehicle_data = VehicleUpdate(registration_number=vehicle_data.registration_number)

        update_vehicle_history(dbsession, vehicle_id)
        return crud.update_vehicle(dbsession, vehicle_data, vehicle_id)

    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(404, 'No vehicle found.')


# AUTOCHARGE API
@router.post('/autocharge/register/{vehicle_id}', status_code=status.HTTP_201_CREATED)
async def register_vehicle_for_autocharge(request: Request, vehicle_id: UUID,
                                          dbsession: SessionLocal = Depends(create_session)):
    '''
    This function registers a vehicle for autocharge.
    However, the MAC address has not been captured, yet
    So the the autocharge status will be Pending.
    '''
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_membership = crud.get_membership_by_vehicle_id(dbsession,
                                                          vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')
        db_vehicle = (
            crud.VehicleCRUD.query(dbsession)
            .filter(models.Vehicle.id == vehicle_id)
            .first()
        )
        if db_vehicle.autocharge is None:
            autocharge_data = AutochargeCreate(
                status=AutochargeStatus.pending,
                vehicle_id=str(db_vehicle.id)
            )
            crud.create_autocharge(dbsession, autocharge_data)
            response_message = {
                "detail_message": "Successfully registered vehicle for Autocharge.",
                "vehicle_id": str(db_vehicle.id)
            }
        else:
            response_message = {
                "detail_message": "Vehicle already registered for Autocharge.",
                "vehicle_id": str(db_vehicle.id)
            }
        return JSONResponse(response_message)
    except Exception as e:
        logger.info('Exception while registering Vehicle for autocharge: %s', str(e))
        raise HTTPException(400, 'Unable to register vehicle for autocharge')


@router.get('/autocharge/{vehicle_id}', status_code=status.HTTP_200_OK)
async def get_autocharge_status(request: Request, vehicle_id: UUID,
                                dbsession: SessionLocal = Depends(create_session)):
    '''
    Function to get the autocharge status of a vehicle
    '''
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_membership = crud.get_membership_by_vehicle_id(dbsession, vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')

        db_vehicle = crud.VehicleCRUD.query(dbsession).filter(models.Vehicle.id == vehicle_id).first()
        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')

        if db_vehicle.autocharge is None:
            response_message = {
                "vehicle_id": str(db_vehicle.id),
                "autocharge_status": None
            }
        else:
            response_message = {
                "vehicle_id": str(db_vehicle.id),
                "autocharge_status": db_vehicle.autocharge.status
            }

        return JSONResponse(response_message)
    except Exception as e:
        logger.info('Exception while deactivating autocharge for Vehicle %s', str(e))
        raise HTTPException(400, 'Unable to get autocharge status for vehicle')


@router.put('/autocharge/activate/{vehicle_id}', status_code=status.HTTP_200_OK)
async def activate_autocharge_for_vehicle(request: Request, vehicle_id: UUID, mac_address: str,
                                          dbsession: SessionLocal = Depends(create_session)):
    '''
    This function ties a VID/MAC address to a particular vehicle record. It also creates
    a corresponding ID Tag. This will be invoked by the mobile app after the mobile app webhook
    is invoked when the VID/MAC address is received via DataTransfer from the charger.
    '''
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        mac_str = str(macaddress.MAC(mac_address)).replace('-', '')
        mac_vid = 'VID:' + mac_str
    except ValueError:
        raise HTTPException(400, 'Invalid MAC Address')

    try:
        db_membership = crud.get_membership_by_vehicle_id(dbsession, vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')
        db_vehicle = crud.VehicleCRUD.query(dbsession).filter(models.Vehicle.id == vehicle_id).first()
        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')
        db_autocharge = db_vehicle.autocharge
        if db_autocharge is None:
            raise HTTPException(400, 'Vehicle has not been registered for autocharge')

        if db_autocharge.status != AutochargeStatus.pending:
            raise HTTPException(400, 'Vehicle has already been Activated previously')

        # Create a corresponding id tag with VID: prefix with the MAC address
        id_tag_data = IDTag(
            id_tag=mac_vid,
            name=f'VID for {db_vehicle.id}',
            type=IDTagType.vid,
            expiration=datetime.now(timezone.utc) + timedelta(days=365 * AUTOCHARGE_VALIDITY_DURATION_YEARS),
            is_active=True,
            description=f'VID id_tag for {db_vehicle.id}',
            member_id=membership_id,
            autocharge_id=db_autocharge.id
        )
        create_id_tag(dbsession, id_tag_data)

        autocharge_update = AutochargeUpdate(mac_address=mac_str, status=AutochargeStatus.active)
        _ = crud.update_autocharge(dbsession, autocharge_update, db_autocharge.id)
        response_message = {
            "detail_message": "Successfully activated vehicle for Autocharge.",
            "vehicle_id": str(db_vehicle.id)
        }

        return JSONResponse(response_message)
    except Exception as e:
        logger.info('Exception while activating Vehicle for autocharge: %s', str(e))
        raise HTTPException(status_code=400, detail='Unable to activate vehicle for autocharge')


@router.put('/autocharge/deactivate/{vehicle_id}', status_code=status.HTTP_200_OK)
async def deactivate_autocharge_for_vehicle(request: Request, vehicle_id: UUID,  # pylint: disable=too-many-branches
                                            dbsession: SessionLocal = Depends(create_session)):
    '''
    This function deactivates the autocharge record for a vehicle. It then soft deletes the
    id tag and the autocharge record.
    '''
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_membership = crud.get_membership_by_vehicle_id(dbsession, vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')
        db_vehicle = crud.VehicleCRUD.query(dbsession).filter(models.Vehicle.id == vehicle_id).first()
        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')

        if db_vehicle.autocharge is None:
            raise HTTPException(400, 'Unable to deactivate as the Vehicle has not been registered for autocharge')

        # Soft delete the corresponding id tag as well as the autocharge record.
        db_autocharge = db_vehicle.autocharge
        id_tag_record = db_autocharge.id_tag
        if id_tag_record is not None:
            id_tag_update = IDTagUpdate(id_tag=id_tag_record.id_tag, name=id_tag_record.name,
                                        type=id_tag_record.type, is_active=False,
                                        member_id=membership_id, expiration=datetime.now(timezone.utc))
            # id_tag_update = IDTagUpdate(autocharge_id=None)
            id_tag_record = IDTagCRUD.update(dbsession, id_tag_record.id,
                                             id_tag_update.dict(exclude_unset=False, exclude_defaults=False),
                                             check_permission=False)
            if id_tag_record is not None:
                logger.info('id_tag_record is not None. id is %s', str(id_tag_record.id))
            crud.delete_id_tag(dbsession, id_tag_record.id)

        autocharge_update = AutochargeUpdate(status=AutochargeStatus.deactivated, vehicle_id=None)
        db_autocharge = AutochargeCRUD.update(dbsession, db_autocharge.id,
                                              autocharge_update.dict(exclude_unset=False, exclude_defaults=False))
        crud.delete_autocharge(dbsession, db_autocharge.id)
        response_message = {
            "detail_message": "Successfully deactivated Autocharge for vehicle.",
            "vehicle_id": str(db_vehicle.id)
        }

        return JSONResponse(response_message)
    except Exception as e:
        logger.info('Exception while deactivating autocharge for Vehicle %s', str(e))
        raise HTTPException(400, 'Unable to deactivate autocharge for vehicle')


@router.put('/autocharge/disable/{vehicle_id}', status_code=status.HTTP_200_OK)
async def disable_autocharge_for_vehicle(request: Request, vehicle_id: UUID,
                                         dbsession: SessionLocal = Depends(create_session)):
    '''
    This function disables the autocharge record for a vehicle. It temporarily deactivates the
    corresponding ID Tag
    '''
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_membership = crud.get_membership_by_vehicle_id(dbsession, vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')
        db_vehicle = crud.VehicleCRUD.query(dbsession).filter(models.Vehicle.id == vehicle_id).first()
        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')
        db_autocharge = db_vehicle.autocharge
        if db_autocharge is None:
            raise HTTPException(400,
                                'Unable to disable autocharge as the Vehicle has not been registered for autocharge')

        if db_autocharge.status != AutochargeStatus.active:
            raise HTTPException(400, 'Autocharge is not activated for this vehicle.')

        autocharge_update = AutochargeUpdate(status=AutochargeStatus.disabled)
        crud.update_autocharge(dbsession, autocharge_update, db_autocharge.id)
        # Temporarily deactivate the corresponding id tag as well as the autocharge record.
        id_tag_update = IDTagUpdate(is_active=False)
        # crud.update_id_tag(dbsession, id_tag_update, db_autocharge.id_tag.id)
        IDTagCRUD.update(dbsession, db_autocharge.id_tag.id,
                         id_tag_update.dict(exclude_unset=True, exclude_defaults=True),
                         check_permission=False)
        response_message = {
            "detail_message": "Successfully disabled autocharge for vehicle.",
            "vehicle_id": str(db_vehicle.id)
        }

        return JSONResponse(response_message)
    except Exception as e:
        logger.info('Exception while disabling autocharge for Vehicle: %s', str(e))
        raise HTTPException(400, 'Unable to disable autocharge for vehicle')


# noqa: MC0001, # pylint: disable=too-many-branches
@router.put('/autocharge/enable/{vehicle_id}', status_code=status.HTTP_200_OK)
async def enable_autocharge_for_vehicle(request: Request, vehicle_id: UUID,
                                        dbsession: SessionLocal = Depends(create_session)):
    '''
    This function enables back the autocharge record for a vehicle if it was temporarily disabled previously
    '''
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_membership = crud.get_membership_by_vehicle_id(dbsession, vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')
        db_vehicle = crud.VehicleCRUD.query(dbsession).filter(models.Vehicle.id == vehicle_id).first()
        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')

        db_autocharge = db_vehicle.autocharge
        if db_autocharge is None:
            raise HTTPException(400, 'Vehicle has not been registered for autocharge')

        if db_autocharge.status != AutochargeStatus.disabled:
            raise HTTPException(400, 'Unable to enable autocharge for this vehicle.')

        autocharge_update = AutochargeUpdate(status=AutochargeStatus.active)
        crud.update_autocharge(dbsession, autocharge_update, db_autocharge.id)
        # Temporarily deactivate the corresponding id tag as well as the autocharge record.
        id_tag_update = IDTagUpdate(is_active=True)
        # crud.update_id_tag(dbsession, id_tag_update, db_autocharge.id_tag.id)
        IDTagCRUD.update(dbsession, db_autocharge.id_tag.id,
                         id_tag_update.dict(exclude_unset=True, exclude_defaults=True),
                         check_permission=False)
        response_message = {
            "detail_message": "Successfully enabled autocharge for vehicle.",
            "vehicle_id": str(db_vehicle.id)
        }

        return JSONResponse(response_message)
    except Exception as e:
        logger.info('Exception while enabling autocharge for Vehicle: %s', str(e))
        raise HTTPException(400, 'Unable to enable autocharge for vehicle')


# Plug and Charge (PnC) API
@router.post('/plug_and_charge/{vehicle_id}', status_code=status.HTTP_200_OK)
async def add_vehicle_pcid(request: Request, pcid: str,  # noqa: MC0001
                           vehicle_id: UUID, background_tasks: BackgroundTasks,
                           dbsession: SessionLocal = Depends(create_session)):
    """
    Add an Emaid link Membership with Vehicle
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    emaid_list = []

    # Step 1: Check for General Validations
    if not bool(re.match(r'^[a-zA-Z0-9]{17,18}$', pcid)):
        raise HTTPException(400, 'Invalid PCID format')
    try:
        try:
            db_vehicle = crud.get_vehicle(dbsession, vehicle_id)
            if db_vehicle is None:
                raise HTTPException(404, 'Vehicle Not Found')
        except exceptions.ApolloObjectDoesNotExist:
            raise HTTPException(404, 'Vehicle Not Found')

        db_membership = crud.get_membership_by_vehicle_id(dbsession, vehicle_id, check_permission=False)
        # Check if the user is adding a PCID to his own vehicle
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner of vehicle.')

        # Sanity checks on the vehicle record
        db_member_vehicle_list = crud.get_membership_vehicle_list(dbsession, db_membership.id)
        for vehicle_record in db_member_vehicle_list:
            # Check if the user has another vehicle with the same PCID
            if str(vehicle_record.id) != str(vehicle_id) and vehicle_record.pcid == pcid:
                raise HTTPException(400, 'User already has a vehicle with this PCID')

        # Step 2: Lookup if the pcid is registered in Hubject Prov Cert Pool
        lookup_pcid_status, message = await lookup_pcid(dbsession, pcid)
        if not lookup_pcid_status:
            raise HTTPException(400, message)

        # Record the PCID. If no contract has been created before allow PCID to be updated.
        if db_vehicle.emaid is None or len(db_vehicle.emaid) == 0:
            crud.update_vehicle(dbsession, VehicleUpdate(pcid=pcid), vehicle_id)
        else:
            if db_vehicle.pcid != pcid:
                raise HTTPException(400, 'PCID mismatch. Remove the vehicle and retry.')

        # Check for any existing contracts for this PCID
        emaid_list = db_vehicle.emaid
        if len(emaid_list) > 0:
            if emaid_list[0].status == EmaidStatus.active:
                raise HTTPException(400, 'Vehicle already has a contract')

    except exceptions.ApolloVehicleError:
        raise HTTPException(400, 'Error Registering PCID for this Vehicle')

    # Step 3: Figure out the fee for that particular country based on user's phone number.
    try:
        db_cc_fee = get_contract_certificate_fee_for_pnc(dbsession, db_membership)
        logger.debug('PnC: Contract Certificate Fee: %s', db_cc_fee.activation_fee)
    except (exceptions.ApolloObjectDoesNotExist, AttributeError):
        raise HTTPException(400, 'Plug and charge is not supported for this Country')

    db_emaid = None
    emaid_prefix = settings.EMAID_COUNTRY_CODE + settings.EMAID_PROVIDER_ID

    try:
        # Step 4: Create EMAID and ID TAG (if no EMAID)
        if len(emaid_list) == 0:
            emaid_tag = emaid_prefix + create_random_emaid_tag()
            emaid_data = EmaidCreate(
                emaid=emaid_tag,  # need make it as dynamic
                status=EmaidStatus.payment_pending,
                status_comment='Registration in progress.',
                vehicle_id=str(db_vehicle.id)
            )
            logger.debug('PnC: Creating Emaid for Vehicle: %s with emaid: %s', str(db_vehicle.id), emaid_tag)
            db_emaid = crud.create_emaid(dbsession, emaid_data)
            # db_vehicle = crud.get_vehicle(dbsession, db_vehicle.id)

            update_vehicle_history(dbsession, db_vehicle.id)

            # Create a corresponding id tag for responding to PnC Authorize messages
            # Do not enable the id tag until the certificate generation is actually complete
            id_tag_data = IDTag(
                id_tag=db_emaid.emaid,
                name=f'Contract for {db_vehicle.pcid}',
                type=IDTagType.pnc,
                expiration=datetime.now(timezone.utc),
                is_active=False,
                description=f'Contract id tag for {db_vehicle.pcid}',
                member_id=membership_id,
                emaid_id=str(db_emaid.id)
            )
            logger.debug('PnC: Creating ID Tag %s for emaid: %s', db_emaid.emaid, db_emaid.id)
            create_id_tag(dbsession, id_tag_data)

            response_message = {
                "detail_message": "Successfully registered vehicle.",
                "vehicle_id": str(db_vehicle.id)
            }
        else:
            if emaid_list[0].status in (EmaidStatus.pending, EmaidStatus.payment_pending, EmaidStatus.failed):
                db_emaid = emaid_list[0]
                response_message = {
                    "detail_message": "Retrying Contract Creation",
                    "vehicle_id": str(db_vehicle.id)
                }
            else:
                raise HTTPException(400, 'Invalid Operation')

        # Step 5: Create Contract Certificate Order
        contract_certificate_order = ContractCertificateOrderCreate(
            amount=db_cc_fee.activation_fee,
            currency=db_cc_fee.payment_currency,
            emaid_id=db_emaid.id,
            status=ContractCertificateFeePaymentStatus.pending)

        logger.debug('PnC: Creating Contract Certificate Order for emaid: %s with amount of %s',
                     db_emaid.emaid, db_cc_fee.activation_fee)

        if db_cc_fee.activation_fee == 0:
            contract_certificate_order.status = ContractCertificateFeePaymentStatus.success
            emaid_update_data = EmaidUpdate(
                status=EmaidStatus.pending,
            )
            db_emaid = crud.update_emaid(dbsession, emaid_update_data, db_emaid.id)
            crud.update_vehicle_history(dbsession, db_vehicle.id)
            logger.debug('PnC: Contract Certificate Fee is 0 for emaid: %s', db_emaid.emaid)

        # Retrying if a previous contract certificate creation attempt failed.
        if db_emaid.status in (EmaidStatus.pending, EmaidStatus.failed):
            contract_certificate_order.status = ContractCertificateFeePaymentStatus.success
            contract_certificate_order.amount = 0

        db_contract_certificate_order = crud.create_contract_certificate_order(dbsession, contract_certificate_order)

        if db_contract_certificate_order.amount == 0:
            # Step 6F: If the fee is 0, proceed to register with Hubject and then return the response
            background_tasks.add_task(pcid_and_callback, db_vehicle, str(db_vehicle.id), 'New')
            logger.debug('PnC: Contract Certificate Fee is 0 for emaid: %s. Returning response.', db_emaid.emaid)
            return JSONResponse(response_message)

        try:
            # Step 6: Create Contract Certificate Payment Request
            logger.debug(
                'PnC: Creating Contract Certificate Payment Request for emaid: %s with amount of %s',
                db_emaid.emaid,
                db_cc_fee.activation_fee
            )
            contract_certificate_pr = crud.create_contract_certificate_payment_request(
                dbsession,
                membership_id,
                db_cc_fee.activation_fee,
                db_cc_fee.payment_currency,
                db_contract_certificate_order.id,
                {'emaid_id': str(db_emaid.id)})
        except exceptions.ApolloPaymentRequestError as e:
            raise HTTPException(400, e.__str__())

        vkey = calculate_md5_hash(f'{contract_certificate_pr.amount}{settings.MERCHANT_ID}'
                                  f'{contract_certificate_pr.invoice_number}{settings.VERIFY_KEY}')
        params = {
            'amount': contract_certificate_pr.amount,
            'orderid': contract_certificate_pr.invoice_number,
            'bill_name': f'{contract_certificate_pr.member.first_name} '
                         f'{contract_certificate_pr.member.last_name}',
            'bill_email': contract_certificate_pr.member.user.email,
            'bill_mobile': contract_certificate_pr.member.user.phone_number,
            'bill_desc': contract_certificate_pr.billing_description,
            'vcode': vkey,
            'channel': 'creditAN',
            'username': settings.MERCHANT_ID,
            'app_name': 'Apollo',
        }
        if db_cc_fee.payment_currency in [Currency.sgd]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        elif db_cc_fee.payment_currency in [Currency.bnd]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        elif db_cc_fee.payment_currency in [Currency.khr]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        else:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-callback'

        if db_cc_fee.payment_currency in [Currency.sgd, Currency.bnd, Currency.khr]:
            params['channel'] = 'creditAI'  # SGD and BND use credtiAI

        url = f'{PAYMENT_DIRECT_API_URL}?' + urllib.parse.urlencode(params)
        return url

    except exceptions.ApolloEmaidError:
        raise HTTPException(400, 'EMAID cannot be created.')


@router.put("/plug_and_charge/renew/{vehicle_id}", status_code=status.HTTP_200_OK)
async def renew_vehicle(vehicle_id: UUID, request: Request, background_tasks: BackgroundTasks,  # noqa: MC0001
                        dbsession: SessionLocal = Depends(create_session)):
    """
    Renew the contract certificate
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    try:
        # Step 1: Check for general validation (PCID format, correct owner,
        # check if PCID is already registered with chargEV database)
        db_membership = crud.get_membership_by_vehicle_id(dbsession, vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')

        db_vehicle = crud.get_vehicle(dbsession, vehicle_id)
        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')

        if db_vehicle.emaid is None or len(db_vehicle.emaid) == 0:
            raise HTTPException(404, 'No contract found.')

        if db_vehicle.emaid[0].status not in (
            EmaidStatus.active,
            EmaidStatus.disabled,
            EmaidStatus.renewal_payment_pending,
            EmaidStatus.renewal_pending,
            EmaidStatus.renewal_rejected
        ):
            raise HTTPException(400, 'Invalid Operation.')

        db_emaid = db_vehicle.emaid[0]

        # Step 2: Figure out the fee for that particular country based on user's phone number.
        db_cc_fee = get_contract_certificate_fee_for_pnc(dbsession, db_vehicle.membership)

        # Step 3: Create Contract Certificate Order
        contract_certificate_order = ContractCertificateOrderCreate(
            amount=db_cc_fee.renewal_fee,
            currency=db_cc_fee.payment_currency,
            emaid_id=db_emaid.id,
            status=ContractCertificateFeePaymentStatus.pending)

        logger.debug(
            'PnC: Creating Contract Certificate Order for emaid for Renewal: %s with amount of %s',
            db_emaid.emaid, db_cc_fee.renewal_fee
        )

        if db_cc_fee.renewal_fee == 0:
            contract_certificate_order.status = ContractCertificateFeePaymentStatus.success
            logger.debug('PnC: Contract Certificate Renewal Fee is 0 for emaid: %s', db_emaid.emaid)

        if db_emaid.status in (EmaidStatus.renewal_pending, EmaidStatus.renewal_rejected):
            contract_certificate_order.status = ContractCertificateFeePaymentStatus.success
            contract_certificate_order.amount = 0

        db_contract_certificate_order = crud.create_contract_certificate_order(dbsession, contract_certificate_order)

        if db_contract_certificate_order.amount == 0:
            # Step 4F: If the fee is 0, proceed to register with Hubject and then return the response
            background_tasks.add_task(pcid_and_callback, db_vehicle, str(db_vehicle.id), 'Renew')
            response_message = {
                "detail_message": "Renewing Contract.",
                "vehicle_id": str(db_vehicle.id)
            }
            return JSONResponse(response_message)

        emaid_update_data = EmaidUpdate(
            status=EmaidStatus.renewal_payment_pending,
        )
        db_emaid = crud.update_emaid(dbsession, emaid_update_data, db_emaid.id)

        try:
            # Step 4: Create Contract Certificate Payment Request
            logger.debug(
                'PnC: Creating Contract Certificate Renewal Payment Request for emaid: %s with amount of %s',
                db_emaid.emaid, db_cc_fee.renewal_fee
            )
            contract_certificate_pr = crud.create_contract_certificate_payment_request(
                dbsession,
                membership_id,
                db_cc_fee.renewal_fee,
                db_cc_fee.payment_currency,
                db_contract_certificate_order.id,
                {'emaid_id': str(db_emaid.id)},
                is_renewal=True)
        except exceptions.ApolloPaymentRequestError as e:
            raise HTTPException(400, e.__str__())

        vkey = calculate_md5_hash(f'{contract_certificate_pr.amount}{settings.MERCHANT_ID}'
                                  f'{contract_certificate_pr.invoice_number}{settings.VERIFY_KEY}')
        params = {
            'amount': contract_certificate_pr.amount,
            'orderid': contract_certificate_pr.invoice_number,
            'bill_name': f'{contract_certificate_pr.member.first_name} '
                         f'{contract_certificate_pr.member.last_name}',
            'bill_email': contract_certificate_pr.member.user.email,
            'bill_mobile': contract_certificate_pr.member.user.phone_number,
            'bill_desc': contract_certificate_pr.billing_description,
            'vcode': vkey,
            'channel': 'creditAN',
            'username': settings.MERCHANT_ID,
            'app_name': 'Apollo',
        }
        if db_cc_fee.payment_currency in [Currency.sgd]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        elif db_cc_fee.payment_currency in [Currency.bnd]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        elif db_cc_fee.payment_currency in [Currency.khr]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        else:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-callback'

        if db_cc_fee.payment_currency in [Currency.sgd, Currency.bnd, Currency.khr]:
            params['channel'] = 'creditAI'  # SGD and BND use credtiAI

        url = f'{PAYMENT_DIRECT_API_URL}?' + urllib.parse.urlencode(params)
        return url
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(404, 'No vehicle found.')
    except AttributeError:
        raise HTTPException(400, 'Plug and charge is not supported for this Country')


@router.put("/plug_and_charge/disable/{vehicle_id}", status_code=status.HTTP_200_OK)
async def disable_plug_and_charge_for_vehicle(request: Request, vehicle_id: UUID,
                                              dbsession: SessionLocal = Depends(create_session)):
    """
    This function disables the contract certificate for a vehicle.
    It temporarily deactivates the corresponding ID Tag.
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_membership = crud.get_membership_by_vehicle_id(
            dbsession, vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')
        db_vehicle = (
            crud.VehicleCRUD.query(dbsession)
            .filter(models.Vehicle.id == vehicle_id)
            .first()
        )
        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')

        emaid_list = db_vehicle.emaid
        if len(emaid_list) == 0:
            response_message = {
                "detail_message": (
                    "Unable to disable contract as the Vehicle has not been "
                    "registered for contract"
                ),
                "vehicle_id": str(db_vehicle.id)
            }
        else:
            if emaid_list[0].status != EmaidStatus.active:
                response_message = {
                    "detail_message": "Plug and Charge is not registered for this vehicle.",
                    "vehicle_id": str(db_vehicle.id)
                }
            else:
                # Temporarily deactivate the corresponding id tag as well as the autocharge record.
                id_tag_record = emaid_list[0].id_tag
                if id_tag_record is not None:
                    id_tag_update = IDTagUpdate(is_active=False)
                    IDTagCRUD.update(dbsession, id_tag_record.id,
                                     id_tag_update.dict(exclude_unset=True, exclude_defaults=True),
                                     check_permission=False)
                emaid_update = EmaidUpdate(status=EmaidStatus.disabled)
                EmaidCRUD.update(dbsession, emaid_list[0].id,
                                 emaid_update.dict(exclude_unset=True, exclude_defaults=True))
                response_message = {
                    "detail_message": "Successfully disabled Plug and Charge for vehicle.",
                    "vehicle_id": str(db_vehicle.id)
                }
        return JSONResponse(response_message)
    except Exception as e:
        logger.info('Exception while disabling Plug and Charge for Vehicle: %s', str(e))
        raise HTTPException(400, 'Unable to disable Plug and Charge for vehicle')


@router.put("/plug_and_charge/enable/{vehicle_id}", status_code=status.HTTP_200_OK)
async def enable_plug_and_charge_for_vehicle(request: Request, vehicle_id: UUID,
                                             dbsession: SessionLocal = Depends(create_session)):
    """
    This function enables back the contract certificate for a vehicle
    if it was temporarily disabled previously
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_membership = crud.get_membership_by_vehicle_id(
            dbsession, vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')
        db_vehicle = (
            crud.VehicleCRUD.query(dbsession)
            .filter(models.Vehicle.id == vehicle_id)
            .first()
        )
        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')

        emaid_list = db_vehicle.emaid
        if len(emaid_list) == 0:
            response_message = {
                "detail_message": (
                    "Unable to enable contract as the Vehicle has not been "
                    "registered for contract"
                ),
                "vehicle_id": str(db_vehicle.id)
            }
        else:
            if emaid_list[0].status != EmaidStatus.disabled:
                response_message = {
                    "detail_message": (
                        "Unable to enable Plug and Charge for this vehicle."
                    ),
                    "vehicle_id": str(db_vehicle.id)
                }
            else:
                id_tag_record = emaid_list[0].id_tag
                if id_tag_record is not None:
                    id_tag_update = IDTagUpdate(is_active=True)
                    IDTagCRUD.update(dbsession, id_tag_record.id,
                                     id_tag_update.dict(exclude_unset=True, exclude_defaults=True),
                                     check_permission=False)
                emaid_update = EmaidUpdate(status=EmaidStatus.active)
                EmaidCRUD.update(dbsession, emaid_list[0].id,
                                 emaid_update.dict(exclude_unset=True, exclude_defaults=True))
                response_message = {
                    "detail_message": "Successfully enabled Plug and Charge for vehicle.",
                    "vehicle_id": str(db_vehicle.id)
                }
        return JSONResponse(response_message)
    except Exception as e:
        logger.info('Exception while enabling Plug and Charge for Vehicle: %s', str(e))
        raise HTTPException(400, 'Unable to enable Plug and Charge for vehicle')


@router.put("/plug_and_charge/deactivate/{vehicle_id}", status_code=status.HTTP_200_OK)
async def deactivate_plug_and_charge_for_vehicle(request: Request, vehicle_id: UUID,
                                                 dbsession: SessionLocal = Depends(create_session)):
    """
    This function deactivates the contract certificate for a vehicle.
    It then soft deletes the id tag and the emaid record.
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    try:
        db_membership = crud.get_membership_by_vehicle_id(
            dbsession, vehicle_id, check_permission=False)
        if str(db_membership.id) != str(membership_id):
            raise HTTPException(403, 'Forbidden, wrong owner.')
        db_vehicle = (
            crud.VehicleCRUD.query(dbsession)
            .filter(models.Vehicle.id == vehicle_id)
            .first()
        )
        if db_vehicle is None:
            raise HTTPException(404, 'Vehicle Not Found')

        emaid_list = db_vehicle.emaid
        if len(emaid_list) == 0:
            raise HTTPException(400, 'Unable to find the contract for this vehicle.')

        if emaid_list[0].status not in [EmaidStatus.active, EmaidStatus.disabled]:
            raise HTTPException(400, 'Unable to deactivate Plug and Charge for this vehicle.')

        hubject_delete_status = await delete_contract_certificate(dbsession, emaid_list[0].emaid)
        if not hubject_delete_status:
            raise HTTPException(400, 'Unable to deactivate Plug and Charge for this vehicle.')

        # Soft delete the corresponding id tag as well as the autocharge record.
        id_tag_record = emaid_list[0].id_tag
        if id_tag_record is not None:
            id_tag_update = IDTagUpdate(id_tag=id_tag_record.id_tag,
                                        name=id_tag_record.name,
                                        type=id_tag_record.type, is_active=False,
                                        member_id=membership_id,
                                        expiration=datetime.now(timezone.utc))

            id_tag_record = IDTagCRUD.update(dbsession, id_tag_record.id,
                                             id_tag_update.dict(exclude_unset=False,
                                                                exclude_defaults=False),
                                             check_permission=False)

            if id_tag_record is not None:
                logger.info('id_tag_record is not None. id is %s', str(id_tag_record.id))
            crud.delete_id_tag(dbsession, id_tag_record.id)
        plugandcharge_update = EmaidUpdate(status=EmaidStatus.deactivated, vehicle_id=None)
        db_plugandcharge = EmaidCRUD.update(dbsession, emaid_list[0].id,
                                            plugandcharge_update.dict(exclude_unset=False,
                                                                      exclude_defaults=False))
        crud.delete_emaid(dbsession, db_plugandcharge.id)
        response_message = {
            "detail_message": "Successfully deactivated Plug and Charge for vehicle.",
            "vehicle_id": str(db_vehicle.id)
        }
        return JSONResponse(response_message)
    except Exception as e:
        logger.info('Exception while deactivating Plug and Charge for Vehicle: %s', str(e))
        raise HTTPException(400, 'Unable to deactivate Plug and Charge for vehicle.')


@router.get("/manual_check_pcid/{vehicle_id}", status_code=status.HTTP_200_OK, response_model=VehicleResponse)
async def manual_check_pcid(vehicle_id: UUID, request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Manual trigger PCID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    _ = auth_token_data.get('membership_id')

    try:
        db_vehicle = crud.get_vehicle(dbsession, vehicle_id)
        await pcid_and_callback(db_vehicle, str(vehicle_id), 'New')

        return db_vehicle
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(404, 'No vehicle or PCID found.')


@router.get("/plug_and_charge/contract_certificate_fee", status_code=status.HTTP_200_OK)
async def get_contract_certificate_fee(request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Get the contract certificate Fee for the current user
    """
    try:
        auth_token_data = decode_auth_token_from_headers(request.headers)
        membership_id = auth_token_data.get('membership_id')
        db_member_obj = crud.get_membership_by_id(dbsession, membership_id)
        db_user_obj = crud.get_user_by_id(dbsession, db_member_obj.user_id)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(404, 'Member not found')
    try:
        iso3166_country_code = phone_country(db_user_obj.phone_number)
        db_cc_fee = crud.get_contract_certificate_fee_by_country(dbsession, iso3166_country_code)
        return db_cc_fee
    except exceptions.NoResultFound:
        raise HTTPException(404, 'Country not supported')


def get_contract_certificate_fee_for_pnc(dbsession, db_membership):
    '''Helper function to get contract certificate fee'''
    db_user = crud.get_user_by_id(dbsession, db_membership.user_id)
    iso3166_country_code = phone_country(db_user.phone_number)
    db_cc_fee = crud.get_contract_certificate_fee_by_country(dbsession, iso3166_country_code)
    logger.debug(
        'PnC: Contract Certificate Renewal Fee: %s for country: %s',
        db_cc_fee.activation_fee, iso3166_country_code
    )
    return db_cc_fee
