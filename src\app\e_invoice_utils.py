import asyncio
import os
from datetime import datetime, timezone
from pathlib import Path
from typing import Union
from zoneinfo import ZoneInfo
import logging

from dateutil import parser
from fastapi import HTTPException

import httpx

from sqlalchemy import desc

from app import settings, models, crud
from app import schema
from app.schema import ChargingSessionBillUpdateEInvoiceStatus, PaymentRequestType
from app.schema.e_invoice import *
from app.crud import (get_e_invoice_by_csb_id, PaymentRequestCRUD, create_e_invoice, update_e_invoice,
                      get_payment_refund_by_id, get_e_credit_note_by_payment_refund_id, create_e_cn,
                      update_e_credit_note)

BASE_DIR = Path(__file__).resolve().parent.parent
logger = logging.getLogger(__name__)


async def login_to_rmp():
    base_url = settings.E_INVOICE_API_BASE_URL
    rmp_client_id = settings.E_INVOICE_CLIENT_ID
    rmp_client_secret = settings.E_INVOICE_CLIENT_SECRET
    payload = {
        'clientId': rmp_client_id,
        'clientSecret': rmp_client_secret,
    }

    path = f'{base_url}/api/auth'

    async with httpx.AsyncClient() as client:
        response = await client.post(path, json=payload)
        if response.status_code >= 400:
            raise HTTPException(400, f'Failed to authenticate with RMP: {response.text}')

        response_json = response.json()
        return response_json['token']


def validate_e_invoice_status_for_mobile_display(dbsession, charging_session_bill, update_data: bool = False):
    gmt_info = ZoneInfo("Asia/Kuala_Lumpur")
    e_invoice = charging_session_bill.e_invoice
    if e_invoice:
        current_date = datetime.now(timezone.utc).astimezone(gmt_info)
        csb_created_at = charging_session_bill.created_at.astimezone(gmt_info)

        is_same_month = current_date.year == csb_created_at.year and current_date.month == csb_created_at.month

        current_status = e_invoice.status

        if current_status == EInvoiceStatus.pending.value and is_same_month:
            pass  # Keep as-is
        elif (current_status not in [EInvoiceStatus.requested.value, EInvoiceStatus.validated.value]
              and not is_same_month):
            e_invoice.status = EInvoiceStatus.not_applicable.value
            if update_data:
                dbsession.commit()
        charging_session_bill.e_invoice = e_invoice
    else:
        charging_session_bill.e_invoice = None

    return charging_session_bill


def validate_e_credit_note_status_for_mobile_display(dbsession, charging_session_bill, e_credit_note,
                                                     update_data: bool = False):
    # gmt_info = ZoneInfo("Asia/Kuala_Lumpur")
    if e_credit_note:
        charging_session_bill = validate_e_invoice_status_for_mobile_display(dbsession, charging_session_bill,
                                                                             update_data=False)
        e_invoice = charging_session_bill.e_invoice

        # current_date = datetime.now(timezone.utc).astimezone(gmt_info)
        # csb_created_at = charging_session_bill.created_at.astimezone(gmt_info)

        # is_same_month = current_date.year == csb_created_at.year and current_date.month == csb_created_at.month

        current_status = e_credit_note.status
        if e_invoice:
            if e_invoice.status == EInvoiceStatus.validated:
                if current_status == ECreditNoteStatus.pending.value:
                    pass  # Keep as-is
                # elif current_status not in [ECreditNoteStatus.requested.value, ECreditNoteStatus.validated.value]:
                #     e_credit_note.status = ECreditNoteStatus.not_applicable.value
                #     if update_data:
                #         dbsession.commit()
            else:
                e_credit_note.status = ECreditNoteStatus.not_applicable
        else:
            e_credit_note.status = ECreditNoteStatus.not_applicable
    return e_credit_note


# pylint:disable=too-many-statements, too-many-locals
async def e_invoice_submission_adapter(dbsession, cs, csb, pr, e_invoice_date_time):  # noqa mccabe: MC0001
    # Convert UTC datetime to GMT+8 (Asia/Kuala_Lumpur)
    e_invoice_date_time = e_invoice_date_time.astimezone(ZoneInfo("Asia/Kuala_Lumpur"))

    # Format date and time in GMT+8
    e_invoice_date = e_invoice_date_time.strftime("%Y-%m-%d")
    e_invoice_time = e_invoice_date_time.strftime("%H:%M:%SZ")

    payment_type = SubmissionPaymentType.others

    # if not pr:
    #     try:
    #         db_member = MembershipCRUD.query(dbsession).filter(
    #             func.lower(models.Membership.user_id_tag) == str(cs['id_tag']).lower()
    #         ).one()
    #         member = db_member
    #     except (NoResultFound, IntegrityError):
    #         try:
    #             db_member = IDTagCRUD.query(dbsession).filter(
    #                 func.lower(models.IDTag.id_tag) == str(cs['id_tag']).lower()
    #             ).one()
    #             db_member = db_member.member
    #             member = db_member
    #         except (NoResultFound, IntegrityError) as e:
    #             logger.error(e)
    #             member = None
    if pr:
        payment_type = SubmissionPaymentType.credit_card
        # if pr.type == schema.PaymentRequestType.payment_terminal:
        #     member = None
        # else:
        #     member = schema.ShallowMembershipResponse(**pr.meta)

    # buyer_email = None
    # buyer_phone = None
    # if member:
    #     user = member.user if member else None
    #     buyer_email = user.email
    #     buyer_phone = user.phone_number

    supplier = SubmissionSupplier(
        name="Green EV Charge Sdn Bhd",
        identification=[
            Identification(
                value="C26659337020",
                schemeID=IdentificationType.tin
            ),
            Identification(
                value="202101011556",
                schemeID=IdentificationType.brn,
            )],
        address=Address(
            address=[
                "Level 16, Menara South Point, Mid Valley City",
                "Medan Syed Putra Selatan, 59200 Kuala Lumpur"
            ],
            postalZone="59200",
            cityName="Kuala Lumpur",
        ),
        email="<EMAIL>",
        contactNumber="+60121234567",
    )

    # buyer = SubmissionBuyer(
    #     email=buyer_email,
    #     contactNumber=buyer_phone
    # )

    if cs['meta']['billing_info']['billing_type'].lower() == 'kwh':
        measurement = Measurement.kwh
        meter_start = cs['meter_start']
        meter_stop = cs['meter_stop']
        quantity = round(((meter_stop - meter_start) / 1000), 2)
    else:
        measurement = Measurement.min
        session_start = parser.isoparse(cs['session_start'])
        session_end = parser.isoparse(cs['session_end'])
        duration = session_end - session_start
        quantity = round(duration.total_seconds() / 60, 2)

    quantity = f"{quantity:.2f}"

    unit_price = f"{cs['meta']['billing_info']['billing_unit_fee']:.2f}"
    total_amount = f"{csb.total_amount:.2f}"
    usage_amount = f"{csb.usage_amount:.2f}"

    invoice_line_item = InvoiceLineItem(
        unitPrice=unit_price,
        subtotal=usage_amount,
        totalExcludingTax=total_amount,
        quantity=quantity,
        measurement=measurement,
        tax=Tax(),
        taxExemption=TaxExemption(
            amount=usage_amount
        ),
    )

    has_discount = False
    discount_amount = csb.usage_amount - csb.total_amount
    if discount_amount > 0:
        has_discount = True
        discount_rate = (discount_amount / csb.usage_amount) * 100
        invoice_line_item.discount = Discount(
            rate=f"{discount_rate:.2f}%",
            amount=f"{discount_amount:.2f}"
        )

    submission_total = SubmissionTotal(
        taxIncludedAmount=total_amount,
        taxExcludedAmount=total_amount,
        payableAmount=total_amount,
        netAmount=total_amount,
        chargeAmount=total_amount,
        taxAmountPerTaxType=[TaxAmountPerType()],
    )
    if has_discount:
        submission_total.discountValue = f"{discount_amount:.2f}"

    submission_doc = SubmissionDocument(
        eInvoiceNumber=csb.invoice_number,
        eInvoiceDate=e_invoice_date,
        eInvoiceTime=e_invoice_time,
        billReferenceNumber=csb.invoice_number,
        # buyer=buyer,
        invoiceLineItems=[invoice_line_item],
        paymentMode=payment_type,
        total=submission_total,
    )
    if settings.E_INVOICE_ADD_SUPPLIER_INFO:
        submission_doc.supplier = supplier

    submission_documents = SubmissionDocuments(
        type=SubmissionType.invoice,
        isSelfIssued=False,
        isConsolidatedInvoice=True,
        document=submission_doc
    )

    return_url = f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/csms/e_invoice/callback"

    e_invoice_submission = EInvoiceSubmission(
        documents=[submission_documents],
        returnUrl=return_url
    )

    return e_invoice_submission


async def construct_and_submit_invoice_to_rmp(dbsession, charging_session, csb, member_id=None):  # noqa: MC0001
    if (settings.ENABLE_E_INVOICE  # pylint: disable=too-many-nested-blocks
            or (settings.E_INVOICE_MEMBER_ID_LIST and str(member_id) in map(str, settings.E_INVOICE_MEMBER_ID_LIST))):
        try:
            db_e_invoice = get_e_invoice_by_csb_id(dbsession, csb.id)
            if not db_e_invoice:
                query = PaymentRequestCRUD.query(dbsession, check_permission=False).filter(
                    models.PaymentRequest.charging_session_bill_id == str(csb.id),
                ).outerjoin(models.PaymentRefund).order_by(desc(models.PaymentRequest.created_at))
                pr = query.first()
                if not pr:
                    return 'Payment Request not created, cannot create e-Invoice'
                if pr.type == PaymentRequestType.wallet:
                    return 'Payment request type is wallet, cannot create e-Invoice'

                if pr.currency != schema.Currency.myr:
                    return 'Payment request type is not MYR, cannot create e-Invoice'

                # cs_data = await get_charging_session(dbsession, charging_session_id, headers)
                # return cs_data

                # e_invoice_date_time = datetime.now()
                e_invoice_date_time = csb.created_at
                submission_model = await e_invoice_submission_adapter(dbsession, charging_session, csb, pr,
                                                                      e_invoice_date_time)
                submission_data = submission_model.dict(exclude_none=True)
                status_timeline = make_status_timeline_entry(schema.EInvoiceStatus.pending,
                                                             schema.EInvoiceAction.pre_submission)

                if submission_data:
                    submission_documents = submission_data["documents"][0]
                    submission_document = submission_documents["document"]
                    submission_total = submission_document["total"]
                    invoice_line_item = submission_document["invoiceLineItems"][0]
                    e_invoice_data = schema.EInvoiceCreate(
                        e_invoice_date_time=e_invoice_date_time,
                        e_invoice_number=csb.invoice_number,
                        charging_session_id=charging_session['id'],
                        charging_session_bill_id=csb.id,
                        status=schema.EInvoiceStatus.pending,
                        submission_payload=submission_data,
                        last_submission_date=datetime.now(),
                        status_timeline=[status_timeline],
                        currency_code=submission_document["currencyCode"],
                        payment_terms=submission_document["paymentTerms"],
                        bill_reference_number=submission_document["billReferenceNumber"],
                        is_self_issued=submission_documents["isSelfIssued"],
                        is_consolidated_invoice=submission_documents["isConsolidatedInvoice"],
                        total_net_amount=submission_total["netAmount"],
                        total_tax_amount=submission_total["taxAmount"],
                        total_charge_amount=submission_total["chargeAmount"],
                        total_discount_value=submission_total["discountValue"],
                        total_payable_amount=submission_total["payableAmount"],
                        total_tax_excluded_amount=submission_total["taxExcludedAmount"],
                        total_tax_included_amount=submission_total["taxIncludedAmount"],
                        invoice_tax=invoice_line_item["tax"],
                        invoice_discount=invoice_line_item.get('discount'),
                        invoice_total_excluding_tax=invoice_line_item["totalExcludingTax"],
                        quantity=invoice_line_item["quantity"],
                        measurement=invoice_line_item["measurement"],
                        unit_price=invoice_line_item["unitPrice"],
                        sub_total=invoice_line_item["subtotal"],
                    )
                    db_e_invoice = create_e_invoice(dbsession, e_invoice_data)

                    if settings.E_INVOICE_USE_RUNNING_NUMBER:
                        db_e_invoice_number = db_e_invoice.e_invoice_running_number
                        db_e_invoice_number = 'INV-' + str(db_e_invoice_number)
                        try:
                            submission_doc = submission_model.documents[0].document

                            # Update eInvoiceNumber and billReferenceNumber
                            submission_doc.eInvoiceNumber = db_e_invoice_number
                            submission_doc.billReferenceNumber = db_e_invoice_number

                        except (IndexError, AttributeError) as e:
                            raise RuntimeError("Failed to update eInvoiceNumber in submission_model") from e

                        # After update, generate dict again
                        submission_data = submission_model.dict(exclude_none=True)
                        e_invoice_update = schema.EInvoiceUpdate(e_invoice_number=db_e_invoice_number,
                                                                 submission_payload=submission_data)

                        db_e_invoice = update_e_invoice(dbsession, str(db_e_invoice.id), e_invoice_update)
            else:
                submission_data = db_e_invoice.submission_payload
                status_timeline = db_e_invoice.status_timeline or []
                new_timeline = list(status_timeline)
                new_timeline.append(make_status_timeline_entry(schema.EInvoiceStatus.pending,
                                                               schema.EInvoiceAction.pre_submission)
                                    )
                e_invoice_update = schema.EInvoiceUpdate(status_timeline=new_timeline)

                db_e_invoice = update_e_invoice(dbsession, db_e_invoice.id, e_invoice_update)

            token = await login_to_rmp()
            results = await submit_to_rmp(dbsession, submission_data, token, db_e_invoice)
            return results
        except Exception as e:  # pylint: disable=broad-except
            logger.error("E-Invoice submission error with error as %s ", str(e))
    return 'E-Invoice is not enabled.'


async def submit_to_rmp(dbsession, submission_data, token, e_invoice):
    base_url = settings.E_INVOICE_API_BASE_URL
    token_headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    document_submission_url = f"{base_url}/api/documentsubmissions"
    status_timeline = e_invoice.status_timeline or []
    new_timeline = list(status_timeline)

    async with httpx.AsyncClient() as client:
        response = await client.post(document_submission_url, json=submission_data, headers=token_headers)
        response_json = response.json()
        logger.info('Doc Submission Response: %s', response_json)
        e_invoice_update = schema.EInvoiceUpdate(submission_response=response_json)

        if response.status_code >= 400:
            e_invoice_update.status = schema.EInvoiceStatus.rejected
            new_timeline.append(make_status_timeline_entry(schema.EInvoiceStatus.rejected,
                                                           schema.EInvoiceAction.post_submission)
                                )
        else:
            new_timeline.append(make_status_timeline_entry(schema.EInvoiceStatus.pending,
                                                           schema.EInvoiceAction.post_submission)
                                )
        e_invoice_update.status_timeline = new_timeline
        db_e_invoice = update_e_invoice(dbsession, e_invoice.id, e_invoice_update)
        if response.status_code < 400:
            csb_update_schema = ChargingSessionBillUpdateEInvoiceStatus(e_invoice_issued=True)
            _ = crud.update_charging_session_bill(dbsession, csb_update_schema,
                                                  str(db_e_invoice.charging_session_bill_id))
        return response_json


def get_e_invoice_callback_template(success: bool):
    if success:
        template_path = os.path.join(BASE_DIR, 'app/defaults/success_request_e_invoice.html')
        with open(template_path, 'r') as file:
            return file.read()

    template_path = os.path.join(BASE_DIR, 'app/defaults/failed_request_e_invoice.html')
    with open(template_path, 'r') as file:
        return file.read()


# pylint:disable=too-many-statements, too-many-locals
async def e_credit_note_submission_adapter(dbsession, cs, csb, pr, e_cn_date_time,  # noqa mccabe: MC0001
                                           refund, db_e_invoice):
    # Convert to GMT+8 timezone before formatting
    e_cn_date_time_gmt8 = e_cn_date_time.astimezone(ZoneInfo("Asia/Kuala_Lumpur"))

    e_invoice_date = e_cn_date_time_gmt8.strftime("%Y-%m-%d")
    e_invoice_time = e_cn_date_time_gmt8.strftime("%H:%M:%SZ")

    payment_type = SubmissionPaymentType.others

    # if not pr:
    #     try:
    #         db_member = MembershipCRUD.query(dbsession).filter(
    #             func.lower(models.Membership.user_id_tag) == str(cs['id_tag']).lower()
    #         ).one()
    #         member = db_member
    #     except (NoResultFound, IntegrityError):
    #         try:
    #             db_member = IDTagCRUD.query(dbsession).filter(
    #                 func.lower(models.IDTag.id_tag) == str(cs['id_tag']).lower()
    #             ).one()
    #             db_member = db_member.member
    #             member = db_member
    #         except (NoResultFound, IntegrityError) as e:
    #             logger.error(e)
    #             member = None
    if pr:
        payment_type = SubmissionPaymentType.credit_card
        # if pr.type == schema.PaymentRequestType.payment_terminal:
        #     member = None
        # else:
        #     member = schema.ShallowMembershipResponse(**pr.meta)

    # buyer_email = None
    # buyer_phone = None
    # if member:
    #     user = member.user if member else None
    #     buyer_email = user.email
    #     buyer_phone = user.phone_number

    refund_amount = float(refund.refund_amount)

    billing_info = cs['meta']['billing_info']
    billing_type = billing_info['billing_type'].lower()
    _ = billing_info['billing_unit_fee']

    # Determine measurement
    measurement = Measurement.kwh if billing_type == 'kwh' else Measurement.min

    # Calculate original quantity (from session data)
    if billing_type == 'kwh':
        original_quantity = round((cs['meter_stop'] - cs['meter_start']) / 1000, 2)
    else:
        session_start = parser.isoparse(cs['session_start'])
        session_end = parser.isoparse(cs['session_end'])
        original_quantity = round((session_end - session_start).total_seconds() / 60, 2)

    # Calculate net unit fee (after discount)
    effective_unit_fee = float(csb.total_amount) / float(original_quantity)
    # Calculate how many kWh/min are being refunded
    refund_quantity = round(refund_amount / effective_unit_fee, 2)

    # Format values
    unit_price = f"{effective_unit_fee:.2f}"
    quantity = f"{refund_quantity:.2f}"
    amount = f"{refund_amount:.2f}"

    supplier = SubmissionSupplier(
        name="Green EV Charge Sdn Bhd",
        identification=[
            Identification(
                value="C26659337020",
                schemeID=IdentificationType.tin
            ),
            Identification(
                value="202101011556",
                schemeID=IdentificationType.brn,
            )],
        address=Address(
            address=[
                "Level 16, Menara South Point, Mid Valley City",
                "Medan Syed Putra Selatan, 59200 Kuala Lumpur"
            ],
            postalZone="59200",
            cityName="Kuala Lumpur",
        ),
        email="<EMAIL>",
        contactNumber="+60121234567",
    )

    # buyer = SubmissionBuyer(
    #     email=buyer_email,
    #     contactNumber=buyer_phone
    # )

    invoice_line_item = InvoiceLineItem(
        unitPrice=unit_price,
        subtotal=amount,
        totalExcludingTax=amount,
        quantity=quantity,
        measurement=measurement,
        tax=Tax(),
        taxExemption=TaxExemption(amount=amount),
    )

    submission_total = SubmissionTotal(
        taxIncludedAmount=amount,
        taxExcludedAmount=amount,
        payableAmount=amount,
        netAmount=amount,
        chargeAmount=amount,
        taxAmountPerTaxType=[TaxAmountPerType()],
    )

    submission_doc = CreditNoteSubmissionDocument(
        originalEInvoiceNumber=db_e_invoice.e_invoice_number,
        eInvoiceNumber=refund.reference_id,
        eInvoiceDate=e_invoice_date,
        eInvoiceTime=e_invoice_time,
        billReferenceNumber=csb.invoice_number,
        invoiceLineItems=[invoice_line_item],
        paymentMode=payment_type,
        total=submission_total,
    )

    if settings.E_INVOICE_ADD_SUPPLIER_INFO:
        submission_doc.supplier = supplier

    submission_documents = CreditNoteDocuments(
        type=SubmissionType.credit_note,
        isSelfIssued=False,
        isConsolidatedInvoice=True,
        document=submission_doc
    )

    return_url = f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/csms/e_invoice/cn/callback"

    e_cn_submission = ECreditNoteSubmission(
        documents=[submission_documents],
        returnUrl=return_url
    )

    return e_cn_submission


async def submit_e_cn_to_rmp(dbsession, submission_data, token, e_cn):
    base_url = settings.E_INVOICE_API_BASE_URL
    token_headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    document_submission_url = f"{base_url}/api/documentsubmissions"
    status_timeline = e_cn.status_timeline or []
    new_timeline = list(status_timeline)

    async with httpx.AsyncClient() as client:
        response = await client.post(document_submission_url, json=submission_data, headers=token_headers)
        response_json = response.json()
        logger.info('Doc Submission Response: %s', response_json)
        e_cn_update = schema.ECreditNoteUpdate(submission_response=response_json)

        if response.status_code >= 400:
            e_cn_update.status = schema.ECreditNoteStatus.rejected
            new_timeline.append(make_status_timeline_entry(schema.ECreditNoteStatus.rejected,
                                                           schema.ECreditNoteAction.post_submission)
                                )
        else:
            new_timeline.append(make_status_timeline_entry(schema.ECreditNoteStatus.pending,
                                                           schema.ECreditNoteAction.post_submission)
                                )
        e_cn_update.status_timeline = new_timeline
        db_e_cn = update_e_credit_note(dbsession, e_cn.id, e_cn_update)
        if response.status_code < 400:
            refund_update_schema = schema.UpdatePaymentRefund(e_credit_note_issued=True)
            _ = crud.update_payment_refund(dbsession, str(db_e_cn.payment_refund_id), refund_update_schema)

        return response_json


async def construct_and_submit_credit_note_to_rmp(dbsession,  # noqa: MC0001
                                                  charging_session, csb, member_id, refund_id):
    if (settings.ENABLE_E_INVOICE  # pylint: disable=too-many-nested-blocks
            or (settings.E_INVOICE_MEMBER_ID_LIST and str(member_id) in map(str, settings.E_INVOICE_MEMBER_ID_LIST))):
        try:
            db_e_invoice = get_e_invoice_by_csb_id(dbsession, csb.id)
            db_refund = get_payment_refund_by_id(dbsession, refund_id)
            db_e_cn = get_e_credit_note_by_payment_refund_id(dbsession, refund_id)
            if not db_e_invoice:
                return 'E-Invoice not generated, cannot generate E-Credit Note'

            query = PaymentRequestCRUD.query(dbsession, check_permission=False).filter(
                models.PaymentRequest.charging_session_bill_id == str(csb.id),
            ).outerjoin(models.PaymentRefund).order_by(desc(models.PaymentRequest.created_at))
            pr = query.first()

            # e_cn_date_time = datetime.now()
            e_cn_date_time = db_refund.created_at
            if not db_e_cn:
                submission_model = await e_credit_note_submission_adapter(dbsession, charging_session,
                                                                          csb, pr,
                                                                          e_cn_date_time, db_refund, db_e_invoice)

                status_timeline = make_status_timeline_entry(schema.ECreditNoteStatus.pending,
                                                             schema.ECreditNoteAction.pre_submission)

                submission_data = submission_model.dict(exclude_none=True)

                if submission_data:
                    submission_documents = submission_data["documents"][0]
                    submission_document = submission_documents["document"]
                    submission_total = submission_document["total"]
                    invoice_line_item = submission_document["invoiceLineItems"][0]
                    e_cn_data = schema.ECreditNoteCreate(
                        e_credit_note_date_time=e_cn_date_time,
                        e_credit_note_number=db_refund.reference_id,
                        e_invoice_id=db_e_invoice.id,
                        payment_refund_id=db_refund.id,
                        status=schema.ECreditNoteStatus.pending,
                        submission_payload=submission_data,
                        last_submission_date=datetime.now(),
                        status_timeline=[status_timeline],
                        currency_code=submission_document["currencyCode"],
                        payment_terms=submission_document["paymentTerms"],
                        bill_reference_number=submission_document["billReferenceNumber"],
                        is_self_issued=submission_documents["isSelfIssued"],
                        is_consolidated_invoice=submission_documents["isConsolidatedInvoice"],
                        total_net_amount=submission_total["netAmount"],
                        total_tax_amount=submission_total["taxAmount"],
                        total_charge_amount=submission_total["chargeAmount"],
                        total_discount_value=submission_total["discountValue"],
                        total_payable_amount=submission_total["payableAmount"],
                        total_tax_excluded_amount=submission_total["taxExcludedAmount"],
                        total_tax_included_amount=submission_total["taxIncludedAmount"],
                        invoice_tax=invoice_line_item["tax"],
                        invoice_discount=invoice_line_item.get('discount'),
                        invoice_total_excluding_tax=invoice_line_item["totalExcludingTax"],
                        quantity=invoice_line_item["quantity"],
                        measurement=invoice_line_item["measurement"],
                        unit_price=invoice_line_item["unitPrice"],
                        sub_total=invoice_line_item["subtotal"],
                    )
                    db_e_cn = create_e_cn(dbsession, e_cn_data)

                    if settings.E_INVOICE_USE_RUNNING_NUMBER:
                        # db_refund_parts = db_refund.reference_id.split("-")
                        # db_refund_id = db_refund_parts[-1]
                        total_e_cn_created = int(crud.ECreditNoteCRUD.query(dbsession).filter(
                            models.ECreditNote.e_invoice_id == db_e_invoice.id).count())
                        db_e_cn_number = 'CN-' + str(db_e_invoice.e_invoice_number) + '-' + str(total_e_cn_created)

                        try:
                            submission_doc = submission_model.documents[0].document

                            # Update eInvoiceNumber and billReferenceNumber
                            submission_doc.eInvoiceNumber = db_e_cn_number
                            submission_doc.billReferenceNumber = db_e_cn_number

                        except (IndexError, AttributeError) as e:
                            raise RuntimeError("Failed to update eInvoiceNumber in submission_model") from e

                        # After update, generate dict again
                        submission_data = submission_model.dict(exclude_none=True)
                        e_cn_update = schema.ECreditNoteUpdate(e_credit_note_number=db_e_cn_number,
                                                               submission_payload=submission_data)

                        db_e_cn = update_e_credit_note(dbsession, str(db_e_cn.id), e_cn_update)

            else:
                age_seconds = (datetime.now(tz=db_e_cn.created_at.tzinfo) - db_e_cn.created_at).total_seconds()
                if age_seconds < 5:
                    logger.info("E-CN %s created %.2fs ago, delaying %.1fs before re-submit",
                                db_e_cn.id, age_seconds, 3)
                    await asyncio.sleep(3)

                submission_data = db_e_cn.submission_payload

            token = await login_to_rmp()
            # return True
            results = await submit_e_cn_to_rmp(dbsession, submission_data, token, db_e_cn)

            return results
        except Exception as e:  # pylint: disable=broad-except
            logger.error("E-Credit-Note submission error with error as %s ", str(e))
    return 'E-Invoice is not enabled.'


# pylint:disable=unsubscriptable-object
def make_status_timeline_entry(status: Union[EInvoiceStatus, ECreditNoteStatus],
                               action: Union[EInvoiceAction, ECreditNoteAction]):
    status_date = datetime.now().isoformat()
    return {
        "status": status.value,
        "action": action.value,
        "status_date": status_date
    }
