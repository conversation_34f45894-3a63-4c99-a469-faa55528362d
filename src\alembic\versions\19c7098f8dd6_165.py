"""165

Revision ID: 19c7098f8dd6
Revises: 22cee48f8ba6
Create Date: 2025-05-15 13:48:31.127042

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '19c7098f8dd6'
down_revision = '22cee48f8ba6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_outlier_session', sa.Column('remarks', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_outlier_session', 'remarks')
    # ### end Alembic commands ###
