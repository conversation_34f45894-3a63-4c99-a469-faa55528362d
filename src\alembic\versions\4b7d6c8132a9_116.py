"""116

Revision ID: 4b7d6c8132a9
Revises: df86f275c34c
Create Date: 2024-08-21 17:15:02.530018

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4b7d6c8132a9'
down_revision = 'df86f275c34c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_operator', sa.Column('ac_default_price', sa.Numeric(scale=4), nullable=True))
    op.add_column('main_operator', sa.Column('dc_default_price', sa.Numeric(scale=4), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_operator', 'dc_default_price')
    op.drop_column('main_operator', 'ac_default_price')
    # ### end Alembic commands ###
