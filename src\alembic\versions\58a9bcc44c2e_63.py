"""63

Revision ID: 58a9bcc44c2e
Revises: ff1dee3aa6f9
Create Date: 2023-11-08 22:05:30.223727

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '58a9bcc44c2e'
down_revision = 'ff1dee3aa6f9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_subscription_custom_plan', 'amount', sa.Numeric(scale=5))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_subscription_custom_plan', 'amount', sa.Numeric(scale=2))
    # ### end Alembic commands ###
