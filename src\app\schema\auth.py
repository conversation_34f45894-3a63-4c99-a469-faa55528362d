import time
import re
from datetime import datetime, timedelta, timezone
from uuid import UUID
from enum import Enum
from typing import Optional, Dict, Any, List
import bcrypt

import phonenumbers
from jose import jwt
from pydantic import BaseModel, EmailStr, validator, constr
from fastapi import HTT<PERSON>Exception
from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

from app import settings

JWT_ALGORITHM = 'HS512'

argon_ph = PasswordHasher()

twilio_client = Client(settings.TWILIO['ACCOUNT_SID'], settings.TWILIO['AUTH_TOKEN'])


class VerificationMethodEnum(str, Enum):
    email = 'email'
    phone = 'phone'


def twilio_validate_phone_number(phone_number: str):
    try:
        twilio_client.lookups.phone_numbers(phone_number).fetch()
        return phone_number
    except TwilioRestException as e:
        raise ValueError(e)


def validator_non_empty_string(val: str):
    if val is not None and len(val) < 1:
        raise ValueError('Value must not be empty.')
    return val


class BasicMessage(BaseModel):
    detail: str


class TokenTypeEnum(str, Enum):
    bearer = 'Bearer'
    mfa_bearer = 'MFA Bearer'


class MembershipType(int, Enum):
    staff = 1
    sub_staff = 2
    regular_user = 3
    custom = 4


class UserType(str, Enum):
    staff = 'Staff'
    regular = 'Regular'


class TokenResponse(BaseModel):
    auth_token: Optional[str]  # pylint: disable=unsubscriptable-object
    token_type: Optional[TokenTypeEnum]  # pylint: disable=unsubscriptable-object


class TokenResponseWithMemberID(TokenResponse):
    member_id: UUID
    is_migrated_user: bool = False


class GuestSignupEnroll(BaseModel):
    """
    Guest enroll
    """
    vehicle_model: Optional[str]  # pylint: disable=unsubscriptable-object
    guest_app_id: str
    is_guest = True
    is_verified = True
    verification_method = VerificationMethodEnum.email
    organization_id: str
    first_name: Optional[str]  # pylint: disable=unsubscriptable-object
    last_name: Optional[str]  # pylint: disable=unsubscriptable-object
    email: Optional[EmailStr]  # pylint: disable=unsubscriptable-object

    @validator('first_name', 'last_name', pre=True, always=True)
    def set_default_names(cls, v, field):
        if field.name == 'first_name':
            return v or 'Guest'
        if field.name == 'last_name':
            return v or 'Customer'
        return v


class SignupRequest(BaseModel):
    """
    Regular user signup validator
    """
    email: EmailStr
    first_name: constr(min_length=1)
    last_name: constr(min_length=1)
    user_id_tag: Optional[str]  # pylint: disable=unsubscriptable-object
    phone_number: constr(min_length=8)

    organization_id: constr(min_length=1)

    password: constr(min_length=8, max_length=128)
    password_confirmation: constr(min_length=8)

    invite_token: Optional[str] = ''  # pylint: disable=unsubscriptable-object
    vehicle_model: Optional[str]  # pylint: disable=unsubscriptable-object
    vehicle_brand: Optional[str]  # pylint: disable=unsubscriptable-object

    allow_marketing: Optional[bool] = False  # pylint: disable=unsubscriptable-object

    @validator('email')
    def lower_case_email(cls, email: str):
        return email.lower()

    @validator('invite_token')
    def prevent_invite_token_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)

    @validator('password')
    def validate_password(cls, password: str):
        error_message = ''
        if not len(password) >= 8:
            error_message += "Password must be at least 8 characters.\n"
        if not re.search(r'.*\d.*', password):
            error_message += "Password must contain digit.\n"
        if not re.search(r'.*[A-Z].*', password):
            error_message += "Password must contain capital letter.\n"
        if not re.search(r'.*[a-z].*', password):
            error_message += "Password must contain lower letter.\n"
        # if not re.search(r'.*\W.*', password):
        #     error_message += "Password must contain special character.\n"
        if error_message:
            raise ValueError(error_message)
        return password

    @validator('phone_number')
    def is_phone_number_valid(cls, phone_number):
        try:
            check_phone_num = phonenumbers.parse(phone_number)
            if not phonenumbers.is_possible_number(check_phone_num):
                raise ValueError("Provided phone number is not valid.")
            formatted_number = phonenumbers.format_number(check_phone_num, phonenumbers.PhoneNumberFormat.E164)
            phone_number = formatted_number
        except phonenumbers.NumberParseException:
            raise ValueError("Provided phone number is not valid.")
        return phone_number

    @validator('password_confirmation')
    def validate_password_confirmation(cls, password_confirmation: str, values: dict):
        if values.get('password', '') != password_confirmation:
            raise ValueError('Passwords must match.')
        return password_confirmation


class SigninPhonePasswordRequest(BaseModel):
    phone_number: str
    password: constr(min_length=8)
    organization_id: UUID

    @validator('phone_number')
    def is_phone_number_valid(cls, phone_number):
        try:
            check_phone_num = phonenumbers.parse(phone_number)
            formatted_number = phonenumbers.format_number(check_phone_num, phonenumbers.PhoneNumberFormat.E164)
            phone_number = formatted_number
        except phonenumbers.NumberParseException:
            return phone_number
        return phone_number


class GuestLogon(BaseModel):
    guest_app_id: str
    organization_id: UUID


class SigninSuperuserEmailPasswordRequest(BaseModel):
    email: str
    password: constr(min_length=8)


class SigninEmailPasswordRequest(BaseModel):
    email: str
    password: constr(min_length=8)
    organization_id: UUID


class MFAInitiateRequest(BaseModel):
    user_id: UUID


class MFAInitialVerifyRequest(BaseModel):
    user_id: UUID
    code: str


class MFAValidateRequest(BaseModel):
    mfa_token: str
    code: str


class SigninOtpRequest(BaseModel):
    phone_number: str


class VerificationMethodUpdate(BaseModel):
    user_id: UUID
    verification_method: VerificationMethodEnum
    is_verified: bool
    migration_status: Optional[bool]  # pylint: disable=unsubscriptable-object
    link_date: Optional[datetime]  # pylint: disable=unsubscriptable-object
    created_at: Optional[datetime]  # pylint: disable=unsubscriptable-object


class VerificationMethod(BaseModel):
    phone_number: Optional[str]  # pylint: disable=unsubscriptable-object
    verification_method: VerificationMethodEnum
    email: str

    @validator('phone_number')
    def validate_phone_number(cls, phone_number: str):
        return twilio_validate_phone_number(phone_number)

    @validator('verification_method')
    def validate_verification_method(cls, verification_method: str, values: dict):
        if verification_method == 'phone' and not values['phone_number']:
            raise ValueError('Phone number is required for a phone verification method.')
        return verification_method


class VerificationToken(BaseModel):
    token: str
    user_id: UUID
    expiration: datetime
    is_used: bool = False


class SubmittedVerificationToken(BaseModel):
    token: str
    email: str


class ResendPhoneToken(BaseModel):
    phone_number: str
    organization_id: UUID

    @validator('phone_number')
    def is_phone_number_valid(cls, phone_number):
        try:
            check_phone_num = phonenumbers.parse(phone_number)
            if not phonenumbers.is_possible_number(check_phone_num):
                raise ValueError("Provided phone number is not valid.")
            formatted_number = phonenumbers.format_number(check_phone_num, phonenumbers.PhoneNumberFormat.E164)
            phone_number = formatted_number
        except phonenumbers.NumberParseException:
            raise ValueError("Provided phone number is not valid.")
        return phone_number


class SubmittedPhoneVerificationToken(BaseModel):
    token: str
    phone_number: str
    organization_id: UUID

    @validator('phone_number')
    def is_phone_number_valid(cls, phone_number):
        try:
            check_phone_num = phonenumbers.parse(phone_number)
            formatted_number = phonenumbers.format_number(check_phone_num, phonenumbers.PhoneNumberFormat.E164)
            phone_number = formatted_number
        except phonenumbers.NumberParseException:
            return phone_number
        return phone_number


class SubmittedEmailVerificationToken(BaseModel):
    token: str
    email: str
    organization_id: UUID


# pylint:disable=unsubscriptable-object
class ExternalToken(BaseModel):
    token: UUID
    is_temporary: bool = True
    version: Optional[str]
    endpoints: str = ''
    url: Optional[str]
    external_service_token: Optional[str]

    class Config:
        orm_mode = True


class ExternalTokenUpdate(BaseModel):
    version: Optional[str]
    endpoints: dict = {}
    external_service_token: Optional[str]
    url: Optional[str]


class ResetPasswordRequest(BaseModel):
    email: str
    organization_id: UUID


class ResetPasswordByPhoneNumberRequest(BaseModel):
    phone_number: str
    organization_id: UUID


class ResetPasswordToken(BaseModel):
    token: str
    user_id: UUID
    expiration: datetime
    is_used: bool = False


class ResetPasswordConfirm(BaseModel):
    email: str
    token: str
    new_password: str
    new_password_confirm: str
    organization_id: UUID


class ResetPasswordByPhoneNumberConfirm(BaseModel):
    phone_number: str
    token: str
    new_password: str
    new_password_confirm: str
    organization_id: UUID


class AuthRequestResponse(BaseModel):
    id: UUID
    is_verified: bool

    class Config:
        orm_mode = True


# pylint:disable=unsubscriptable-object
class User(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    email: Optional[str]
    phone_number: Optional[str]
    is_superuser: bool
    is_guest: Optional[bool] = False
    guest_app_id: Optional[str]
    is_migrated: Optional[bool] = False
    is_verified: Optional[bool] = False
    migration_status: Optional[bool] = False
    link_date: Optional[datetime]
    migrated_date: Optional[datetime]
    verification_method: Optional[str]

    class Config:
        orm_mode = True


# pylint:disable=unsubscriptable-object
class NonMigratedUser(BaseModel):
    id: UUID
    email: Optional[str]

    class Config:
        orm_mode = True


class RoleResponse(BaseModel):
    id: UUID

    class Config:
        orm_mode = True


class TokenResponseWithUser(TokenResponse):
    user: User
    first_name: str
    last_name: str
    membership_type: MembershipType
    mfa_setup_required: bool = False
    mfa_enabled: bool = False
    roles: Optional[List[RoleResponse]]


class InviteResponse(BaseModel):
    id: UUID
    user_id: UUID
    accepted_invite_count: int
    token: str

    class Config:
        orm_mode = True


class Invite(BaseModel):
    token: str


class InviteUpdate(BaseModel):
    token: Optional[str] = ''

    @validator('token')
    def prevent_token_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)


# A pylint issue on the Optional class https://github.com/PyCQA/pylint/issues/3882
# pylint:disable=unsubscriptable-object
class MembershipUpdate(BaseModel):
    first_name: Optional[constr(min_length=1)]
    last_name: Optional[constr(min_length=1)]
    user_id_tag: Optional[str]
    email: Optional[EmailStr]
    phone_number: Optional[constr(min_length=8)]

    current_password: Optional[constr(min_length=8, max_length=128)]
    password: Optional[constr(min_length=8, max_length=128)]
    password_confirmation: Optional[constr(min_length=8, max_length=128)]

    membership_type: Optional[MembershipType]

    @validator('first_name')
    def prevent_first_name_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)

    @validator('last_name')
    def prevent_last_name_empty_string(cls, val: str, values: dict):
        return validator_non_empty_string(val)

    @validator('email')
    def lower_case_email(cls, email: str):
        return email.lower()

    @validator('password')
    def validate_password(cls, password: str):
        error_message = ''
        if not len(password) >= 8:
            error_message += "Password must be at least 8 characters.\n"
        if not re.search(r'.*\d.*', password):
            error_message += "Password must contain digit.\n"
        if not re.search(r'.*[A-Z].*', password):
            error_message += "Password must contain capital letter.\n"
        if not re.search(r'.*[a-z].*', password):
            error_message += "Password must contain lower letter.\n"
        # if not re.search(r'.*\W.*', password):
        #     error_message += "Password must contain special character.\n"
        if error_message:
            raise ValueError(error_message)
        return password

    @validator('phone_number')
    def is_phone_number_valid(cls, phone_number):
        try:
            check_phone_num = phonenumbers.parse(phone_number)
            if not phonenumbers.is_possible_number(check_phone_num):
                raise ValueError("Provided phone number is not valid.")
        except phonenumbers.NumberParseException:
            raise ValueError("Provided phone number is not valid.")
        return phone_number

    @validator('password_confirmation')
    def validate_password_confirmation(cls, password_confirmation: str, values: dict):
        if values.get('password', '') != password_confirmation:
            raise ValueError('Passwords must match.')
        return password_confirmation


class CustomMembershipUpdate(MembershipUpdate):
    role_id: Optional[UUID]


class UserAuth(BaseModel):
    id: UUID
    password: Optional[str]
    login_attempts: Optional[List[str]]

    class Config:
        orm_mode = True

    def verify_password(
            self,
            plain_password: str,
            err_message: str = "Email ID and password does not matches.",
    ):
        if not self.password:
            raise HTTPException(400, "Password not found.")
        try:
            result = bcrypt.checkpw(plain_password.encode('utf-8'), self.password.encode('utf-8'))
            if not result:
                raise HTTPException(400, err_message)
        except VerifyMismatchError:
            raise HTTPException(400, err_message)

    def generate_token(self, membership_id: UUID = None):
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(self.id),
                "membership_id": membership_id,
            },
            settings.JWT_SECRET,
            algorithm=JWT_ALGORITHM,
        )
        return TokenResponse(auth_token=token, token_type=TokenTypeEnum.bearer)

    def renew_token(self, token_dict: dict, token_str: str):
        # If token is expired, create a new token
        if time.time() > token_dict['exp']:
            return self.generate_token()
        raise HTTPException(400, 'Token is still valid.')

    def generate_token_with_membership(self, membership_id: UUID = None, is_migrated: bool = False):
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(self.id),
                "membership_id": membership_id,
            },
            settings.JWT_SECRET,
            algorithm=JWT_ALGORITHM,
        )
        return TokenResponseWithMemberID(auth_token=token, token_type=TokenTypeEnum.bearer, member_id=membership_id,
                                         is_migrated_user=is_migrated)

    def generate_token_dict(self, membership_id: UUID = None):
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(self.id),
                "membership_id": membership_id,
            },
            settings.JWT_SECRET,
            algorithm=JWT_ALGORITHM,
        )
        return dict(auth_token=token, token_type=TokenTypeEnum.bearer)

    def generate_mfa_token(self, membership_id: UUID = None):
        token_type = TokenTypeEnum.mfa_bearer
        token = jwt.encode(
            {
                "exp": datetime.now().astimezone() + timedelta(minutes=settings.MFA_EXPIRY_MINUTES),
                "user_id": str(self.id),
                "membership_id": str(membership_id),
                "type": token_type.value,
            },
            settings.MFA_JWT_SECRET,
            algorithm=JWT_ALGORITHM,
        )
        return dict(auth_token=token, token_type=token_type)


class IDTagType(str, Enum):
    rfid = 'RFID'
    vid = 'VID'
    pnc = 'PnC'
    other = 'Other'


class IDTag(BaseModel):
    id_tag: str
    parent_id_tag_id: Optional[UUID]
    name: str
    type: IDTagType
    expiration: datetime
    is_active: bool
    description: Optional[str]
    member_id: Optional[UUID]
    organization_id: Optional[UUID]
    is_external: Optional[bool] = False
    meta: Optional[dict]
    emaid_id: Optional[UUID]
    autocharge_id: Optional[UUID]


class IDTagAuthorizationHistories(BaseModel):
    id_tag: str
    result: str
    result_reason: Optional[str]
    connector_id: Optional[str]
    request_type: Optional[str]


class OCPICPOToken(BaseModel):
    partner_ocpi_cpo_token_id: str
    country_code: str
    party_id: str
    type: str
    contract_id: str
    description: Optional[str]
    meta: Optional[dict]

    visual_number: Optional[str]
    issuer: str
    group_id: Optional[str]
    valid: bool = True
    whitelist: str
    default_profile_type: Optional[str]
    energy_contract: Optional[dict]
    language: Optional[str]

    class Config:
        orm_mode = True


class OCPICPOTokenUpdate(BaseModel):
    type: Optional[str]
    contract_id: Optional[str]
    description: Optional[str]
    meta: Optional[dict]

    visual_number: Optional[str]
    issuer: Optional[str]
    group_id: Optional[str]
    valid: Optional[bool]
    whitelist: Optional[str]
    default_profile_type: Optional[str]
    energy_contract: Optional[dict]
    language: Optional[str]

    class Config:
        orm_mode = True


class OCPICPOTokenResponse(BaseModel):
    id: UUID
    partner_ocpi_cpo_token_id: str
    country_code: str
    party_id: str
    type: str
    contract_id: str
    description: Optional[str]
    meta: Optional[dict]

    visual_number: Optional[str]
    issuer: str
    group_id: Optional[str]
    valid: bool = True
    whitelist: str
    default_profile_type: Optional[str]
    energy_contract: Optional[dict]
    language: Optional[str]
    updated_at: Optional[datetime]
    created_at: Optional[datetime]

    class Config:
        orm_mode = True


class OCPIToken(BaseModel):
    country_code: str = settings.OCPI_COUNTRY_CODE
    party_id: str = settings.OCPI_PARTY_ID
    type: str = 'APP_USER'
    contract_id: str
    description: Optional[str]
    meta: Optional[dict]

    member_id: Optional[UUID]

    visual_number: Optional[str]
    issuer: str = 'CHARGEV'
    group_id: Optional[str]
    valid: bool = True
    whitelist: str = 'ALWAYS'
    default_profile_type: Optional[str]
    energy_contract: Optional[dict]
    language: Optional[str]

    class Config:
        orm_mode = True


class OCPITokenResponse(BaseModel):
    id: UUID
    country_code: str
    party_id: str
    type: str
    contract_id: str
    description: Optional[str]
    meta: Optional[dict]

    member_id: Optional[UUID]

    visual_number: Optional[str]
    issuer: str
    group_id: Optional[str]
    valid: bool = True
    whitelist: str
    default_profile_type: Optional[str]
    energy_contract: Optional[dict]
    language: Optional[str]
    updated_at: Optional[datetime]
    created_at: Optional[datetime]

    partner_ocpi_cpo_token_id: Optional[str]

    class Config:
        orm_mode = True


class IDTagUpdate(BaseModel):
    id_tag: Optional[str]
    parent_id_tag_id: Optional[UUID]
    name: Optional[str]
    type: Optional[IDTagType]
    expiration: Optional[datetime]
    is_active: Optional[bool]
    description: Optional[str]
    member_id: Optional[UUID]
    organization_id: Optional[UUID]
    is_external: Optional[bool]
    meta: Optional[dict]
    emaid_id: Optional[UUID]
    autocharge_id: Optional[UUID]


class OrganizationIDTagView(BaseModel):
    id: UUID
    name: str
    parent_id: Optional[UUID]

    class Config:
        orm_mode = True


class MembershipIDTagView(BaseModel):
    id: UUID
    user: User
    membership_type: MembershipType
    organization: Optional[OrganizationIDTagView]

    class Config:
        orm_mode = True


class IDTagResponse(IDTag):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    member: Optional[MembershipIDTagView]

    class Config:
        orm_mode = True


class MessageReason(str, Enum):
    start_ocpp_operation = 'Start OCPP Operation'
    make_reservation = 'Make Reservation'
    cancel_reservation = 'Cancel Reservation'
    authorization = 'Authorization'
    response = 'Response'
    charging_bill = 'Charging Bill'
    request_call_back = 'Request Call Back'


class MessageBody(BaseModel):
    message_type: str
    parameters: Dict[str, Any]


class Message(BaseModel):
    reason: MessageReason
    sender: Optional[str]
    receiver: Optional[str]
    reply_to: Optional[str]
    body: MessageBody
    request_id: Optional[str]

    class Config:
        use_enum_values = True


class StatusCallbackPayload(BaseModel):
    charging_session_id: Optional[str]
    transaction_id: Optional[str]
    ocpp_status: str
    serial_number: str
    member_id: Optional[str]
    charge_point_id: str
    connector_id: str


class CommandCallbackPayload(BaseModel):
    command_result: str
    command_message: Optional[str] = None
    member_id: Optional[str]
    charge_point_id: str
    connector_id: str
    display_support: Optional[bool] = False


class Callback(BaseModel):
    payload: dict
    event: str


class CallbackWithNotification(BaseModel):
    payload: dict
    event: str
    show_in_app: bool = False
    event_banner: Optional[str]
    event_in_app_description: Optional[str]


class CallbackHTTPResponse(Callback):
    response: dict


class CallbackResponse(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    payload: dict
    response: dict
    event: str

    class Config:
        orm_mode = True


class MembershipExtended(BaseModel):
    membership_id: UUID


class MembershipExtendedVerificationMethodUpdate(BaseModel):
    id: UUID
    verification_method: VerificationMethodEnum
    email_verified: Optional[bool]
    phone_verified: Optional[bool]
    verification_token_id: Optional[None]

    class Config:
        orm_mode = True


class ManualLinkingTokenUpdate(BaseModel):
    id: UUID
    is_used: bool

    class Config:
        orm_mode = True


class OCPIHistories(BaseModel):
    type: str

    ocpi_module: Optional[str]
    request_url: str
    request_header: Optional[str] = None
    request_content: str
    request_method: Optional[str] = None

    request_body: Optional[dict] = {}

    response_content: Optional[str]
    response_code: Optional[str]

    response_body: Optional[dict] = {}


class EMSPLogs(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    type: str

    ocpi_module: Optional[str]
    request_url: str
    request_header: Optional[str] = None
    # request_content: str
    request_method: Optional[str] = None

    # request_body: Optional[dict] = {}

    # response_content: Optional[str]
    response_code: Optional[str]

    # response_body: Optional[dict] = {}

    class Config:
        orm_mode = True


class EMSPLogsDetails(BaseModel):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    type: str

    ocpi_module: Optional[str]
    request_url: str
    request_header: Optional[str] = None
    request_content: str
    request_method: Optional[str] = None

    request_body: Optional[dict] = {}

    response_content: Optional[str]
    response_code: Optional[str]

    response_body: Optional[dict] = {}

    class Config:
        orm_mode = True


class RequestEmailVerification(BaseModel):
    email: EmailStr
    organization_id: UUID


class RequestManualLinkEmail(BaseModel):
    target_member_id: UUID


class DunningAdminBlockBody(BaseModel):
    admin_block_date: datetime
    admin_block_remark: str

    @validator("admin_block_date")
    def validate_admin_block_date(cls, value):
        now = datetime.now(timezone.utc)
        if value <= now:
            raise ValueError("admin_block_date must be greater than the current date and time.")
        return value


class DunningAdminUnblockBody(BaseModel):
    admin_unblock_days: int
    admin_unblock_remark: str

    @validator("admin_unblock_days")
    def validate_admin_block_days(cls, value):
        if value <= 0:
            raise ValueError("admin_block_days must be greater than 0.")
        return value
