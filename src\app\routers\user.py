import logging
import re

from uuid import UUID
from datetime import datetime, timed<PERSON>ta
from secrets import token_urlsafe

from fastapi import APIRouter, HTTPException, Depends, Request
from sqlalchemy.exc import NoResultFound

from app import settings, schema, crud, exceptions
from app.mail import Send<PERSON>ridMail, RecipientsSchema
from app.database import create_session, SessionLocal
from app.permissions import permission

from app.settings import APOLLO_MAIN_DOMAIN, MAIN_ROOT_PATH

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH
csms_path = f"{APOLLO_MAIN_DOMAIN}/{MAIN_ROOT_PATH}/api/v1/csms"

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/csms/user",
    tags=['user', ],
    dependencies=[Depends(permission)]
)


@router.get("/member/{membership_id}/", response_model=schema.MembershipResponse)
async def get_membership(request: Request, membership_id: UUID,
                         dbsession: SessionLocal = Depends(create_session)):
    """
    Get Membership object

    :param str membership_id: Target Membership ID
    """
    try:
        return crud.get_membership_by_id(dbsession, membership_id)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, str(e))


@router.post("/member/{migrated_member_id}/send-manual-link-email", status_code=201)
async def send_manual_link_verification_email(data: schema.RequestManualLinkEmail, migrated_member_id: UUID,  # noqa: MC0001
                                              request: Request, dbsession: SessionLocal = Depends(create_session)):
    """
    Send verification email to migrated user's email for manual-linking confirmation

    :param str migrated_member_id: Migrated Membership ID
    """
    success_msg = 'Manual Linking Verification Email has been sent to '
    try:
        if migrated_member_id == data.target_member_id:
            raise HTTPException(status_code=404,
                                detail='Cannot manual link to the same user.')

        migrated_member = crud.get_membership_by_id(dbsession, migrated_member_id)
        migrated_user = migrated_member.user
        success_msg += f'{migrated_user.email}.'

        if migrated_member.membership_type is not schema.MembershipType.regular_user or migrated_user.is_superuser:
            raise HTTPException(status_code=404,
                                detail='Manual Linking only available for regular user.')
        if not migrated_user.is_migrated or migrated_user.migration_status:
            raise HTTPException(status_code=404,
                                detail='Selected Migrated User is not available for manual linking.')

        target_member = crud.get_membership_by_id(dbsession, data.target_member_id)
        target_user = target_member.user
        if migrated_member.membership_type is not schema.MembershipType.regular_user or target_user.is_superuser:
            raise HTTPException(status_code=404,
                                detail='Manual Linking only available for regular user.')
        if target_user.is_migrated:
            raise HTTPException(status_code=404,
                                detail='Target User cannot be a migrated user.')

        extended_membership_data = schema.ManualLinkingToken(
            migrated_member_id=str(migrated_member.id),
            target_member_id=str(target_member.id),
            token=re.sub(r'\W+', '', token_urlsafe(settings.MANUAL_LINK_TOKEN['TOKEN_BYTES_LENGTH'])),
            expiration=datetime.now() + timedelta(minutes=settings.MANUAL_LINK_TOKEN['TOKEN_DURATION_MINUTES']),
            is_used=False,
        )

        try:
            manual_linking_token = crud.get_user_manual_linking_token_by_membership_id(dbsession,
                                                                                       str(migrated_member.id),
                                                                                       str(target_member.id))
            if manual_linking_token.expiration > datetime.now().astimezone():
                raise HTTPException(status_code=422, detail='Verification email is already sent and not expired yet.')

            manual_linking_token = crud.create_manual_linking_token(dbsession, extended_membership_data)

        except exceptions.ApolloObjectDoesNotExist:
            try:
                manual_linking_token = crud.get_user_manual_linking_token_by_membership_id(dbsession,
                                                                                           str(migrated_member.id))
                if manual_linking_token.expiration > datetime.now().astimezone() and \
                        str(manual_linking_token.target_member_id) != str(data.target_member_id):
                    raise HTTPException(status_code=422,
                                        detail='User have a verification email that is not expired yet.')
            except exceptions.ApolloObjectDoesNotExist:
                manual_linking_token = crud.create_manual_linking_token(dbsession, extended_membership_data)

        verification_url = (f'{csms_path}/auth/manual-linking-verify-email?token={manual_linking_token.token}&'
                            f'migrated_member_id={str(migrated_member.id)}&target_member_id={str(target_member.id)}')

        verification_dict = {
            'migrated_email': migrated_user.email,
            'target_email': target_user.email,
            'url': verification_url
        }
        sendgrid_mail = SendGridMail('Account Linking Confrimation', RecipientsSchema(emails=[migrated_user.email, ]),
                                     settings.SENDER_EMAIL, settings.SENDER_NAME)
        sendgrid_mail.send_html_mail(verification_dict, "manual_linking_confirmation.html")
        return schema.BasicMessage(detail=success_msg)
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, str(e))


@router.get("/{user_id}/", response_model=schema.User)
async def get_user(request: Request, user_id: UUID,
                   dbsession: SessionLocal = Depends(create_session)):
    """
    Get User object

    :param str user_id: Target User ID
    """
    try:
        return crud.UserCRUD.get(dbsession, user_id)
    except NoResultFound:
        raise HTTPException(400, 'User object does not exist.')
