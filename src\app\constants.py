from app.settings import (
    MERCHANT_ID,
    RAZER_PAYMENT_DIRECT_API_URL,
    RAZER_PAYMENT_RECURRING_API_URL,
    RAZER_PAYMENT_IPN_URL,
    RAZER_REFUND_API_URL,
    RAZER_TOKEN_API_URL,
    RAZER_PAYMENT_RECONCILATION_API_URL,
    RAZER_PAYMENT_RECONCILATION_BY_OID_URL,
    SG_MERCHANT_ID,
    SG_RAZER_PAYMENT_DIRECT_API_URL,
    SG_RAZER_PAYMENT_RECURRING_API_URL,
    SG_RAZER_PAYMENT_IPN_URL,
    SG_RAZER_TOKEN_API_URL, RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL, RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL,
    SG_RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL, SG_RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL,
    SG_RAZER_PAYMENT_RECONCILATION_API_URL,
    SG_RAZER_PAYMENT_RECONCILATION_BY_OID_URL, RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL,
    SG_RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL,
    BN_MERCHANT_ID,
    BN_RAZER_PAYMENT_DIRECT_API_URL,
    BN_RAZER_PAYMENT_RECURRING_API_URL,
    BN_RAZER_PAYMENT_IPN_URL,
    BN_RAZER_TOKEN_API_URL,
    BN_RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL,
    BN_RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL,
    BN_RAZER_PAYMENT_RECONCILATION_API_URL,
    BN_RAZER_PAYMENT_RECONCILATION_BY_OID_URL,
    BN_RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL,
    KH_MERCHANT_ID,
    KH_RAZER_PAYMENT_DIRECT_API_URL,
    KH_RAZER_PAYMENT_RECURRING_API_URL,
    KH_RAZER_PAYMENT_IPN_URL,
    KH_RAZER_TOKEN_API_URL,
    KH_RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL,
    KH_RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL,
    KH_RAZER_PAYMENT_RECONCILATION_API_URL,
    KH_RAZER_PAYMENT_RECONCILATION_BY_OID_URL,
    KH_RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL, RAZER_PAYMENT_RECONCILATION_BY_TID_URL,
    SG_RAZER_PAYMENT_RECONCILATION_BY_TID_URL, BN_RAZER_PAYMENT_RECONCILATION_BY_TID_URL,
    KH_RAZER_PAYMENT_RECONCILATION_BY_TID_URL,
)


class DefaultRoles:
    staff = 'Staff'
    sub_staff = 'Sub Staff'
    regular_user = 'Regular User'
    sys_admin = 'System Administrator'
    custom = 'Custom'


AUTHORIZATION_METHOD_SUCCESS_MSG = 'Access is authorized.'
AUTHORIZATION_METHOD_ERRORS = {
    'unknown': 'Unknown error.',
    'resource_missing': 'Resource missing.',
    'resource_access_denied': 'Access to resource denied, User has no assigned role for this resource.',
    'auth_token_required': 'Auth token required.',
    'invalid_method': 'Invalid method to access resource.',
}

PAYMENT_DIRECT_API_URL = f'{RAZER_PAYMENT_DIRECT_API_URL}/{MERCHANT_ID}'
PAYMENT_PRE_AUTH_REQUEST_API_URL = f'{RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL}'
PAYMENT_PRE_AUTH_CAPTURE_API_URL = f'{RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL}'
PAYMENT_PRE_AUTH_REFUND_API_URL = f'{RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL}'
PAYMENT_RECURRING_API_URL = f'{RAZER_PAYMENT_RECURRING_API_URL}'
PAYMENT_RECONCILATION_API_URL = f'{RAZER_PAYMENT_RECONCILATION_API_URL}'
PAYMENT_RECONCILATION_BY_OID_URL = f'{RAZER_PAYMENT_RECONCILATION_BY_OID_URL}'
PAYMENT_RECONCILATION_BY_TID_URL = f'{RAZER_PAYMENT_RECONCILATION_BY_TID_URL}'
PAYMENT_IPN_URL = f'{RAZER_PAYMENT_IPN_URL}'
TOKEN_API_URL = f'{RAZER_TOKEN_API_URL}'
REFUND_API_URL = f'{RAZER_REFUND_API_URL}'

SG_PAYMENT_DIRECT_API_URL = f'{SG_RAZER_PAYMENT_DIRECT_API_URL}/{SG_MERCHANT_ID}'
SG_PAYMENT_RECURRING_API_URL = f'{SG_RAZER_PAYMENT_RECURRING_API_URL}'
SG_PAYMENT_PRE_AUTH_REQUEST_API_URL = f'{SG_RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL}'
SG_PAYMENT_PRE_AUTH_CAPTURE_API_URL = f'{SG_RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL}'
SG_PAYMENT_PRE_AUTH_REFUND_API_URL = f'{SG_RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL}'
SG_PAYMENT_RECONCILATION_API_URL = f'{SG_RAZER_PAYMENT_RECONCILATION_API_URL}'
SG_PAYMENT_RECONCILATION_BY_OID_URL = f'{SG_RAZER_PAYMENT_RECONCILATION_BY_OID_URL}'
SG_PAYMENT_RECONCILATION_BY_TID_URL = f'{SG_RAZER_PAYMENT_RECONCILATION_BY_TID_URL}'

SG_PAYMENT_IPN_URL = f'{SG_RAZER_PAYMENT_IPN_URL}'
SG_TOKEN_API_URL = f'{SG_RAZER_TOKEN_API_URL}'

BN_PAYMENT_DIRECT_API_URL = f'{BN_RAZER_PAYMENT_DIRECT_API_URL}/{BN_MERCHANT_ID}'
BN_PAYMENT_RECURRING_API_URL = f'{BN_RAZER_PAYMENT_RECURRING_API_URL}'
BN_PAYMENT_PRE_AUTH_REQUEST_API_URL = f'{BN_RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL}'
BN_PAYMENT_PRE_AUTH_CAPTURE_API_URL = f'{BN_RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL}'
BN_PAYMENT_PRE_AUTH_REFUND_API_URL = f'{BN_RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL}'
BN_PAYMENT_RECONCILATION_API_URL = f'{BN_RAZER_PAYMENT_RECONCILATION_API_URL}'
BN_PAYMENT_RECONCILATION_BY_OID_URL = f'{BN_RAZER_PAYMENT_RECONCILATION_BY_OID_URL}'
BN_PAYMENT_RECONCILATION_BY_TID_URL = f'{BN_RAZER_PAYMENT_RECONCILATION_BY_TID_URL}'
BN_PAYMENT_IPN_URL = f'{BN_RAZER_PAYMENT_IPN_URL}'
BN_TOKEN_API_URL = f'{BN_RAZER_TOKEN_API_URL}'

KH_PAYMENT_DIRECT_API_URL = f'{KH_RAZER_PAYMENT_DIRECT_API_URL}/{KH_MERCHANT_ID}'
KH_PAYMENT_RECURRING_API_URL = f'{KH_RAZER_PAYMENT_RECURRING_API_URL}'
KH_PAYMENT_PRE_AUTH_REQUEST_API_URL = f'{KH_RAZER_PAYMENT_PRE_AUTH_REQUEST_API_URL}'
KH_PAYMENT_PRE_AUTH_CAPTURE_API_URL = f'{KH_RAZER_PAYMENT_PRE_AUTH_CAPTURE_API_URL}'
KH_PAYMENT_PRE_AUTH_REFUND_API_URL = f'{KH_RAZER_PAYMENT_PRE_AUTH_REFUND_API_URL}'
KH_PAYMENT_RECONCILATION_API_URL = f'{KH_RAZER_PAYMENT_RECONCILATION_API_URL}'
KH_PAYMENT_RECONCILATION_BY_OID_URL = f'{KH_RAZER_PAYMENT_RECONCILATION_BY_OID_URL}'
KH_PAYMENT_RECONCILATION_BY_TID_URL = f'{KH_RAZER_PAYMENT_RECONCILATION_BY_TID_URL}'
KH_PAYMENT_IPN_URL = f'{KH_RAZER_PAYMENT_IPN_URL}'
KH_TOKEN_API_URL = f'{KH_RAZER_TOKEN_API_URL}'
