"""52

Revision ID: c6e0f541a597
Revises: 2c096a395dc8
Create Date: 2023-08-18 07:34:58.010412

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c6e0f541a597'
down_revision = '2c096a395dc8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_external_organization', sa.Column('organization_type', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_external_organization', 'organization_type')
    # ### end Alembic commands ###
