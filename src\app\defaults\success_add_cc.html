<!DOCTYPE html>
<html>
  <head>
    <link
      href="https://fonts.googleapis.com/css?family=Lato"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css?family=Calibri"
      rel="stylesheet"
    />
    <style>
      body {
        font-family: "Lato";
        background-color: "#F3F3F3";
      }
      div {
        width: 75vw;
        height: 50vh;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
      }
      img {
        width: 30vw;
        display: block;
        margin-left: auto;
        margin-right: auto;
      }
      p {
        font-weight: bold;
        align-items: center;
        align-content: center;
        text-align: center;
        font-size: 3vh;
      }
      div > button {
        background-color: #2fcc8b;
        border: 2px solid #2fcc8b;
        border-radius: 20px;
        padding-top: 3%;
        padding-bottom: 3%;
        width: 100%;
        color: #ffffff;
        font-weight: bold;
        font-size: 1.8vh;
      }
    </style>
  </head>
  <body>
    <div class="success">
      <img
        src="data:image/png;base64, 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"
        alt="Success Icon"
      />
      <p>Credit card has been added successfully</p>
      <button onclick="runFunction()">OK</button>
    </div>
  </body>
</html>
<script>
  window.onload = function () {
    window.parent.postMessage(
      { status: "success", html: document.documentElement.outerHTML },
      "*"
    );

    // Auto-unzoom functionality
    var viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute(
        "content",
        "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
      );
    } else {
      var meta = document.createElement("meta");
      meta.setAttribute("name", "viewport");
      meta.setAttribute(
        "content",
        "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
      );
      document.head.appendChild(meta);
    }
  };

  function runFunction() {
    try {
      window.webkit.messageHandlers.backToIos.postMessage({ status: "ok" });
    } catch (err) {
      console.log("swift catch");
    }

    try {
      backToAndroid.onBackClicked();
    } catch (err) {
      console.log("android catch");
    }
  }
</script>
