"""167

Revision ID: 2f145e021709
Revises: c0e56f9497ae
Create Date: 2025-05-21 15:24:58.921664

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2f145e021709'
down_revision = 'c0e56f9497ae'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_payment_refund', sa.Column('pg_transaction_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_payment_refund', 'pg_transaction_id')
    # ### end Alembic commands ###
