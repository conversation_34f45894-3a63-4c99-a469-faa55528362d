import datetime
from abc import ABC, abstractproperty
from typing import Union

from sqlalchemy.orm import Session, Query
from sqlalchemy.exc import NoResultFound
from sqlalchemy.sql import func

from app.models import Base, Organization, ActivityLog, Membership, User, filters_using_like, UserAuditLog
from app.schema import MembershipResponse, ActivityLogType, AuditLogType
from app.middlewares import get_request_user, get_request_audit
from app import exceptions


class BaseCRUD(ABC):
    membership = get_request_user
    audit_log = get_request_audit

    @abstractproperty
    def model(cls):
        """declare the ORM Model being used
        """

    @classmethod
    def can_create(cls, dbsession: Session, data: dict, *args, **kwargs):
        """check if user can create an object data
        return None or raise exception with proper message
        """

    @classmethod
    def can_write(cls, dbsession: Session, object_id, *args, **kwargs):
        """check if user can modify the object data
        return None or raise exception with proper message
        """

    @classmethod
    def can_delete(cls, dbsession: Session, object_id, *args, **kwargs):
        """check if user can delete the object data
        return None or raise exception with proper message
        """
        cls.can_write(dbsession, object_id, *args, **kwargs)

    @classmethod
    def query(cls, dbsession: Session, *columns):
        """make a query to the ORM Model
        """
        if columns:
            return dbsession.query(*columns).select_from(cls.model).filter(cls.model.is_deleted.is_(False))
        return dbsession.query(cls.model).filter(cls.model.is_deleted.is_(False))

    @classmethod
    def get(cls, dbsession: Session, object_id: str, *args, **kwargs):
        """make a query to the ORM Model using ID filter
        """
        return cls.query(dbsession, *args, **kwargs).filter(cls.model.id == object_id,
                                                            cls.model.is_deleted.is_(False)).one()

    @classmethod
    def add(cls, dbsession: Session, data: dict, *args, check_permission=True, audit_log=True, **kwargs):
        """create an object
        """
        if check_permission:
            cls.can_create(dbsession, data, *args, **kwargs)
        db_object = cls.model(**data)
        dbsession.add(db_object)
        dbsession.commit()
        dbsession.refresh(db_object)
        cls.log_activity(dbsession, data, ActivityLogType.create)
        if audit_log:
            try:
                log_audit_trail(cls, dbsession, AuditLogType.create, data_after=data,
                                object_id=str(db_object.id))
            except Exception:  # pylint: disable=broad-except
                _ = ''
        return db_object

    @classmethod
    def update(cls, dbsession: Session, object_id, data: dict, *args, check_permission=True, **kwargs):
        """update an object
        """
        if check_permission:
            cls.can_write(dbsession, object_id, *args, **kwargs)
        db_object = cls.get(dbsession, object_id, *args, **kwargs)
        data_before = {}
        try:
            for key, value in data.items():
                data_before[key] = getattr(db_object, key)
                setattr(db_object, key, value)
            update_audit_log = True
        except Exception:  # pylint: disable=broad-except
            print("Error getting value from model: %s")
            setattr(db_object, key, value)
            update_audit_log = False
        dbsession.commit()
        dbsession.refresh(db_object)
        cls.log_activity(dbsession, data, ActivityLogType.update)
        try:
            if update_audit_log:
                log_audit_trail(cls, dbsession, AuditLogType.update,
                                data_before=data_before, data_after=data,
                                object_id=str(db_object.id), remarks=db_object)
        except Exception:  # pylint: disable=broad-except
            _ = ''

        return db_object

    @classmethod
    def delete(cls, dbsession: Session, object_id, *args, check_permission=True, **kwargs):
        """delete an object
        """
        if check_permission:
            cls.can_delete(dbsession, object_id, *args, **kwargs)
        db_object = cls.get(dbsession, object_id, *args, **kwargs)
        data_before = {}
        for c in db_object.__table__.columns:
            try:
                data_before[c.name] = getattr(db_object, c.name)
            except Exception as e:  # pylint: disable=broad-except
                print("Error getting value from model: %s", str(e))
        data = {'is_deleted': True, 'deleted_at': datetime.datetime.now()}
        try:
            for key, value in data.items():
                setattr(db_object, key, value)
        except Exception as e:  # pylint: disable=broad-except
            print("Error setting value from input: %s", str(e))
        dbsession.commit()
        data_after = dict(data_before)
        data_after.update(data)
        cls.log_activity(dbsession, data_after, ActivityLogType.delete)
        try:
            log_audit_trail(cls, dbsession, AuditLogType.delete, data_before=data_before, data_after={},
                            object_id=str(db_object.id))
        except Exception:  # pylint: disable=broad-except
            _ = ''

        return object_id

    @classmethod
    def child_organizations(cls, dbsession: Session, membership: MembershipResponse):
        """return child organizations for a membership
        """
        child_orgs = []
        db_organization = dbsession.query(Organization).get(membership.organization_id)
        if db_organization:
            org_list = [db_organization]
            for org in org_list:
                org_list += org.children
                for child in org.children:
                    child_orgs.append(child.id)
        return child_orgs

    @classmethod
    def parent_organizations(cls, dbsession: Session, membership: MembershipResponse):
        """return child organizations for a membership
        """
        parent_orgs = set()
        db_organization = dbsession.query(Organization).get(membership.organization_id)
        if db_organization:
            current_org = db_organization
            while current_org.parent:
                if current_org.parent.id in parent_orgs:
                    break
                parent_orgs.add(current_org.parent.id)
                current_org = current_org.parent
        return list(parent_orgs)

    @classmethod
    def log_activity(cls, dbsession: Session, data: dict, log_type: ActivityLogType):
        user_id = None
        membership = cls.membership()
        if membership:
            user_id = str(membership.user.id)
        for key, value in data.items():
            data[key] = str(value)
        db_activity = ActivityLog(user_id=user_id, table_name=cls.model.__tablename__, data=data, type=log_type)
        dbsession.add(db_activity)
        dbsession.commit()

    @classmethod
    def check_root_organization(cls, dbsession: Session, organization_id: str):
        try:
            db_org = dbsession.query(Organization).filter(Organization.id == organization_id).one()
        except NoResultFound:
            raise exceptions.ApolloObjectDoesNotExist(model_name='Organization')
        if db_org.parent_id is not None:
            return True
        raise exceptions.ApolloOperationOnRootOrganization()


class RelationBaseCRUD(BaseCRUD):

    @classmethod
    def query(cls, dbsession: Session, *columns):
        """make a query to the ORM Model
        """
        if columns:
            return dbsession.query(*columns).select_from(cls.model)
        return dbsession.query(cls.model)

    @classmethod
    def get(cls, dbsession: Session, object_id: str, *args, **kwargs):
        """make a query to the ORM Model using ID filter
        """
        return cls.query(dbsession, *args, **kwargs).filter(cls.model.id == object_id).one()

    @classmethod
    def delete(cls, dbsession: Session, object_id, *args, check_permission=True, **kwargs):
        """delete an object
        """
        if check_permission:
            cls.can_delete(dbsession, object_id, *args, **kwargs)
        db_object = cls.get(dbsession, object_id, *args, **kwargs)
        data = {}
        for col in db_object.__table__.columns:
            try:
                obj = getattr(db_object, col.name)
                data[col.name] = obj
            except Exception as e:  # pylint: disable=broad-except
                print("Error getting value from model: %s", str(e))
        data = {}
        dbsession.delete(db_object)
        dbsession.commit()
        try:
            log_audit_trail(cls, dbsession, AuditLogType.delete,
                            data_before=data, data_after={}, object_id=str(db_object.id))
        except Exception:  # pylint: disable=broad-except
            print("Error Loggin Audit Trail")
        cls.log_activity(dbsession, data, ActivityLogType.delete)
        return object_id


def filter_membership(query: Query, filters: dict) -> Query:
    username = filters.pop("name")
    if username:
        query = query.filter(
            func.concat(Membership.first_name, ' ', Membership.last_name).ilike(f'%{username}%')
        )
    organization = filters.pop("organization")
    if organization:
        query = query.join(Organization).filter(Organization.name.ilike(f'%{organization}%'))
    query = filters_using_like(filters, query, Membership)
    return query


def filter_user(query: Query, filters: dict) -> Query:
    query = filters_using_like(filters, query, User)
    return query


# pylint:disable=unsubscriptable-object
def log_audit_trail(cls, dbsession: Session, log_type: AuditLogType,  # noqa
                    data_before: dict = None, data_after: dict = None,
                    object_id: Union[str, list] = None, remarks: Union[Base, dict] = None):
    api = cls.audit_log()
    if api and api['audit']:
        user_id = None
        membership_id = None
        membership = cls.membership()
        if membership:
            user_id = str(membership.user.id)
            membership_id = str(membership.id)
        if isinstance(data_before, dict):
            for key, value in data_before.items():
                data_before[key] = str(value)
        elif data_before is None:
            data_before = {}
        if isinstance(data_after, dict):
            for key, value in data_after.items():
                data_after[key] = str(value)
        elif data_after is None:
            data_after = {}
        table_name = cls.model.__tablename__
        module = str(table_name)
        if module.startswith("main_"):
            module = module[len("main_"):]

        module = " ".join(word.capitalize() for word in module.split("_"))
        if not remarks:
            if log_type == AuditLogType.create:
                remarks = data_after
            if log_type == AuditLogType.delete:
                remarks = data_before
        else:
            if isinstance(remarks, Base):
                dct = {}
                for c in remarks.__table__.columns:
                    try:
                        data = getattr(remarks, c.name)
                    except AttributeError:
                        data = ""
                    except Exception as e:  # pylint: disable=broad-except
                        print("Cannot convert database model into dict: ", str(e))
                        data = ""
                    dct[c.name] = str(data)
                remarks = dct
        if log_type == AuditLogType.update:
            key_to_remain = compare_dicts(data_before, data_after)
            if not key_to_remain:
                return
            remarks_ = {}
            remarks_['prev'] = {key: data_before[key] for key in key_to_remain}
            remarks_['new'] = {key: data_after[key] for key in key_to_remain}
            data_before = {**remarks, **data_before}
            data_after = {**remarks, **data_after}
            remarks = remarks_
        if not isinstance(object_id, (dict, list)):
            object_id = (str(object_id),)
        db_activity = UserAuditLog(user_id=user_id, membership_id=membership_id,
                                   table_name=table_name, action=log_type,
                                   data_before=data_before, data_after=data_after,
                                   api=api['api'], group_id=api['group_id'], module=module,
                                   object_id=object_id, remarks=remarks)

        dbsession.add(db_activity)
        dbsession.commit()


def compare_dicts(dict1, dict2):
    differing_keys = []
    for key in dict1.keys():
        if key in ('updated_at',):
            continue
        if key in dict2.keys():
            if isinstance(dict1[key], dict) and isinstance(dict2[key], dict):
                sub_diff = compare_dicts(dict1[key], dict2[key])
                if sub_diff:
                    differing_keys.append(key)
            elif isinstance(dict1[key], list) and isinstance(dict2[key], list):
                if dict1[key] != dict2[key]:
                    differing_keys.append(key)
            elif dict1[key] != dict2[key]:
                differing_keys.append(key)
    return differing_keys
