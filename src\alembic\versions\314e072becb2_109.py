"""109

Revision ID: 314e072becb2
Revises: 4949660ec2df
Create Date: 2024-07-22 11:03:14.102207

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '314e072becb2'
down_revision = '4949660ec2df'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('hubject_token',
    sa.Column('role', sa.String(), nullable=False),
    sa.Column('token', sa.Text(), nullable=True),
    sa.Column('expiry', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('role')
    )
    op.add_column('main_id_tag', sa.Column('emaid_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key('main_id_tag_fk_emaid', 'main_id_tag', 'main_emaid', ['emaid_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('main_id_tag_fk_emaid', 'main_id_tag', type_='foreignkey')
    op.drop_column('main_id_tag', 'emaid_id')
    op.drop_table('hubject_token')
    # ### end Alembic commands ###
