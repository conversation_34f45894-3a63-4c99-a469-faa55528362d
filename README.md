
# Apollo

## Development Setup

There are two ways to run for development: with Docker or with manual/traditional setup

### Docker

You will need to have [<PERSON><PERSON>](https://docs.docker.com/get-docker/) installed on your machine.
This method will make every component of the project to run on docker containers.

1. You need to copy .env.example and create new files called .env
2. run `docker compose up --build`
3. Your app should be run in `localhost:8001`

### Docker Full

This methods is when you want to run main and charger at the same time

1. copy `.env.full.example` to `.env.full`
2. run `docker compose -f docker-compose.full.yml up --build`
3. Go to Charger repo
4. copy `.env.full.example` to `.env.full` under charger repo
5. run `docker compose -f docker-compose.full.yml up --build` under charger repo

### Backend Manual Setup

This method will make the development server run on your machine, and is advantageous when you want to do some quick
changes and see the changes as you update the code.

**Requirements**

- Python == `3.8`

``` bash
# Environment setup
$ cd ./src
$ pip install pipenv
$ pipenv install --three

$ pipenv run uvicorn app.main:app --reload

```

### Create a Migration

To create a migration.

``` bash
$ pipenv run alembic revision --autogenerate -m "your comment"
```

Normally, we use the running number as the comment, this can only be obtain by checking the last revision manually.
Example script:

``` bash
$ pipenv run alembic revision --autogenerate -m 50
```

### Forward Migration

You can upgrade one or more revision

``` bash
$ pipenv run alembic upgrade #num % e.g +1
```

or upgrade to the latest migration.

``` bash
$ pipenv run alembic upgrade head
```

### Backward Migration

You can and should downgrade one revision by one revision only (May not work, please test on local).

``` bash
$ pipenv run alembic downgrade -1 
```

### Requirement and Tips to Kickstart:

1. Recommend: It is advised to use pycharm as your editor.
2. Requirement: All submissions must be made as new branches, and pull requests (PRs) must be created to be upmerged.
3. Requirement: All submissions (PRs) will run through GitHub Actions (GHA), following the flow below:
    1. Prospector: A tool that analyzes your code (static code analysis). Includes: a. Pylint b. PyCodeStyle c. McCabe
       Complexity
    2. Bandit: A tool that checks for potential security issue.
    3. PyTest: Framework for testing.
4. Recommend: It is advisable to use a database via restoration instead of a clean database..
    1. Reasoning: Some elements are not taken in place for Alembic, e.g., MaterialView, Permission, Running-Number,
       Indexes.
5. Recommend Reading: `OCPP v1.6`, `OCPI v2.0.1` - If job-scope required to understand, `Celery Tasks / RabbitMQ`
   , `Microservices via RestAPI`, `What is Alembic`, `What is Virtual Env in Python`
6. Tips: If you modify a new index, alembic will not know if you use the same name, so change the name and regen a new
   alembic revision file.
7. Tips: CSMS Route (Frontend) will be `/api/v1/csms` where Mobile Route (For mobile app) will be on `/api/v1`. However, it will be `router/v2/` in our file convention.
8. Techstacks used: Websockets, FastAPI, Celery, RabbitMQ, Asyncio, Sqlalchemy, Postgres, Pandas + Numpy - Reporting.
9. Requirement: All workflow must be adhering the workflow. Please check How to work on feature (Workflow).
10. Tips: Please request for the following upon joining the project:
    1. Github Repository
    2. Jira Access
    3. Postman Access
    4. Added to work comm. channel
    5. Granted miro access
    6. Granted Cloud [AWS] Access (If required)
11. IMPORTANT Tips: If you are cloning / up-merging from any DB. Please confirm that notification_emails in Charger is set to be `[]`. We had multiple issue where it sent tons of email to user.
12. IMPORTANT Tips: Do not do charging on other person that is not on development team!, this can cause tons of issue on email notifcation or here and there.
13. IMPORTANT Tips: Remove all the OCPI endpoint if you are cloning from any DB. Refraint from doing webhook / callback to non-relevant party.

### How to work on feature (Workflow)

1. Checkout from source branch (Normally develop)
    1. Naming convention:
        1. Copy directly from Jira - There is a way where it use your subject name as branch name
        2. Manually create a name, example: feat-ocpi-billing-request
2. Work on your request
    1. On checkpoint (Normally EOD), push to cloud.
    2. Create a PR (Draft PR) - Pre-review will be done.
3. Work completion
    1. Create Pull Request
    2. Notify supervisor as necessary
        1. Private Message, or -
        2. Github Reviewer
4. Supervisor to review and perform necessary merge / changes.

### Unit Testing

1. Unit-test / integration test must be written for money related flow.
    1. Eg: Calculate billing fee, perform billing on different scenario (Credit-card, Wallet, OCPI)
2. WARNING: Running PyTest will clean / wipe your database, please backup / refrain from using non-local database.

### Running test using docker

The first thing you need to be aware of, you need to make sure that
you have project well-prepared, like **docker images** built, **pipfile
dependencies** installed, also for instance in Pycharm you have to mark
**src directory as Sources Root directory** so the program would know
the correct root directory. If everything is set up run
following commands in order to fill up the local database:

1. `docker compose -f docker-compose.full.yml up --build --remove-orphans`
2. `docker compose -f docker-compose.full.yml exec -T apollo-main python cli.py init`

After that all defaults records should be created and should
allow you to test main service manually on local on [Main service](localhost:8001/apollo-main/docs)

### Running test using local

1. `pipenv run prospector` - Prospector
2. `pipenv run bandit -r . -x tests,citests` - Bandit
3. `pipenv run pytest` - PyTest (`WARNING`: Will wipe DB)

[//]: # (### Coding Guidelines)

[//]: # (Please adhere to the best practice in the document [Click Here]&#40;https://docs.google.com/document/d/1FafPIye7YDq67yE99CwbWrDgY030k_H0JuCy7umlvek/edit&#41; )

## Requirement for Local Setup:

1. Install RabbitMQ, enable RabbitMQ Management Tool
2. Install Postgres14 with pgAdmin
3. Install PostGIS for Charger DB
   - https://postgis.net/

### RabbitMQ Setup

1. First Login: User: `guest` Password: `guest`
2. Create a new user with your preference (will be used as login later).
    1. How: `Navbar` -> `Add a user` -> `Username` -> `Password` -> `Set - Admin`
    2. Normally uses `apollo` as login with same password.
3. Refresh -> Click on newly created user and set `permission`, `topic permission` and `update user`.
    1. Permission:
        1. Virtual Host: `/`
        2. Configure regexp: `.*`
        3. Write regexp: `.*`
        4. Read regexp: `.*`
        5. `Set permission`
    2. Topic Permission:
        1. Virtual Host: `/`
        2. Exchanges: `(AMQP Default)`
        3. Write regexp: `.*`
        4. Read regexp: `.*`
        5. `Set topic permission`
    3. Update user:
        1. Password: Same password
        2. Tags: Admin
        3. `Update user`

### Postgres Restore

1. Create a new user:
    1. General:
        1. Name: `apollo`
    2. Definition:
        1. Password: Desired - Normally `apollo`
        2. Account expires: `No expires`
        3. Connection limit: `-1`
    3. Privileges:
        1. Can login: `True`
        2. Superuser: `True`
2. Create new database
    1. Perform restore

### Local Installation

1. Follow `Requirement for Local Setup`
2. Follow `RabbitMQ Setup`
3. Follow `Postgres Restore`
4. Install `pipenv` - Refer to `https://pipenv.pypa.io/en/latest/installation.html`.
    - Important: Remember, to use pypa instead of sudo apt!
5. Run `python3 -m pipenv shell` or `python -m pipenv shell`
    - You should see a new shell has been newly created
6. Run `pipenv python3 --version`
    - You should see it say 3.9
7. Run `pipenv install --dev`
    - You should it it install twice (One for normal, one for dev; This is normal)

### Running Main-REST-API

1. Run `Local Installation Step`
2. Run `python3 -m pipenv shell` (PS: If you haven't do so)
3. Run `pipenv run uvicorn app.main:app --reload --port 8001`
    - It should be up and running on `localhost:8001`

### Running Celery task

1. Run `Local Installation Step`
2. Run `python3 -m pipenv shell` (PS: If you haven't do so)
3. Run `pipenv run celery -A app.celery worker -l INFO`
    - It should be up, if you see just a few line (no workers), ideally mean OOM and unable to start.
    - For Windows: `pipenv run celery -A app.celery worker -l INFO --pool=solo`

# Business-Logic for Pre-Auth

### Pre-Auth Flow

The pre-auth flow is designed to handle the pre-authorization of payments before a charging session begins. The main
goal is to ensure that the user has sufficient funds or a valid payment method to cover the cost of the charging
session.

### Payment Types

Supports two payment types:

- **Credit Card**: The default payment method where the user's credit card is pre-authorized for a certain amount.
- **Wallet**: An alternative payment method where the user's wallet balance is used to cover the cost of the charging
  session.

The `payment_type` is determined based on the `use_wallet` flag in the filters object.

### Pre-Auth Amount

The pre-auth amount is the amount that will be pre-authorized on the user's payment method before the charging session
begins. The code retrieves the pre-auth amount based on the user's `membership_id` and the connector's currency.

- If the `use_wallet` flag is `False` (i.e., credit card payment), the code checks if the pre-auth amount is present and
  within the allowed limits. If the pre-auth amount is not found or exceeds the maximum allowed amount, an exception is
  raised.
- If the `use_wallet` flag is `True`, the code retrieves the user's wallet balance and checks if it's sufficient to
  cover the connector's starting fee and above a minimum threshold. If the balance is insufficient, an exception is
  raised.

### 3DS and 2DS

The code supports both 3D Secure (3DS) and 2D Secure (2DS) payment authentication methods. The `use_3ds` flag in the
filters object determines which method to use.

- If `use_3ds` is True, the code initiates a 3DS payment flow.
- If `use_3ds` is False, the code first attempts a 2DS payment flow. If the 2DS flow fails (indicated by a response code
  of 301 (Internal return / mapper)), the code falls back to a 3DS flow.

### Pre-Auth Flow Steps

The pre-auth flow follows these steps:

1. Check if there is an existing non-binded pre-auth payment for the given `membership_id`, `connector_currency`,
   and `payment_type`. If found, return the existing pre-auth payment object.
2. If `use_wallet` is `True`, initiate a wallet pre-auth payment flow.
3. If `use_3ds` is `True`, initiate a 3DS payment flow.
4. If `use_3ds` is `False`, initiate a 2DS payment flow. If the 2DS flow fails, fall back to a 3DS flow.

Based on the outcome of the payment flow, return the appropriate pre-auth payment object along with any necessary action
details (URL, method, data) for 3DS flows.

The pre-auth flow ensures that the user has a valid payment method and sufficient funds before allowing the charging
session to begin. It handles different payment types (credit card and wallet) and authentication methods (3DS and 2DS)
to provide a flexible and secure payment process.

### Workflow for Pre-Auth-Payment

1. **Create a PaymentRequest**: Initiates the transaction process, specifying the intent to charge an amount.
2. **Link PreAuthPayment to PaymentRequest**: Represents the pre-authorization of funds to ensure availability. This
   step is linked to the initial `PaymentRequest`.
3. **Finalize the Amount**: Once charging is completed and final amount is determined, moves to capture the
   pre-authorized funds.
4. **Create a New PaymentRequest for Capture**: Create a new `PaymentRequest` with finalized amount, just like native
   recurring flow (Create `ChargingSessionBill`, link `PaymentRequest` to `ChargingSessionBill`)
5. **Create PreAuthPaymentCapture**: Link to the original `PreAuthPayment` (indicating which pre-authorization is being
   captured) and the new `PaymentRequest` (representing the final capture transaction).

## Workflow for Recurring Payment

1. **Finalize the Amount**: Once charging is completed and final amount is determined, moves to capture the
   pre-authorized funds.
2. **Check if `ChargingSessionBill` Exist with `ChargingSession`**: If exist, reuse
3. **Create `ChargingSessionBill`**: Create a bill that is link to `ChargingSession`
4. **Create `PaymentRequest`**: Check for exist, ideally shouldn't exist for first billing, else refer to RebillingFlow
   Create a new `PaymentRequest` with finalized amount linked to `ChargingSessionBill`
5. **Create `RecurringPayment`**: Create a new `RecurringPayment` , link to `PaymentRequest`, noting down the response,
   initial `Accepted` by PaymentGateway will result in Invoice being send; a `Rejected` will result Update
   to `PaymentRequest` as `Rejected`
6. **If PaymentGateway Return (Async)**: Result in update to `PaymentRequest` only.

## Workflow for Recurring Payment (Rebilling)

1. **Recall via tasks**: Re-finalize amount,
2. **Check if ChargingSessionBill Exist with `ChargingSession`**: If exists, reuse, else refer to Recurring Flow
3. **Check for `PaymentRequest`**: It should already exist, else refer to RecurringFlow
4. **Check `PaymentRequest` Status**: If status is `Pending`, meaning it is awaiting for PaymentGateway returns, thus
   await.
5. **If status is Failed / Rejected: Create Recurring Payment**: Create a new `RecurringPayment`, link
   to `PaymentRequest`, noting down the response, initial `Accepted` by PaymentGateway will result in Invoice being
   send; a `Rejected` will result Update to `PaymentRequest` as `Rejected`
6. **If PaymentGateway Return (Async)**: Result in update to `PaymentRequest` only.
7. *TIPS*: Ideally, one PaymentGateway will have multiple RecurringPayment if it is failed for first time. And each
   initial `Accepted` will result in `Invoice Generation and Send`, thus be-careful.
8. *TIPS*: There may be chance the person will use a new card, and the credit-card info is used in RecurringPayment,
   thus it is completely fine to blacklist, etc.

## Logic to take note:

1. Free Charging:
    1. If total seconds consumed (session_start - session_end) <= 60 seconds, consider free
    2. If charging usage (meter_stop - meter_start)<= 5 wh, consider free.
2. protocol_type:
    1. The "protocol_type" on Location is critical, as of current date, there is "OCPP/OCPI/CDG/Partner"
    2. OCPP mean everything remain same, where a discount can be applied. Discount can be applied to this protocol
    3. CDG is a party that cannot join using OCPI, but want to do roaming arrangement, what is done is the websocket is
       splitted into two different services (CDG and CEV), but need to act as "fake-roaming". No discount will be
       applied to this protocol.
    4. Partner is where they are using native OCPP, but support third-party services, such as PaymentGateway, there is
       no discount applied to this type of protocol.

## OCPI Logic:

1. If CEV is EMSP, we are the receiver, we use other platform's charger.
2. If CEV is CPO, we are the sender, we host the charger, and other platform / party will use our charger.
3. The final bill amount is rely on CPO (If we are EMSP) by CDR, thus there may be difference.
4. standardize_tariff is incident where previously we do not honor the step_size, please pay attention to the tariff
   module, the price is by price/per_hour or price/kwh, where the step_size would mean, you are to compute the price
   every n_seconds or n_wh.
    1. Meaning to say, lets say if its type: time, price: rm1.5/hour, step_size: 1200, meaning every 20 minute you are
       to compute the price meaning to say it is actually: 0.5/20 minutes.

## Mobile Create Subscription Logic:

1. User press subscribe
2. Backend create new entry called `Subscription` + `SubscriptionOrder` + `PaymentRequest`
3. Backend supply URL to RMS, `SubscriptionOrder` is `Pending` by default; `Subscription` default is `True`
   and `Subscription` `status` is `pending`.
4. When payment done, RMS to redirect to call-back and update `Subscription` to `success` + `SubscriptionOrder` status
   to `success`+ `PaymentRequest` to `done`.
5. If `PaymentRequest` reason is `Subscription Order` -> New Subscription, we pull a card from `SubscriptionCard` and
   create a new entry to `SubscriptionIDTag` with the "Card Info".
6. Update `Subscription` with the `SubscriptionCard` and `SubscriptionIDTag`. -> This is critical as if user re-new
   their subscription, the id-tag is automatically renewed with the subscription.

### Note on Call-back during Subscription:

1. There is a step where it will check for all id-tag that was used, but card is marked as un-used, it will
   automatically update all the card that was used as linked_at, thus not usable for being picked anymore.
2. The reason behind is where the CSMS administrator randomly pick one RFID card and give to a random user, it will
   break the code.

### Mobile Renew Subscription Logic:

1. User press renew
2. Backend supply URL to RMS, `SubscriptionOrder` is `Pending` by default.
3. When payment done, RMS to redirect to call-back and update `Subscription` to `success` + `SubscriptionOrder` status
   to `success`+ `PaymentRequest` to `done`.
4. If `PaymentRequest` reason is `Subscription Renewal`:
    1. If is expired: Update `start_date` to current date, `end_date` to current date + 1 year.
    2. If not expired: Update `end_date` to `end_date` + 1 year.
5. Update `SubscriptionIDTag` entry `expiration` to `end_date`.

## Pricing Control

### Default Pricing:

- Each charge point has connectors, which are the actual points where vehicles plug in to charge.
- Each connector has a default price, which is the standard rate users pay if no special pricing is applied.

### Subscriptions (Member Rate):

- Users can subscribe to different plans that may offer special pricing.
- Subscriptions can include custom pricing plans or discount types, which influence the final price users pay.

### Discount Types (Subscriptions Type)

- Subscriptions can have a discount type that is either percentage-based or fixed:
    1. <b>Percentage-Based Discounts</b>: This discount is applied as a percentage off the default price. For example, a
       100% discount means the user pays nothing for the charge points, except where custom plans specify a different
       price.
    2. <b>Fixed Discounts</b>: This means the user pays a fixed price defined by the subscription, regardless of the
       default price.

### Custom Plans (On-top of Subscriptions)

- Some subscriptions include custom plans that set specific prices for certain charge points or scenarios. When a custom
  plan is present, it overrides other discount types.
- How it works:
    1. <b>Without a Subscription</b>: Users pay the default price for using a charge point connector.
    2. <b>Subscription with Custom Plan (Connector in Custom Plan)</b>: Users with a subscription that includes a custom
       plan pay the price specified in the custom plan, overriding the default price. This price is a fixed price and no
       further discount is made.
    3. <b>Subscription with Percentage as Type</b>: If a subscription has a percentage type of discount, this percentage
       is applied to the default price. For example, a 100% discount means the user pays nothing for the charge points,
       except where custom plans apply.
    4. <b>Subscription with Fixed as Type</b>: If a subscription offers a fixed discount, the user pays the fixed price
       defined by the subscription.
- Examples:
    1. <b>Default Pricing</b>:
        1. A charge point’s connector has a default price of $0.30 per kWh.
        2. A user without any subscription pays $0.30 per kWh.
    2. <b>Subscription with Custom Plan on Top</b>:
        1. A user subscribes to a plan that offers a custom price of $0.20 per kWh.
        2. This user pays $0.20 per kWh, overriding the default price of $0.30 per kWh.
    3. <b>Subscription with 100% Percentage Discount</b>:
        1. Another user subscribes to a plan offering a 100% discount.
        2. This user pays nothing ($0.00) per kWh, except for charge points covered by custom plans.
    4. <b>Subscription with 0% Percentage Discount</b>:
        1. A user subscribes to a plan with a 0% discount, meaning they pay the price specified in the custom plans.
        2. If there are no custom plans, they pay the default price.
    5. <b>Subscription with $1.00 as Fixed Discount</b>:
        1. A user subscribes to a plan with a subscription type as Fixed and with $1.00 configured, meaning they pay the
           price for all connector at 1.00, if no custom plans.
        2. If custom plans is in place, the custom plan will override the 1.00$

### Active Subscriptions

1. A user can have multiple subscriptions, but only one subscription can be active at any given time.
2. The active subscription is determined by default settings.
3. All pricing for a user is based on their active subscription.
4. A subscription can be default to True (Ideally mean Active), but expired, meaning no subscription is applied.

### Pricing Calculation

1. The price is determined and stored at the time the charging session is started.
2. If the user active a subscription after starting a session, the user will need to pay the price without-subscription.

## Charge Point Visibility and Access Control

### Operator Types

1. <b>Public Operators</b>: These operators allow anyone to see their charge points. It's like a public park where
   everyone has access.
2. <b>Private Operators</b>: These operators restrict access to their charge points. It's like a private club where only
   certain members can enter.

### User Associations

- For private operators, only specific users are given access.
- This means if you are part of a private operator's group, you can see and use their charge points. If you're not part
  of this group, you cannot see them.

### Charge Point Visibility

- Charge points can be either public or private:
- <b>Public Charge Points</b>:
    1. These are visible to everyone (that the user has access to) either from a public operator or a private operator
       with user-operator association.
- <b>Private Charge Points</b>:
    1. No one can see it from the mobile application (Excluding CSMS)
    2. Even if you're part of a public operator, you cannot see private charge points.

### How It Works

1. If a charge point is managed by a public operator:
    - Everyone can see it unless the charge point itself is marked as private.
    - This is like a well-known, publicly available charging station that everyone knows about.
2. If a charge point is managed by a private operator:
    - Only the users who are specifically associated with this operator can see the charge points.
    - For example, if a company operates privately, only its employees or members can access the charging stations it
      provides.
    - If the charge point is marked is private, no one can see it.

## QR Code
- **IMPORTANT: All QR have logo embedded and it should be replaced with the
  actual logo of the client.**
- The QR Code path is tailored after discussion with mobile side, as two type of scanning mechanism is introduced: 
  1. In-App Scanning - Current behaviour, the app after scan will not open any link but do perform scanning of URL and opening necessary info.
  2. QR-Code Scanner - Will direct to other services / repository (Deeplink repository, it will check for your device and tries to open the relevant app using URI Intent and fallback to App Store / Play Store)
- Three type of QR code is being introduced: Connector, Charge Point and Location.
- **Connector:**
  1. screen=chargerDetailPage/serial_number=Test_apollo/connector_number=1 
- **Charge Point:**
  1. screen=chargerDetailPage/serial_number=Test_apollo 
- **Location:**
  1. screen=chargerDetailPage/location_id=uuid
- Full example for connector
  1. https://staging.yinson-apollo.com/deeplink/screen=chargerDetailPage/serial_number=Test_apollo/connector_number=xxxx


<br/>
<br/>


# Audit Log
_____

### Overview
#### What is group id?
- Sometimes, one user action might trigger multiple db action, for example when we create new user, we also create membership and wallet. Therefore this group_id meant to group them into one action to be shown in the frontend, so that we know which action lead to this db action. If not it's hard to group them together.
#### ContextVar (Important for charger repo)
- ContextVar is used to pass a variable along the chain of calls so that we can share the same context, it's good option to use for async. However, it has weird behaviour where if you set it, then it's hard to set the value again.
- The value will be stored inside _request_audit_ctx_var.

### Function Explanation
1. `get_request_audit`:
   - Used to get the ContextVar value 
2. `deactivate_audit`
   - Used deactivate the audit for that particular context.
   - Usage:
     1. use inside the api that we don't want to log the audit. 
     2. to be save, let's it be the first line of code that the api hit. 
     3. Or as long as we haven't set the ContextVar yet, we should be okay to call this. - Because once we call the set the ContextVar, it will be a bit difficult to deactivate it again.

### Workflow for Audit Logging
### Main
1. For all api that located inside main repo, only concern on their `routers` and `crud`. 
2. By default all the audit logged by their DB activity. 
3. If there's any issue with the audit log value, check the flow (incase there's any custom audit log). If not it's mean that it use the default function which is defined in `crud/base.py`. Check function called `log_audit_trail`.  We call this function inside base crud function for create, delete and update. This is to ensure that we log every changes that user do that is persistent in db.

### `log_audit_trail` walkthrough
* Notice this line `module = " ".join ...`
1. This will be the module (in csms it will be the tab name and also the module column)
2. Also notice how we set the remarks. For create and update it is straight forward. But for update, we use data_after and data_before to compare both data, we want our remark to contain only data that have changes. So that frontend can display in table.
3. Short summary on the `compare_dicts` function:
   1. It will compare two dict, if the dict contain another dict, it will go check inside it.

### Custom Case for Main Repo
As for now (13/8/2024), only this list contain custom audit log:
- `crud/wallet.py`: `generate_redeem_code`
- `crud/role.py` : `create_organization_role, add_resources_to_role, remove_resouces_from_role`
- `routers/organization_memberships.py`: `@router.patch("/{organization_id}/membership/custom/{membership_id}", response_model=schema.MembershipResponse)`
* It is quite straight forward, follow the example from the code above.
* The only reason why we want it to be custom is that the audit log, log exactly what we give to db, this might not be useful to user for example we only have organization_id instead of organization_name. Therefore this custom is important. 
* To put the audit in `routers` or `crud` should be judge from the complexity and impact, because sometimes, when we write in `crud` we need additional query just to get certain info which already there in routers. Therefore, writing it in `routers` makes more sense.


### Charger REST
Charger might be a bit complicated because we don't log the audit log in charger db, but we'll save in main. How exactly we do it? We save all the needed information (table_name, log_type, data_before, data_after, remarks) and transport the data from charger to main by saving the data inside the header, this header supposely not being shown to user but still fine to show.
Since all the request and response from charger will go thru main, we track down this and save it in our main db. 

In main (main-rest-api), we have two function called `write_audit_trail` and `audit_log_charger` in `routers/charger.py`. This function will help us to save the audit log into the table. Notice that we check `'X-Audit' in resp_header`, meaning that we save the data that we need to log in this `X-Audit` in header.

Most things in charger repo middleware are the same with main repo except the `class RequestContextMiddleware`. What it will do is, it will save the audit data into header under `X-Audit`

- There's two major function that need to highlighted:
    1. `set_request_audit`: This is used in normal flow
    2. `set_entire_audit_req`: This only for specific case, where we want to overwrite everything, usually when we want to clear the contextVar before use it again.

### Custom Case in Charger Repo
Why we need this? For example, when we update something in charge point that requires operation request, I will be shown in the audit log as created and module will be Operatin Request (this is make sense to use but not to user), that's why we need to customize so that it have the tags that's user friendly.

There's couple of example but I'll explain the operation request. When we have any user request we will call this router `@router.post('/{charge_point_id}/ocpp', response_model=OperationRequestSchema, status_code=status.HTTP_200_OK)`. 
- Usually what happened, is that we'll log the first db activity. The reason why it always set to 'create' before are due to this line of code:
`db_operation_request = await crud.create(db, OperationRequest, operation_request)`

Because we hit this function first and it will set the ContextVar and we cannot set it again. Therefore, we should take the control by using this line of code:
`audit_log = get_request_audit()`

Once we call the code, the `await crud.create` above won't be able to set the ContextVar. This is important if there's multiple action and we want just certain action to be logged (just for charger repo).

```
audit_log['audit'] = True
operation_request_info = db_operation_request.__dict__
operation_request_info['parameters'] = message.body.parameters
operating_request_audit(audit_log, operation_request_info, cp)

```
The code block above, we give the audit_log control to operating_request, we use it to set the ContextVar. Why the above need to use message.body.parameters is that we also want to log certain parameters for clarity.

### Recap on how middleware work
When creating a middleware don't forget to include it in `main.py`. When the request enter the backend, it will go thru middleware first, the middleware will do whatever it programmed to and then give the control to the routers.
- `await call_next(request)`: This particular line will give the control back and wait for the response, we manipulate the response before we send it back to client. This way we can inject the header with our data.

____

# Static Data Generation
____
### Format and Summary

Please check './Static Data Submission Template_19 April.xlsx' in 'Instruction Sheet' for the detail of how the final excel should looks like.

Here's some key points:

1. For each carpark code, split them into separate sheet. Start with 'EV Charge Point Carpark 1', there's no specification about the order of which carpark should come first.
2. 'EV Charge Points' sheet should contain all charge points starting with one charge point record (from 1st to last day of the month).
3. Sheet that are not related should not be include in the end result. 'BCSS'
4. 'Ad-Hoc' should only be compiled when requested. Note that the data for this might also include previous months up to 1 year of data. For now, we don't have any CSMS page for this, but the endpoint for this already done. However, the end result for this 'Ad-Hoc' will be in split file than the other. It's agreed that they will do the compilation manually, for now. (Might need to implement automation in the future.)

### Code - Charger

In summary, charger repo will be the one that responsible for data. If the data is not right (expect format), please check the query. We'll go through the flow first before we go through each function.

There's two scheduled task in the celery ('lta_celery'):
1. `apollo.charger.lta_tasks.generate_daily_lta_static.daily`
    - This task include:
        - Gathering data for evcp ('charge point') and carpark (`get_lta_evcp_carpark_static`)
        - Gathering data for ocpp (`lta_ocpp_precompute_complete`)
2. `apllo.charger.lta_tasks.generate_monthly_lta_static.monthly`
    - This task include:
        - Gathering data for 'summary' sheet. (`get_lta_summary_static`)
        - Gathering data for 'operating hours' (`get_lta_operating_hours`)
            - This actually for Ad-Hoc just in-case we want to store previous month operating hours for the charge points. There's no requirement for this.

Let's start with explaining each function flow. Start with `lta_ocpp_precompute_complete` because this might have issue and bit complicated.

1. OCPP Precompute
    1. `lta_ocpp_precompute_complete`
        - Before we the energy usage with ocpp data, we need to get the ocpp data first. There's a function that can do this beforehand, might consider to add this in the task before calling this precompute function. This file we will it as raw_ocpp.
        - This raw_ocpp stored in S3 in this format:
            * `key = f'{LTA_S3_PREFIX}/OCPP_LOG_RAW/{date_file_name}/ocpp_{day_file_name}.parquet'`
        - If there's no such file in S3, we'll create one using this `raw_ocpp_raw_storing`, this function will be explained in the next section. 
        - Once we have the data, we'll do some precomputation. There a function called `transferred_energy_calc`. If there's anything wrong with the energy, check this function and also make sure that raw_ocpp data have the data.
        - Some key point for `transferred_energy_calc`:
            * It contain transaction_id, we'll use this to take advantage of meter_start and meter_stop value stored in db.
            * The first and last hour of the session, we will also include the meter_start and meter_stop in the calculation. 
            * Can we handle it just using ocpp? Yes, but it might be a bit complicated to match the meter_start with the data, meter_stop won't have much trouble.
        - Issue that might occur:
            * Missing RAW OCPP 

    2. `raw_ocpp_raw_storing`
        - First source that we use is `opensearch`
            - In the `opensearch` query we have `size` to limit the size of data that we query, this is to reduce pressure from opensearch.
            - Date inside the query have included the timezone.
            - One things that need keep in mind is `scroll_id`
            - This is opensearch features sames as `pagination`
            - `os_query(client, index_name, query, "10m")`: 10m here mean 10 minutes, meaning that each scroll will be stored 10 minutes in the opensearch. If the process took longer than that, try to increase it.
            - Once we got the data, we'll process it:
                * Store the timestamp
                * Store the meter value, filtered by unit, and the `measurand`
            - Issue that might occur:
                * Ocpp have some weird data or format especially the datetime
                * Check if the data to see if `measurand` are correct.
        - Second source we use db
            - This second source only will trigger if the `opensearch` fail
            - The `opensearch` might failed due to above mentioned issues and also if opensearch already clear the data. 
            - This function will compute by using `charging_session` table in db.
            - Since we store the meter_value in charging_session, we can easily calculate the same exact things that we do from `opensearch` it's just that this value already been filtered.
        - This raw data will be stored with this key:
            * `key = f'{LTA_S3_PREFIX}/OCPP_LOG_RAW/{date_file_name}/ocpp_{day_file_name}.parquet'`

2. `get_lta_evcp_carpark_static`
    - This function is actually straight forward but it does 2 things at once.
        1. Generate data for EVCP ('Charge Point', EVCP is the old format name)
            - It will be stored in this key:
                * `f'{LTA_S3_PREFIX}/{date_file_name}/evce_{day_file_name}.parquet'`
        2. Generate data for Carpark
            - It will be stored in this key:
                * `f'{LTA_S3_PREFIX}/{date_file_name}/carpark_{day_file_name}.parquet'`
    - This two actually straight forward, if the data is wrong it's due to query.
    - There's two query:
        1. For uptime
            - Check unavailability log and compute if there's any
        2. For utilization
            - Check the charging_session and compute the usage for each charge_point for every 1 hour interval if there's data.
            - Meaning that if the session start at 11.15AM and end at 1:00 PM,
                * The utilization for 11AM is 75% because the charge point is not utilized for the first 15 minutes only. Therefore, `45/60 * 100 = 75`.
                * Note that this is based on charge_point, not connector. If the charge point have two connectors and only one being utilized, we still count it.
    - Most of the hardwork done by the sql query, if there's any issue, try to run the query and check the data first.
    - All other things just to group them up, etc.
    - Carpark calculation take from the same query, it just then `pandas groupby` them using their carpark_code, postal_code.

3. `get_lta_summary_static`
    - It's straight forward. Mostly the work done by sql query
    - It will be stored in this key:
        * `f'{LTA_S3_PREFIX}/{date_file_name}/summary.parquet'`

4. `get_lta_operating_hours`
    - It' straight forward. Mostly the work done by sql query
    - It will be stored in this key:
        * `f'{LTA_S3_PREFIX}/{date_file_name}/operating_hours.parquet'`

5. `get_lta_charge_point_list`
    - This might be an issue later.
    - This function will get the the erc_number and serial_number for the related charge points.
    - Why we have this? Before this we use serial_number, but now we need to use ERC number, the problem if we change the erc_number in the middle of the month, we might have issue with the data. That's why we have serial_number and using this we will map the erc_number with the serial_number when we compile the file later in main.
    - It will be stored in this key:
        * `f'{LTA_S3_PREFIX}/{date_file_name}/charge_points.parquet'`
    - In the meantime, we can call it manually because we only need this once a month.

### Code - Main

In summary, main repo responsible to compile all the data coming from charger and put it together as excel file with format. There's for if data have issue, it most likely not coming from main, unless it's format related.

There's one scheduled task in the celery ('lta_celery'):
1. `apolo.main.tasks.generage_lta_static_excel.monthly`
    - Since charger repo also have montly task this task should start once the charger complete its part.

This task will call one function: `generate_upload_lta_static_excel`. At the end of the function, it will send the data thru emails listed in `.env`, note that we should store list in `.env` because we want to send it to more than 1 email.

The flow:
* It will load a template from S3, the template is a modified version of the file mentioned in the summary (I'll provide it once I find it).
* The function cannot work if the template is not there. It need to be saved in this key: `f'{settings.LTA_S3_PREFIX}/LTA_Template.xlsx'`. It then will be saved into a temp file. This file we'll be use to copy all the necessary format (number format, font, alingment, header, sheetname, etc)
* The function will find the files from S3 in `{date_file_name}` directory.
* Here's we'll glue the ocpp data with carpark data.
* The most complex about this function not the merging the data. This is quite straight forward by using `pandas`
* The complicated things is working with `excel`
* There's two excel library used here:
    - `xlsxwriter`
    - `openpyxl`
* Why need both?
    - This can be done using one but might be complicated. It's easier to work with `xlsxwriter` but the problem is `xlsxwriter` can only write and not read.
    - Advantage of using `openpyxl` is that we don't have to load the whole excel to RAM to work with it. And it's easier to copy from one excel to another using `openpyxl`
    - That's why we use it to copy from template to the target excel file

* There's less likey issue here in main repo except some formatting. If LTA decide to change the format, here's the place to start with.


____

# Report Export Overview

____


This project provides functionality for generating and exporting various reports. The export functionality supports generating CSV files and delivering them securely via presigned URLs, with email notifications sent to users. Key issues such as synchronization, retry logic, and filtering have been addressed to ensure smooth and reliable report generation.

## Features
- **Multiple Report Types:** Supports mutiple exporting reports.
- **Customizable Columns:** Ability to customize report columns based on user needs.
- **CSV Export:** All reports are exported in CSV format for compatibility with common data analysis tools.
- **Presigned URL Downloads:** Provides secure report download links via presigned URLs, with expiration for security.
- **Pagination Support:** Paginated results for handling large datasets effectively.

## Key Fixes and Improvements

1. **Retry Logic for Task Failures:**
   - **Issue:** The system encountered issues when generating reports due to transient errors, leading to failed tasks without retry mechanisms in place.
   - **Fix:** Introduced robust retry logic using Celery's `autoretry_for` and `retry_backoff` features. Now, tasks automatically retry in case of errors (e.g., network issues or file system problems), with exponential backoff to avoid overwhelming the system.
   - **Retry Configuration:**
     - `autoretry_for=(KombuError,)`: Automatically retries on Kombu errors.
     - `retry_backoff=3`: Implements a backoff strategy for retrying, starting with a 3-second delay, and doubling the delay for each subsequent attempt.

2. **Synchronous vs Asynchronous Handling:**
   - **Issue:** Previously, report generation was blocking the main application flow, slowing down user requests and potentially causing timeouts, especially for larger datasets.
   - **Fix:** Offloaded report generation tasks to Celery workers, making the process asynchronous. This ensures the main application remains responsive while reports are being generated in the background.
   - **Improvement:** Users no longer experience delays in the main application, and once the report is ready, an email notification is sent automatically with the download link.

3. **Presigned URL Expiration and Security:**
   - **Issue:** Users reported that the download URLs for the exported reports had security concerns, as they were active for too long or exposed to unauthorized access.
   - **Fix:** Implemented secure presigned URLs for downloading reports. These URLs are generated with a specific validity period (configurable) and emailed directly to the user. After the expiration period, the URL becomes invalid, ensuring that reports are only accessible within the given time frame.
   - **Enhancement:** Improved email notifications to include clear instructions on the expiration of the download link.

4. **Improved CSV Export Structure:**
   - **Issue:** Users requested more flexibility in customizing the CSV export columns and report format.
   - **Fix:** Enhanced the `GenerateReport` class to allow dynamic customization of report headers, column order, and data fields. Users can now specify:
     - `title`: Report title.
     - `header`: Custom headers for the CSV file.
     - `columns`: Customizable data columns.
     - `reorder_columns`: Flexibility in reordering the columns.
     - `timezone`: Handle timezone conversions for date fields.


## Secure URL Generation with JWT and File Upload

This section explains the process of generating a secure download link using JWT for report export and uploading the report to an S3 bucket.

1. **Uploading the Report to S3:**
   - The report file is first saved locally and uploaded to an S3 bucket using the `upload_csv_with_presigned_url` function.
   - The function accepts the local file path and the file name as arguments and uploads the file to the designated S3 bucket.

2. **JWT Token Generation for Secure Download:**
   - After the report is uploaded, a JWT token is created to secure the download link.
   - The token includes an expiration time (`exp`), the bucket name, and the file name.
   - The JWT is signed using a secret key defined in `settings.JWT_EMAIL_REPORT_SECRET`, and the algorithm is specified in the `schema.JWT_ALGORITHM`.

3. **Encoding the JWT Token in the URL:**
   - The generated JWT token is encoded as a query parameter using the `urlencode` method.
   - The token is appended to the URL for secure access to the report download endpoint.

4. **Sending the Presigned URL via Email:**
   - Finally, the presigned URL is emailed to the user, along with the report title and the expiry duration.
____


# GST Display in Mobile App
1. **GST Display Calculation:**
   - GST displayed in the mobile UI is computed using a hardcoded GST value in the Charger Repository.
     - **Example:**  
       If the location's country is Singapore:  
       ```
       billing_unit_fee_after_vat = billing_unit_fee * 1.10
       ```

2. **Display Purpose Only:**
   - The GST shown in the Mobile UI (`billing_unit_fee_after_vat`) serves **only for display purposes** and does not influence the final bill amount.

3. **Final Bill Amount:**
   - The final bill is computed based on the **VAT-exclusive price** (`billing_unit_fee`) by recalculating the GST amount during backend processing.

# Tax Computation on Bill / Invoice
### **Local / Native Tax Computation:**
1. The bill or invoice calculation uses the following criteria:
   - **`main_tax_rate` Table:** Determines the country-specific tax percentage.
   - **`main_operator` Table:** Considers the `is_taxable` attribute.  
     **Example:**  
     If a charge point is operated by Operator A and `is_taxable` is `False`, no tax will be applied to the final bill.

### **OCPI Tax Computation (eMSP - We as eMSP):**
1. As an eMSP, invoices are received in the form of a **Charging Detail Record (CDR)** from the partner.
   - The partner typically sends CDR that includes tax based on their local regulations.

2. **Additional Tax Logic:**
   - If additional tax is required:
     - Currently, if there is no tax supplied:
     - The system checks the `main_tax_rate` table to match the **CDR currency** with the corresponding country.
     - If `enforce_tax_on_ocpi` is `True`, an **additional tax rate** (`ocpi_additional_tax_rate`) is applied to the total amount.
     - The computed tax is added to the final amount in `charging_session_bill`.
   - **Note:** The original CDR is not modified.


____
# **Payment Gateway**

The system integrates with two primary payment gateways, each with distinct characteristics and requirements:

## **1. Supported Payment Gateways**
   - **Fiuu:** A full-service payment gateway with extensive use of hosted payment pages.
   - **CyberSource:** A payment processor where custom-hosted HTML forms are commonly used.

## **2. Configuration and Conflict Resolution**
To ensure smooth operation, follow these guidelines:
   - **Correct Configuration:**
     - The system configuration setting `PAYMENT_GATEWAY_TYPE` must match the actual gateway in use.
   - **Saved Card Validation:**
     - The saved card in the `main_credit_card` table must have the correct `payment_gateway` value (`Fiuu` or `CyberSource`).
   - **Consistency Check:**
     - Ensure both the system configuration and saved card gateway match. Any mismatch between the two will cause system errors.

## **3. Key Differences Between Fiuu and CyberSource**
   - **Fiuu:**
     - Acts as a full-service payment gateway.
     - Relies heavily on **hosted payment sites** for transaction handling.
   - **CyberSource:**
     - Functions primarily as a payment processor.
     - Requires hosting of custom **HTML payment forms** within the application for user interaction.
ssor, we typically host our own HTML, and all the logic are on our own, they are very raw-computation and all HTML are to served by us.

---
# **Idle Fee**
1. Idle fee will be considered started on when:
   - Idle Enabled Charger and 
   - Charging Session Ended (StopTransaction OCPP Signal) or
   - SuspendedEV OCPP Status (StatusNotification OCPP Signal) or
   - Last MeterValue lesser than Last Two MeterValue of 20wh (MeterValue OCPP Signal)
   
2. Idle fee will be considered to be removed on when:
   - Hogging Started, but Charger send "Charging" (StatusNotification OCPP Signal) OCPP Status
   
3. Idle fee will be considered ended on when:
   - Last OCPP Status Signal is "Finishing" and New OCPP Status Signal is "Available" - We may need to modify this rule.

4. Invoice will be generated on when:
   - When idle fee is update to "Completed" for Idle Enabled Charger.

5. Idle fee will not be applicable to invoice on when:
   - There is breakdown (Charger went offline) for N seconds (Default: 300 Seconds)
   - There is "Faulted" OCPP Signal (StatusNotification) during charging session ongoing
   - The Idle Duration is <= Idle Grace Period (Default: 900 Seconds)
