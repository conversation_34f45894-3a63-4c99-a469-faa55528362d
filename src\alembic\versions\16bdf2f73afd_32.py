"""32

Revision ID: 16bdf2f73afd
Revises: f15b2fba5258
Create Date: 2023-04-06 16:55:39.720306

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '16bdf2f73afd'
down_revision = 'f15b2fba5258'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('unique_credit_card', table_name='main_credit_card')
    op.create_index('unique_credit_card', 'main_credit_card', ['member_id', 'last_four_digit', 'type', 'currency', 'brand', 'is_deleted'], unique=True, postgresql_where=sa.text('NOT is_deleted'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('unique_credit_card', table_name='main_credit_card', postgresql_where=sa.text('NOT is_deleted'))
    op.create_index('unique_credit_card', 'main_credit_card', ['member_id', 'last_four_digit', 'type', 'brand', 'is_deleted'], unique=False)
    # ### end Alembic commands ###
