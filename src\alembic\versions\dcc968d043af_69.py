"""69

Revision ID: dcc968d043af
Revises: bf963a62059d
Create Date: 2024-01-30 22:51:38.800688

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'dcc968d043af'
down_revision = 'bf963a62059d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('main_emaid_member_id_fkey', 'main_emaid', type_='foreignkey')
    op.drop_column('main_emaid', 'member_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_emaid', sa.Column('member_id', postgresql.UUID(), autoincrement=False, nullable=True))
    op.create_foreign_key('main_emaid_member_id_fkey', 'main_emaid', 'main_membership', ['member_id'], ['id'])
    # ### end Alembic commands ###
