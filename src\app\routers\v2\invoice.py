import logging
import urllib
import json
from datetime import datetime, timedelta, timezone
from urllib.parse import urlencode
import curlify
import requests

from fastapi import APIRouter, HTTPException, Depends, Request, status
from fastapi_pagination import Params
from jose import jwt

from twilio.rest import Client

from app import schema, models
from app import settings, crud, exceptions
from app.crud import get_charging_session_bill_by_charging_session, get_user_outstanding_bills, \
    create_dunning_repayment_log, get_payment_request_by_charging_session_bill_id, get_outlier_by_charging_session_id
from app.database import create_session, SessionLocal
from app.permissions import permission, x_api_key
from app.constants import (
    PAYMENT_DIRECT_API_URL,
    SG_PAYMENT_PRE_AUTH_REQUEST_API_URL,
    BN_PAYMENT_PRE_AUTH_REQUEST_API_URL,
    KH_PAYMENT_PRE_AUTH_REQUEST_API_URL,
    PAYMENT_PRE_AUTH_REQUEST_API_URL,
    SG_PAYMENT_DIRECT_API_URL,
    BN_PAYMENT_DIRECT_API_URL,
    KH_PAYMENT_DIRECT_API_URL
)
from app.utils import charging_history_merge_operator_details, decode_auth_token_from_headers, \
    generate_charger_header, send_request, \
    get_charge_history_record, RouteErrorHandler, map_charging_session_id_tag_with_member, safe_get, \
    calculate_md5_hash, MAIN_URL_PREFIX, get_pre_auth_url_and_value_from_object

from app.e_invoice_utils import validate_e_invoice_status_for_mobile_display

logger = logging.getLogger(__name__)

CHARGER_URL_PREFIX_V2 = f'{settings.CHARGER_DOMAIN}/{settings.CHARGER_ROOT_PATH_V2}'
ROOT_PATH = settings.MAIN_ROOT_PATH

twilio_client = Client(settings.TWILIO['ACCOUNT_SID'], settings.TWILIO['AUTH_TOKEN'])

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/invoice",
    tags=['v2 invoice'],
    dependencies=[Depends(permission), Depends(x_api_key)],
    route_class=RouteErrorHandler

)


@router.get("/{charging_session_id}",
            status_code=status.HTTP_200_OK, response_model=schema.InvoiceResponse)
async def get_invoice(request: Request, charging_session_id: str,
                      dbsession: SessionLocal = Depends(create_session)) -> schema.InvoiceResponse:
    """
    Get a Invoice

    :param str charging_session_id: Target Invoice ID
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    logger.debug('header for request to charger service: %s', headers)

    try:
        invoice = await crud.get_invoice(dbsession, charging_session_id, headers)
        return invoice
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())


@router.get("/history/charge_histories", status_code=status.HTTP_200_OK, tags=['v2 charging', ])
async def get_charging_histories(request: Request, params: Params = Depends(),
                                 dbsession: SessionLocal = Depends(create_session)):
    """
    Get Member's Charge History List
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    db_membership = dbsession.query(models.Membership).filter(
        models.Membership.id == membership_id
    ).first()
    logger.debug('header for request to charger service: %s', headers)
    ruby_user_ids = [db_membership.ruby_user_id]

    path = 'charging/charge_histories'

    query_params = request.query_params
    query_params = dict(query_params)

    if len(ruby_user_ids) != 0 and ruby_user_ids[0] is not None:
        query_params['ruby_user_ids'] = ruby_user_ids

    url = f'{CHARGER_URL_PREFIX_V2}/{path}'

    response = await send_request('GET', url, headers=headers, query_params=query_params)
    data = response.json()

    if response.status_code == 200:
        charging_session = []
        for charger_charging_session in data['items']:
            charging_record = get_charge_history_record(charger_charging_session, dbsession)
            charging_record = map_charging_session_id_tag_with_member(dbsession, charging_record)

            charging_session.append(charging_record)
        data['items'] = charging_session
        return data

    raise HTTPException(400, 'No Record Found')


@router.get("/ocpi/history/charge_histories", status_code=status.HTTP_200_OK, tags=['v2 charging', ])
async def ocpi_get_charging_histories(request: Request,  # pylint: disable=too-many-branches  # noqa : MC001
                                      failed_payments: bool = False,
                                      params: Params = Depends(), dbsession: SessionLocal = Depends(create_session)):
    """
    Get Member's Charge History List
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    db_membership = dbsession.query(models.Membership).filter(
        models.Membership.id == membership_id
    ).first()
    logger.debug('header for request to charger service: %s', headers)
    ruby_user_ids = [db_membership.ruby_user_id]

    path = 'charging/ocpi/charge_histories'

    query_params = request.query_params
    query_params = dict(query_params)
    if failed_payments:
        outstanding_bill_list = get_user_outstanding_bills(dbsession, membership_id)
        outstanding_bill_ids = [str(outstanding_bill[0]) for outstanding_bill in outstanding_bill_list]
        headers['outstanding_session_ids'] = json.dumps(list(set(outstanding_bill_ids)))

    if len(ruby_user_ids) != 0 and ruby_user_ids[0] is not None:
        query_params['ruby_user_ids'] = ruby_user_ids

    url = f'{CHARGER_URL_PREFIX_V2}/{path}'

    response = await send_request('GET', url, headers=headers, query_params=query_params)
    data = response.json()

    if response.status_code == 200:
        charging_session = []
        for charger_charging_session in data['items']:
            estimated_cost = charger_charging_session.get('estimated_cost', None)
            if estimated_cost:
                tax_rate = estimated_cost.get('tax_rate', None)
                if tax_rate is not None:
                    charger_charging_session['estimated_cost']['tax_rate'] = float(tax_rate)
            operators = []
            operators_by_name = []
            invoice_generated = True
            for item in data['items']:
                operator_id = safe_get(item, 'meta', 'operator', 'id')
                operator_name = safe_get(item, 'meta', 'location', 'ocpi_partner_operator', 'name')
                if operator_name is not None:
                    operators_by_name.append(operator_name)
                if operator_id is not None:
                    operators.append(operator_id)

            charging_session_id = safe_get(charger_charging_session, 'id')
            charging_session_bill = get_charging_session_bill_by_charging_session(dbsession, charging_session_id)
            if not charging_session_bill:
                invoice_generated = False

            charger_charging_session['e_invoice'] = None
            charger_charging_session['refund_exists'] = False
            charger_charging_session['outlier_status'] = None

            try:
                if charging_session_bill:
                    charging_session_bill = validate_e_invoice_status_for_mobile_display(dbsession,
                                                                                         charging_session_bill,
                                                                                         update_data=False)
                    charger_charging_session['e_invoice'] = charging_session_bill.e_invoice
            except Exception as e:  # pylint: disable=broad-except
                logger.error("Error on e-invoice generation with error as %s", str(e))

            try:
                if charging_session_bill:
                    db_pr = get_payment_request_by_charging_session_bill_id(dbsession, charging_session_bill.id)
                    if db_pr and db_pr.payment_refund:
                        charger_charging_session['refund_exists'] = True
            except Exception as e:  # pylint: disable=broad-except
                logger.error("Error on payment refund mapping with error as %s", str(e))

            try:
                outlier_obj = get_outlier_by_charging_session_id(dbsession, charging_session_id)
                if outlier_obj is not None:
                    charger_charging_session['outlier_status'] = outlier_obj.status
            except Exception as e:  # pylint: disable=broad-except
                logger.error("Error on outlier status mapping with error as %s", str(e))

            # Add the invoice_generated field to the item
            charger_charging_session['invoice_generated'] = invoice_generated

            charging_record = get_charge_history_record(charger_charging_session, dbsession)
            charging_record = map_charging_session_id_tag_with_member(dbsession, charging_record)

            operators = crud.get_external_organization_by_operator_list(dbsession, operators)
            operators = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in operators]

            operators_dict = {}
            for l2 in operators:
                operator_details = l2.dict()
                operators_dict[str(operator_details['id'])] = {k: v for k, v in operator_details.items() if k != 'id'}

            if len(operators_by_name) > 0:
                operators_by_name = crud.get_external_organization_by_operator_name(dbsession,
                                                                                    operators_by_name)
                operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                                     operators_by_name]
                for l2 in operators_by_name:
                    operator_details = l2.dict()
                    operators_dict[str(operator_details['original_name'])] = {k: v for k, v in
                                                                              operator_details.items()
                                                                              if k != 'original_name'}

            response_json_merged = charging_history_merge_operator_details(charging_record, operators_dict)
            charging_session.append(response_json_merged)
        data['items'] = charging_session
        return data

    raise HTTPException(400, 'No Record Found')


@router.get("/ocpi/history/charge_histories/{charging_session_id}", status_code=status.HTTP_200_OK,
            tags=['v2 charging', ])
async def ocpi_get_charging_history(request: Request,  # pylint: disable=too-many-branches  # noqa : MC001
                                    charging_session_id: str,
                                    dbsession: SessionLocal = Depends(create_session)):
    """
    Get Member's Charge History
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    headers = generate_charger_header(dbsession, membership_id)
    db_membership = dbsession.query(models.Membership).filter(
        models.Membership.id == membership_id
    ).first()
    logger.debug('header for request to charger service: %s', headers)
    ruby_user_ids = [db_membership.ruby_user_id]

    path = f'charging/ocpi/charge_histories/{charging_session_id}'

    query_params = request.query_params
    query_params = dict(query_params)

    if len(ruby_user_ids) != 0 and ruby_user_ids[0] is not None:
        query_params['ruby_user_ids'] = ruby_user_ids

    url = f'{CHARGER_URL_PREFIX_V2}/{path}'
    response = await send_request('GET', url, headers=headers, query_params=query_params)
    charger_charging_session = response.json()

    if not charger_charging_session:
        raise HTTPException(400, 'No Record Found')

    estimated_cost = charger_charging_session.get('estimated_cost', None)
    if estimated_cost:
        tax_rate = estimated_cost.get('tax_rate', None)
        if tax_rate is not None:
            charger_charging_session['estimated_cost']['tax_rate'] = float(tax_rate)
    operators = []
    operators_by_name = []
    invoice_generated = True
    operator_id = safe_get(charger_charging_session, 'meta', 'operator', 'id')
    operator_name = safe_get(charger_charging_session, 'meta', 'location', 'ocpi_partner_operator', 'name')
    if operator_name is not None:
        operators_by_name.append(operator_name)
    if operator_id is not None:
        operators.append(operator_id)

    charging_session_id = safe_get(charger_charging_session, 'id')
    charging_session_bill = get_charging_session_bill_by_charging_session(dbsession, charging_session_id)
    if not charging_session_bill:
        invoice_generated = False

    charger_charging_session['e_invoice'] = None
    charger_charging_session['refund_exists'] = False
    charger_charging_session['outlier_status'] = None

    try:
        if charging_session_bill:
            charging_session_bill = validate_e_invoice_status_for_mobile_display(dbsession,
                                                                                 charging_session_bill,
                                                                                 update_data=False)
            charger_charging_session['e_invoice'] = charging_session_bill.e_invoice
    except Exception as e:  # pylint: disable=broad-except
        logger.error("Error on e-invoice generation with error as %s", str(e))

    try:
        if charging_session_bill:
            db_pr = get_payment_request_by_charging_session_bill_id(dbsession, charging_session_bill.id)
            if db_pr and db_pr.payment_refund:
                charger_charging_session['refund_exists'] = True
    except Exception as e:  # pylint: disable=broad-except
        logger.error("Error on payment refund mapping with error as %s", str(e))

    try:
        outlier_obj = get_outlier_by_charging_session_id(dbsession, charging_session_id)
        if outlier_obj is not None:
            charger_charging_session['outlier_status'] = outlier_obj.status
    except Exception as e:  # pylint: disable=broad-except
        logger.error("Error on outlier status mapping with error as %s", str(e))

    # Add the invoice_generated field to the item
    charger_charging_session['invoice_generated'] = invoice_generated

    charging_record = get_charge_history_record(charger_charging_session, dbsession)
    charging_record = map_charging_session_id_tag_with_member(dbsession, charging_record)

    operators = crud.get_external_organization_by_operator_list(dbsession, operators)
    operators = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in operators]

    operators_dict = {}
    for l2 in operators:
        operator_details = l2.dict()
        operators_dict[str(operator_details['id'])] = {k: v for k, v in operator_details.items() if k != 'id'}

    if len(operators_by_name) > 0:
        operators_by_name = crud.get_external_organization_by_operator_name(dbsession,
                                                                            operators_by_name)
        operators_by_name = [schema.ExternalOrganizationResponse.from_orm(operator) for operator in
                             operators_by_name]
        for l2 in operators_by_name:
            operator_details = l2.dict()
            operators_dict[str(operator_details['original_name'])] = {k: v for k, v in
                                                                      operator_details.items()
                                                                      if k != 'original_name'}

    response_json_merged = charging_history_merge_operator_details(charging_record, operators_dict)
    return response_json_merged


@router.get("/ocpi/pdf/url/{charging_session_id}", status_code=status.HTTP_200_OK)
async def get_invoice_pdf(request: Request, charging_session_id: str,  # noqa: MC0001
                          dbsession: SessionLocal = Depends(create_session)):
    """
    return Invoice PDF URL
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    if not membership_id:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized")

    print(settings.JWT_REPORT_SECRET)

    token = jwt.encode(
        {
            "exp": datetime.now(tz=timezone.utc) + timedelta(days=1),
            "charging_session_id": str(charging_session_id),
            "membership_id": f'{membership_id}',
        },
        settings.JWT_REPORT_SECRET,
        algorithm=schema.JWT_ALGORITHM,
    )

    # Encode the token as a query parameter
    query_string = urlencode({'key': token, 'generate_pdf': True})
    url = f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/invoice/ocpi/pdf/download/?{query_string}"

    return url


@router.post('/payment/{charging_session_id}', status_code=status.HTTP_200_OK)  # noqa: MC0001
async def outstanding_session_payment(request: Request,  # noqa: MC0001
                                      charging_session_id: str,
                                      new_card: bool = False,
                                      dbsession: SessionLocal = Depends(create_session)):  # noqa: MC0001
    """
    Endpoint for user to pay outstanding bills
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    db_charging_session_bill = crud.get_charging_session_bill_by_charging_session(dbsession, charging_session_id)
    if not db_charging_session_bill:
        raise HTTPException(400, 'ChargingSessionBill object does not exist.')
    try:
        db_payment_request = crud.get_payment_request_by_charging_session_bill_id(dbsession,
                                                                                  db_charging_session_bill.id)
        pay_via_preauth_and_recurring = db_payment_request.pay_via_preauth_and_recurring is True
    except exceptions.ApolloObjectDoesNotExist as e:
        raise HTTPException(400, e.__str__())

    if db_charging_session_bill.status not in [schema.ChargingSessionBillStatus.failed,
                                               schema.ChargingSessionBillStatus.rejected] and \
            not pay_via_preauth_and_recurring:
        raise HTTPException(400, 'Payment not allowed for this charging session.')

    if db_charging_session_bill.status in [schema.ChargingSessionBillStatus.free,
                                           schema.ChargingSessionBillStatus.paid]:
        raise HTTPException(400, 'Payment already fullfilled for this charging session.')

    if pay_via_preauth_and_recurring and db_payment_request.pre_auth_outstanding_capture_status not in [
            schema.PaymentRequestStatus.failed,
            schema.PaymentRequestStatus.rejected
    ]:
        raise HTTPException(400, 'Payment not allowed for this charging session.')

    currency = db_payment_request.currency

    amount = db_payment_request.amount if not pay_via_preauth_and_recurring else \
        db_payment_request.pre_auth_outstanding_amount
    invoice_number = db_payment_request.invoice_number if not pay_via_preauth_and_recurring else \
        db_payment_request.pre_auth_outstanding_order_id

    # to use new card to pay
    if new_card:
        merchant_id = None
        payment_url = None
        verify_key = None
        if currency == schema.Currency.myr:
            payment_url = PAYMENT_DIRECT_API_URL
            merchant_id = settings.MERCHANT_ID
            verify_key = settings.VERIFY_KEY
        elif currency == schema.Currency.sgd:
            payment_url = SG_PAYMENT_DIRECT_API_URL
            merchant_id = settings.SG_MERCHANT_ID
            verify_key = settings.SG_VERIFY_KEY
        elif currency == schema.Currency.bnd:
            payment_url = BN_PAYMENT_DIRECT_API_URL
            merchant_id = settings.BN_MERCHANT_ID
            verify_key = settings.BN_VERIFY_KEY
        elif currency == schema.Currency.khr:
            payment_url = KH_PAYMENT_DIRECT_API_URL
            merchant_id = settings.KH_MERCHANT_ID
            verify_key = settings.KH_VERIFY_KEY

        vkey = calculate_md5_hash(f'{amount}{merchant_id}'
                                  f'{invoice_number}{verify_key}')
        params = {
            'amount': amount,
            'orderid': invoice_number,
            'bill_name': f'{db_payment_request.member.first_name} '
                         f'{db_payment_request.member.last_name}',
            'bill_email': db_payment_request.member.user.email,
            'bill_mobile': db_payment_request.member.user.phone_number,
            'bill_desc': db_payment_request.billing_description,
            'vcode': vkey,
            'channel': 'creditAN',
            'username': merchant_id,
            'app_name': 'Apollo',
            'hscl': 0,
            'guest_checkout': 1,
            'currency': currency,
        }
        if currency in [schema.Currency.sgd]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        elif currency in [schema.Currency.bnd]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        elif currency in [schema.Currency.khr]:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
        else:
            params['returnurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-return-url'
            params['callbackurl'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-callback'

        if currency in [schema.Currency.sgd, schema.Currency.bnd, schema.Currency.khr]:
            params['channel'] = 'creditAI'  # SGD and BND use credtiAI

        url = f'{payment_url}?' + urllib.parse.urlencode(params)

        response = {
            "action_url": url,
            "action_method": "GET",
            "action_data": []
        }
        repayment_log = schema.DunningRepaymentLogCreate(
            member_id=membership_id,
            bill_id=db_charging_session_bill.id,
            charging_session_id=charging_session_id,
            payment_amount=amount,
            payment_payload=params,
            payment_response=response
        )
        create_dunning_repayment_log(dbsession, repayment_log)

        return response

    # to use existing card to pay
    channel = 'CREDITAN'
    if currency in [schema.Currency.sgd, schema.Currency.bnd, schema.Currency.khr]:
        channel = 'CREDITAI'  # SGD and BND use credtiAI

    cc = crud.get_member_primary_cc(dbsession, membership_id, currency)
    if not cc:
        cc = crud.get_member_primary_cc(dbsession, membership_id)
        if not cc:
            raise HTTPException(403, 'No CC')

    if cc.payment_gateway == schema.CreditCardPaymentGateway.fiuu:
        if currency == 'SGD':
            vkey = calculate_md5_hash(
                f'{amount}{settings.SG_MERCHANT_ID}{invoice_number}'
                f'{settings.SG_VERIFY_KEY}')

        elif currency == 'BND':
            vkey = calculate_md5_hash(
                f'{amount}{settings.BN_MERCHANT_ID}{invoice_number}'
                f'{settings.BN_VERIFY_KEY}')

        elif currency == 'KHR':
            vkey = calculate_md5_hash(
                f'{amount}{settings.KH_MERCHANT_ID}{invoice_number}'
                f'{settings.KH_VERIFY_KEY}')

        else:
            vkey = calculate_md5_hash(
                f'{amount}{settings.MERCHANT_ID}{invoice_number}'
                f'{settings.VERIFY_KEY}')

        payload = {
            'MerchantID': settings.MERCHANT_ID,
            'ReferenceNo': invoice_number,
            'TxnType': 'SALS',
            'TxnChannel': channel,
            'TxnCurrency': currency,
            'TxnAmount': amount,
            'CC_TOKEN': cc.token,
            'CustName': cc.bill_name,
            'CustEmail': cc.bill_email,
            'CustContact': cc.bill_mobile,
            'CustDesc': db_payment_request.billing_description,
            'Signature': vkey,
            'ReturnURL': f'{MAIN_URL_PREFIX}/api/v1/csms/payment/pre-auth/payment-return-url',
            'NotificationURL': f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-callback',
            'CallbackURL': f'{MAIN_URL_PREFIX}/api/v1/csms/payment/payment-callback',
            'app_name': 'Apollo',
            'mpstokenstatus': 2,
            'non_3DS': 0,
        }
        response = None
        if currency == 'SGD':
            payload['MerchantID'] = settings.SG_MERCHANT_ID
            payload['CallbackURL'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
            payload['NotificationURL'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
            response = requests.request("POST", SG_PAYMENT_PRE_AUTH_REQUEST_API_URL, data=payload)
        elif currency == 'BND':
            payload['MerchantID'] = settings.BN_MERCHANT_ID
            payload['CallbackURL'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
            payload['NotificationURL'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
            response = requests.request("POST", BN_PAYMENT_PRE_AUTH_REQUEST_API_URL, data=payload)

        elif currency == 'KHR':
            payload['MerchantID'] = settings.KH_MERCHANT_ID
            payload['CallbackURL'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
            payload['NotificationURL'] = f'{MAIN_URL_PREFIX}/api/v1/csms/payment/sg/payment-callback'
            response = requests.request("POST", KH_PAYMENT_PRE_AUTH_REQUEST_API_URL, data=payload)
        else:
            response = requests.request("POST", PAYMENT_PRE_AUTH_REQUEST_API_URL, data=payload)
        print(curlify.to_curl(response.request))
        response_data = response.json()
        is_succeed = response_data.get('status', None)
        if is_succeed is False:
            raise HTTPException(400,
                                f"Repayment failed due to: "
                                f"{response_data['error_code']} - {response_data['error_desc']}")
        action_url, action_method, action_data = get_pre_auth_url_and_value_from_object(response_data)

        response = {
            "action_url": action_url,
            "action_method": action_method,
            "action_data": action_data
        }

        repayment_log = schema.DunningRepaymentLogCreate(
            member_id=membership_id,
            bill_id=db_charging_session_bill.id,
            charging_session_id=charging_session_id,
            payment_amount=amount,
            payment_payload=payload,
            payment_response=response
        )
        create_dunning_repayment_log(dbsession, repayment_log)
        return response

    raise HTTPException(403, 'Cybersource currently not supported')


@router.get("/pdf/refund/url/{payment_refund_id}", status_code=status.HTTP_200_OK)
async def get_invoice_pdf(request: Request, payment_refund_id: str,  # noqa: MC0001
                          dbsession: SessionLocal = Depends(create_session)):
    """
    return Refund Credit Note PDF URL
    """
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')

    if not membership_id:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized")

    token = jwt.encode(
        {
            "exp": datetime.now(tz=timezone.utc) + timedelta(days=1),
            "payment_refund_id": str(payment_refund_id),
            "membership_id": f'{membership_id}',
        },
        settings.JWT_REPORT_SECRET,
        algorithm=schema.JWT_ALGORITHM,
    )

    # Encode the token as a query parameter
    query_string = urlencode({'key': token, 'generate_pdf': True})
    url = (f"{settings.APOLLO_MAIN_DOMAIN}/{settings.MAIN_ROOT_PATH}/api/v1/invoice/pdf/refund/download/"
           f"?{query_string}")

    return url
