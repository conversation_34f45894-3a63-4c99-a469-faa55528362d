"""106

Revision ID: dedd10553ac8
Revises: a15fd74d0dd2
Create Date: 2024-06-10 13:45:22.248155

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'dedd10553ac8'
down_revision = 'a15fd74d0dd2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_charging_session_bill', sa.Column('hogging_fee', sa.Numeric(scale=2), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_charging_session_bill', 'hogging_fee')
    # ### end Alembic commands ###
