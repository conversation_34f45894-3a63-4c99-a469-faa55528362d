"""131

Revision ID: c6fdd5e9e434
Revises: 50b438a03081
Create Date: 2024-10-28 20:45:13.938259

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c6fdd5e9e434'
down_revision = '50b438a03081'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_charging_session_bill', sa.Column('preferred_payment_method', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_charging_session_bill', 'preferred_payment_method')

    # ### end Alembic commands ###
