import logging

from fastapi import APIRouter, Depends, HTTPException
from twilio.rest import Client

from app import settings, schema, crud, exceptions
from app.database import create_session, SessionLocal
from app.permissions import x_api_key
from app.schema import VerificationMethodEnum
from app.settings import APOL<PERSON><PERSON>_MAIN_DOMAIN, MAIN_ROOT_PATH
from app.utils import (
    RouteErrorHandler,
)

csms_path = f"{APOLLO_MAIN_DOMAIN}/{MAIN_ROOT_PATH}/api/v1/csms"

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

twilio_client = Client(settings.TWILIO['ACCOUNT_SID'], settings.TWILIO['AUTH_TOKEN'])

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/guest",
    tags=['v2 auth', ],
    route_class=RouteErrorHandler,
    dependencies=[Depends(x_api_key)],
)


@router.post("/enroll", tags=['auth', ])
async def guest_enroll(data: schema.GuestSignupEnroll, dbsession: SessionLocal = Depends(create_session)):  # noqa
    """
    Sign-up endpoint for a normal user using their phone and password
    """

    user_data = data.dict()
    user_data['user_type'] = schema.UserType.regular
    vehicle_model = user_data.pop('vehicle_model', None)

    # This wont be be set as Guest Customer, as pop will always be valid, it wont check for null,
    # the update is in Schema (GuestSignupEnroll)
    first_name = user_data.pop('first_name', 'Guest')
    last_name = user_data.pop('last_name', 'Customer')
    try:
        db_user = crud.create_user_v2(dbsession, user_data)
    except exceptions.ApolloDuplicateMembershipError:
        db_user = crud.get_user_by_guest_app_id_and_organization_id(dbsession,
                                                                    user_data['guest_app_id'],
                                                                    user_data['organization_id'])
    try:
        # Create User's membership
        membership_data = schema.GuestMembershipEnroll(
            organization_id=user_data['organization_id'],
            user_id_tag='GS_' + crud.create_user_id_tag(dbsession),
            user_id=str(db_user.id),
            vehicle_model=vehicle_model,
            last_name=last_name,
            first_name=first_name,
            allow_marketing=False,
        )
        db_member = crud.create_guest_membership(dbsession, membership_data)
        # Attach default role to the membership
        regular_role = crud.get_regular_role(dbsession)
        db_member.roles.append(regular_role)

        # Create User's Extended Membership Profile
        extended_membership_data = schema.MembershipExtended(
            membership_id=str(db_member.id),
            verification_method=VerificationMethodEnum.email,
            email_verified=True,
        )

        _ = crud.create_user_membership_extended(dbsession, extended_membership_data)

        membership_dunning_data = schema.InitiateMembershipDunning(
            membership_id=str(db_member.id),
        )
        crud.create_membership_dunning(dbsession, membership_dunning_data)

        user = crud.get_user_by_guest_app_id_and_organization_id(dbsession, data.guest_app_id, data.organization_id)
        user = schema.UserAuth.from_orm(user)
        return user.generate_token_with_membership(str(db_member.id))

    except exceptions.ApolloDuplicateMembershipError:
        user = crud.get_user_by_guest_app_id_and_organization_id(dbsession, data.guest_app_id, data.organization_id)
        membership = crud.get_user_membership_by_organization_id(dbsession, str(user.id),
                                                                 str(data.organization_id))
        user = schema.UserAuth.from_orm(user)
        return user.generate_token_with_membership(str(membership.id))

    except Exception as e:  # noqa
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/logon", response_model=schema.TokenResponseWithMemberID, tags=['auth', ])
async def guest_logon(data: schema.GuestLogon,
                      dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-in endpoint for a normal user using their phone and password
    """
    try:
        user = crud.get_user_by_guest_app_id_and_organization_id(dbsession, data.guest_app_id, data.organization_id)
        membership = crud.get_user_membership_by_organization_id(dbsession, str(user.id),
                                                                 str(data.organization_id))
        membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, str(membership.id))
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=4040, detail="User not found.")

    if not membership_extended.email_verified and not membership_extended.phone_verified:
        raise HTTPException(status_code=4040, detail="User not found.")

    # Get user's membership to organization
    try:
        membership = crud.get_user_membership_by_organization_id(
            dbsession,
            str(user.id),
            data.organization_id
        )
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=404, detail='User not found.')

    user = schema.UserAuth.from_orm(user)
    return user.generate_token_with_membership(str(membership.id))
