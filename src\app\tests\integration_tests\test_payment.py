from re import M
from urllib.parse import urlencode
import uuid
import json
from datetime import datetime, timedelta, timezone
from unittest.mock import patch, AsyncMock, Mock, ANY
from contextlib import contextmanager
import urllib
from starlette.datastructures import QueryParams

import jwt
import pytest
from bs4 import BeautifulSoup
from fastapi.testclient import TestClient

from app import settings, models, schema
from app.main import app, ROOT_PATH
from app.schema import PaymentRequestCreditCard, PaymentRequestReason
from app.utils import calculate_md5_hash, CHARGER_URL_PREFIX
from app.tests.factories import (OperatorFactory, UserFactory, OrganizationFactory, CreditCardFactory, IDTagFactory,
                                 MembershipFactory, ResourceServerFactory, ResourceFactory, RoleFactory,
                                 PaymentRequestFactory, PromoCodeFactory, OrganizationPromoCodeFactory,
                                 ChargingSessionBillFactory, PromoCodeUsageFactory, BlacklistCreditCardFactory,
                                 PreAuthPaymentFactory)
from app.tests.mocks.async_client import (
    MockAsyncClientGeneratorPaymentIPN,
    MockAsyncClientGeneratorChargingSessionUniqueIDTag,
    MockAsyncClientGeneratorChargingSession,
    MockResponse,
    MockAsyncClientGeneratorMonthlyRevenue,
)
from app.database import SessionLocal, create_session, Base, engine
from app.constants import PAYMENT_DIRECT_API_URL
from app.constants import PAYMENT_RECURRING_API_URL

CHARGING_SESSION_ID = str(uuid.uuid4())
CHARGE_POINT_ID = str(uuid.uuid4())
OPERATOR_ID = str(uuid.uuid4())

LOCATION = {
    'id': 'b27b2389-a02d-4325-98a3-faaec01b0bc1',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'name': 'name',
    'latitude': 1.1,
    'longitude': 2.2,
    'address': 'address',
    'city': 'city',
    'state': 'state',
    'country': 'country',
}

CHARGE_POINT = {
    'id': CHARGE_POINT_ID,
    'serial_number': 'test_cp',
    'status': 'Available',
    'is_private': False,
    'is_connected': True,
    'operator_id': OPERATOR_ID,
    'location': LOCATION
}

CONNECTOR_TYPE = {
    'id': 'b27b2389-a02d-4325-98a3-faaec01b0bc1',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'name': 'test_connector_type',
    'kind': 'test_connector_type_name',
}

CONNECTOR = {
    'id': '912388d7-c48b-4de8-a826-c398bdae049b',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'number': 1,
    'info': {},
    'billing_type': schema.BillingType.time,
    'billing_unit_fee': 1.0,
    'billing_cycle': 1,
    'billing_currency': 'MYR',
    'connection_fee': 0,
    'status': 'Available',
    'ocpp_status': 'Available',
    'charge_point': CHARGE_POINT,
    'connector_type': CONNECTOR_TYPE
}

CHARGING_SESSION = {
    'id': CHARGING_SESSION_ID,
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'status': 'Done',
    'meter_start': 1100,
    'meter_stop': 1120,
    'meter_values': [],
    'session_start': '2022-01-01 00:00:00',
    'session_end': '2022-01-01 00:30:00',
    'info': {},
    'id_tag': 'test_id_tag',
    'transaction_id': 10000,
    'reservation_id': None,
    'charge_point_connector': CONNECTOR
}


class MockAsyncClientChargingSession:

    async def get(url, *args, **kwargs):
        return MockResponse(CHARGING_SESSION, 200)


client = TestClient(app)


def create_res_server_and_roles(db: SessionLocal, mem: MembershipFactory, organization_id: str,
                                res_path: str, res_scope: str, root_path: str = 'apollo-main'):
    # setup resource for testserver endpoint
    rs = ResourceServerFactory(root_path=root_path)
    res = ResourceFactory(
        path=res_path,
        scope=res_scope,
        resourceserver=rs,
        require_auth_token=False
    )

    role = RoleFactory(organization_id=organization_id)

    # attach resource to created role for staff
    role.resources.append(res)
    db.commit()

    # assign role to staff
    mem.roles.append(role)
    db.commit()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def override_create_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceServerFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ResourceFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RoleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        IDTagFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PaymentRequestFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PreAuthPaymentFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PromoCodeFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationPromoCodeFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ChargingSessionBillFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PromoCodeUsageFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        BlacklistCreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        yield db
    finally:
        db.close()


app.dependency_overrides[create_session] = override_create_session

CONNECTOR_ID = str(uuid.uuid4())
CHARGE_POINT_ID = str(uuid.uuid4())
OPERATOR_ID = str(uuid.uuid4())
PAYMENT_BASE_URL = f'{ROOT_PATH}/api/v1/csms/payment'
PAYMENT_SCHEMA = {
    'nbcb': 1,
    'amount': 10.0,
    'tranID': 'transactionid',
    'domain': 'domain',
    'appcode': 'appcode',
    'error_code': 'error_code',
    'error_desc': 'error_desc',
    'currency': 'MYR',
    'paydate': '2022-01-01 00:00:00',
    'channel': 'channel',
    'extraP': json.dumps({
        'ccbrand': 'Visa',
        'cclast4': '1111',
        'cctype': schema.CreditCardType.credit,
        'token': 'ajhfdiou0q923u4',
    }),
}
LOCATION = {
    'id': 'b27b2389-a02d-4325-98a3-faaec01b0bc1',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'name': 'name',
    'latitude': 1.1,
    'longitude': 2.2,
    'address': 'address',
    'city': 'city',
    'state': 'state',
    'country': 'country',
}

CHARGE_POINT = {
    'id': CHARGE_POINT_ID,
    'serial_number': 'test_cp',
    'status': 'Available',
    'is_private': False,
    'is_connected': True,
    'operator_id': OPERATOR_ID,
    'location': LOCATION
}

CONNECTOR_TYPE = {
    'id': 'b27b2389-a02d-4325-98a3-faaec01b0bc1',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'name': 'test_connector_type',
    'kind': 'test_connector_type_name',
}

CONNECTOR = {
    'id': '912388d7-c48b-4de8-a826-c398bdae049b',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'number': 1,
    'info': {},
    'billing_type': schema.BillingType.time,
    'billing_unit_fee': 1.0,
    'billing_cycle': 1,
    'billing_currency': 'MYR',
    'connection_fee': 0,
    'status': 'Available',
    'ocpp_status': 'Available',
    'charge_point': CHARGE_POINT,
    'connector_type': CONNECTOR_TYPE
}

CHARGING_SESSION = {
    'id': CHARGING_SESSION_ID,
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'status': 'Done',
    'meter_start': 1100,
    'meter_stop': 1120,
    'meter_values': [],
    'session_start': '2022-01-01 00:00:00',
    'session_end': '2022-01-01 00:30:00',
    'info': {},
    'id_tag': 'test_id_tag',
    'transaction_id': 10000,
    'reservation_id': None,
    'charge_point_connector': CONNECTOR
}


# @patch('app.routers.payments.httpx.AsyncClient',
#        side_effect=MockAsyncClientGeneratorPaymentIPN)
# def test_successfull_payment_callback(async_client_mock, test_db):
#     with contextmanager(override_create_session)() as db:
#         organization = OrganizationFactory()
#         db.commit()
#         organization_id = str(organization.id)

#         staff = UserFactory(
#             is_verified=True,
#             verification_method='email',
#             is_superuser=True,
#         )
#         db.commit()

#         mem = MembershipFactory(
#             organization_id=organization_id,
#             user_id=f'{staff.id}',
#             membership_type=schema.MembershipType.staff,
#         )
#         db.commit()
#         membership_id = str(mem.id)

#         pr = PaymentRequestFactory(
#             amount='10.00',
#             type=schema.PaymentRequestType.recurring,
#             billing_description='Charging Payment',
#             reason=schema.PaymentRequestReason.payment,
#             connector_id=CONNECTOR_ID,
#             status=schema.PaymentRequestStatus.pending,
#             member_id=membership_id,
#             meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
#         )
#         db.commit()

#         payment = PAYMENT_SCHEMA
#         pre_skey = calculate_md5_hash(
#             f'{payment["tranID"]}{pr.id}{schema.PaymentStatus.successful}'
#             f'{payment["domain"]}{payment["amount"]}{payment["currency"]}'
#         )
#         skey = calculate_md5_hash(
#             f'{payment["paydate"]}{payment["domain"]}'
#             f'{pre_skey}{payment["appcode"]}{settings.SECRET_KEY}'
#         )

#         payment.update(
#             orderid=str(pr.id),
#             status=schema.PaymentStatus.successful,
#             skey=skey
#         )

#         create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-callback',
#                                     'get,patch,post,delete', 'apollo-main')

#         url = f'{PAYMENT_BASE_URL}/payment-callback'
#         response = client.post(url, data=payment)
#         assert response.status_code == 200
#         async_client_mock.assert_called_once()


# def test_failed_payment_callback(test_db):
#     with contextmanager(override_create_session)() as db:
#         organization = OrganizationFactory()
#         db.commit()
#         organization_id = str(organization.id)

#         staff = UserFactory(
#             is_verified=True,
#             verification_method='email',
#             is_superuser=True,
#         )
#         db.commit()

#         mem = MembershipFactory(
#             organization_id=organization_id,
#             user_id=f'{staff.id}',
#             membership_type=schema.MembershipType.staff,
#         )
#         db.commit()
#         membership_id = str(mem.id)

#         pr = PaymentRequestFactory(
#             amount='10.00',
#             type=schema.PaymentRequestType.recurring,
#             billing_description='Charging Payment',
#             reason=schema.PaymentRequestReason.payment,
#             connector_id=CONNECTOR_ID,
#             status=schema.PaymentRequestStatus.pending,
#             member_id=membership_id,
#             meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
#         )
#         db.commit()

#         payment = PAYMENT_SCHEMA
#         pre_skey = calculate_md5_hash(
#             f'transactionid{pr.id}{schema.PaymentStatus.failure}'
#             f'{payment["domain"]}{payment["amount"]}{payment["currency"]}'
#         )
#         skey = calculate_md5_hash(
#             f'{payment["paydate"]}{payment["domain"]}'
#             f'{pre_skey}{payment["appcode"]}{settings.SECRET_KEY}'
#         )

#         payment.update(
#             orderid=str(pr.id),
#             tranID='transactionid',
#             status=schema.PaymentStatus.failure,
#             skey=skey
#         )

#         create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-callback',
#                                     'get,patch,post,delete', 'apollo-main')

#         url = f'{PAYMENT_BASE_URL}/payment-callback'
#         response = client.post(url, data=payment)
#         assert response.status_code == 200
#         query = db.query(models.PaymentRequest) \
#             .filter(models.PaymentRequest.type == schema.PaymentRequestType.direct)
#         assert query.count() == 1

@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_failed_payment_callback_fail(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            organization_id=organization_id,
            is_verified=True,
            verification_method='email',
            is_superuser=True,
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        pr = PaymentRequestCreditCard(currency='MYR').dict()
        pr = PaymentRequestFactory(
            **pr,
            member_id=membership_id
        )
        db.commit()

        payment = PAYMENT_SCHEMA
        payment['error_code'] = ''
        pre_skey = calculate_md5_hash(
            f'transactionid{pr.id}{schema.PaymentStatus.failure}'
            f'{payment["domain"]}{payment["amount"]}{payment["currency"]}'
        )
        skey = calculate_md5_hash(
            f'{payment["paydate"]}{payment["domain"]}'
            f'{pre_skey}{payment["appcode"]}{settings.SECRET_KEY}'
        )

        payment.update(
            orderid=str(pr.id),
            tranID='transactionid',
            status=schema.PaymentStatus.failure,
            skey=skey,
        )

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-callback',
                                    'get,patch,post,delete', 'apollo-main')

        url = f'{PAYMENT_BASE_URL}/payment-callback'
        response = client.post(url, data=payment)
        assert response.status_code == 200
        db.commit()
        query = db.query(models.PaymentRequest) \
            .filter(models.PaymentRequest.type == schema.PaymentRequestType.direct)
        assert query.count() == 1


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_failed_payment_return_url_fail_html(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            organization_id=organization_id,
            is_verified=True,
            verification_method='email',
            is_superuser=True,
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        pr = PaymentRequestCreditCard(currency='MYR').dict()
        pr = PaymentRequestFactory(
            **pr,
            member_id=membership_id
        )
        db.commit()

        payment = PAYMENT_SCHEMA
        pre_skey = calculate_md5_hash(
            f'transactionid{pr.id}{schema.PaymentStatus.failure}'
            f'{payment["domain"]}{payment["amount"]}{payment["currency"]}'
        )
        skey = calculate_md5_hash(
            f'{payment["paydate"]}{payment["domain"]}'
            f'{pre_skey}{payment["appcode"]}{settings.SECRET_KEY}'
        )

        payment.update(
            orderid=str(pr.id),
            tranID='transactionid',
            status=schema.PaymentStatus.failure,
            skey=skey
        )
        payment['error_code'] = ''

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-return-url',
                                    'get,patch,post,delete', 'apollo-main')

        url = f'{PAYMENT_BASE_URL}/payment-return-url'
        response = client.post(url, data=payment)
        assert bool(
            BeautifulSoup(response.content.decode('utf-8'), "html.parser").find(
                text="Sorry, card was not added successfully."))

        assert response.status_code == 200
        db.commit()
        query = db.query(models.PaymentRequest) \
            .filter(models.PaymentRequest.type == schema.PaymentRequestType.direct)
        assert query.count() == 1


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_failed_payment_return_url_fail_html_with_error_code(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            organization_id=organization_id,
            is_verified=True,
            verification_method='email',
            is_superuser=True,
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        pr = PaymentRequestCreditCard(currency='MYR').dict()
        pr = PaymentRequestFactory(
            **pr,
            member_id=membership_id
        )
        db.commit()

        payment = PAYMENT_SCHEMA
        pre_skey = calculate_md5_hash(
            f'transactionid{pr.id}{schema.PaymentStatus.failure}'
            f'{payment["domain"]}{payment["amount"]}{payment["currency"]}'
        )
        skey = calculate_md5_hash(
            f'{payment["paydate"]}{payment["domain"]}'
            f'{pre_skey}{payment["appcode"]}{settings.SECRET_KEY}'
        )

        payment.update(
            orderid=str(pr.id),
            tranID='transactionid',
            status=schema.PaymentStatus.failure,
            skey=skey,
            error_code='error_code'
        )

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-return-url',
                                    'get,patch,post,delete', 'apollo-main')

        url = f'{PAYMENT_BASE_URL}/payment-return-url'
        response = client.post(url, data=payment)
        assert bool(
            BeautifulSoup(response.content.decode('utf-8'), "html.parser").find(
                text="Sorry, card was not added successfully."))
        assert bool(
            BeautifulSoup(response.content.decode('utf-8'), "html.parser").find(
                text="Code: error_code"))
        assert response.status_code == 200
        db.commit()
        query = db.query(models.PaymentRequest) \
            .filter(models.PaymentRequest.type == schema.PaymentRequestType.direct)
        assert query.count() == 1


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_failed_pre_auth_payment_return_url_fail_html_with_error_code(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            organization_id=organization_id,
            is_verified=True,
            verification_method='email',
            is_superuser=True,
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        pr = PaymentRequestCreditCard(currency='MYR', reason=PaymentRequestReason.pre_auth).dict()
        pr = PaymentRequestFactory(
            **pr,
            member_id=membership_id,
        )
        db.commit()

        PreAuthPaymentFactory(
            payment_request_id=pr.id
        )
        db.commit()

        payment = PAYMENT_SCHEMA
        pre_skey = calculate_md5_hash(
            f'transactionid{pr.id}{schema.PaymentStatus.failure}'
            f'{payment["domain"]}{payment["amount"]}{payment["currency"]}'
        )
        skey = calculate_md5_hash(
            f'{payment["paydate"]}{payment["domain"]}'
            f'{pre_skey}{payment["appcode"]}{settings.SECRET_KEY}'
        )

        payment.update(
            orderid=str(pr.id),
            tranID='transactionid',
            status=schema.PaymentStatus.failure,
            skey=skey,
            error_code='error_code'
        )

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/pre-auth/payment-return-url',
                                    'get,patch,post,delete', 'apollo-main')
        query_string = urlencode(payment, doseq=True)

        url = f'{PAYMENT_BASE_URL}/pre-auth/payment-return-url?{query_string}'

        response = client.get(url)
        assert bool(
            BeautifulSoup(response.content.decode('utf-8'), "html.parser").find(
                text="Sorry, pre-authorization charge failed."))
        assert bool(
            BeautifulSoup(response.content.decode('utf-8'), "html.parser").find(
                text="Code: error_code"))
        assert response.status_code == 200
        db.commit()
        query = db.query(models.PaymentRequest) \
            .filter(models.PaymentRequest.type == schema.PaymentRequestType.direct)
        assert query.count() == 1
        query = db.query(models.PreAuthPayment)
        assert query.count() == 1
        assert query[0].is_successful is False


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_success_pre_auth_payment_return_url(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            organization_id=organization_id,
            is_verified=True,
            verification_method='email',
            is_superuser=True,
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        pr = PaymentRequestCreditCard(currency='MYR', reason=PaymentRequestReason.pre_auth).dict()
        pr = PaymentRequestFactory(
            **pr,
            member_id=membership_id,
        )
        db.commit()
        PreAuthPaymentFactory(
            payment_request_id=pr.id
        )
        db.commit()

        payment = PAYMENT_SCHEMA
        pre_skey = calculate_md5_hash(
            f'transactionid{pr.id}{schema.PaymentStatus.successful}'
            f'{payment["domain"]}{payment["amount"]}{payment["currency"]}'
        )
        skey = calculate_md5_hash(
            f'{payment["paydate"]}{payment["domain"]}'
            f'{pre_skey}{payment["appcode"]}{settings.SECRET_KEY}'
        )

        payment.update(
            orderid=str(pr.id),
            tranID='transactionid',
            status=schema.PaymentStatus.successful,
            skey=skey,
            error_code='error_code'
        )

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/pre-auth/payment-return-url',
                                    'get,patch,post,delete', 'apollo-main')
        query_string = urlencode(payment, doseq=True)

        url = f'{PAYMENT_BASE_URL}/pre-auth/payment-return-url?{query_string}'

        response = client.get(url)
        assert bool(
            BeautifulSoup(response.content.decode('utf-8'), "html.parser").find(
                text="Pre-Auth Successful, Press OK to Initiate Charging"))
        assert response.status_code == 200
        db.commit()
        query = db.query(models.PaymentRequest) \
            .filter(models.PaymentRequest.type == schema.PaymentRequestType.direct)
        assert query.count() == 1
        query = db.query(models.PreAuthPayment)
        assert query.count() == 1
        assert query[0].is_successful


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_success_payment_return_url_success(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            organization_id=organization_id,
            is_verified=True,
            verification_method='email',
            is_superuser=True,
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        pr = PaymentRequestCreditCard(currency='MYR').dict()
        pr = PaymentRequestFactory(
            **pr,
            member_id=membership_id
        )
        db.commit()

        payment = PAYMENT_SCHEMA

        pre_skey = calculate_md5_hash(
            f'transactionid{pr.id}{schema.PaymentStatus.successful}'
            f'{payment["domain"]}{payment["amount"]}{payment["currency"]}'
        )
        skey = calculate_md5_hash(
            f'{payment["paydate"]}{payment["domain"]}'
            f'{pre_skey}{payment["appcode"]}{settings.SECRET_KEY}'
        )

        payment.update(
            orderid=str(pr.id),
            tranID='transactionid',
            status=schema.PaymentStatus.successful,
            skey=skey,
            error_code='',
            error_desc='',
        )

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-return-url',
                                    'get,patch,post,delete', 'apollo-main')

        url = f'{PAYMENT_BASE_URL}/payment-return-url'
        response = client.post(url, data=payment)
        assert bool(
            BeautifulSoup(response.content.decode('utf-8'), "html.parser").find(
                text="Credit card has been added successfully"))

        assert response.status_code == 200
        db.commit()
        query = db.query(models.PaymentRequest) \
            .filter(models.PaymentRequest.type == schema.PaymentRequestType.direct)
        assert query.count() == 1


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_failed_payment_return_url_fail_blacklist_html(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            organization_id=organization_id,
            is_verified=True,
            verification_method='email',
            is_superuser=True,
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        cc = CreditCardFactory(member_id=membership_id, primary=False, last_four_digit='1111')
        db.commit()

        BlacklistCreditCardFactory(member_id=membership_id, last_four_digit=cc.last_four_digit)
        db.commit()

        pr = PaymentRequestCreditCard(currency='MYR').dict()
        pr = PaymentRequestFactory(
            **pr,
            member_id=membership_id
        )
        db.commit()

        payment = PAYMENT_SCHEMA
        pre_skey = calculate_md5_hash(
            f'transactionid{pr.id}{schema.PaymentStatus.successful}'
            f'{payment["domain"]}{payment["amount"]}{payment["currency"]}'
        )
        skey = calculate_md5_hash(
            f'{payment["paydate"]}{payment["domain"]}'
            f'{pre_skey}{payment["appcode"]}{settings.SECRET_KEY}'
        )

        payment.update(
            orderid=str(pr.id),
            tranID='transactionid',
            status=schema.PaymentStatus.successful,
            skey=skey
        )

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-return-url',
                                    'get,patch,post,delete', 'apollo-main')

        url = f'{PAYMENT_BASE_URL}/payment-return-url'
        response = client.post(url, data=payment)
        assert bool(
            BeautifulSoup(response.content.decode('utf-8'), "html.parser").find(
                text="Sorry, your card has been blocked by our system due to multiple failed transactions "
                     "reported by our payment system."))
        assert response.status_code == 200
        db.commit()
        query = db.query(models.PaymentRequest) \
            .filter(models.PaymentRequest.type == schema.PaymentRequestType.direct)
        assert query.count() == 1


def test_create_pre_charging_payment_request_succeeds(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/init-charging',
                                    'get,patch,post,delete', 'apollo-main')

        promo_code = PromoCodeFactory(
            start_at=str(datetime.now() - timedelta(days=1)),
            expire_at=str(datetime.now() + timedelta(days=1)),
            type=schema.PromoType.amount,
            amount=5.0,
        )
        db.commit()

        OrganizationPromoCodeFactory(promo_code_id=str(promo_code.id),
                                     organization_id=str(organization.id))
        db.commit()

        pr = {
            'promo_code': promo_code.code,
            'charge_point_id': CHARGE_POINT_ID,
            'connector_id': CONNECTOR_ID
        }

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/init-charging'
        response = client.post(url, json=pr, headers={'authorization': token})
        assert response.status_code == 201
        assert response.json().get('id') is not None


def test_create_pre_charging_payment_request_fails(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/init-charging',
                                    'get,patch,post,delete', 'apollo-main')

        promo_code = PromoCodeFactory(
            start_at=str(datetime.now() - timedelta(days=1)),
            expire_at=str(datetime.now() + timedelta(days=1)),
            type=schema.PromoType.amount,
            amount=5.0,
            limit=1
        )
        db.commit()

        OrganizationPromoCodeFactory(promo_code_id=str(promo_code.id),
                                     organization_id=str(organization.id))
        db.commit()

        PromoCodeUsageFactory(
            amount=10,
            member_id=str(mem.id),
            promo_code_id=str(promo_code.id)
        )
        db.commit()

        PromoCodeUsageFactory(
            amount=5,
            member_id=str(mem.id),
            promo_code_id=str(promo_code.id)
        )
        db.commit()

        pr = {
            'promo_code': promo_code.code,
            'charge_point_id': CHARGE_POINT_ID,
            'connector_id': CONNECTOR_ID
        }

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/init-charging'
        response = client.post(url, json=pr, headers={'authorization': token})
        assert response.status_code == 400
        assert response.json()['detail'] == 'Promotion already used'


def test_create_pre_auth_payment_request(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/deposit',
                                    'get,patch,post,delete', 'apollo-main')

        PromoCodeFactory(
            start_at=str(datetime.now() - timedelta(days=1)),
            expire_at=str(datetime.now() + timedelta(days=1)),
            type=schema.PromoType.amount,
            amount=5.0,
        )
        db.commit()

        pr = {
            'type': schema.PaymentRequestType.direct,
            'amount': '10.0',
            'currency': schema.Currency.myr,
            'billing_description': 'billing_description',
            'update_token': False
        }

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/deposit'
        response = client.post(url, json=pr, headers={'authorization': token})
        assert response.status_code == 307


def test_list_pending_direct_payment_requests(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/pending',
                                    'get,patch,post,delete', 'apollo-main')

        PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charging Payment',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            status=schema.PaymentRequestStatus.pending,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charging Payment',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            status=schema.PaymentRequestStatus.pending,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/pending'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 200
        assert len(response.json()) == 2


def test_get_payment_request(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
                                    'get,patch,post,delete', 'apollo-main')

        pr = PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charging Payment',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            status=schema.PaymentRequestStatus.pending,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/{pr.id}'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 200
        assert response.json()['id'] == str(pr.id)


def test_get_payment_request_not_found(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
                                    'get,patch,post,delete', 'apollo-main')

        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/{uuid.uuid4()}'
        response = client.get(url, headers={'authorization': token})
        assert response.status_code == 400
        assert response.json().get('detail') == 'PaymentRequest object does not exist.'


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorMonthlyRevenue)
def test_get_monthly_revenue_succeeds(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id,
            is_superuser=True,
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
                                    'get,patch,post,delete', 'apollo-main')

        cs_1 = ChargingSessionBillFactory(
            id='1e2dacc7-68d1-4fda-ab80-773f39adb9c0',
            charging_session_id='84197658-55a0-409f-b2c7-f08c0d5b8c26',
            usage_amount=float(10.0),
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid,
        )
        db.commit()

        cs_2 = ChargingSessionBillFactory(
            id='baf705b7-24fc-42db-aaf3-2f5a4111c252',
            charging_session_id='ac1f06d9-38a3-4299-8ec5-2ae68443eb67',
            usage_amount=float(10.0),
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid,
        )
        db.commit()

        PaymentRequestFactory(
            charging_session_bill_id=uuid.UUID('1e2dacc7-68d1-4fda-ab80-773f39adb9c0'),
            amount='10.0',
            status='paid',
            member_id=mem.id
        )

        PaymentRequestFactory(
            charging_session_bill_id=uuid.UUID('baf705b7-24fc-42db-aaf3-2f5a4111c252'),
            amount='10.0',
            status='paid',
            member_id=mem.id
        )
        db.commit()

        year = datetime.now().year
        month = datetime.now().month

        amount = float(cs_1.usage_amount) + float(cs_2.usage_amount)
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/monthly-revenue/?year={year}'
        response = client.get(url, headers={'authorization': token})

        # temporary disable
        # assert response.status_code == 401
        assert month
        assert amount
        assert response.status_code == 200
        # assert response.json()[f'{month}']['MYR'] == amount


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorMonthlyRevenue)
def test_get_monthly_revenue_fails(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
                                    'get,patch,post,delete', 'apollo-main')

        # year = datetime.now().year
        # month = datetime.now().month

        # token = jwt.encode(
        #     {
        #         "exp": datetime.utcnow() + timedelta(days=1),
        #         "user_id": str(staff.id),
        #         "membership_id": f'{mem.id}',
        #     },
        #     settings.JWT_SECRET,
        #     algorithm=schema.JWT_ALGORITHM,
        # )

        # url = f'{PAYMENT_BASE_URL}/monthly-revenue/?year={year}'
        # response = client.get(url, headers={'authorization': token})

        # temporary disable
        # assert response.status_code == 401
        # assert month
        # assert response.status_code == 200
        # assert response.json()[f'{month}']['MYR'] == 0
        # assert response.json()[f'{month}']['SGD'] == 0


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorChargingSessionUniqueIDTag)
def test_get_unique_users_report(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff_1 = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        staff_2 = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem_1 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff_1.id}',
            membership_type=schema.MembershipType.staff,
            user_id_tag='id_tag_test_1',
        )
        db.commit()
        membership_id_1 = str(mem_1.id)

        mem_2 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff_2.id}',
            membership_type=schema.MembershipType.staff,
            user_id_tag='id_tag_test_2',
        )
        db.commit()
        membership_id_2 = str(mem_2.id)

        staff_3 = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem_3 = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff_3.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id_3 = str(mem_3.id)

        create_res_server_and_roles(db, mem_2, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
                                    'get,patch,post,delete', 'apollo-main')

        IDTagFactory(member_id=membership_id_1, id_tag='id_tag_test_1')
        db.commit()

        IDTagFactory(member_id=membership_id_2, id_tag='id_tag_test_2')
        db.commit()

        IDTagFactory(member_id=membership_id_3, id_tag='id_tag_test_3')
        db.commit()

        ChargingSessionBillFactory(
            id=uuid.UUID('ac1f06d9-38a3-4299-8ec5-2ae68443eb67'),
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid
        )
        db.commit()

        ChargingSessionBillFactory(
            id=uuid.UUID('84197658-55a0-409f-b2c7-f08c0d5b8c26'),
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid
        )
        db.commit()

        start_date = datetime.now().year
        end_date = datetime.now() + timedelta(days=1)

        number_of_unique_users = 2
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff_1.id),
                "membership_id": f'{mem_1.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/report/unique-users?date_start={start_date}&date_end={end_date}'
        response = client.get(url, headers={'authorization': token})

        assert response.status_code == 200
        assert response.json() == number_of_unique_users


# @patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorChargingSession)
# def test_get_report(async_client_mock, test_db):
#     with contextmanager(override_create_session)() as db:
#         organization = OrganizationFactory()
#         db.commit()
#         organization_id = str(organization.id)
#
#         staff_1 = UserFactory(
#             is_verified=True,
#             verification_method='email',
#             organization_id=organization_id
#         )
#         db.commit()
#
#         staff_2 = UserFactory(
#             is_verified=True,
#             verification_method='email',
#             organization_id=organization_id
#         )
#         db.commit()
#
#         mem_1 = MembershipFactory(
#             organization_id=organization_id,
#             user_id=f'{staff_1.id}',
#             membership_type=schema.MembershipType.staff,
#         )
#         db.commit()
#         membership_id_1 = str(mem_1.id)
#
#         mem_2 = MembershipFactory(
#             organization_id=organization_id,
#             user_id=f'{staff_2.id}',
#             membership_type=schema.MembershipType.staff,
#         )
#         db.commit()
#         membership_id_2 = str(mem_2.id)
#
#         staff_3 = UserFactory(
#             is_verified=True,
#             verification_method='email',
#             organization_id=organization_id
#         )
#         db.commit()
#
#         mem_3 = MembershipFactory(
#             organization_id=organization_id,
#             user_id=f'{staff_3.id}',
#             membership_type=schema.MembershipType.staff,
#         )
#         db.commit()
#         membership_id_3 = str(mem_3.id)
#
#         create_res_server_and_roles(db, mem_2, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
#                                     'get,patch,post,delete', 'apollo-main')
#
#         IDTagFactory(member_id=membership_id_1, id_tag='id_tag_test_1')
#         db.commit()
#
#         IDTagFactory(member_id=membership_id_2, id_tag='id_tag_test_2')
#         db.commit()
#
#         IDTagFactory(member_id=membership_id_3, id_tag='id_tag_test_3')
#         db.commit()
#
#         ChargingSessionBillFactory(
#             id=uuid.UUID('ac1f06d9-38a3-4299-8ec5-2ae68443eb67'),
#             charging_session_id=CHARGING_SESSION_ID,
#             usage_amount=10.5,
#             usage_type=schema.BillingType.kwh,
#             status=schema.ChargingSessionBillStatus.paid
#         )
#         db.commit()
#
#         ChargingSessionBillFactory(
#             id=uuid.UUID('84197658-55a0-409f-b2c7-f08c0d5b8c26'),
#             charging_session_id=CHARGING_SESSION_ID,
#             usage_amount=10.5,
#             usage_type=schema.BillingType.kwh,
#             status=schema.ChargingSessionBillStatus.paid
#         )
#         db.commit()
#
#         PaymentRequestFactory(
#             charging_session_bill_id=uuid.UUID('ac1f06d9-38a3-4299-8ec5-2ae68443eb67'),
#             amount='10.0',
#             status='paid',
#             member_id=membership_id_1
#         )
#
#         PaymentRequestFactory(
#             charging_session_bill_id=uuid.UUID('84197658-55a0-409f-b2c7-f08c0d5b8c26'),
#             amount='10.0',
#             status='paid',
#             member_id=membership_id_1
#         )
#         db.commit()
#
#         start_date = datetime.now().year
#         end_date = datetime.now() + timedelta(days=1)
#
#         number_of_transactions = 2
#
#         token = jwt.encode(
#             {
#                 "exp": datetime.utcnow() + timedelta(days=1),
#                 "user_id": str(staff_1.id),
#                 "membership_id": f'{mem_1.id}',
#             },
#             settings.JWT_SECRET,
#             algorithm=schema.JWT_ALGORITHM,
#         )
#
#         url = f'{PAYMENT_BASE_URL}/report/number-of-transactions?date_start={start_date}&date_end={end_date}'
#         response = client.get(url, headers={'authorization': token})
#
#         assert response.status_code == 200
#         assert response.json() == number_of_transactions
#
#         url = f'{PAYMENT_BASE_URL}/report/avg-energy-consumption?date_start={start_date}&date_end={end_date}'
#         response = client.get(url, headers={'authorization': token})
#
#         assert response.status_code == 200
#         assert response.json() == 0.03
#
#         url = f'{PAYMENT_BASE_URL}/report/total-energy-consumed?date_start={start_date}&date_end={end_date}'
#         response = client.get(url, headers={'authorization': token})
#
#         assert response.status_code == 200
#         assert response.json() == 0.06
#
#         # url = f'{PAYMENT_BASE_URL}/report/total-payments?date_start={start_date}&date_end={end_date}'
#         # response = client.get(url, headers={'authorization': token})
#         #
#         # assert response.status_code == 200
#         # assert response.json() == 20.0
#
#         url = f'{PAYMENT_BASE_URL}/report/avg-operation-duration?date_start={start_date}&date_end={end_date}'
#         response = client.get(url, headers={'authorization': token})
#
#         assert response.status_code == 200
#         # assert response.json() == 2100.0
#
#         url = f'{PAYMENT_BASE_URL}/report/total-operation-duration?date_start={start_date}&date_end={end_date}'
#         response = client.get(url, headers={'authorization': token})
#
#         assert response.status_code == 200
#         # assert response.json() == 4200.0
#
#         url = f'{PAYMENT_BASE_URL}/report/total-greenhouse-gas-saved?date_start={start_date}&date_end={end_date}'
#         response = client.get(url, headers={'authorization': token})
#
#         assert response.status_code == 200
#         assert response.json() == 0.02
#
#         url = f'{PAYMENT_BASE_URL}/report/total-charged-mileage?date_start={start_date}&date_end={end_date}'
#         response = client.get(url, headers={'authorization': token})
#
#         assert response.status_code == 200
#         assert response.json() == 0.36


@patch('app.crud.invoice.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorChargingSession)
def test_get_dashboard_energy_invalid_datetime_format(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_superuser=True,
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/?',
                                    'get,patch,post,delete', 'apollo-main')

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.done,
            charging_session_bill_id=str(charging_session_bill.id),
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    url = f'{PAYMENT_BASE_URL}/report/get-energy-transaction'
    response = client.get(url, headers={'authorization': token},
                          params={"type": "Day", "date_start": "10-01-202200:00:00"})
    assert response.status_code == 422


@patch('app.crud.invoice.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorChargingSession)
def test_get_dashboard_charging_duration_invalid_datetime_format(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_superuser=True,
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{ROOT_PATH}/api/v1/csms/payment/?',
                                    'get,patch,post,delete', 'apollo-main')

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.done,
            charging_session_bill_id=str(charging_session_bill.id),
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

    url = f'{ROOT_PATH}/api/v1/csms/payment/report/get-energy-transactions-charging-duration/'
    response = client.get(url, headers={'authorization': token},
                          params={"type": "Day", "date_start": "10-01-202200:00:00"})
    assert response.status_code == 422


@patch('app.utils.httpx.AsyncClient')
def test_get_dashboard_total_transaction_invalid_datetime_format(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        staff = UserFactory(
            is_superuser=True,
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()
        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        create_res_server_and_roles(db, mem, str(organization.id),
                                    fr'{PAYMENT_BASE_URL}/report/energy-total-transaction/?',
                                    'get,patch,post,delete', 'apollo-main')
        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid
        )
        db.commit()
        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            status=schema.PaymentRequestStatus.done,
            charging_session_bill_id=str(charging_session_bill.id),
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()
        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
    url = f'{PAYMENT_BASE_URL}/report/energy-total-transaction'
    response = client.get(url, headers={'authorization': token}, params={"type": "days",
                                                                         "date_start": "01-10-202200:00:00"})
    assert response.status_code == 422


@patch('app.utils.httpx.AsyncClient')
def test_charger_utilization_does_not_exist(async_client_mock, test_db):
    async_client_mock.__aenter__.return_value = {'status_code': 400}
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.utcnow() + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/report/charger-utilization',
                                    'get,patch,post,delete', 'apollo-main')

        url = f'{PAYMENT_BASE_URL}/report/charger-utilization'
        response = client.get(url, headers={'authorization': token},
                              params={'type': 'days', 'date_start': '2022-10-01 00:00:00'})

        assert response.status_code == 400
        assert response.json() == {'detail': 'ChargerUtilization object does not exist.'}


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_sg_payment_return_url(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            organization_id=organization_id,
            is_verified=True,
            verification_method='email',
            is_superuser=True,
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        pr = PaymentRequestCreditCard(currency='SGD').dict()
        pr = PaymentRequestFactory(
            **pr,
            member_id=membership_id
        )
        db.commit()

        payment = PAYMENT_SCHEMA

        pre_skey = calculate_md5_hash(
            f'transactionid{pr.id}{schema.PaymentStatus.successful}'
            f'{payment["domain"]}{payment["amount"]}{payment["currency"]}'
        )
        skey = calculate_md5_hash(
            f'{payment["paydate"]}{payment["domain"]}'
            f'{pre_skey}{payment["appcode"]}{settings.SG_SECRET_KEY}'
        )

        payment.update(
            orderid=str(pr.id),
            tranID='transactionid',
            status=schema.PaymentStatus.successful,
            skey=skey,
            error_code='',
            error_desc='',
        )

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/sg/payment-return-url',
                                    'get,patch,post,delete', 'apollo-main')

        url = f'{PAYMENT_BASE_URL}/sg/payment-return-url'
        response = client.post(url, data=payment)
        assert bool(
            BeautifulSoup(response.content.decode('utf-8'), "html.parser").find(
                text="Credit card has been added successfully"))

        assert response.status_code == 200
        db.commit()
        query = db.query(models.PaymentRequest) \
            .filter(models.PaymentRequest.type == schema.PaymentRequestType.direct)
        assert query.count() == 1


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_sg_payment_callback(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            organization_id=organization_id,
            is_verified=True,
            verification_method='email',
            is_superuser=True,
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        pr = PaymentRequestCreditCard(currency='SGD').dict()
        pr = PaymentRequestFactory(
            **pr,
            member_id=membership_id
        )
        db.commit()

        payment = PAYMENT_SCHEMA
        payment['error_code'] = ''
        pre_skey = calculate_md5_hash(
            f'transactionid{pr.id}{schema.PaymentStatus.failure}'
            f'{payment["domain"]}{payment["amount"]}{payment["currency"]}'
        )
        skey = calculate_md5_hash(
            f'{payment["paydate"]}{payment["domain"]}'
            f'{pre_skey}{payment["appcode"]}{settings.SG_SECRET_KEY}'
        )

        payment.update(
            orderid=str(pr.id),
            tranID='transactionid',
            status=schema.PaymentStatus.failure,
            skey=skey,
        )

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/sg/payment-callback',
                                    'get,patch,post,delete', 'apollo-main')

        url = f'{PAYMENT_BASE_URL}/sg/payment-callback'
        response = client.post(url, data=payment)
        assert response.status_code == 200
        db.commit()
        query = db.query(models.PaymentRequest) \
            .filter(models.PaymentRequest.type == schema.PaymentRequestType.direct)
        assert query.count() == 1


@patch('requests.request')
def test_send_payment_request_recurring_myr(mock_request, test_db):
    mock_response = MockResponse([{'status': 'value'}], 200)
    mock_request.return_value = mock_response
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        CreditCardFactory(member_id=membership_id, primary=True, last_four_digit='1111')
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
                                    'get,patch,post,delete', 'apollo-main')

        pr = PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Charging Payment',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            status=schema.PaymentRequestStatus.pending,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/{pr.id}'
        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 200
        mock_request.assert_called_once()


@patch('requests.request')
def test_send_payment_request_recurring_sgd(mock_request, test_db):
    mock_response = MockResponse([{'status': 'value'}], 200)
    mock_request.return_value = mock_response
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        CreditCardFactory(
            member_id=membership_id,
            primary=True,
            last_four_digit='1111',
            currency='SGD'
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
                                    'get,patch,post,delete', 'apollo-main')

        pr = PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Charging Payment',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            status=schema.PaymentRequestStatus.pending,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            currency='SGD'
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/{pr.id}'
        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 200
        mock_request.assert_called_once()


@patch('requests.request')
def test_send_payment_request_recurring_bnd(mock_request, test_db):
    mock_response = MockResponse([{'status': 'value'}], 200)
    mock_request.return_value = mock_response
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        CreditCardFactory(
            member_id=membership_id,
            primary=True,
            last_four_digit='1111',
            currency='BND'
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
                                    'get,patch,post,delete', 'apollo-main')

        pr = PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Charging Payment',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            status=schema.PaymentRequestStatus.pending,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            currency='BND'
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/{pr.id}'
        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 200
        mock_request.assert_called_once()


@patch('requests.request')
def test_send_payment_request_recurring_khr(mock_request, test_db):
    mock_response = MockResponse([{'status': 'value'}], 200)
    mock_request.return_value = mock_response
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        CreditCardFactory(
            member_id=membership_id,
            primary=True,
            last_four_digit='1111',
            currency='KHR'
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
                                    'get,patch,post,delete', 'apollo-main')

        pr = PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Charging Payment',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            status=schema.PaymentRequestStatus.pending,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            currency='KHR'
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/{pr.id}'
        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 200
        mock_request.assert_called_once()


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_send_payment_request_recurring_no_cc(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
                                    'get,patch,post,delete', 'apollo-main')

        pr = PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Charging Payment',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            status=schema.PaymentRequestStatus.pending,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/{pr.id}'
        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 400
        assert response.json().get('detail') == 'Primary Credit Card not found'


@patch('app.utils.httpx.AsyncClient', side_effect=MockAsyncClientGeneratorPaymentIPN)
def test_send_payment_request_direct(async_client_mock, test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/payment-request/.*',
                                    'get,patch,post,delete', 'apollo-main')

        pr = PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charging Payment',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR_ID,
            status=schema.PaymentRequestStatus.pending,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        str_key = f'{pr.amount}{settings.MERCHANT_ID}{pr.id}{settings.VERIFY_KEY}'
        vkey = calculate_md5_hash(str_key)
        params = {
            'amount': pr.amount,
            'orderid': pr.id,
            'bill_name': f'{mem.first_name} {mem.last_name}',
            'bill_email': f'{staff.email}',
            'bill_mobile': f'{staff.phone_number}',
            'bill_desc': f'{pr.billing_description}',
            'vcode': vkey,
            'channel': 'credit',
            'username': settings.MERCHANT_ID,
            'app_name': 'Apollo',
        }
        expected = f'{PAYMENT_DIRECT_API_URL}?' + urllib.parse.urlencode(params)

        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/payment-request/{pr.id}'
        response = client.post(url, headers={'authorization': token})
        assert response.status_code == 307
        assert response.headers["location"] == expected


def test_rebilling_invoices(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/rebilling-issued-invoice/.*',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )
        valid_data = [str(uuid.uuid4())]
        url = f'{PAYMENT_BASE_URL}/rebilling-issued-invoice'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            client.post(url, headers={'authorization': token}, json=valid_data)
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'POST',
                url=f'{CHARGER_URL_PREFIX}/charging/rebilling-issued-invoice',
                headers=ANY,
                data=json.dumps(valid_data)
            )


def test_get_number_of_unique_user_report(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/report/unique-users/.*',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/report/unique-users'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {'id_tags_list': [str(CHARGE_POINT_ID)]}
            mock_send.return_value = mock_response

            client.get(url, headers={'authorization': token})
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                url=f'{CHARGER_URL_PREFIX}/charging/charging-sessions-unique-id-tags',
                headers=ANY,
                query_params=ANY
            )


def test_get_number_of_transactions_report(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/number-of-transactions/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/report/number-of-transactions'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            client.get(url, headers={'authorization': token})
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                url=f'{CHARGER_URL_PREFIX}/charging/total-charging-transaction',
                headers=ANY,
                query_params=ANY
            )


def test_get_avg_energy_consumption_report(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/avg-energy-consumption/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/report/avg-energy-consumption'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = [100, 200, 300]
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.json() == 0.2
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                url=f'{CHARGER_URL_PREFIX}/charging/dashboard-meter-values',
                headers=ANY,
                query_params=ANY
            )


def test_get_total_energy_consumed_report(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/total-energy-consumed/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/report/total-energy-consumed'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = [100, 200, 300, 400]
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.json() == 1.0
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                url=f'{CHARGER_URL_PREFIX}/charging/dashboard-meter-values',
                headers=ANY,
                query_params=ANY
            )


def test_get_total_payments_v2(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/report/total-payments/.*',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        data = {
            'currency': 'MYR'
        }
        url = f'{PAYMENT_BASE_URL}/report/total-payments'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token}, json=data)
            assert response.json() == 0
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                url=f'{CHARGER_URL_PREFIX}/connector/list-connector',
                headers=ANY
            )


def test_get_total_payments_v2_superuser(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_superuser=True,
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        ChargingSessionBillFactory(
            id='1e2dacc7-68d1-4fda-ab80-773f39adb9c0',
            charging_session_id='84197658-55a0-409f-b2c7-f08c0d5b8c26',
            usage_amount=float(10.0),
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid,
        )
        db.commit()

        PaymentRequestFactory(
            charging_session_bill_id=uuid.UUID('1e2dacc7-68d1-4fda-ab80-773f39adb9c0'),
            amount='50.0',
            status='paid',
            member_id=mem.id
        )

        create_res_server_and_roles(db, mem, str(organization.id), fr'{PAYMENT_BASE_URL}/report/total-payments/.*',
                                    'get,patch,post,delete', 'apollo-main')
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        data = {
            'currency': 'MYR'
        }
        url = f'{PAYMENT_BASE_URL}/report/total-payments'

        response = client.get(url, headers={'authorization': token}, json=data)
        assert response.json() == 50.0


def test_get_avg_operation_duration(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/avg-operation-duration/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/report/avg-operation-duration'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            client.get(url, headers={'authorization': token})
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                url=f'{CHARGER_URL_PREFIX}/charge_point/report/operation-duration',
                headers=ANY,
                query_params=ANY
            )


def test_get_total_operation_duration(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/total-operation-duration/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/report/total-operation-duration'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            client.get(url, headers={'authorization': token})
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                url=f'{CHARGER_URL_PREFIX}/charge_point/report/operation-duration',
                headers=ANY,
                query_params=ANY
            )


def test_get_total_greenhouse_gas_saved(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/total-greenhouse-gas-saved/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/report/total-greenhouse-gas-saved'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = [100, 200, 300]
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.json() == 0.2
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                url=f'{CHARGER_URL_PREFIX}/charging/dashboard-meter-values',
                headers=ANY,
                query_params=ANY
            )


def test_get_total_charged_mileage(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/total-charged-mileage/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/report/total-charged-mileage'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = [100, 200, 300]
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.json() == 3.6
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                url=f'{CHARGER_URL_PREFIX}/charging/dashboard-meter-values',
                headers=ANY,
                query_params=ANY
            )


def test_get_energy_transaction(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/get-energy-transaction/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/report/get-energy-transaction'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            client.get(url, headers={'authorization': token})
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{CHARGER_URL_PREFIX}/charging/dashboard/get_dashboard_energy',
                headers=ANY,
                query_params=ANY
            )


def test_get_avg_charger_power_output(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/avg-charger-power-output/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/report/avg-charger-power-output?types=Day'

        with patch('app.utils.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            client.get(url, headers={'authorization': token})
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{CHARGER_URL_PREFIX}/charging/kw-report',
                headers=ANY,
                query_params=ANY
            )


def test_get_energy_transactions_charging_duration(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/get-energy-transactions-charging-duration/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        date_start = str(datetime.now())
        date_end = str(datetime.now() + timedelta(days=1))
        query = f'types=Day&date_start={date_start}&date_end={date_end}'
        url = f'{PAYMENT_BASE_URL}/report/get-energy-transactions-charging-duration/?{query}'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            client.get(url, headers={'authorization': token})
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{CHARGER_URL_PREFIX}/charging/stats/charging-duration',
                headers=ANY,
                query_params=QueryParams(query)
            )


def test_get_energy_transactions_charging_duration_error(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/get-energy-transactions-charging-duration/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        date_start = str(datetime.now())
        date_end = str(datetime.now() + timedelta(days=1))
        query = f'types=Day&date_start={date_start}&date_end={date_end}'
        url = f'{PAYMENT_BASE_URL}/report/get-energy-transactions-charging-duration/?{query}'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 404
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.status_code == 400
            assert response.json().get('detail') == 'ChargingSession object does not exist.'
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{CHARGER_URL_PREFIX}/charging/stats/charging-duration',
                headers=ANY,
                query_params=QueryParams(query)
            )


def test_get_energy_transactions_by_session(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/get-energy-transactions-by-session/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        date_start = str(datetime.now())
        date_end = str(datetime.now() + timedelta(days=1))
        query = f'types=Day&date_start={date_start}&date_end={date_end}'
        url = f'{PAYMENT_BASE_URL}/report/get-energy-transactions-by-session/?{query}'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            client.get(url, headers={'authorization': token})
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{CHARGER_URL_PREFIX}/charging/stats/energy-transactions-start-end-charging',
                headers=ANY,
                query_params=QueryParams(query)
            )


def test_get_energy_transactions_by_session_error(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/get-energy-transactions-by-session/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        date_start = str(datetime.now())
        date_end = str(datetime.now() + timedelta(days=1))
        query = f'types=Day&date_start={date_start}&date_end={date_end}'
        url = f'{PAYMENT_BASE_URL}/report/get-energy-transactions-by-session/?{query}'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 404
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.status_code == 400
            assert response.json().get('detail') == 'ChargingSession object does not exist.'
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{CHARGER_URL_PREFIX}/charging/stats/energy-transactions-start-end-charging',
                headers=ANY,
                query_params=QueryParams(query)
            )


def test_get_energy_total_transaction(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/energy-total-transaction/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        date_start = str(datetime.now())
        date_end = str(datetime.now() + timedelta(days=1))
        query = f'types=days&date_start={date_start}&date_end={date_end}'
        url = f'{PAYMENT_BASE_URL}/report/energy-total-transaction?{query}'

        with patch('app.utils.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = [{'date': date_start, 'total': 1}]
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.status_code == 200
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{CHARGER_URL_PREFIX}/charging/total-transactions',
                headers=ANY,
                query_params=QueryParams(query)
            )


def test_get_energy_total_transaction_error(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/energy-total-transaction/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        date_start = str(datetime.now())
        date_end = str(datetime.now() + timedelta(days=1))
        query = f'types=days&date_start={date_start}&date_end={date_end}'
        url = f'{PAYMENT_BASE_URL}/report/energy-total-transaction?{query}'

        with patch('app.utils.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 404
            mock_response.json.return_value = []
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.status_code == 400
            assert response.json().get('detail') == 'ChargingSession object does not exist.'
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{CHARGER_URL_PREFIX}/charging/total-transactions',
                headers=ANY,
                query_params=QueryParams(query)
            )


def test_get_charger_utilization(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/charger-utilization/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        date_start = str(datetime.now())
        date_end = str(datetime.now() + timedelta(days=1))
        query = f'types=days&date_start={date_start}&date_end={date_end}'
        url = f'{PAYMENT_BASE_URL}/report/charger-utilization?{query}'

        with patch('app.utils.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {'utilized_rate': 1.0, 'idle_rate': 2.0, 'offline_rate': 3.0}
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.status_code == 200
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{CHARGER_URL_PREFIX}/connector/charger-utilization',
                headers=ANY,
                query_params=QueryParams(query)
            )


def test_get_charger_utilization_error(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/report/charger-utilization/.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        date_start = str(datetime.now())
        date_end = str(datetime.now() + timedelta(days=1))
        query = f'types=days&date_start={date_start}&date_end={date_end}'
        url = f'{PAYMENT_BASE_URL}/report/charger-utilization?{query}'

        with patch('app.utils.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 404
            mock_response.json.return_value = {}
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.status_code == 400
            assert response.json().get('detail') == 'ChargerUtilization object does not exist.'
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'GET',
                f'{CHARGER_URL_PREFIX}/connector/charger-utilization',
                headers=ANY,
                query_params=QueryParams(query)
            )


def test_get_charging_reconcilation(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        cs_bill = ChargingSessionBillFactory(
            id=CHARGING_SESSION_ID,
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=float(10.0),
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid,
        )
        db.commit()

        PaymentRequestFactory(
            charging_session_bill_id=str(cs_bill.id),
            amount='50.0',
            status='paid',
            member_id=mem.id
        )

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/reconcilation//.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/reconcilation/'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"items": []}
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.status_code == 200
            assert response.json().get('items') == []
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'POST',
                url=f'{CHARGER_URL_PREFIX}/charging/reconcilation',
                headers=ANY,
                data='{}',
                query_params={'size': 50, 'page': 1}
            )


def test_get_charging_reconcilation_mock_data(test_db):
    with contextmanager(override_create_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        cs_bill = ChargingSessionBillFactory(
            id=CHARGING_SESSION_ID,
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=float(10.0),
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.paid,
        )
        db.commit()

        PaymentRequestFactory(
            charging_session_bill_id=str(cs_bill.id),
            amount='50.0',
            status='paid',
            member_id=mem.id
        )

        create_res_server_and_roles(
            db, mem, str(organization.id),
            fr'{PAYMENT_BASE_URL}/reconcilation//.*',
            'get,patch,post,delete', 'apollo-main'
        )
        token = jwt.encode(
            {
                "exp": datetime.now(timezone.utc) + timedelta(days=1),
                "user_id": str(staff.id),
                "membership_id": f'{mem.id}',
            },
            settings.JWT_SECRET,
            algorithm=schema.JWT_ALGORITHM,
        )

        url = f'{PAYMENT_BASE_URL}/reconcilation/'

        with patch('app.routers.payments.send_request', new_callable=AsyncMock) as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "items": [
                    {
                        "id": CHARGING_SESSION_ID,
                        "created_at": datetime.now(),
                        "meter_start": 10055,
                        "meter_stop": 111111,
                        "charging_session_type": "Local"
                    }
                ]
            }
            mock_send.return_value = mock_response

            response = client.get(url, headers={'authorization': token})
            assert response.status_code == 200
            assert response.json().get('items')[0]['id'] == CHARGING_SESSION_ID
            assert response.json().get('items')[0]['usage'] == 101.056
            assert mock_send.call_count == 1
            mock_send.assert_called_with(
                'POST',
                url=f'{CHARGER_URL_PREFIX}/charging/reconcilation',
                headers=ANY,
                data='{}',
                query_params={'size': 50, 'page': 1}
            )
