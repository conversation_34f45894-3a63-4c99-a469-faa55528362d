"""39

Revision ID: cd6aaaf6d179
Revises: 65f43d5af4d5
Create Date: 2023-05-28 13:46:33.999019

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'cd6aaaf6d179'
down_revision = '65f43d5af4d5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_payment_callback', 'order_id',
                    existing_type=postgresql.UUID(as_uuid=True),
                    type_=sa.String(),
                    existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('main_payment_callback', 'order_id',
                    existing_type=sa.String(),
                    type_=postgresql.UUID(as_uuid=True),
                    existing_nullable=True)
    # ### end Alembic commands ###
