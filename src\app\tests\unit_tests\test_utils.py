from datetime import timed<PERSON><PERSON>, datetime  # noqa
from decimal import Decimal
import json
import uuid
from contextlib import contextmanager
from unittest.mock import patch
from zoneinfo import ZoneInfo

import pytest
from fastapi import HTTPException
from faker import Faker

from app import utils, schema, crud, models
from app.models.organization import OperatorChargepoint
from app import ruby_proxy_utils
from app.tests.mocks.async_client import MockResponse
from app.database import SessionLocal, Base, engine
from app.schema.v2 import PreferredPaymentFlowEnums
from app.tests.factories import (ChargingSessionBillFactory, PromoCodeFactory, UserFactory,
                                 MembershipFactory, OrganizationFactory, CreditCardFactory,
                                 IDTagFactory, WalletFactory, PaymentRequestFactory,
                                 OrganizationPromoCodeFactory, OperatorFactory, SubscriptionPlanFactory,
                                 SubscriptionFactory, RecurringPaymentFactory, PreAuthPaymentFactory,
                                 PreAuthPaymentCaptureFactory, PrepaidWalletPlanFactory,
                                 PrepaidWalletPlanBatchFactory, ********************************,
                                 VehicleFactory, EmaidFactory)

from app.tests.utils import faker_phone_number

CHARGE_POINT_ID = str(uuid.uuid4())
CHARGING_SESSION_ID = str(uuid.uuid4())

CONNECTOR_TYPE = {
    'id': 'b27b2389-a02d-4325-98a3-faaec01b0bc1',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'name': 'test_connector_type',
    'kind': 'test_connector_type_name',
}

LOCATION = {
    'id': 'b27b2389-a02d-4325-98a3-faaec01b0bc1',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'name': 'name',
    'latitude': 1.1,
    'longitude': 2.2,
    'address': 'address',
    'city': 'city',
    'state': 'state',
    'country': 'country',
    'protocol_type': 'OCPP'
}
CHARGE_POINT = {
    'id': CHARGE_POINT_ID,
    'serial_number': 'cp_test',
    'status': 'Available',
    'is_private': False,
    'is_connected': True,
    'operator_id': None,
    'location': LOCATION,
}

CONNECTOR = {
    'id': '912388d7-c48b-4de8-a826-c398bdae049b',
    'created_at': '2022-01-01 00:00:00',
    'updated_at': '2022-01-01 00:00:00',
    'number': 1,
    'info': {},
    'billing_type': schema.BillingType.time,
    'billing_unit_fee': 1.0,
    'billing_cycle': 1,
    'billing_currency': 'MYR',
    'connection_fee': 0,
    'status': 'Available',
    'ocpp_status': 'Available',
    'charge_point': CHARGE_POINT,
    'connector_type': CONNECTOR_TYPE
}
fake = Faker()


@pytest.fixture(scope='function')
def test_db():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def create_db_session():
    try:
        db = SessionLocal()
        UserFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        MembershipFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        CreditCardFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        IDTagFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        WalletFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PaymentRequestFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PromoCodeFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        ChargingSessionBillFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OrganizationPromoCodeFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        OperatorFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionPlanFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        SubscriptionFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        RecurringPaymentFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        PreAuthPaymentFactory._meta.sqlalchemy_session = db
        PreAuthPaymentCaptureFactory._meta.sqlalchemy_session = db
        PrepaidWalletPlanFactory._meta.sqlalchemy_session = db
        PrepaidWalletPlanBatchFactory._meta.sqlalchemy_session = db
        ********************************._meta.sqlalchemy_session = db
        VehicleFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access
        EmaidFactory._meta.sqlalchemy_session = db  # pylint: disable=protected-access

        yield db
    finally:
        db.close()


def test_calculate_md5_succeeds():
    string = 'test'
    md5_string = '098f6bcd4621d373cade4e832627b4f6'
    assert utils.calculate_md5_hash(string) == md5_string


@pytest.mark.asyncio
@patch('app.utils.PreChargingFlow._get_from_charger')
async def test_pre_charging_flow_get_connector_succeeds(_get_from_charger_mock):
    _get_from_charger_mock.return_value = MockResponse(CONNECTOR, 200)
    pre_charging_flow = utils.PreChargingFlow({}, CONNECTOR['id'])
    await pre_charging_flow._get_connector()
    assert pre_charging_flow._connector == CONNECTOR
    assert pre_charging_flow._charge_point_id == CONNECTOR['charge_point']['id']


@pytest.mark.asyncio
@patch('app.utils.crud.base.BaseCRUD.membership')
async def test_pre_charging_flow_check_member_rfid_succeeds(membership_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        IDTagFactory(member_id=membership_id, type=schema.IDTagType.rfid)
        db.commit()

        pre_charging_flow = utils.PreChargingFlow({}, CONNECTOR['id'])
        pre_charging_flow._connector = CONNECTOR
        assert pre_charging_flow._check_member_rfid(db, membership_id) is True


@pytest.mark.asyncio
@patch('app.utils.crud.base.BaseCRUD.membership')
async def test_pre_charging_flow_check_member_rfid_fails(membership_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        pre_charging_flow = utils.PreChargingFlow({}, CONNECTOR['id'])
        pre_charging_flow._connector = CONNECTOR
        assert pre_charging_flow._check_member_rfid(db, membership_id) is False


@pytest.mark.asyncio
@patch('app.utils.crud.base.BaseCRUD.membership')
async def test_pre_charging_flow_check_payment_method_succeeds(membership_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        pre_charging_flow = utils.PreChargingFlow({}, CONNECTOR['id'])
        pre_charging_flow._connector = CONNECTOR
        try:
            pre_charging_flow._check_payment_method(db, membership_id)
            assert True
        except HTTPException:
            assert False


@pytest.mark.asyncio
@patch('app.utils.crud.base.BaseCRUD.membership')
async def test_pre_charging_flow_check_payment_method_fails(membership_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        pre_charging_flow = utils.PreChargingFlow({}, CONNECTOR['id'])
        pre_charging_flow._connector = CONNECTOR
        try:
            pre_charging_flow._check_payment_method(db, membership_id)
            assert False
        except HTTPException:
            assert True


@pytest.mark.asyncio
@patch('app.utils.PreChargingFlow._get_from_charger')
@patch('app.utils.PreChargingFlow._check_active_charging_session')
@patch('app.utils.PreChargingFlow._check_active_reservation')
@patch('app.utils.PreChargingFlow._check_default_subscription_plan')
@patch('app.utils.crud.base.BaseCRUD.membership')
@patch('app.utils.apply_subscription_discount_connector')
async def test_pre_charging_flow_start_flow_succeeds(_discount_connector, membership_mock,
                                                     _check_active_reservation_mock,
                                                     _check_active_charging_session_mock,
                                                     _check_default_subscription_plan_mock,
                                                     _get_from_charger_mock,
                                                     test_db):
    _get_from_charger_mock.return_value = MockResponse(CONNECTOR, 200)
    _discount_connector.return_value = 0.0
    _check_default_subscription_plan_mock.return_value = True
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        SubscriptionFactory(member_id=membership_id, subscription_plan_id=subscription_plan_id_1, is_default=True,
                            end_date=datetime.now())

        db.commit()

        IDTagFactory(member_id=membership_id, type=schema.IDTagType.rfid)
        db.commit()

        pre_charging_flow = utils.PreChargingFlow({}, CONNECTOR['id'])
        pre_charging_flow._connector = CONNECTOR
        try:
            await pre_charging_flow.start_flow(db, membership_id)
            assert True
        except HTTPException:
            assert False


@pytest.mark.asyncio
@patch('app.utils.PreChargingFlow._get_from_charger')
@patch('app.utils.PreChargingFlow._check_active_charging_session')
@patch('app.utils.PreChargingFlow._check_active_reservation')
@patch('app.utils.PreChargingFlow._check_default_subscription_plan')
@patch('app.utils.crud.base.BaseCRUD.membership')
@patch('app.utils.apply_subscription_discount_connector')
async def test_pre_charging_flow_no_cc_free_charger_start_flow_succeeds(_discount_connector, membership_mock,
                                                                        _check_active_reservation_mock,
                                                                        _check_active_charging_session_mock,
                                                                        _check_default_subscription_plan_mock,
                                                                        _get_from_charger_mock,
                                                                        test_db):
    _get_from_charger_mock.return_value = MockResponse(CONNECTOR, 200)
    _discount_connector.return_value = 0.0
    _check_default_subscription_plan_mock.return_value = False
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        IDTagFactory(member_id=membership_id, type=schema.IDTagType.rfid)
        db.commit()

        pre_charging_flow = utils.PreChargingFlow({}, CONNECTOR['id'])
        pre_charging_flow._connector = CONNECTOR
        try:
            await pre_charging_flow.start_flow(db, membership_id)
            assert _discount_connector.called
            assert True
        except HTTPException:
            assert False


@pytest.mark.asyncio
@patch('app.utils.PreChargingFlow._get_from_charger')
@patch('app.utils.PreChargingFlow._check_active_charging_session')
@patch('app.utils.PreChargingFlow._check_active_reservation')
@patch('app.utils.crud.base.BaseCRUD.membership')
@patch('app.utils.PreChargingFlow._check_charging_fee')
async def test_pre_charging_flow_no_cc_non_free_charger_start_flow_fails(_check_charging_fee, membership_mock,
                                                                         _check_active_reservation_mock,
                                                                         _check_active_charging_session_mock,
                                                                         _get_from_charger_mock,
                                                                         test_db):
    _get_from_charger_mock.return_value = MockResponse(CONNECTOR, 200)
    _check_charging_fee.return_value = False
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        IDTagFactory(member_id=membership_id, type=schema.IDTagType.rfid)
        db.commit()

        pre_charging_flow = utils.PreChargingFlow({}, CONNECTOR['id'])
        pre_charging_flow._connector = CONNECTOR
        try:
            await pre_charging_flow.start_flow(db, membership_id)
            assert False
        except HTTPException as e:
            assert e.detail == 'Please add a credit card in order to start charging'


@pytest.mark.asyncio
@patch('app.utils.PreChargingFlow._get_from_charger')
@patch('app.utils.PreChargingFlow._check_active_charging_session')
@patch('app.utils.PreChargingFlow._check_active_reservation')
@patch('app.utils.crud.base.BaseCRUD.membership')
@patch('app.utils.PreChargingFlow._check_charging_fee')
async def test_pre_charging_flow_no_cc_non_free_charger_valid_subscription_start_flow_fails(
        _check_charging_fee,
        membership_mock,
        _check_active_reservation_mock,
        _check_active_charging_session_mock,
        _get_from_charger_mock,
        test_db):
    _get_from_charger_mock.return_value = MockResponse(CONNECTOR, 200)
    _check_charging_fee.return_value = False
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)
        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        SubscriptionFactory(member_id=membership_id, subscription_plan_id=subscription_plan_id_1, is_default=True,
                            end_date=datetime.now())

        db.commit()

        IDTagFactory(member_id=membership_id, type=schema.IDTagType.rfid)
        db.commit()

        pre_charging_flow = utils.PreChargingFlow({}, CONNECTOR['id'])
        pre_charging_flow._connector = CONNECTOR
        try:
            await pre_charging_flow.start_flow(db, membership_id)
            assert False
        except HTTPException as e:
            assert e.detail == 'Please add a credit card in order to start charging'


@pytest.mark.asyncio
@patch('app.utils.OCPIPreChargingFlow._get_from_charger')
@patch('app.utils.OCPIPreChargingFlow._check_active_charging_session')
@patch('app.utils.OCPIPreChargingFlow._check_active_reservation')
@patch('app.utils.crud.base.BaseCRUD.membership')
@patch('app.utils.apply_subscription_discount_connector')
async def test_emsp_pre_charging_flow_no_cc_non_free_charger_valid_subscription_start_flow_fails(
        apply_subscription_discount_connector_mock,
        membership_mock,
        _check_active_reservation_mock,
        _check_active_charging_session_mock,
        _get_from_charger_mock,
        test_db):
    _get_from_charger_mock.return_value = MockResponse(CONNECTOR, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)
        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        SubscriptionFactory(member_id=membership_id, subscription_plan_id=subscription_plan_id_1, is_default=True,
                            end_date=datetime.now())

        db.commit()

        IDTagFactory(member_id=membership_id, type=schema.IDTagType.rfid)
        db.commit()

        pre_charging_flow = utils.OCPIPreChargingFlow({}, CONNECTOR['id'])
        pre_charging_flow._connector = CONNECTOR
        try:
            await pre_charging_flow.ocpi_start_flow(db, membership_id)
            assert False
        except HTTPException as e:
            assert apply_subscription_discount_connector_mock.called is True
            assert e.detail == 'Please add a credit card in order to start charging'


@pytest.mark.asyncio
@patch('app.utils.OCPIPreChargingFlow._get_from_charger')
@patch('app.utils.OCPIPreChargingFlow._check_active_charging_session')
@patch('app.utils.OCPIPreChargingFlow._check_active_reservation')
@patch('app.utils.crud.base.BaseCRUD.membership')
async def test_emsp_pre_charging_flow_no_cc_free_charger_valid_subscription_start_flow_success(
        membership_mock,
        _check_active_reservation_mock,
        _check_active_charging_session_mock,
        _get_from_charger_mock,
        test_db):
    connector_obj = CONNECTOR
    connector_obj['billing_unit_fee'] = 0.0
    _get_from_charger_mock.return_value = MockResponse(connector_obj, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)
        subscription_plan_1 = SubscriptionPlanFactory(organization_id=organization_id)
        db.commit()
        subscription_plan_id_1 = str(subscription_plan_1.id)

        SubscriptionFactory(member_id=membership_id, subscription_plan_id=subscription_plan_id_1, is_default=True,
                            end_date=datetime.now())

        db.commit()

        IDTagFactory(member_id=membership_id, type=schema.IDTagType.rfid)
        db.commit()

        pre_charging_flow = utils.OCPIPreChargingFlow({}, CONNECTOR['id'])
        pre_charging_flow._connector = CONNECTOR
        try:
            await pre_charging_flow.ocpi_start_flow(db, membership_id)
            assert True
        except HTTPException:
            assert False


@pytest.mark.asyncio
@patch('app.utils.PreChargingFlow._get_from_charger')
@patch('app.utils.PreChargingFlow._check_active_charging_session')
@patch('app.utils.PreChargingFlow._check_active_reservation')
@patch('app.utils.crud.base.BaseCRUD.membership')
async def test_pre_charging_flow_start_flow_fails(membership_mock, _check_active_reservation_mock,
                                                  _check_active_charging_session_mock,
                                                  _get_from_charger_mock, test_db):
    _get_from_charger_mock.return_value = MockResponse(CONNECTOR, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        pre_charging_flow = utils.PreChargingFlow({}, CONNECTOR['id'])
        pre_charging_flow._connector = CONNECTOR
        try:
            await pre_charging_flow.start_flow(db, membership_id)
            assert False
        except Exception:
            assert True


@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_with_pre_payment_request_with_credit_card(membership_mock, send_recurring_payment_mock, get_invoice_mock,
                                                       send_invoice_mail_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        promo_code = PromoCodeFactory(
            start_at=str(datetime.now() - timedelta(days=1)),
            expire_at=str(datetime.now() + timedelta(days=1)),
            type=schema.PromoType.amount,
            amount=5.0,
        )
        db.commit()

        OrganizationPromoCodeFactory(
            organization_id=organization_id,
            promo_code_id=str(promo_code.id)
        )
        db.commit()

        promo_usage = crud.create_promo_usage(db, promo_code.code, membership_id,
                                              CHARGE_POINT_ID, '0.00')
        pr = PaymentRequestFactory(
            amount='0.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            promo_usage_id=promo_usage.id,
            status=schema.PaymentRequestStatus.pre_charging,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        utils.pay_with_pre_payment_request(
            db,
            str(promo_code.id),
            str(promo_usage.id),
            str(pr.id),
            0,
            membership_id,
            10.00,
            CHARGING_SESSION_ID,
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            'MGT-1012'
        )

        assert crud.get_recurring_payment_by_payment_request(db, pr.invoice_number) is not None


@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_with_pre_payment_request_with_wallet(membership_mock, send_invoice_mail_mock, get_invoice_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        promo_code = PromoCodeFactory(
            start_at=str(datetime.now() - timedelta(days=1)),
            expire_at=str(datetime.now() + timedelta(days=1)),
            type=schema.PromoType.amount,
            amount=5.0,
        )
        db.commit()

        OrganizationPromoCodeFactory(
            organization_id=organization_id,
            promo_code_id=str(promo_code.id)
        )
        db.commit()

        promo_usage = crud.create_promo_usage(db, promo_code.code, membership_id,
                                              CHARGE_POINT_ID, '0.00')
        wallet = WalletFactory(
            member_id=membership_id,
            currency='MYR'
        )
        db.commit()

        pr = PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        pr = PaymentRequestFactory(
            amount='0.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            promo_usage_id=promo_usage.id,
            status=schema.PaymentRequestStatus.pre_charging,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        utils.pay_with_pre_payment_request(
            db,
            promo_code.id,
            promo_usage.id,
            pr.id,
            20.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            'MGT-1012'
        )

        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 20 - (10 - 5)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_with_credit_card(membership_mock, send_recurring_payment_mock,
                                                           get_invoice_mock, send_invoice_mail_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        utils.pay_without_pre_charging_request(
            db,
            0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        query = db.query(models.RecurringPayment).filter()
        assert query.count() != 0


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_with_wallet(membership_mock, send_invoice_mail_mock,
                                                      get_invoice_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        utils.pay_without_pre_charging_request(
            db,
            20.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.wallet).one()
        assert float(result.amount) == float(10.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_with_partial_payment(membership_mock, send_recurring_payment_mock,
                                                               get_invoice_mock, send_invoice_mail_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            preferred_payment_flow=PreferredPaymentFlowEnums.partial
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Partial',
            preferred_payment_method='Wallet',
            is_low_wallet_balance=True,
        )
        db.commit()

        wallet = WalletFactory(member_id=membership_id)
        db.commit()

        PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )

        utils.pay_without_pre_charging_request(
            db,
            10.0,
            membership_id,
            20.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.partial).one()
        assert float(result.amount) == float(20.0)
        assert float(result.wallet_deduct_amount) == float(10.0)
        assert float(result.non_wallet_deduct_amount) == float(10.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_with_partial_payment_but_method_as_cc(membership_mock,
                                                                                send_recurring_payment_mock,
                                                                                get_invoice_mock,
                                                                                send_invoice_mail_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            preferred_payment_flow=PreferredPaymentFlowEnums.partial
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Partial',
            preferred_payment_method='Credit-Card'
        )
        db.commit()

        wallet = WalletFactory(member_id=membership_id)
        db.commit()

        PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )

        utils.pay_without_pre_charging_request(
            db,
            10.0,
            membership_id,
            20.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10.0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
        assert float(result.amount) == float(20.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_with_flow_as_wallet_but_method_as_cc(membership_mock,
                                                                               send_recurring_payment_mock,
                                                                               get_invoice_mock,
                                                                               send_invoice_mail_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            preferred_payment_flow=PreferredPaymentFlowEnums.partial
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Wallet',
            preferred_payment_method='Credit-Card'
        )
        db.commit()

        wallet = WalletFactory(member_id=membership_id)
        db.commit()

        PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )

        utils.pay_without_pre_charging_request(
            db,
            10.0,
            membership_id,
            20.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10.0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
        assert float(result.amount) == float(20.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_with_flow_as_cc_and_method_as_cc(membership_mock,
                                                                           send_recurring_payment_mock,
                                                                           get_invoice_mock,
                                                                           send_invoice_mail_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            preferred_payment_flow=PreferredPaymentFlowEnums.partial
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Credit-Card',
            preferred_payment_method='Credit-Card'
        )
        db.commit()

        wallet = WalletFactory(member_id=membership_id)
        db.commit()

        PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )

        utils.pay_without_pre_charging_request(
            db,
            10.0,
            membership_id,
            20.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10.0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
        assert float(result.amount) == float(20.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_pay_without_pre_charging_request_with_wallet_method_as_cc(membership_mock,
                                                                             send_recurring_payment_mock,
                                                                             get_invoice_mock, send_invoice_mail_mock,
                                                                             test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Wallet',
            preferred_payment_method='Credit-Card'
        )
        db.commit()
        PaymentRequestFactory(
            amount='0.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        utils.pay_without_pre_charging_request(
            db,
            0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        query = db.query(models.RecurringPayment).filter()
        assert query.count() != 0

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        pr = db.query(models.PaymentRequest).one()
        assert pr.status == schema.PaymentRequestStatus.pending


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_partial_payment_wallet(membership_mock, send_recurring_payment_mock,
                                                                 get_invoice_mock, send_invoice_mail_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            preferred_payment_flow=PreferredPaymentFlowEnums.partial,
            preferred_payment_method='Wallet',
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(member_id=membership_id)
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )

        utils.pay_without_pre_charging_request(
            db,
            20.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10.0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.wallet).one()
        assert float(result.amount) == float(10.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_payment_flow_wallet_exceeding(membership_mock, send_recurring_payment_mock,
                                                                        get_invoice_mock, send_invoice_mail_mock,
                                                                        test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            preferred_payment_flow=PreferredPaymentFlowEnums.wallet
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Wallet',
            preferred_payment_method='Wallet',
        )
        db.commit()

        wallet = WalletFactory(member_id=membership_id)
        db.commit()

        PaymentRequestFactory(
            amount='9.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )

        db.commit()
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 9.00

        utils.pay_without_pre_charging_request(
            db,
            9.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 0.0

        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.wallet).one()
        assert float(result.amount) == float(9.0)
        assert result.status == 'Done'

        csb = db.query(models.ChargingSessionBill).filter(
            models.ChargingSessionBill.id == str(charging_session_bill.id)).one()
        assert float(csb.outstanding_balance) == float(1.0)
        assert csb.status == 'Paid'


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_partial_payment_credit_card(membership_mock, send_recurring_payment_mock,
                                                                      get_invoice_mock, send_invoice_mail_mock,
                                                                      test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            preferred_payment_flow=PreferredPaymentFlowEnums.partial
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Partial',
            preferred_payment_method='Wallet',
            is_low_wallet_balance=True,
        )
        db.commit()

        wallet = WalletFactory(member_id=membership_id)
        db.commit()

        utils.pay_without_pre_charging_request(
            db,
            0.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 0.0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.partial).one()
        assert float(result.amount) == float(10.0)
        assert float(result.wallet_deduct_amount) == float(0.0)
        assert float(result.non_wallet_deduct_amount) == float(10.0)


def test_calculate_charging_usage_kw():
    assert utils.calculate_charging_usage(0, 100000, schema.BillingType.kwh) == 100


def test_calculate_charging_usage_kw_per_two_kwh():
    charging_cost = utils.calculate_charging_usage(0, 100000, schema.BillingType.kwh, 2)
    assert charging_cost == 50
    assert utils.calculate_charging_cost(2.5, charging_cost, 0) == 125


def test_calculate_charging_usage_kw_per_three_kwh_decimal():
    charging_cost = utils.calculate_charging_usage(51231, 616162, schema.BillingType.kwh, 2)
    # assert charging_cost == 282.47
    assert charging_cost == 282.4655
    assert round(float(utils.calculate_charging_cost(2.5, charging_cost, 0)), 2) == 706.16


def test_charging_usage_kw_ceil_with_charging_cost():
    charging_cost = utils.calculate_charging_usage(1000, 616162, schema.BillingType.kwh)
    # assert charging_cost == 615.16
    assert charging_cost == 615.162
    assert round(float(utils.calculate_charging_cost(2.5, charging_cost, 0)), 2) == 1537.91


def test_calculate_cost_charging_usage_kw_per_two_kwh():
    charging_cost = utils.calculate_charging_usage(0, 100000, schema.BillingType.kwh, 2)
    assert charging_cost == 50
    assert round(float(utils.calculate_charging_cost(2.5, charging_cost, 0)), 2) == 125


def test_charging_usage_kw_ceil():
    charging_cost = utils.calculate_charging_usage(1000, 616162, schema.BillingType.kwh)
    # assert charging_cost == 615.16
    assert charging_cost == 615.162


def test_calculate_charging_usage_time():
    assert utils.calculate_charging_usage(datetime.strftime(datetime.now(), "%Y-%m-%dT%H:%M:%SZ"),
                                          datetime.strftime((datetime.now()
                                                             + timedelta(seconds=690)), "%Y-%m-%dT%H:%M:%SZ"),
                                          schema.BillingType.time) == 12


def test_calculate_charging_cost():
    assert utils.calculate_charging_cost(2.5, 10, 20) == 45


def test_generate_charger_header(test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        operator = OperatorFactory(organization_id=organization_id)
        db.commit()
        operator_id = str(operator.id)

        charge_point_id = uuid.uuid4()
        operator_cp = OperatorChargepoint(charge_point_id=charge_point_id, operator_id=operator_id)
        db.add(operator_cp)
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            user_id_tag='test_id_tag',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        IDTagFactory(member_id=membership_id)
        db.commit()

        headers = utils.generate_charger_header(db, membership_id)
        assert headers['is_superuser'] == 'False'
        assert headers['membership_type'] == schema.MembershipType.staff.name
        assert str(charge_point_id) in json.loads(headers['accessible_charge_points'])
        assert headers['id_tag'] is mem.user_id_tag


def test_payment_request_done(test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)

        pr = PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        utils.payment_request_done(db, pr.id)
        db_pr = db.query(models.PaymentRequest).get(pr.id)
        assert db_pr.status == 'Done'


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_with_wallet_but_insufficient_fund(membership_mock, send_invoice_mail_mock,
                                                                            get_invoice_mock,
                                                                            send_recurring_payment_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        utils.pay_without_pre_charging_request(
            db,
            20.0,
            membership_id,
            21.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 20.0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
        assert float(result.amount) == float(21.0)


@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
@pytest.mark.asyncio
async def test_pay_without_pre_charging_request_ruby_with_wallet_but_insufficient_fund(membership_mock,
                                                                                       send_invoice_mail_mock,
                                                                                       get_invoice_mock,
                                                                                       send_recurring_payment_mock,
                                                                                       test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        await ruby_proxy_utils.pay_without_pre_charging_request_ruby(
            db,
            20.0,
            membership_id,
            21.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 20.0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
        assert float(result.amount) == float(21.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.get_invoice_ocpi')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
@pytest.mark.asyncio
async def test_pay_without_pre_charging_request_ocpi_with_wallet_but_insufficient_fund(membership_mock,
                                                                                       send_invoice_mail_mock,
                                                                                       get_invoice_mock,
                                                                                       send_recurring_payment_mock,
                                                                                       test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        await utils.pay_without_pre_charging_request_ocpi(
            db,
            20.0,
            membership_id,
            21.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            datetime.now(),
            datetime.now(),
            'MGT-1012',
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 20.0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
        assert float(result.amount) == float(21.0)


@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
@pytest.mark.asyncio
async def test_pay_without_pre_charging_request_ruby_with_wallet(membership_mock, send_invoice_mail_mock,
                                                                 get_invoice_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        await ruby_proxy_utils.pay_without_pre_charging_request_ruby(
            db,
            20.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.wallet).one()
        assert float(result.amount) == float(10.0)


@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.get_invoice_ocpi')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
@pytest.mark.asyncio
async def test_pay_without_pre_charging_request_ocpi_with_wallet(membership_mock, send_invoice_mail_mock,
                                                                 get_invoice_mock, send_recurring_payment_mock,
                                                                 test_db):
    with contextmanager(create_db_session)() as db:
        send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        await utils.pay_without_pre_charging_request_ocpi(
            db,
            20.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            datetime.now(),
            datetime.now(),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        # As of July 12, 2024, OCPI will no longer deduct wallet - REVERTED ON JULY 25, 2024
        assert float(wallet.balance) == 10.00
        # As of July 12, 2024, OCPI will no longer deduct wallet - REVERTED ON JULY 25, 2024
        # assert float(wallet.balance) == 10.00

    # As of July 12, 2024, OCPI will no longer deduct wallet - REVERTED ON JULY 25, 2024
    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.wallet).one()
        assert float(result.amount) == float(10.0)

    # As of July 12, 2024, OCPI will no longer deduct wallet - REVERTED ON JULY 25, 2024
    # with contextmanager(create_db_session)() as db:
    #     result = db.query(models.PaymentRequest).filter(
    #         models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
    #     assert float(result.amount) == float(10.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.get_invoice_ocpi')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
@pytest.mark.asyncio
async def test_pay_without_pre_charging_request_ocpi_partial_payment_wallet(membership_mock, send_invoice_mail_mock,
                                                                            get_invoice_mock,
                                                                            send_recurring_payment_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            preferred_payment_flow=PreferredPaymentFlowEnums.partial
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Partial',
            preferred_payment_method='Wallet',
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 20.00

        await utils.pay_without_pre_charging_request_ocpi(
            db,
            20.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            datetime.now(),
            datetime.now(),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10.0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.wallet).one()
        assert float(result.amount) == float(10.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_recurring_to_payment_gateway_ocpi')
@patch('app.utils.crud.get_invoice_ocpi_sync')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
@pytest.mark.asyncio
async def test_pay_without_pre_charging_request_ocpi_with_partial_payment(membership_mock, send_invoice_mail_mock,
                                                                          get_invoice_mock, send_recurring_payment_mock,
                                                                          test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            preferred_payment_flow=PreferredPaymentFlowEnums.partial
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Partial',
            preferred_payment_method='Wallet',
            is_low_wallet_balance=True,
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10.00

        await utils.pay_without_pre_charging_request_ocpi(
            db,
            10.0,
            membership_id,
            20.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            datetime.now(),
            datetime.now(),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 0.0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.partial).one()
        assert float(result.amount) == float(20.0)
        assert float(result.wallet_deduct_amount) == float(10.0)
        assert float(result.non_wallet_deduct_amount) == float(10.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.get_invoice_ocpi')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
@pytest.mark.asyncio
async def test_pay_without_pre_charging_request_ocpi_partial_payment_credit_card(membership_mock,
                                                                                 send_invoice_mail_mock,
                                                                                 get_invoice_mock,
                                                                                 send_recurring_payment_mock,
                                                                                 test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            preferred_payment_flow=PreferredPaymentFlowEnums.partial
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Partial',
            preferred_payment_method='Credit-Card',
        )
        db.commit()

        wallet = WalletFactory(member_id=membership_id)
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 20.00

        await utils.pay_without_pre_charging_request_ocpi(
            db,
            20.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            datetime.now(),
            datetime.now(),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 20.0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
        assert float(result.amount) == float(10.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_with_wallet_with_same_amount_become_0(membership_mock, send_invoice_mail_mock,
                                                                                get_invoice_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='21.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        utils.pay_without_pre_charging_request(
            db,
            21.0,
            membership_id,
            21.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 0

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.wallet).one()
        assert float(result.amount) == float(21.0)


@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
@pytest.mark.asyncio
async def test_pay_without_pre_charging_request_ruby_with_wallet_with_same_amount_become_0(membership_mock,
                                                                                           send_invoice_mail_mock,
                                                                                           get_invoice_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='21.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        await ruby_proxy_utils.pay_without_pre_charging_request_ruby(
            db,
            21.0,
            membership_id,
            21.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 0.00

    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.wallet).one()
        assert float(result.amount) == float(21.0)


@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.get_invoice_ocpi')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
@pytest.mark.asyncio
async def test_pay_without_pre_charging_request_ocpi_with_wallet_same_amount_become_0(membership_mock,
                                                                                      send_invoice_mail_mock,
                                                                                      get_invoice_mock,
                                                                                      send_recurring_payment_mock,
                                                                                      test_db):
    with contextmanager(create_db_session)() as db:
        send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='21.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        await utils.pay_without_pre_charging_request_ocpi(
            db,
            21.0,
            membership_id,
            21.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            datetime.now(),
            datetime.now(),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 0.00

    # As of July 12, 2024, OCPI will no longer deduct wallet - REVERTED ON JULY 25, 2024
    with contextmanager(create_db_session)() as db:
        result = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.wallet).one()
        assert float(result.amount) == float(21.0)

    # As of July 12, 2024, OCPI will no longer deduct wallet - REVERTED ON JULY 25, 2024
    # with contextmanager(create_db_session)() as db:
    #     result = db.query(models.PaymentRequest).filter(
    #         models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
    #     assert float(result.amount) == float(21.0)


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_pay_without_pre_charging_request_with_credit_card(membership_mock, send_recurring_payment_mock,
                                                                     get_invoice_mock, send_invoice_mail_mock,
                                                                     test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()
        PaymentRequestFactory(
            amount='0.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        utils.pay_without_pre_charging_request(
            db,
            0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        query = db.query(models.RecurringPayment).filter()
        assert query.count() != 0

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        pr = db.query(models.PaymentRequest).one()
        assert pr.status == schema.PaymentRequestStatus.pending


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_pay_without_pre_charging_request_with_wallet(membership_mock, send_recurring_payment_mock,
                                                                get_invoice_mock, send_invoice_mail_mock,
                                                                test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
            preferred_payment_flow=PreferredPaymentFlowEnums.partial
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()
        PaymentRequestFactory(
            amount='0.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='charging_session_bill_for_1234',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        utils.pay_without_pre_charging_request(
            db,
            0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        query = db.query(models.RecurringPayment).filter()
        assert query.count() != 0

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        pr = db.query(models.PaymentRequest).one()
        assert pr.status == schema.PaymentRequestStatus.pending


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_pay_without_pre_charging_request_with_partial_payment(membership_mock,
                                                                         send_invoice_mail_mock,
                                                                         get_invoice_mock,
                                                                         send_recurring_payment_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Declined'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Partial',
            preferred_payment_method='Wallet',
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10.00

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.partial,
            billing_description='charging_session_bill_for_1234',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
            wallet_deduct_amount='10.00',
            wallet_deduct_status=schema.PaymentRequestStatus.done,
            non_wallet_deduct_amount='10.00',
            non_wallet_deduct_status=schema.PaymentRequestStatus.pending,
            charging_session_bill_id=str(charging_session_bill.id),
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 2
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 00.00

        utils.pay_without_pre_charging_request(
            db,
            10.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 00.00

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 2

        pr = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.invoice_number == 'MGT-1012',
            models.PaymentRequest.reason == schema.PaymentRequestReason.payment).one()
        db.commit()

        assert pr.status == schema.PaymentRequestStatus.rejected


@patch('app.utils.send_recurring_payment_my')
@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebill_pay_without_pre_charging_request_with_partial_payment_w_more_wallet_balance(membership_mock,
                                                                                            send_invoice_mail_mock,
                                                                                            get_invoice_mock,
                                                                                            send_recurring_payment_mock,
                                                                                            test_db,
                                                                                            ):
    with contextmanager(create_db_session)() as db:
        send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Partial',
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10.00

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.partial,
            billing_description='charging_session_bill_for_1234',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
            wallet_deduct_amount='10.00',
            wallet_deduct_status=schema.PaymentRequestStatus.done,
            non_wallet_deduct_amount='10.00',
            non_wallet_deduct_status=schema.PaymentRequestStatus.pending,
            charging_session_bill_id=str(charging_session_bill.id),
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 2
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 00.00

        PaymentRequestFactory(
            amount='50.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 50.00

        utils.pay_without_pre_charging_request(
            db,
            10.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 50.00

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 3

        pr = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.invoice_number == 'MGT-1012',
            models.PaymentRequest.reason == schema.PaymentRequestReason.payment).one()
        db.commit()

        assert pr.status == schema.PaymentRequestStatus.pending

        pr = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.invoice_number == 'MGT-1012',
            models.PaymentRequest.reason == schema.PaymentRequestReason.payment).count()

        db.commit()

        assert pr == 1


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.settings.ENABLE_PARTIAL_PAYMENT_FLOW', 'True')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_pay_without_pre_charging_request_with_partial_payment_rejected(membership_mock,
                                                                                  send_invoice_mail_mock,
                                                                                  get_invoice_mock,
                                                                                  send_recurring_payment_mock, test_db,
                                                                                  ):
    with contextmanager(create_db_session)() as db:
        send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Declined'}}, 200)
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending,
            preferred_payment_flow='Partial',
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10.00

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.partial,
            billing_description='charging_session_bill_for_1234',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
            wallet_deduct_amount='10.00',
            wallet_deduct_status=schema.PaymentRequestStatus.done,
            non_wallet_deduct_amount='10.00',
            non_wallet_deduct_status=schema.PaymentRequestStatus.pending,
            charging_session_bill_id=str(charging_session_bill.id),
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 2
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 00.00

        utils.pay_without_pre_charging_request(
            db,
            10.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 00.00

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 2

        pr = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.invoice_number == 'MGT-1012',
            models.PaymentRequest.reason == schema.PaymentRequestReason.payment).one()
        db.commit()

        assert pr.status == schema.PaymentRequestStatus.rejected


@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_pay_without_pre_charging_request_with_wallet_not_allowed(membership_mock, send_invoice_mail_mock,
                                                                            get_invoice_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()
        PaymentRequestFactory(
            amount='10.50',
            type=schema.PaymentRequestType.wallet,
            billing_description='charging_session_bill_for_1234',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 2

        utils.pay_without_pre_charging_request(
            db,
            20.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 20.00

    with contextmanager(create_db_session)() as db:
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 2

        pr = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.reason == schema.PaymentRequestReason.payment).one()
        db.commit()

        assert pr.status == schema.PaymentRequestStatus.failed


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_pay_without_pre_charging_request_with_wallet_initial_recurring(membership_mock,
                                                                                  send_invoice_mail_mock,
                                                                                  get_invoice_mock, test_db):
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()
        PaymentRequestFactory(
            amount='9.50',
            type=schema.PaymentRequestType.recurring,
            billing_description='charging_session_bill_for_1234',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 2

        utils.pay_without_pre_charging_request(
            db,
            20.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10.50

    with contextmanager(create_db_session)() as db:
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 2

        pr = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.reason == schema.PaymentRequestReason.payment).one()
        db.commit()

        assert pr.status == schema.PaymentRequestStatus.done


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_with_previous_recurring_payment_pay_without_pre_charging_request_with_credit_card(
        membership_mock,
        send_recurring_payment_mock,
        get_invoice_mock, send_invoice_mail_mock,
        test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()
        pr = PaymentRequestFactory(
            amount='0.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        RecurringPaymentFactory(payment_request_id=pr.id)
        db.commit()
        query = db.query(models.RecurringPayment).filter()
        assert query.count() == 1

        utils.pay_without_pre_charging_request(
            db,
            0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        query = db.query(models.RecurringPayment).filter(models.RecurringPayment.payment_request_id == pr.id)
        assert query.count() == 2

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        pr = db.query(models.PaymentRequest).one()
        assert pr.status == schema.PaymentRequestStatus.pending


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_with_no_previous_recurring_payment_pay_without_pre_charging_request_with_credit_card(
        membership_mock,
        send_recurring_payment_mock,
        get_invoice_mock, send_invoice_mail_mock,
        test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()
        pr = PaymentRequestFactory(
            amount='0.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        utils.pay_without_pre_charging_request(
            db,
            0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        query = db.query(models.RecurringPayment).filter(models.RecurringPayment.payment_request_id == pr.id)
        assert query.count() == 1

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        pr = db.query(models.PaymentRequest).one()
        assert pr.status == schema.PaymentRequestStatus.pending


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_with_multiple_previous_recurring_payment_pay_without_pre_charging_request_with_credit_card(
        membership_mock,
        send_recurring_payment_mock,
        get_invoice_mock, send_invoice_mail_mock,
        test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()
        pr = PaymentRequestFactory(
            amount='0.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()
        query = db.query(models.RecurringPayment).filter()
        assert query.count() == 6

        utils.pay_without_pre_charging_request(
            db,
            0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        query = db.query(models.RecurringPayment).filter(models.RecurringPayment.payment_request_id == pr.id)
        assert query.count() == 7

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        pr = db.query(models.PaymentRequest).one()
        assert pr.status == schema.PaymentRequestStatus.pending


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_without_pre_charging_request_with_credit_card_fails(membership_mock, send_recurring_payment_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Rejected'}}, 200)

    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        utils.pay_without_pre_charging_request(
            db,
            0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        query = db.query(models.RecurringPayment).filter()
        assert query.count() == 1

        pr = db.query(models.PaymentRequest).one()
        assert pr.status == schema.PaymentRequestStatus.rejected


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_with_multiple_previous_recurring_payment_pay_without_pre_charging_request_with_credit_card_fails(
        membership_mock,
        send_recurring_payment_mock,
        test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Rejected'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()
        pr = PaymentRequestFactory(
            amount='0.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()

        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()
        query = db.query(models.RecurringPayment).filter()
        assert query.count() == 6

        utils.pay_without_pre_charging_request(
            db,
            0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012'
        )
        query = db.query(models.RecurringPayment).filter(models.RecurringPayment.payment_request_id == pr.id)
        assert query.count() == 7

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        pr = db.query(models.PaymentRequest).one()
        assert pr.status == schema.PaymentRequestStatus.rejected


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_pay_without_pre_charging_request_ocpi(membership_mock, send_recurring_payment_mock,
                                                         get_invoice_mock, send_invoice_mail_mock,
                                                         test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()
        pr = PaymentRequestFactory(
            amount='0.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1
        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()

        pr = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
        assert pr.status == schema.PaymentRequestStatus.failed

        utils.pay_without_pre_charging_request(
            db,
            0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012',
            is_ocpi=True
        )
        query = db.query(models.RecurringPayment).filter()
        assert query.count() == 2

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 1

        pr = db.query(models.PaymentRequest).one()
        assert pr.status == schema.PaymentRequestStatus.pending


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_rebilling_pay_without_pre_charging_request_ocpi_with_wallet(membership_mock, send_recurring_payment_mock,
                                                                     get_invoice_mock, send_invoice_mail_mock,
                                                                     test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        db.commit()
        wallet = WalletFactory(
            member_id=membership_id
        )
        db.commit()

        pr = PaymentRequestFactory(
            amount='20.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Charge Wallet',
            reason=schema.PaymentRequestReason.deposit,
            member_id=membership_id,
            status=schema.PaymentRequestStatus.done,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
        )
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()
        PaymentRequestFactory(
            amount='10.00',
            type=schema.PaymentRequestType.recurring,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.failed,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 2

        db.commit()
        RecurringPaymentFactory(id=uuid.uuid4(), payment_request_id=pr.id)
        db.commit()
        query = db.query(models.RecurringPayment).filter()
        assert query.count() == 1

        pr = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
        assert pr.status == schema.PaymentRequestStatus.failed

        utils.pay_without_pre_charging_request(
            db,
            50000.0,
            membership_id,
            10.0,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            'bill_name',
            'bill_email',
            'bill_mobile',
            'cc_token',
            'MYR',
            json.loads(schema.MembershipResponse.from_orm(mem).json()),
            'MGT-1012',
            is_ocpi=True
        )
        query = db.query(models.RecurringPayment).filter()
        assert query.count() != 0
        # As of July 12, 2024, OCPI will no longer deduct wallet - REVERTED ON JULY 25, 2024
        assert query.count() == 1
        # As of July 12, 2024, OCPI will no longer deduct wallet - REVERTED ON JULY 25, 2024
        # assert query.count() == 1

        query = db.query(models.PaymentRequest).filter()
        assert query.count() == 2

        # pr = db.query(models.PaymentRequest).filter(
        #     models.PaymentRequest.type == schema.PaymentRequestType.recurring).one()
        # As of July 12, 2024, OCPI will no longer deduct wallet - REVERTED ON JULY 25, 2024
        # assert pr is None

        # As of July 12, 2024, OCPI will no longer deduct wallet - REVERTED ON JULY 25, 2024
        wallet = crud.update_wallet_balance(db, wallet.id)
        assert float(wallet.balance) == 10

        assert pr.status == schema.PaymentRequestStatus.done


@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_capture_pre_auth_request_to_pg_gateway')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_with_preauth_payment_success(membership_mock, send_capture_payment_mock, get_invoice_mock,
                                          send_invoice_mail_mock, test_db):
    send_capture_payment_mock.return_value = MockResponse({'StatCode': 00, 'Domain': 'ABC'}, 200,
                                                          content=b'{"StatCode": 00}')
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)

        pr = PaymentRequestFactory(
            amount='150.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Pre-Auth',
            reason=schema.PaymentRequestReason.pre_auth,
            status=schema.PaymentRequestStatus.done,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        pre_auth_payment = PreAuthPaymentFactory(
            transaction_type=schema.TransactionType.auts,
            transaction_channel=schema.TransactionChannel.credit,
            token='TOKEN123',
            currency=pr.currency,
            amount=pr.amount,
            bill_name='BILL NAME',
            bill_email=staff.email,
            bill_mobile=staff.phone_number,
            bill_desc=pr.billing_description,
            payment_request_id=str(pr.id),
            reference_id=str(pr.invoice_number),
            is_3ds=True,
            payment_type='Credit-Card',
            payment_gateway='Fiuu',
            is_successful=True,
            response={},
            is_binded=True,
            is_refunded=False,
            transaction_id='TRANS123',
            is_used=False,
            failed_refund=False,
            charging_session_id=CHARGING_SESSION_ID,
        )
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        utils.pay_with_pre_auth_request(
            db,
            pre_auth_payment.id,
            pr.id,
            membership_id,
            10.50,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            pr.currency,
            {},
            'MGT-123',
            False,
            reference_number=pre_auth_payment.reference_id,
            cc_info={}
        )
        query = db.query(models.PreAuthPaymentCapture).filter()
        assert query.count() == 1
        result = query.one()
        assert result.reference_id == pre_auth_payment.reference_id
        query = db.query(models.PreAuthPayment).filter()
        assert query.count() == 1
        result = query.one()
        assert result.is_used
        assert float(result.used_amount) == float(10.50)
        db_pr = db.query(models.ChargingSessionBill).one()
        assert db_pr.status == 'Paid'


@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_capture_pre_auth_request_to_pg_gateway')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_with_preauth_payment_failed_rebill_still_within_timeframe(membership_mock, send_recurring_payment_mock,
                                                                       send_capture_payment_mock,
                                                                       get_invoice_mock,
                                                                       send_invoice_mail_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    send_capture_payment_mock.return_value = MockResponse({'StatCode': 00, 'Domain': 'ABC'}, 200,
                                                          content=b'{"StatCode": 00}')
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )

        db.commit()

        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)

        pr = PaymentRequestFactory(
            amount='150.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Pre-Auth',
            reason=schema.PaymentRequestReason.pre_auth,
            status=schema.PaymentRequestStatus.done,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-1012',
        )
        db.commit()
        pre_auth_payment = PreAuthPaymentFactory(
            transaction_type=schema.TransactionType.auts,
            transaction_channel=schema.TransactionChannel.credit,
            token='TOKEN123',
            currency=pr.currency,
            amount=pr.amount,
            bill_name='BILL NAME',
            bill_email=staff.email,
            bill_mobile=staff.phone_number,
            bill_desc=pr.billing_description,
            payment_request_id=str(pr.id),
            reference_id=str(pr.invoice_number),
            is_3ds=True,
            payment_type='Credit-Card',
            payment_gateway='Fiuu',
            is_successful=True,
            response={},
            is_binded=True,
            is_refunded=False,
            transaction_id='TRANS123',
            is_used=False,
            failed_refund=False,
            charging_session_id=CHARGING_SESSION_ID,
        )
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        pr = PaymentRequestFactory(
            amount='10.50',
            type=schema.PaymentRequestType.direct,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.rejected,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number=pre_auth_payment.reference_id,
            charging_session_bill_id=charging_session_bill.id,
        )
        db.commit()
        _ = PreAuthPaymentCaptureFactory(
            transaction_id='TRANS-123',
            amount='10.50',
            reference_id=pre_auth_payment.reference_id,
            response={},
            pre_auth_payment_id=pre_auth_payment.id,
            payment_request_id=pr.id
        )
        db.commit()
        pre_auth_payment = db.query(models.PreAuthPayment).one()
        assert pre_auth_payment.is_used is False

        utils.pay_with_pre_auth_request(
            db,
            pre_auth_payment.id,
            pr.id,
            membership_id,
            10.50,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            pr.currency,
            {},
            'MGT-123',
            False,
            reference_number=pre_auth_payment.reference_id,
            cc_info={}
        )

        query = db.query(models.PreAuthPaymentCapture).filter()
        assert query.count() == 2
        result = query.all()
        for i in result:
            assert i.reference_id == pre_auth_payment.reference_id
        query = db.query(models.PreAuthPayment).filter()
        assert query.count() == 1
        result = query.one()
        assert result.is_used
        assert float(result.used_amount) == float(10.50)
        db_pr = db.query(models.ChargingSessionBill).one()
        assert db_pr.status == 'Paid'


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_capture_pre_auth_request_to_pg_gateway')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_with_preauth_payment_failed_rebill_expired_pay_with_recurring(membership_mock, send_recurring_payment_mock,
                                                                           send_capture_payment_mock,
                                                                           get_invoice_mock,
                                                                           send_invoice_mail_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    send_capture_payment_mock.return_value = MockResponse({'StatCode': 00, 'Domain': 'ABC'}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        _ = WalletFactory(
            member_id=membership_id
        )
        db.commit()
        pr = PaymentRequestFactory(
            amount='150.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Pre-Auth',
            reason=schema.PaymentRequestReason.pre_auth,
            status=schema.PaymentRequestStatus.done,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='PRE-1012',
        )
        db.commit()
        pre_auth_payment = PreAuthPaymentFactory(
            transaction_type=schema.TransactionType.auts,
            transaction_channel=schema.TransactionChannel.credit,
            token='TOKEN123',
            currency=pr.currency,
            amount=pr.amount,
            bill_name='BILL NAME',
            bill_email=staff.email,
            bill_mobile=staff.phone_number,
            bill_desc=pr.billing_description,
            payment_request_id=str(pr.id),
            reference_id=str(pr.invoice_number),
            is_3ds=True,
            payment_type='Credit-Card',
            payment_gateway='Fiuu',
            is_successful=True,
            response={},
            is_binded=True,
            is_refunded=False,
            transaction_id='TRANS123',
            is_used=False,
            failed_refund=False,
            charging_session_id=CHARGING_SESSION_ID,
            created_at=datetime.now() - timedelta(days=30)
        )
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()

        pr = PaymentRequestFactory(
            amount='10.50',
            type=schema.PaymentRequestType.direct,
            billing_description='Pre Charging Request',
            reason=schema.PaymentRequestReason.payment,
            connector_id=CONNECTOR['id'],
            status=schema.PaymentRequestStatus.rejected,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='MGT-123',
            charging_session_bill_id=charging_session_bill.id,
            reference_number=pre_auth_payment.reference_id,
        )
        db.commit()
        _ = PreAuthPaymentCaptureFactory(
            transaction_id='TRANS-123',
            amount='10.50',
            reference_id=pre_auth_payment.reference_id,
            response={},
            pre_auth_payment_id=pre_auth_payment.id,
            payment_request_id=pr.id
        )
        db.commit()
        pre_auth_payment = db.query(models.PreAuthPayment).one()
        assert pre_auth_payment.is_used is False

        utils.pay_with_pre_auth_request(
            db,
            pre_auth_payment.id,
            pr.id,
            membership_id,
            10.50,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            pr.currency,
            {},
            'MGT-123',
            False,
            reference_number=pre_auth_payment.reference_id,
            cc_info={'bill_name': 'bill_name',
                     'bill_email': 'bill_email',
                     'bill_mobile': 'bill_mobile',
                     'cc_token': 'cc_token',
                     'pay_with_cc': True}
        )
        query = db.query(models.PreAuthPaymentCapture).filter()
        assert query.count() == 1
        result = query.one()
        assert result.reference_id == pre_auth_payment.reference_id
        query = db.query(models.PreAuthPayment).filter()
        assert query.count() == 1
        result = query.one()
        assert result.used_amount is None
        db_pr = db.query(models.ChargingSessionBill).one()
        assert db_pr.status == 'Pending'
        query = db.query(models.RecurringPayment).filter()
        assert query.count() == 1
        result = query.one()
        assert float(result.amount) == 10.50
        assert result.order_id == 'MGT-123'


@patch('app.settings.PAYMENT_GATEWAY_TYPE', 'Fiuu')
@patch('app.utils.send_invoice_mail')
@patch('app.utils.crud.get_invoice')
@patch('app.utils.send_capture_pre_auth_request_to_pg_gateway')
@patch('app.utils.send_recurring_payment_my')
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_pay_with_preauth_payment_expired_pay_with_recurring(membership_mock, send_recurring_payment_mock,
                                                             send_capture_payment_mock,
                                                             get_invoice_mock,
                                                             send_invoice_mail_mock, test_db):
    send_recurring_payment_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)

    send_capture_payment_mock.return_value = MockResponse({'StatCode': 00, 'Domain': 'ABC'}, 200)
    get_invoice_mock.return_value = MockResponse({0: {'status': 'Accepted'}}, 200)
    with contextmanager(create_db_session)() as db:
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)

        staff = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()
        membership_id = str(mem.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(mem)

        CreditCardFactory(member_id=membership_id)
        _ = WalletFactory(
            member_id=membership_id
        )
        db.commit()
        pr = PaymentRequestFactory(
            amount='150.00',
            type=schema.PaymentRequestType.direct,
            billing_description='Pre-Auth',
            reason=schema.PaymentRequestReason.pre_auth,
            status=schema.PaymentRequestStatus.done,
            member_id=membership_id,
            meta=json.loads(schema.MembershipResponse.from_orm(mem).json()),
            invoice_number='PRE-1012',
        )
        db.commit()
        pre_auth_payment = PreAuthPaymentFactory(
            transaction_type=schema.TransactionType.auts,
            transaction_channel=schema.TransactionChannel.credit,
            token='TOKEN123',
            currency=pr.currency,
            amount=pr.amount,
            bill_name='BILL NAME',
            bill_email=staff.email,
            bill_mobile=staff.phone_number,
            bill_desc=pr.billing_description,
            payment_request_id=str(pr.id),
            reference_id=str(pr.invoice_number),
            is_3ds=True,
            payment_type='Credit-Card',
            payment_gateway='Fiuu',
            is_successful=True,
            response={},
            is_binded=True,
            is_refunded=False,
            transaction_id='TRANS123',
            is_used=False,
            failed_refund=False,
            charging_session_id=CHARGING_SESSION_ID,
            created_at=datetime.now() - timedelta(days=30)
        )
        db.commit()

        charging_session_bill = ChargingSessionBillFactory(
            charging_session_id=CHARGING_SESSION_ID,
            usage_amount=10.5,
            usage_type=schema.BillingType.kwh,
            status=schema.ChargingSessionBillStatus.pending
        )
        db.commit()
        utils.pay_with_pre_auth_request(
            db,
            pre_auth_payment.id,
            pr.id,
            membership_id,
            10.50,
            CHARGING_SESSION_ID,
            CONNECTOR['id'],
            str(charging_session_bill.id),
            pr.currency,
            {},
            'MGT-123',
            False,
            reference_number=pre_auth_payment.reference_id,
            cc_info={'bill_name': 'bill_name',
                     'bill_email': 'bill_email',
                     'bill_mobile': 'bill_mobile',
                     'cc_token': 'cc_token',
                     'pay_with_cc': True}
        )

        query = db.query(models.PreAuthPayment).filter()
        assert query.count() == 1
        result = query.one()
        assert result.used_amount is None
        db_pr = db.query(models.ChargingSessionBill).one()
        assert db_pr.status == 'Pending'
        query = db.query(models.RecurringPayment).filter()
        assert query.count() == 1
        result = query.one()
        assert float(result.amount) == 10.50
        assert result.order_id == 'MGT-123'


@patch('app.utils.crud.base.BaseCRUD.membership')
def test_topup_prepaid_wallet_transaction(membership_mock, test_db):
    with contextmanager(create_db_session)() as db:
        # 1. create wallet
        organization = OrganizationFactory()
        db.flush()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.flush()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.flush()
        membership_id = str(member.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(member)

        wallet = WalletFactory(
            member_id=membership_id,
            currency=schema.Currency.myr,
        )
        db.commit()

        # 2. create prepaid wallet plan + batch + subscription + topup
        prepaid_wallet_plan = PrepaidWalletPlanFactory(
            plan_type=schema.PrepaidWalletPlanType.carry_over,
            interval=schema.PrepaidWalletPlanInterval.monthly,
        )
        db.flush()

        prepaid_wallet_plan_id = str(prepaid_wallet_plan.id)
        prepaid_wallet_subscription = ********************************(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            member_id=membership_id,
        )
        prepaid_wallet_plan_batch = PrepaidWalletPlanBatchFactory(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            # snapshots of the plan information at the time of batch creation
            amount=prepaid_wallet_plan.amount,
            currency=prepaid_wallet_plan.currency,
            plan_type=prepaid_wallet_plan.plan_type,
            interval=prepaid_wallet_plan.interval,
            start_time=datetime.now(ZoneInfo('UTC')),
            end_time=datetime.now(ZoneInfo('UTC')) + timedelta(days=30),
            status=None,
        )
        db.flush()

        prepaid_wallet_subscription_id = str(prepaid_wallet_subscription.id)
        prepaid_wallet_plan_batch_id = str(prepaid_wallet_plan_batch.id)
        utils.topup_prepaid_wallet_transaction(
            db=db,
            member_id=membership_id,
            amount=100.50,
            currency=schema.PrepaidWalletCurrency.myr,
            prepaid_wallet_subscription_id=prepaid_wallet_subscription_id,
            prepaid_wallet_plan_batch_id=prepaid_wallet_plan_batch_id,
            auto_commit=False,
        )
        utils.topup_prepaid_wallet_transaction(
            db=db,
            member_id=membership_id,
            amount=50.30,
            currency=schema.PrepaidWalletCurrency.myr,
            prepaid_wallet_subscription_id=prepaid_wallet_subscription_id,
            prepaid_wallet_plan_batch_id=prepaid_wallet_plan_batch_id,
            auto_commit=False,
        )
        db.commit()

        # 3. validate number of transactions created and information is correct
        prepaid_wallet_transactions = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.prepaid_wallet_subscription_id == prepaid_wallet_subscription_id,
            models.PrepaidWalletTransaction.currency == schema.PrepaidWalletCurrency.myr,
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
        ).all()
        assert len(prepaid_wallet_transactions) == 2
        assert sum(trans.amount for trans in prepaid_wallet_transactions) == Decimal('150.80')

        payment_requests = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.reason == schema.PaymentRequestReason.prepaid_wallet_topup,
            models.PaymentRequest.status == schema.PaymentRequestStatus.done,
            models.PaymentRequest.member_id == membership_id,
            models.PaymentRequest.currency == schema.Currency.myr,
            models.PaymentRequest.type == schema.PaymentRequestType.wallet,
        ).all()
        assert len(payment_requests) == 2
        assert sum(Decimal(pr.amount) for pr in payment_requests) == Decimal('150.80')
        assert (
            {pr.prepaid_wallet_transaction_id for pr in payment_requests}
            == {trans.id for trans in prepaid_wallet_transactions}
        )

        wallet_transactions = db.query(models.WalletTransaction).filter(
            models.WalletTransaction.wallet_id == wallet.id,
            models.WalletTransaction.currency == schema.Currency.myr,
            models.WalletTransaction.transaction_type == schema.PaymentRequestReason.prepaid_wallet_topup,
        ).all()
        assert len(wallet_transactions) == 2
        assert sum(wt.amount for wt in wallet_transactions) == Decimal('150.80')
        assert (
            {wt.prepaid_wallet_transaction_id for wt in wallet_transactions}
            == {trans.id for trans in prepaid_wallet_transactions}
        )

        # 4. Check if the wallet balance is correct
        utils.update_wallet_balance(db=db, wallet_id=wallet.id)
        wallet = db.query(models.Wallet).get(wallet.id)
        assert Decimal(str(wallet.balance)) == Decimal('150.80')

        crud.update_wallet_balance(db=db, wallet_id=wallet.id)
        wallet = crud.get_wallet(db=db, wallet_id=wallet.id)
        assert Decimal(str(wallet.balance)) == Decimal('150.80')


@patch('app.utils.crud.base.BaseCRUD.membership')
def test_deduct_prepaid_wallet_transaction(membership_mock, test_db):
    with contextmanager(create_db_session)() as db:
        # 1. create wallet
        organization = OrganizationFactory()
        db.flush()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.flush()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.flush()
        membership_id = str(member.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(member)

        wallet = WalletFactory(
            member_id=membership_id,
            currency=schema.Currency.myr,
        )
        db.commit()

        # 2. create prepaid wallet plan + batch + subscription + deduction
        prepaid_wallet_plan = PrepaidWalletPlanFactory(
            plan_type=schema.PrepaidWalletPlanType.carry_over,
            interval=schema.PrepaidWalletPlanInterval.monthly,
        )
        db.flush()

        prepaid_wallet_plan_id = str(prepaid_wallet_plan.id)
        prepaid_wallet_subscription = ********************************(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            member_id=membership_id,
        )
        prepaid_wallet_plan_batch = PrepaidWalletPlanBatchFactory(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            # snapshots of the plan information at the time of batch creation
            amount=prepaid_wallet_plan.amount,
            currency=prepaid_wallet_plan.currency,
            plan_type=prepaid_wallet_plan.plan_type,
            interval=prepaid_wallet_plan.interval,
            start_time=datetime.now(ZoneInfo('UTC')),
            end_time=datetime.now(ZoneInfo('UTC')) + timedelta(days=30),
            status=None,
        )
        db.flush()

        prepaid_wallet_subscription_id = str(prepaid_wallet_subscription.id)
        prepaid_wallet_plan_batch_id = str(prepaid_wallet_plan_batch.id)
        utils.deduct_prepaid_wallet_transaction(
            db=db,
            member_id=membership_id,
            amount=100.50,
            currency=schema.PrepaidWalletCurrency.myr,
            prepaid_wallet_subscription_id=prepaid_wallet_subscription_id,
            prepaid_wallet_plan_batch_id=prepaid_wallet_plan_batch_id,
            prepaid_wallet_transaction_id=None,  # relevant in integration test
            charging_session_bill_ids=None,  # relevant in integration test
            auto_commit=False,
        )
        utils.deduct_prepaid_wallet_transaction(
            db=db,
            member_id=membership_id,
            amount=50.30,
            currency=schema.PrepaidWalletCurrency.myr,
            prepaid_wallet_subscription_id=prepaid_wallet_subscription_id,
            prepaid_wallet_plan_batch_id=prepaid_wallet_plan_batch_id,
            prepaid_wallet_transaction_id=None,  # relevant in integration test
            charging_session_bill_ids=None,  # relevant in integration test
            auto_commit=False,
        )
        db.commit()

        # 3. validate number of transactions created and information is correct
        prepaid_wallet_transactions = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.prepaid_wallet_subscription_id == prepaid_wallet_subscription_id,
            models.PrepaidWalletTransaction.currency == schema.PrepaidWalletCurrency.myr,
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.deduction,
        ).all()
        assert len(prepaid_wallet_transactions) == 2
        assert sum(trans.amount for trans in prepaid_wallet_transactions) == Decimal('150.80')

        payment_requests = db.query(models.PaymentRequest).filter(
            models.PaymentRequest.reason == schema.PaymentRequestReason.prepaid_wallet_deduction,
            models.PaymentRequest.status == schema.PaymentRequestStatus.done,
            models.PaymentRequest.member_id == membership_id,
            models.PaymentRequest.currency == schema.Currency.myr,
            models.PaymentRequest.type == schema.PaymentRequestType.wallet,
        ).all()
        assert len(payment_requests) == 2
        assert sum(Decimal(pr.amount) for pr in payment_requests) == Decimal('150.80')
        assert (
            {pr.prepaid_wallet_transaction_id for pr in payment_requests}
            == {trans.id for trans in prepaid_wallet_transactions}
        )

        wallet_transactions = db.query(models.WalletTransaction).filter(
            models.WalletTransaction.wallet_id == wallet.id,
            models.WalletTransaction.currency == schema.Currency.myr,
            models.WalletTransaction.transaction_type == schema.PaymentRequestReason.prepaid_wallet_deduction,
        ).all()
        assert len(wallet_transactions) == 2
        assert sum(wt.amount for wt in wallet_transactions) == Decimal('150.80')
        assert (
            {wt.prepaid_wallet_transaction_id for wt in wallet_transactions}
            == {trans.id for trans in prepaid_wallet_transactions}
        )

        # 4. Check if the wallet balance is correct
        utils.update_wallet_balance(db=db, wallet_id=wallet.id)
        wallet = db.query(models.Wallet).get(wallet.id)
        assert Decimal(str(wallet.balance)) == Decimal('-150.80')

        crud.update_wallet_balance(db=db, wallet_id=wallet.id)
        wallet = crud.get_wallet(db=db, wallet_id=wallet.id)
        assert Decimal(str(wallet.balance)) == Decimal('-150.80')


@pytest.mark.parametrize(
    "start_time, interval, next_start_time, next_end_time",
    [
        (
            datetime(2025, 1, 1, 0, 0, 0, 0),
            schema.PrepaidWalletPlanInterval.weekly,
            datetime(2025, 1, 8, 0, 0, 0, 0),
            datetime(2025, 1, 14, 23, 59, 59, 999999),
        ),
        (
            datetime(2025, 1, 25, 0, 0, 0, 0),
            schema.PrepaidWalletPlanInterval.weekly,
            datetime(2025, 2, 1, 0, 0, 0, 0),
            datetime(2025, 2, 7, 23, 59, 59, 999999),
        ),
        (  # leap year
            datetime(2024, 2, 25, 0, 0, 0, 0),
            schema.PrepaidWalletPlanInterval.weekly,
            datetime(2024, 3, 3, 0, 0, 0, 0),
            datetime(2024, 3, 9, 23, 59, 59, 999999),
        ),
        (  # leap year
            datetime(2024, 1, 1, 0, 0, 0, 0),
            schema.PrepaidWalletPlanInterval.monthly,
            datetime(2024, 2, 1, 0, 0, 0, 0),
            datetime(2024, 2, 29, 23, 59, 59, 999999),
        ),
        (
            datetime(2025, 7, 10, 0, 0, 0, 0),
            schema.PrepaidWalletPlanInterval.monthly,
            datetime(2025, 8, 1, 0, 0, 0, 0),
            datetime(2025, 8, 31, 23, 59, 59, 999999),
        ),
        (
            datetime(2025, 3, 3, 0, 0, 0, 0),
            schema.PrepaidWalletPlanInterval.yearly,
            datetime(2026, 1, 1, 0, 0, 0, 0),
            datetime(2026, 12, 31, 23, 59, 59, 999999),
        ),
        (  # leap year
            datetime(2023, 10, 3, 0, 0, 0, 0),
            schema.PrepaidWalletPlanInterval.yearly,
            datetime(2024, 1, 1, 0, 0, 0, 0),
            datetime(2024, 12, 31, 23, 59, 59, 999999),
        ),
        (  # with timezone
            datetime(2025, 1, 1, 0, 0, 0, 0, tzinfo=ZoneInfo('Asia/Kuala_Lumpur')),
            schema.PrepaidWalletPlanInterval.weekly,
            datetime(2025, 1, 8, 0, 0, 0, 0, tzinfo=ZoneInfo('Asia/Kuala_Lumpur')),
            datetime(2025, 1, 14, 23, 59, 59, 999999, tzinfo=ZoneInfo('Asia/Kuala_Lumpur')),
        ),
    ]
)
def test_get_next_start_end_time(
    test_db,
    start_time,
    interval,
    next_start_time,
    next_end_time,
):
    assert utils.get_next_start_end_time(start_time, interval) == (next_start_time, next_end_time)


@pytest.mark.parametrize(
    "start_time, interval, current_end_time",
    [
        (
            datetime(2025, 1, 1, 11, 22, 33, 456),
            schema.PrepaidWalletPlanInterval.weekly,
            datetime(2025, 1, 7, 23, 59, 59, 999999),
        ),
        (
            datetime(2025, 1, 25, 23, 59, 59, 999999),
            schema.PrepaidWalletPlanInterval.weekly,
            datetime(2025, 1, 31, 23, 59, 59, 999999),
        ),
        (  # leap year
            datetime(2024, 2, 25, 0, 0, 0, 0),
            schema.PrepaidWalletPlanInterval.weekly,
            datetime(2024, 3, 2, 23, 59, 59, 999999),
        ),
        (  # leap year
            datetime(2024, 2, 1, 11, 22, 33, 456),
            schema.PrepaidWalletPlanInterval.monthly,
            datetime(2024, 2, 29, 23, 59, 59, 999999),
        ),
        (
            datetime(2025, 7, 10, 0, 0, 0, 0),
            schema.PrepaidWalletPlanInterval.monthly,
            datetime(2025, 7, 31, 23, 59, 59, 999999),
        ),
        (
            datetime(2025, 3, 30, 11, 11, 11, 123),
            schema.PrepaidWalletPlanInterval.monthly,
            datetime(2025, 3, 31, 23, 59, 59, 999999),
        ),
        (
            datetime(2025, 3, 3, 0, 0, 0, 0),
            schema.PrepaidWalletPlanInterval.yearly,
            datetime(2025, 12, 31, 23, 59, 59, 999999),
        ),
        (  # leap year
            datetime(2024, 10, 3, 0, 0, 0, 0),
            schema.PrepaidWalletPlanInterval.yearly,
            datetime(2024, 12, 31, 23, 59, 59, 999999),
        ),
        (  # with timezone
            datetime(2025, 1, 1, 11, 22, 33, 456, tzinfo=ZoneInfo('Asia/Kuala_Lumpur')),
            schema.PrepaidWalletPlanInterval.weekly,
            datetime(2025, 1, 7, 23, 59, 59, 999999, tzinfo=ZoneInfo('Asia/Kuala_Lumpur')),
        ),
    ]
)
def test_get_current_end_time(
    test_db,
    start_time,
    interval,
    current_end_time,
):
    assert utils.get_current_end_time(start_time, interval) == current_end_time


def test_create_next_prepaid_wallet_plan_batch(test_db):
    with contextmanager(create_db_session)() as db:
        # 1. create prepaid wallet plan + initial batch
        prepaid_wallet_plan = PrepaidWalletPlanFactory(
            plan_type=schema.PrepaidWalletPlanType.reset,
            interval=schema.PrepaidWalletPlanInterval.monthly,
            is_active=False,
        )
        db.flush()

        start_time = datetime.now(ZoneInfo('UTC')).replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        end_time = utils.get_current_end_time(start_time, schema.PrepaidWalletPlanInterval.monthly)

        prepaid_wallet_plan_id = str(prepaid_wallet_plan.id)
        prepaid_wallet_plan_batch = PrepaidWalletPlanBatchFactory(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            # snapshots of the plan information at the time of batch creation
            amount=prepaid_wallet_plan.amount,
            currency=prepaid_wallet_plan.currency,
            plan_type=prepaid_wallet_plan.plan_type,
            interval=prepaid_wallet_plan.interval,
            start_time=start_time,
            end_time=end_time,
            status=None,
        )
        db.commit()

        prepaid_wallet_plan_batch_id = str(prepaid_wallet_plan_batch.id)

        # 2.1 Next batch not created because plan is inactive
        current_batch_1, next_batch_1 = utils.create_next_prepaid_wallet_plan_batch(db, prepaid_wallet_plan_batch_id)
        assert current_batch_1.id == prepaid_wallet_plan_batch.id
        assert next_batch_1 is None

        batch_count_1 = db.query(models.PrepaidWalletPlanBatch).count()
        assert batch_count_1 == 1

        # 2.2 Next batch created because plan is active
        prepaid_wallet_plan.is_active = True
        db.commit()

        current_batch_2, next_batch_2 = utils.create_next_prepaid_wallet_plan_batch(db, prepaid_wallet_plan_batch_id)
        assert current_batch_2.id == prepaid_wallet_plan_batch.id
        assert current_batch_2.next_batch_id == next_batch_2.id

        batch_count_2 = db.query(models.PrepaidWalletPlanBatch).count()
        assert batch_count_2 == 2

        # 2.3 Next batch not created because next batch already exists
        current_batch_3, next_batch_3 = utils.create_next_prepaid_wallet_plan_batch(db, prepaid_wallet_plan_batch_id)
        assert current_batch_3.id == prepaid_wallet_plan_batch.id
        assert current_batch_3.next_batch_id == next_batch_3.id

        # idempotent (same as previous)
        assert current_batch_3.id == current_batch_2.id
        assert current_batch_3.next_batch_id == current_batch_2.next_batch_id
        batch_count_3 = db.query(models.PrepaidWalletPlanBatch).count()
        assert batch_count_3 == 2

        # 2.4 Create 3rd batch with resume_current=True, this 3rd batch will be created by shortening the 2nd batch
        current_batch_4, next_batch_4 = utils.create_next_prepaid_wallet_plan_batch(
            db, next_batch_3.id, resume_current=True
        )
        assert current_batch_4.id == next_batch_3.id
        assert current_batch_4.next_batch_id == next_batch_4.id

        # check if the end time of current batch is less than start time of next batch
        assert current_batch_4.end_time < next_batch_4.start_time
        assert current_batch_4.end_time == next_batch_4.start_time - timedelta(microseconds=1)
        assert next_batch_4.end_time == end_time
        batch_count_4 = db.query(models.PrepaidWalletPlanBatch).count()
        assert batch_count_4 == 3


def test_create_new_prepaid_wallet_plan_batch(test_db):
    with contextmanager(create_db_session)() as db:
        prepaid_wallet_plan = PrepaidWalletPlanFactory(
            plan_type=schema.PrepaidWalletPlanType.reset,
            interval=schema.PrepaidWalletPlanInterval.monthly,
        )
        db.flush()

        prepaid_wallet_plan_batch = utils.create_new_prepaid_wallet_plan_batch(db, prepaid_wallet_plan.id)
        assert prepaid_wallet_plan_batch is not None
        assert prepaid_wallet_plan_batch.prepaid_wallet_plan_id == prepaid_wallet_plan.id
        assert prepaid_wallet_plan.latest_batch_id == prepaid_wallet_plan_batch.id


@patch('app.utils.crud.base.BaseCRUD.membership')
def test_topup_prepaid_wallet_subscriber_fresh_plan(membership_mock, test_db):
    with contextmanager(create_db_session)() as db:
        # 1.1 create wallet
        organization = OrganizationFactory()
        db.flush()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.flush()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.flush()
        membership_id = str(member.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(member)

        _ = WalletFactory(
            member_id=membership_id,
            currency=schema.Currency.myr,
        )
        db.commit()

        # 1.2 create prepaid wallet plan + batch + subscription + topup
        prepaid_wallet_plan = PrepaidWalletPlanFactory(
            plan_type=schema.PrepaidWalletPlanType.reset,
            interval=schema.PrepaidWalletPlanInterval.monthly,
            amount=50,
            is_active=False,
        )
        db.flush()

        prepaid_wallet_plan_id = str(prepaid_wallet_plan.id)
        prepaid_wallet_plan_batch = utils.create_new_prepaid_wallet_plan_batch(db, prepaid_wallet_plan.id)

        prepaid_wallet_subscription = ********************************(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            member_id=membership_id,
        )
        db.commit()
        db.refresh(prepaid_wallet_plan_batch)
        db.refresh(prepaid_wallet_subscription)

        # 2.1 No topup because plan is not active
        utils.topup_prepaid_wallet_subscriber(
            db=db,
            prepaid_wallet_subscription=prepaid_wallet_subscription,
            prepaid_wallet_plan_batch=prepaid_wallet_plan_batch,
        )
        db.commit()

        topup_transaction_count = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
        ).count()
        assert topup_transaction_count == 0

        prepaid_wallet_plan.is_active = True
        db.commit()
        db.refresh(prepaid_wallet_plan_batch)
        db.refresh(prepaid_wallet_subscription)

        # 2.2 Topup successful after Plan is activated
        utils.topup_prepaid_wallet_subscriber(
            db=db,
            prepaid_wallet_subscription=prepaid_wallet_subscription,
            prepaid_wallet_plan_batch=prepaid_wallet_plan_batch,
        )
        db.commit()

        topup_transaction_count = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
        ).count()
        assert topup_transaction_count == 1

        # 2.3 No topup because already has topup transaction for this batch
        utils.topup_prepaid_wallet_subscriber(
            db=db,
            prepaid_wallet_subscription=prepaid_wallet_subscription,
            prepaid_wallet_plan_batch=prepaid_wallet_plan_batch,
        )
        db.commit()

        topup_transaction_count = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
        ).count()
        assert topup_transaction_count == 1


@pytest.mark.parametrize(
    "plan_type, plan_amount, deduction_amount, expected_topup_amount",
    [
        (schema.PrepaidWalletPlanType.reset, 50, 30, 50),
        (schema.PrepaidWalletPlanType.carry_over, 50, 30, 80),
        (schema.PrepaidWalletPlanType.carry_over, 50, 0, 50),
    ]
)
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_topup_prepaid_wallet_subscriber_existing_plan_next_batch(
    membership_mock,
    test_db,
    plan_type,
    plan_amount,
    deduction_amount,
    expected_topup_amount,
):
    with contextmanager(create_db_session)() as db:
        # 1.1 create wallet
        organization = OrganizationFactory()
        db.flush()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.flush()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.flush()
        membership_id = str(member.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(member)

        _ = WalletFactory(
            member_id=membership_id,
            currency=schema.Currency.myr,
        )
        db.commit()

        # 1.2 create prepaid wallet plan + batch + subscription + topup
        prepaid_wallet_plan = PrepaidWalletPlanFactory(
            plan_type=plan_type,
            interval=schema.PrepaidWalletPlanInterval.monthly,
            amount=plan_amount,
        )
        db.flush()

        prepaid_wallet_plan_id = str(prepaid_wallet_plan.id)
        prepaid_wallet_plan_batch = utils.create_new_prepaid_wallet_plan_batch(db, prepaid_wallet_plan.id)

        prepaid_wallet_subscription = ********************************(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            member_id=membership_id,
        )
        db.commit()
        db.refresh(prepaid_wallet_plan_batch)
        db.refresh(prepaid_wallet_subscription)

        # 2.1 Topup successful since Plan and Subscription is Activa
        utils.topup_prepaid_wallet_subscriber(
            db=db,
            prepaid_wallet_subscription=prepaid_wallet_subscription,
            prepaid_wallet_plan_batch=prepaid_wallet_plan_batch,
        )
        db.commit()

        topup_transaction_count = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
        ).count()
        assert topup_transaction_count == 1

        topup_trans = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
        ).one()

        # 2.2 Artificially create deduction transaction and create and topup next batch
        utils.deduct_prepaid_wallet_transaction(
            db=db,
            member_id=membership_id,
            amount=deduction_amount,
            currency=schema.PrepaidWalletCurrency.myr,
            prepaid_wallet_subscription_id=prepaid_wallet_subscription.id,
            prepaid_wallet_plan_batch_id=prepaid_wallet_plan_batch.id,
            prepaid_wallet_transaction_id=topup_trans.id,
            charging_session_bill_ids=None,
        )
        _, next_batch = utils.create_next_prepaid_wallet_plan_batch(
            db=db,
            prepaid_wallet_plan_batch_id=prepaid_wallet_plan_batch.id,
        )
        db.commit()

        utils.topup_prepaid_wallet_subscriber(
            db=db,
            prepaid_wallet_subscription=prepaid_wallet_subscription,
            prepaid_wallet_plan_batch=next_batch,
        )
        db.commit()

        # 2.3 Check topup transaction count and latest topup's amount
        topup_transaction_count = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
        ).count()
        assert topup_transaction_count == 2

        latest_topup_trans = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
            models.PrepaidWalletTransaction.prepaid_wallet_plan_batch_id == next_batch.id,
        ).one()
        assert latest_topup_trans.amount == expected_topup_amount


@pytest.mark.parametrize(
    "initial_topup, payment_requests, expected_deduction",
    [
        (
            100,
            [
                {"amount": "10", "wallet_deduct_amount": None, "type": schema.PaymentRequestType.wallet},
                {"amount": "20", "wallet_deduct_amount": "15", "type": schema.PaymentRequestType.partial},
                {"amount": "30", "wallet_deduct_amount": "20", "type": schema.PaymentRequestType.partial_direct},
            ],
            Decimal('55'),
        ),
        (
            100,
            [],
            Decimal('100'),
        ),
        (
            0,
            [
                {"amount": "10", "wallet_deduct_amount": None, "type": schema.PaymentRequestType.wallet},
                {"amount": "20", "wallet_deduct_amount": "15", "type": schema.PaymentRequestType.partial},
                {"amount": "30", "wallet_deduct_amount": "20", "type": schema.PaymentRequestType.partial_direct},
            ],
            Decimal('0'),
        ),
        # Add more test cases as needed
    ]
)
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_deduct_unused_prepaid_wallet_topup(
    membership_mock,
    test_db,
    initial_topup,
    payment_requests,
    expected_deduction,
):
    with contextmanager(create_db_session)() as db:
        # 1. create wallet
        organization = OrganizationFactory()
        db.flush()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.flush()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.flush()
        membership_id = str(member.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(member)

        _ = WalletFactory(
            member_id=membership_id,
            currency=schema.Currency.myr,
        )
        db.commit()

        # 2. create prepaid wallet plan + batch + subscription + topup
        prepaid_wallet_plan = PrepaidWalletPlanFactory(
            plan_type=schema.PrepaidWalletPlanType.reset,  # irrelevant to deduction
            interval=schema.PrepaidWalletPlanInterval.monthly,
            amount=initial_topup,
        )
        db.flush()

        prepaid_wallet_plan_id = str(prepaid_wallet_plan.id)
        prepaid_wallet_subscription = ********************************(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            member_id=membership_id,
        )

        utc_today = datetime.now(ZoneInfo('UTC')).replace(hour=0, minute=0, second=0, microsecond=0)
        start_time, end_time = utils.get_next_start_end_time(utc_today, schema.PrepaidWalletPlanInterval.monthly)
        prepaid_wallet_plan_batch = PrepaidWalletPlanBatchFactory(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            # snapshots of the plan information at the time of batch creation
            amount=prepaid_wallet_plan.amount,
            currency=prepaid_wallet_plan.currency,
            plan_type=prepaid_wallet_plan.plan_type,
            interval=prepaid_wallet_plan.interval,
            start_time=start_time,
            end_time=end_time,
            status=None,
        )
        db.flush()

        utils.topup_prepaid_wallet_subscriber(
            db=db,
            prepaid_wallet_subscription=prepaid_wallet_subscription,
            prepaid_wallet_plan_batch=prepaid_wallet_plan_batch,
        )
        db.commit()

        # 3. Use wallet credit by creating payment requests
        payment_request_time = start_time + (end_time - start_time) / 2  # ensure searchable in the batch
        charging_session_bill_ids = set()
        for request in payment_requests:
            charging_session_bill = ChargingSessionBillFactory(
                id=str(uuid.uuid4()),
                charging_session_id=str(uuid.uuid4()),
            )
            payment_request = PaymentRequestFactory(
                type=request["type"],
                amount=request["amount"],
                wallet_deduct_amount=request["wallet_deduct_amount"],
                currency=schema.Currency.myr,
                reason=schema.PaymentRequestReason.payment,
                status=schema.PaymentRequestStatus.done,
                wallet_deduct_status=schema.PaymentRequestStatus.done if request["wallet_deduct_amount"] else None,
                member_id=membership_id,
                charging_session_bill_id=str(charging_session_bill.id),
            )
            payment_request.created_at = payment_request_time
            charging_session_bill_ids.add(payment_request.charging_session_bill_id)

        db.commit()

        # 4. Deduct unused topup
        prepaid_wallet_topup_transaction = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
            models.PrepaidWalletTransaction.prepaid_wallet_plan_batch_id == prepaid_wallet_plan_batch.id,
        ).one()
        utils.deduct_unused_prepaid_wallet_topup(
            db=db,
            topup_transaction=prepaid_wallet_topup_transaction,
            prepaid_wallet_plan_batch=prepaid_wallet_plan_batch,
            auto_commit=True,
        )

        # 5. Check deduction transaction
        prepaid_wallet_deduction_transaction = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.prepaid_wallet_subscription_id == prepaid_wallet_subscription.id,
            models.PrepaidWalletTransaction.prepaid_wallet_plan_batch_id == prepaid_wallet_plan_batch.id,
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.deduction,
        ).one_or_none()

        assert prepaid_wallet_deduction_transaction is not None
        assert prepaid_wallet_deduction_transaction.amount == expected_deduction
        assert prepaid_wallet_deduction_transaction.prepaid_wallet_transaction_id == prepaid_wallet_topup_transaction.id

        # for 0 amount deduction, no charging_session_bill_ids are attached
        if expected_deduction > 0:
            assert {
                str(bill_id) for bill_id in
                prepaid_wallet_deduction_transaction.charging_session_bill_ids
            } == charging_session_bill_ids
        else:
            assert not prepaid_wallet_deduction_transaction.charging_session_bill_ids

        # 6. Idempotency check (won't double-deduct)
        previous_deduction_count = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.deduction,
            models.PrepaidWalletTransaction.prepaid_wallet_subscription_id == prepaid_wallet_subscription.id,
            models.PrepaidWalletTransaction.prepaid_wallet_plan_batch_id == prepaid_wallet_plan_batch.id,
        ).count()
        utils.deduct_unused_prepaid_wallet_topup(
            db=db,
            topup_transaction=prepaid_wallet_topup_transaction,
            prepaid_wallet_plan_batch=prepaid_wallet_plan_batch,
            auto_commit=True,
        )
        latest_deduction_count = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.deduction,
            models.PrepaidWalletTransaction.prepaid_wallet_subscription_id == prepaid_wallet_subscription.id,
            models.PrepaidWalletTransaction.prepaid_wallet_plan_batch_id == prepaid_wallet_plan_batch.id,
        ).count()
        assert previous_deduction_count == latest_deduction_count


@pytest.mark.parametrize(
    "plan_type, initial_topup, payment_requests, expected_deduction, expected_topup, expected_balance",
    [
        (
            schema.PrepaidWalletPlanType.reset,
            100,
            [
                {"amount": "10", "wallet_deduct_amount": None, "type": schema.PaymentRequestType.wallet},
                {"amount": "20", "wallet_deduct_amount": "15", "type": schema.PaymentRequestType.partial},
                {"amount": "30", "wallet_deduct_amount": "20", "type": schema.PaymentRequestType.partial_direct},
            ],
            Decimal('55'),
            Decimal('50'),
            Decimal('50'),
        ),
        (
            schema.PrepaidWalletPlanType.carry_over,
            100,
            [
                {"amount": "10", "wallet_deduct_amount": None, "type": schema.PaymentRequestType.wallet},
                {"amount": "20", "wallet_deduct_amount": "15", "type": schema.PaymentRequestType.partial},
                {"amount": "30", "wallet_deduct_amount": "20", "type": schema.PaymentRequestType.partial_direct},
            ],
            Decimal('55'),
            Decimal('105'),
            Decimal('105'),
        ),
        (
            schema.PrepaidWalletPlanType.reset,
            0,
            [
                {"amount": "10", "wallet_deduct_amount": None, "type": schema.PaymentRequestType.wallet},
                {"amount": "20", "wallet_deduct_amount": "15", "type": schema.PaymentRequestType.partial},
                {"amount": "30", "wallet_deduct_amount": "20", "type": schema.PaymentRequestType.partial_direct},
            ],
            Decimal('0'),
            Decimal('50'),
            Decimal('5'),
        ),
        (
            schema.PrepaidWalletPlanType.carry_over,
            0,
            [
                {"amount": "10", "wallet_deduct_amount": None, "type": schema.PaymentRequestType.wallet},
                {"amount": "20", "wallet_deduct_amount": "15", "type": schema.PaymentRequestType.partial},
                {"amount": "30", "wallet_deduct_amount": "20", "type": schema.PaymentRequestType.partial_direct},
            ],
            Decimal('0'),
            Decimal('50'),
            Decimal('5'),
        ),
        # Add more test cases as needed
    ]
)
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_prepaid_wallet_subscription_reset_or_carry_over(
    membership_mock,
    test_db,
    plan_type,
    initial_topup,
    payment_requests,
    expected_deduction,
    expected_topup,
    expected_balance,
):
    with contextmanager(create_db_session)() as db:
        # 1. create wallet
        organization = OrganizationFactory()
        db.flush()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.flush()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.flush()
        membership_id = str(member.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(member)

        wallet = WalletFactory(
            member_id=membership_id,
            currency=schema.Currency.myr,
        )
        db.commit()

        # 2. create prepaid wallet plan + batch + subscription + topup
        prepaid_wallet_plan = PrepaidWalletPlanFactory(
            plan_type=plan_type,
            interval=schema.PrepaidWalletPlanInterval.monthly,
            amount=50,
        )
        db.flush()

        prepaid_wallet_plan_id = str(prepaid_wallet_plan.id)
        prepaid_wallet_subscription = ********************************(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            member_id=membership_id,
        )

        utc_today = datetime.now(ZoneInfo('UTC')).replace(hour=0, minute=0, second=0, microsecond=0)
        start_time, end_time = utils.get_next_start_end_time(utc_today, schema.PrepaidWalletPlanInterval.monthly)
        prepaid_wallet_plan_batch = PrepaidWalletPlanBatchFactory(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            # snapshots of the plan information at the time of batch creation
            amount=prepaid_wallet_plan.amount,
            currency=prepaid_wallet_plan.currency,
            plan_type=prepaid_wallet_plan.plan_type,
            interval=prepaid_wallet_plan.interval,
            start_time=start_time,
            end_time=end_time,
            status=None,
        )
        db.flush()

        prepaid_wallet_subscription_id = str(prepaid_wallet_subscription.id)
        prepaid_wallet_plan_batch_id = str(prepaid_wallet_plan_batch.id)
        utils.topup_prepaid_wallet_transaction(
            db=db,
            member_id=membership_id,
            amount=initial_topup,
            currency=schema.PrepaidWalletCurrency.myr,
            prepaid_wallet_subscription_id=prepaid_wallet_subscription_id,
            prepaid_wallet_plan_batch_id=prepaid_wallet_plan_batch_id,
            auto_commit=False,
        )
        db.commit()

        # 3. Use wallet credit by creating payment requests
        payment_request_time = start_time + (end_time - start_time) / 2  # ensure searchable in the batch
        charging_session_bill_ids = set()
        for request in payment_requests:
            charging_session_bill = ChargingSessionBillFactory(
                id=str(uuid.uuid4()),
                charging_session_id=str(uuid.uuid4()),
            )
            payment_request = PaymentRequestFactory(
                type=request["type"],
                amount=request["amount"],
                wallet_deduct_amount=request["wallet_deduct_amount"],
                currency=schema.Currency.myr,
                reason=schema.PaymentRequestReason.payment,
                status=schema.PaymentRequestStatus.done,
                wallet_deduct_status=schema.PaymentRequestStatus.done if request["wallet_deduct_amount"] else None,
                member_id=membership_id,
                charging_session_bill_id=str(charging_session_bill.id),
            )
            payment_request.created_at = payment_request_time
            charging_session_bill_ids.add(payment_request.charging_session_bill_id)

        db.commit()

        # 4. Reset/Carry Over prepaid wallet subscription
        utils.reset_or_carry_over_prepaid_wallet_subscription(
            db=db,
            prepaid_wallet_plan_batch_id=prepaid_wallet_plan_batch_id,
        )

        # 5. Check wallet transactions
        prepaid_wallet_deduction_transaction = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.prepaid_wallet_subscription_id == prepaid_wallet_subscription_id,
            models.PrepaidWalletTransaction.prepaid_wallet_plan_batch_id == prepaid_wallet_plan_batch_id,
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.deduction,
        ).one_or_none()

        prepaid_wallet_topup_transaction = db.query(models.PrepaidWalletTransaction).filter(
            models.PrepaidWalletTransaction.prepaid_wallet_subscription_id == prepaid_wallet_subscription_id,
            models.PrepaidWalletTransaction.prepaid_wallet_transaction_id == prepaid_wallet_deduction_transaction.id,
            models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
        ).one_or_none()

        assert prepaid_wallet_deduction_transaction is not None
        assert prepaid_wallet_topup_transaction is not None
        assert prepaid_wallet_deduction_transaction.amount == expected_deduction
        assert prepaid_wallet_topup_transaction.amount == expected_topup

        # for 0 amount deduction, no charging_session_bill_ids are attached
        if expected_deduction > 0:
            assert {
                str(bill_id) for bill_id in
                prepaid_wallet_deduction_transaction.charging_session_bill_ids
            } == charging_session_bill_ids

        # 6. Check wallet balance
        utils.update_wallet_balance(db=db, wallet_id=wallet.id)
        wallet = db.query(models.Wallet).get(wallet.id)

        # Final Wallet Balance:
        # initial topup
        # - payment requests
        # - what remains
        # + topup based on plan's latest amount (if reset)
        # + what remains (if carry_over)
        # Example:
        # Reset: 100 - (10 + 15 + 20) - 55 + 50 = 50
        # Carry Over: 100 - (10 + 15 + 20) - 55 + 55 = 55
        assert Decimal(str(wallet.balance)) == expected_balance

        # 7. Check batches status
        db.refresh(prepaid_wallet_plan_batch)
        assert prepaid_wallet_plan_batch.status == schema.PrepaidWalletPlanBatchStatus.deduction_done

        next_batch = db.query(models.PrepaidWalletPlanBatch).get(prepaid_wallet_plan_batch.next_batch_id)
        assert next_batch.status == schema.PrepaidWalletPlanBatchStatus.topup_done

        db.refresh(prepaid_wallet_plan)
        assert prepaid_wallet_plan.latest_batch_id == next_batch.id


@pytest.mark.parametrize(
    "plan_type, plan_amount, payment_requests, expected_deduction, expected_topup, expected_balance",
    [
        (
            schema.PrepaidWalletPlanType.reset,
            100,
            [
                {"amount": "10", "wallet_deduct_amount": None, "type": schema.PaymentRequestType.wallet},
                {"amount": "20", "wallet_deduct_amount": "15", "type": schema.PaymentRequestType.partial},
                {"amount": "30", "wallet_deduct_amount": "20", "type": schema.PaymentRequestType.partial_direct},
            ],
            Decimal('55'),
            Decimal('100'),
            Decimal('100'),
        ),
        (
            schema.PrepaidWalletPlanType.carry_over,
            100,
            [
                {"amount": "10", "wallet_deduct_amount": None, "type": schema.PaymentRequestType.wallet},
                {"amount": "20", "wallet_deduct_amount": "15", "type": schema.PaymentRequestType.partial},
                {"amount": "30", "wallet_deduct_amount": "20", "type": schema.PaymentRequestType.partial_direct},
            ],
            Decimal('55'),
            Decimal('100'),
            Decimal('100'),
        ),
    ]
)
@patch('app.utils.crud.base.BaseCRUD.membership')
def test_prepaid_wallet_subscription_halfway_deactivated_and_reactivated(
    membership_mock,
    test_db,
    plan_type,
    plan_amount,
    payment_requests,
    expected_deduction,
    expected_topup,
    expected_balance,
):
    with contextmanager(create_db_session)() as db:
        # 1.1 create wallet
        organization = OrganizationFactory()
        db.flush()
        organization_id = str(organization.id)

        user = UserFactory(
            is_verified=True,
            is_superuser=True,
            verification_method='email',
            organization_id=organization_id
        )
        db.flush()

        member = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.flush()
        membership_id = str(member.id)
        membership_mock.return_value = schema.MembershipResponse.from_orm(member)

        wallet = WalletFactory(
            member_id=membership_id,
            currency=schema.Currency.myr,
        )
        db.commit()

        # 1.2 create prepaid wallet plan + batch + subscription + topup
        prepaid_wallet_plan = PrepaidWalletPlanFactory(
            plan_type=plan_type,
            interval=schema.PrepaidWalletPlanInterval.monthly,
            amount=plan_amount,
            is_active=True,
        )
        db.flush()

        prepaid_wallet_plan_id = str(prepaid_wallet_plan.id)
        month_start = datetime.now(ZoneInfo('UTC')).replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        month_end = utils.get_current_end_time(month_start, schema.PrepaidWalletPlanInterval.monthly)
        _ = utils.create_new_prepaid_wallet_plan_batch(db, prepaid_wallet_plan.id, month_start)

        _ = ********************************(
            prepaid_wallet_plan_id=prepaid_wallet_plan_id,
            member_id=membership_id,
        )
        db.commit()
        # db.refresh(prepaid_wallet_plan)
        # db.refresh(prepaid_wallet_plan_batch)
        # db.refresh(prepaid_wallet_subscription)

        # 1.3 Use wallet credit by creating payment requests
        now_utc = datetime.now(ZoneInfo('UTC'))
        payment_request_time = now_utc + (month_end - now_utc) / 2  # ensure searchable in the batch
        charging_session_bill_ids = set()
        for request in payment_requests:
            charging_session_bill = ChargingSessionBillFactory(
                id=str(uuid.uuid4()),
                charging_session_id=str(uuid.uuid4()),
            )
            payment_request = PaymentRequestFactory(
                type=request["type"],
                amount=request["amount"],
                wallet_deduct_amount=request["wallet_deduct_amount"],
                currency=schema.Currency.myr,
                reason=schema.PaymentRequestReason.payment,
                status=schema.PaymentRequestStatus.done,
                wallet_deduct_status=schema.PaymentRequestStatus.done if request["wallet_deduct_amount"] else None,
                member_id=membership_id,
                charging_session_bill_id=str(charging_session_bill.id),
            )
            payment_request.created_at = payment_request_time
            charging_session_bill_ids.add(payment_request.charging_session_bill_id)

        db.commit()

        # 2.1 Update Plan to Active to trigger topup whole batch
        # db.refresh(prepaid_wallet_plan)
        utils.topup_prepaid_wallet_plan(db, prepaid_wallet_plan)

        latest_batch = db.query(models.PrepaidWalletPlanBatch).order_by(
            models.PrepaidWalletPlanBatch.created_at.desc()
        ).first()
        assert latest_batch.status == schema.PrepaidWalletPlanBatchStatus.topup_done

        latest_trans = db.query(models.PrepaidWalletTransaction).order_by(
            models.PrepaidWalletTransaction.created_at.desc()
        ).first()
        assert latest_trans.transaction_type == schema.PrepaidWalletTransactionType.topup
        assert latest_trans.amount == plan_amount

        # 2.2 Deactivate Plan to trigger deduction
        prepaid_wallet_plan.is_active = False
        latest_batch = crud.get_prepaid_wallet_plan_batch(db, prepaid_wallet_plan.latest_batch_id)
        utils.deduct_prepaid_wallet_batch(db, latest_batch)

        db.commit()
        # db.refresh(prepaid_wallet_plan)

        latest_batch = db.query(models.PrepaidWalletPlanBatch).order_by(
            models.PrepaidWalletPlanBatch.created_at.desc()
        ).first()
        assert latest_batch.status == schema.PrepaidWalletPlanBatchStatus.deduction_done

        latest_trans = db.query(models.PrepaidWalletTransaction).order_by(
            models.PrepaidWalletTransaction.created_at.desc()
        ).first()
        assert latest_trans.transaction_type == schema.PrepaidWalletTransactionType.deduction
        assert latest_trans.amount == expected_deduction

        # earliest_topup_trans = db.query(models.PrepaidWalletTransaction).filter(
        #     models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
        # ).order_by(
        #     models.PrepaidWalletTransaction.created_at.asc()
        # ).first()
        # print(f"{earliest_topup_trans.__dict__=} \n")

        # 2.3 Activate again to trigger new batch creation and topup
        prepaid_wallet_plan.is_active = True
        db.commit()
        # db.refresh(prepaid_wallet_plan)
        utils.topup_prepaid_wallet_plan(db, prepaid_wallet_plan)

        latest_batch = db.query(models.PrepaidWalletPlanBatch).order_by(
            models.PrepaidWalletPlanBatch.created_at.desc()
        ).first()
        assert latest_batch.status == schema.PrepaidWalletPlanBatchStatus.topup_done

        latest_trans = db.query(models.PrepaidWalletTransaction).order_by(
            models.PrepaidWalletTransaction.created_at.desc()
        ).first()
        assert latest_trans.transaction_type == schema.PrepaidWalletTransactionType.topup
        assert latest_trans.amount == expected_topup

        # earliest_topup_trans = db.query(models.PrepaidWalletTransaction).filter(
        #     models.PrepaidWalletTransaction.transaction_type == schema.PrepaidWalletTransactionType.topup,
        # ).order_by(
        #     models.PrepaidWalletTransaction.created_at.asc()
        # ).first()
        # print(f"{earliest_topup_trans.__dict__=} \n")

        # 2.4 Update and check wallet balance
        utils.update_wallet_balance(db=db, wallet_id=wallet.id)
        wallet = db.query(models.Wallet).get(wallet.id)
        assert Decimal(str(wallet.balance)) == expected_balance

        # 2.5 Sanity check
        # - ensure no mutation of transactions after above operations
        # - ensure batches are continuity within same interval
        trans = db.query(models.PrepaidWalletTransaction).order_by(
            models.PrepaidWalletTransaction.created_at.asc()
        ).all()

        topup_1 = trans[0]
        deduct_1 = trans[1]
        topup_2 = trans[2]

        assert topup_1.amount == plan_amount
        assert deduct_1.amount == expected_deduction
        assert topup_2.amount == expected_topup

        batches = db.query(models.PrepaidWalletPlanBatch).order_by(
            models.PrepaidWalletPlanBatch.created_at.asc()
        ).all()

        batch_1 = batches[0]
        batch_2 = batches[1]

        assert prepaid_wallet_plan.latest_batch_id == batch_2.id
        assert batch_1.status == schema.PrepaidWalletPlanBatchStatus.deduction_done
        assert batch_2.status == schema.PrepaidWalletPlanBatchStatus.topup_done
        assert batch_1.start_time == month_start
        assert batch_2.end_time == month_end
        assert batch_2.start_time == batch_1.end_time + timedelta(microseconds=1)


@pytest.mark.asyncio
async def test_delete_emaid_for_pcid_locally(test_db):
    with contextmanager(create_db_session)() as db:
        vehicle = VehicleFactory()
        db.commit()
        id = vehicle.id
        organization = OrganizationFactory()
        db.commit()
        organization_id = str(organization.id)
        user = UserFactory(
            is_verified=True,
            verification_method='phone',
            phone_number=faker_phone_number(fake),
            organization_id=organization_id
        )
        db.commit()

        mem = MembershipFactory(
            organization_id=organization_id,
            user_id=f'{user.id}',
            user_id_tag='test_id_tag',
            vehicles=[vehicle]
        )
        db.commit()

        staff = UserFactory(
            is_verified=True,
            verification_method='email',
            organization_id=organization_id,
            is_superuser=True
        )
        db.commit()

        MembershipFactory(
            organization_id=organization_id,
            user_id=f'{staff.id}',
            membership_type=schema.MembershipType.staff,
        )
        db.commit()

        from app.middlewares import set_admin_as_context_user
        set_admin_as_context_user(db)

        emaid = EmaidFactory(vehicle_id=str(vehicle.id))
        emaid.vehicle = vehicle
        db.commit()

        idtag = IDTagFactory(id_tag=emaid.emaid, member_id=mem.id, emaid_id=str(emaid.id), type=schema.IDTagType.pnc)
        idtag.emaid = emaid
        db.commit()

        idtag = idtag.id_tag
        emaid = emaid.emaid

        # Before
        db_vehicle = db.query(models.Vehicle).filter(models.Vehicle.id == id).first()
        db_emaid = db.query(models.Emaid).filter(models.Emaid.emaid == emaid).first()
        db_id_tag = db.query(models.IDTag).filter(models.IDTag.id_tag == idtag).first()

        assert db_vehicle.pcid == 'HUBOpenProvCert001'
        assert db_emaid.is_deleted is False
        assert db_id_tag.is_deleted is False

        await utils.delete_emaid_for_pcid_locally(db, vehicle.pcid, db_emaid.emaid)

        # After
        db_vehicle = db.query(models.Vehicle).filter(models.Vehicle.id == id).first()
        db_emaid = db.query(models.Emaid).filter(models.Emaid.id == db_emaid.id).first()
        db_id_tag = db.query(models.IDTag).filter(models.IDTag.id == db_id_tag.id).first()

        assert db_vehicle.pcid is None
        assert db_emaid.is_deleted is True
        assert db_id_tag.is_active is False


@patch('app.settings.OTP_VALIDATE_COUNTRY', True)
def test_is_phone_number_in_supported_country():
    phone_number1 = "+601122334455"  # MY
    phone_number2 = "+88122345678"  # RU

    res = utils.is_phone_number_in_supported_country(phone_number1)
    assert res is True

    res = utils.is_phone_number_in_supported_country(phone_number2)
    assert res is False


@patch('app.settings.OTP_VALIDATE_COUNTRY', False)
def test_is_phone_number_in_supported_country_without_validation():
    phone_number1 = "+601122334455"  # MY
    phone_number2 = "+88122345678"  # RU

    res = utils.is_phone_number_in_supported_country(phone_number1)
    assert res is True

    res = utils.is_phone_number_in_supported_country(phone_number2)
    assert res is True
