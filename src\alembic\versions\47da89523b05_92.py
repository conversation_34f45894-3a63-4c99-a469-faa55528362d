"""92

Revision ID: 47da89523b05
Revises: a10bb957d869
Create Date: 2024-05-08 20:39:18.565279

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '47da89523b05'
down_revision = 'a10bb957d869'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('main_pre_auth_payment_refund',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('transaction_id', sa.String(), nullable=True),
    sa.Column('domain', sa.String(), nullable=True),
    sa.Column('response', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('pre_auth_payment_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.ForeignKeyConstraint(['pre_auth_payment_id'], ['main_pre_auth_payment.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('main_pre_auth_payment_refund')
    # ### end Alembic commands ###
