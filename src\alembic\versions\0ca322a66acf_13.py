"""13

Revision ID: 0ca322a66acf
Revises: 51d0a8d7e263
Create Date: 2022-10-06 08:41:13.702412

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0ca322a66acf'
down_revision = '51d0a8d7e263'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('main_subscription', sa.Column('number', sa.String(), nullable=True))
    op.add_column('main_subscription_custom_plan', sa.Column('charger_name', sa.String(), nullable=True))
    op.add_column('main_subscription_custom_plan', sa.Column('connector_number', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('main_subscription_custom_plan', 'connector_number')
    op.drop_column('main_subscription_custom_plan', 'charger_name')
    op.drop_column('main_subscription', 'number')
    # ### end Alembic commands ###
