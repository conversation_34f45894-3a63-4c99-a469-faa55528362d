import logging

from datetime import datetime, timedelta, timezone
from typing import Callable, Optional
from jose import jwt

from fastapi import APIRouter, Depends, HTTPException, Request

from twilio.base.exceptions import TwilioRestException
from twilio.rest import Client

from app import settings, schema, crud, exceptions, models
from app.crud import get_member_by_id_tag, check_member_authorization_by_id_tag, get_migrated_user_by_email

from app.schema import v2 as schema_v2
from app.mail import Send<PERSON><PERSON><PERSON><PERSON>, RecipientsSchema
from app.permissions import x_api_key

from app.database import create_session, SessionLocal
from app.utils import (
    decode_auth_token_from_headers,
    RouteErrorHandler,
    restrict_regular_user_registration_v2,
    is_valid_otp_channel,
    is_phone_number_in_supported_country
)
from app.middlewares import set_admin_as_context_user

from app.settings import APOLLO_MAIN_DOMAIN, MAIN_ROOT_PATH, ENABLE_RESTRICT_OTP_FAILURE, OTP_CHANNEL_SMS

csms_path = f"{APOLLO_MAIN_DOMAIN}/{MAIN_ROOT_PATH}/api/v1/csms"

logger = logging.getLogger(__name__)

ROOT_PATH = settings.MAIN_ROOT_PATH

FAILED_OTP_MESSAGE = settings.FAILED_OTP_MESSAGE
INVALID_OTP_CHANNEL_MESSAGE = settings.INVALID_OTP_CHANNEL_MESSAGE
UNSUPPORTED_COUNTRY_PHONE_NUMBER_MESSAGE = settings.UNSUPPORTED_COUNTRY_PHONE_NUMBER_MESSAGE

twilio_client = Client(settings.TWILIO['ACCOUNT_SID'], settings.TWILIO['AUTH_TOKEN'])

router = APIRouter(
    prefix=f"/{ROOT_PATH}/api/v1/auth",
    tags=['v2 auth', ],
    route_class=RouteErrorHandler,
    dependencies=[Depends(x_api_key)],
)


def send_otp(phone_number: str, channel: str = OTP_CHANNEL_SMS,
             on_error: Optional[Callable[[], None]] = None):  # pylint: disable=unsubscriptable-object
    if not is_valid_otp_channel(channel):
        raise HTTPException(status_code=422, detail=INVALID_OTP_CHANNEL_MESSAGE)
    if not is_phone_number_in_supported_country(phone_number):
        raise HTTPException(status_code=422, detail=UNSUPPORTED_COUNTRY_PHONE_NUMBER_MESSAGE)

    try:
        # OTP expires in 10mins
        # https://support.twilio.com/hc/en-us/articles/************-What-is-the-Default-Verify-V2-Expiration-Time-
        return twilio_client.verify \
            .services(settings.TWILIO['VERIFY_SERVICE_SID']) \
            .verifications \
            .create(to=phone_number, channel=channel)
    except Exception as e:  # pylint: disable=broad-except
        if on_error:
            on_error()
        logger.error("Unable to send OTP! Exception: %s", str(e))
        raise HTTPException(status_code=422, detail='Unable to send OTP.')


def verify_otp(phone_number: str, token: str):
    return twilio_client.verify \
        .services(settings.TWILIO['VERIFY_SERVICE_SID']) \
        .verification_checks \
        .create(to=phone_number, code=token)


@router.post('/ruby/authorization', response_model=schema.Message, tags=['auth,'])
async def ruby_authorization(id_tag: str, dbsession: SessionLocal = Depends(create_session)):
    set_admin_as_context_user(dbsession)
    result = False
    reason = 'Invalid'
    try:
        db_id_tag = check_member_authorization_by_id_tag(dbsession, id_tag)
        expiry_date = db_id_tag.expiration
        if not db_id_tag.is_active:
            result = False
            reason = 'Blocked'

        if db_id_tag.expiration.astimezone(timezone.utc) < datetime.now(timezone.utc):
            result = False
            reason = 'Expired'

    except exceptions.ApolloObjectDoesNotExist:
        try:
            get_member_by_id_tag(dbsession, id_tag)
            expiry_date = (datetime.now() + timedelta(
                days=100)).isoformat()  # user_id_tag has no expiration
            result = True
            reason = 'user_id_tag'
        except exceptions.ApolloObjectDoesNotExist:
            response_message = schema.Message(
                reason=schema.MessageReason.response,
                body=schema.MessageBody(
                    message_type='AuthorizationResponse',
                    parameters={
                        'hash': None,
                        'id_tag': id_tag,
                        'expiry_date': None,
                        'is_authorized': result,
                        'reason': reason
                    }
                )
            )
            return response_message.dict()

    response_message = schema.Message(
        reason=schema.MessageReason.response,
        body=schema.MessageBody(
            message_type='AuthorizationResponse',
            parameters={
                'hash': None,
                'id_tag': id_tag,
                'expiry_date': str(expiry_date),
                'is_authorized': result,
                'reason': reason
            }
        )
    )
    return response_message.dict()


# pylint: disable=too-many-locals
# pylint: disable=too-many-statements
# pylint: disable=too-many-branches
@router.post("/signup", response_model=schema.BasicMessage, tags=['auth', ])
async def signup(data: schema.SignupRequest, channel: str = OTP_CHANNEL_SMS,  # noqa: MC0001
                 dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-up endpoint for a normal user using their phone and password
    """
    success_msg = 'Verification code has been sent to your phone number.'

    if not is_valid_otp_channel(channel):
        raise HTTPException(status_code=422, detail=INVALID_OTP_CHANNEL_MESSAGE)

    user_data = data.dict()
    user_data['is_guest'] = False
    user_data['user_type'] = schema.UserType.regular
    password = user_data.pop('password')
    # organization_id = user_data.pop('organization_id')
    first_name = user_data.pop('first_name')
    last_name = user_data.pop('last_name')
    user_id_tag = user_data.pop('user_id_tag')
    vehicle_model = user_data.pop('vehicle_model', None)
    vehicle_brand = user_data.pop('vehicle_brand', None)
    allow_marketing = user_data.pop('allow_marketing', False)
    set_admin_as_context_user(dbsession)

    try:
        restrict_regular_user_registration_v2(dbsession, user_data.get('phone_number'), user_data.get('email'),
                                              user_data['user_type'])
        db_user = crud.create_user_v2(dbsession, user_data)
    except exceptions.ApolloDuplicateMembershipError:
        pass
    except exceptions.ApolloDuplicateUser:
        try:
            db_user = crud.get_normal_user_by_phone_or_email_v2(dbsession, data.phone_number, data.email)
            if db_user.is_verified:
                if db_user.email == data.email:
                    raise HTTPException(400, 'Email already registered')
                if db_user.phone_number == data.phone_number:
                    raise HTTPException(400, 'Phone number already registered')
        except exceptions.ApolloObjectDoesNotExist:
            raise HTTPException(400, (
                "Not permitted to register with provided info, "
                "please contact support for assistance."
            ))

    # If db_user is migrated, DO NOT update the email as it's CDG's way to identify a unique user.
    if db_user.is_migrated:
        membership = crud.get_user_membership_by_organization_id(dbsession, str(db_user.id),
                                                                 str(db_user.organization_id))
        try:
            membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, str(membership.id))
        except exceptions.ApolloObjectDoesNotExist:
            extended_membership_data = schema.MembershipExtended(
                membership_id=str(membership.id),
            )
            membership_extended = crud.create_user_membership_extended(dbsession, extended_membership_data)

        crud.update_failed_otp_count_on_extended_membership(db=dbsession,
                                                            membership_extended_id=str(membership_extended.id),
                                                            reset=True)

        try:
            crud.get_membership_dunning_by_membership_id(dbsession, str(membership.id))
        except exceptions.ApolloObjectDoesNotExist:
            membership_dunning_data = schema.InitiateMembershipDunning(
                membership_id=str(membership.id),
            )
            crud.create_membership_dunning(dbsession, membership_dunning_data)
        if not membership_extended.phone_verified or not membership_extended.email_verified:
            set_admin_as_context_user(dbsession)
            if membership_extended.verification_token:
                if membership_extended.verification_token.created_at + timedelta(
                        minutes=2) > datetime.now().astimezone():
                    raise HTTPException(status_code=422, detail='Verification token has not expired yet.')
            if membership.user.email != data.email:
                raise HTTPException(400, 'Email already registered')
                # db_user_exists = crud.UserCRUD.query(dbsession, check_permission=False) \
                #     .filter(models.User.email == data.email,
                #             models.User.organization_id == data.organization_id) \
                #     .one_or_none()
                # if db_user_exists:
                #     if db_user_exists.id != membership.user_id and db_user_exists.is_verified:

            db_user_exists = crud.UserCRUD.query(dbsession, check_permission=False) \
                .filter(models.User.phone_number == data.phone_number) \
                .one_or_none()
            if db_user_exists:
                if db_user_exists.id != membership.user_id and db_user_exists.is_verified:
                    raise HTTPException(400, 'Phone number already registered')

                crud.UserCRUD.update(dbsession, db_user_exists.id, {'phone_number': None})
            crud.update_membership_profile(dbsession, data, str(membership.id))
            crud.update_user_password(
                dbsession,
                password,
                str(membership.id),
            )
            send_otp(data.phone_number, channel=channel)
            crud.create_dummy_verification_and_update_membership_verification(dbsession, str(db_user.id),
                                                                              str(membership_extended.id))
            return schema.BasicMessage(detail=success_msg)
    # ELSE DO WITH NORMAL FLOW
    try:
        # Create User's membership
        # if user_id_tag:
        #     if crud.user_id_tag_exists(dbsession, user_id_tag):
        #         raise exceptions.ApolloDuplicateUserIdTagError()
        # else:
        user_id_tag = crud.create_user_id_tag(dbsession)

        membership_data = schema.Membership(
            first_name=first_name,
            last_name=last_name,
            organization_id=user_data['organization_id'],
            user_id_tag=user_id_tag,
            user_id=str(db_user.id),
            password=password,
            vehicle_model=vehicle_model,
            allow_marketing=allow_marketing,
        )
        if vehicle_brand is not None:
            del membership_data.vehicle_model

        db_member = crud.create_user_membership(dbsession, membership_data)
        # Attach default role to the membership
        regular_role = crud.get_regular_role(dbsession)
        db_member.roles.append(regular_role)

        # Create User's Extended Membership Profile
        extended_membership_data = schema.MembershipExtended(
            membership_id=str(db_member.id),
        )

        db_membership_extended = crud.create_user_membership_extended(dbsession, extended_membership_data)
        crud.update_failed_otp_count_on_extended_membership(db=dbsession,
                                                            membership_extended_id=str(db_membership_extended.id),
                                                            reset=True)

        membership_dunning_data = schema.InitiateMembershipDunning(
            membership_id=str(db_member.id),
        )
        crud.create_membership_dunning(dbsession, membership_dunning_data)
        if vehicle_brand is not None:
            vehicle_data = schema.VehicleCreate(
                brand=vehicle_brand,
                model=vehicle_model,
            )
            db_vehicle = crud.create_vehicle(dbsession, vehicle_data)
            crud.add_membership_vehicle(dbsession, str(db_vehicle.id), str(db_member.id))
        send_otp(data.phone_number, channel=channel)
        crud.create_dummy_verification_and_update_membership_verification(dbsession, str(db_user.id),
                                                                          str(db_membership_extended.id))

        return schema.BasicMessage(detail=success_msg)

    # ChargEV Requirements: To auto resend OTP if user has been registered but not verified
    except exceptions.ApolloDuplicateMembershipError:
        membership = crud.get_user_membership_by_organization_id(dbsession, str(db_user.id), str(data.organization_id))
        try:
            membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, str(membership.id))
        except exceptions.ApolloObjectDoesNotExist:
            extended_membership_data = schema.MembershipExtended(
                membership_id=str(membership.id),
            )

            membership_extended = crud.create_user_membership_extended(dbsession, extended_membership_data)

        try:
            crud.get_membership_dunning_by_membership_id(dbsession, str(membership.id))
        except exceptions.ApolloObjectDoesNotExist:
            membership_dunning_data = schema.InitiateMembershipDunning(
                membership_id=str(membership.id),
            )
            crud.create_membership_dunning(dbsession, membership_dunning_data)
        crud.update_failed_otp_count_on_extended_membership(db=dbsession,
                                                            membership_extended_id=str(membership_extended.id),
                                                            reset=True)

        if not membership_extended.phone_verified:
            # set_member_as_context_user(dbsession, str(membership.id))
            set_admin_as_context_user(dbsession)
            if membership_extended.verification_token:
                if membership_extended.verification_token.created_at + timedelta(
                        minutes=2) > datetime.now().astimezone():
                    raise HTTPException(status_code=422, detail='Verification token has not expired yet.')

            if membership.user.email != data.email:
                db_user_exists = crud.UserCRUD.query(dbsession, check_permission=False) \
                    .filter(models.User.email == data.email,
                            models.User.organization_id == data.organization_id) \
                    .one_or_none()

                if db_user_exists:
                    if db_user_exists.id != membership.user_id and db_user_exists.is_verified:
                        raise HTTPException(400, 'Email already registered')

                    crud.UserCRUD.update(dbsession, db_user_exists.id, {'email': None})

            elif membership.user.phone_number != data.phone_number:
                db_user_exists = crud.UserCRUD.query(dbsession, check_permission=False) \
                    .filter(models.User.phone_number == data.phone_number,
                            models.User.organization_id == data.organization_id,
                            models.User.user_type == schema.UserType.regular) \
                    .one_or_none()

                if db_user_exists:
                    if db_user_exists.id != membership.user_id and db_user_exists.is_verified:
                        raise HTTPException(400, 'Phone number already registered')

                    crud.UserCRUD.update(dbsession, db_user_exists.id, {'phone_number': None})

            try:
                vehicle_query = crud.get_membership_vehicle_list_query(dbsession, membership.id, check_permission=False)
                db_vehicle = vehicle_query.order_by(models.Vehicle.created_at.asc()).all()
                for vehicle in db_vehicle:
                    crud.remove_membership_vehicle(dbsession, str(vehicle.id), str(membership.id))
                    crud.delete_vehicle(dbsession, str(vehicle.id))

                if vehicle_brand is not None:
                    vehicle_data = schema.VehicleCreate(
                        brand=vehicle_brand,
                        model=vehicle_model,
                    )
                    db_vehicle = crud.create_vehicle(dbsession, vehicle_data)
                    crud.add_membership_vehicle(dbsession, str(db_vehicle.id), str(membership.id))
            except Exception as e:  # pylint: disable=broad-except
                logger.error("Sign up API with vehicle creation failed with error as: %s", e)

            crud.update_membership_profile(dbsession, data, str(membership.id))
            crud.update_user_password(
                dbsession,
                password,
                str(membership.id),
            )
            send_otp(data.phone_number, channel=channel)
            crud.create_dummy_verification_and_update_membership_verification(dbsession, str(db_user.id),
                                                                              str(membership_extended.id))
            return schema.BasicMessage(detail=success_msg)

        if db_user.email == data.email and db_user.phone_number != data.phone_number:
            raise HTTPException(400, 'Email already registered')

        raise HTTPException(status_code=422, detail='Membership for user in this organization already exists.')
    except HTTPException as http_e:
        print('HTTP exception:', http_e.detail)  # This will catch the 422 from send_otp
        raise  # Re-raise the same HTTPException
    except Exception as e:  # noqa
        raise HTTPException(status_code=400, detail=str(e))


@router.post('/signup/request-email', response_model=schema.BasicMessage, tags=['auth,'])
async def migration_email_verify_request(data: schema.RequestEmailVerification,  # pylint:disable=too-many-branches
                                         dbsession: SessionLocal = Depends(create_session)):
    try:
        migrated_user = get_migrated_user_by_email(dbsession, data.email)
        if not migrated_user:
            raise HTTPException(status_code=404, detail='Email Not Found.')

        try:
            membership = crud.get_user_membership_by_organization_id(dbsession, str(migrated_user.id),
                                                                     str(migrated_user.organization_id))
            membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, str(membership.id))
            if not membership_extended.phone_verified:
                raise HTTPException(status_code=404,
                                    detail='Please Make Sure Phone Number is Verified First Before Continuing.')
        except exceptions.ApolloObjectDoesNotExist:
            raise HTTPException(status_code=404, detail='Membership not found.')

        if membership_extended.email_verified:
            raise HTTPException(status_code=400, detail="Email already verified.")

        if membership_extended.verification_token:
            if membership_extended.verification_token.created_at + timedelta(minutes=2) > datetime.now().astimezone():
                raise HTTPException(status_code=400, detail="Verification token has not expired yet.")
        token = crud.generate_email_six_digit_token(dbsession, str(migrated_user.id))
        token_dict = {
            'otp_code': token.token
        }

        sendgrid_mail = SendGridMail('Verification Code', RecipientsSchema(emails=[migrated_user.email, ]),
                                     settings.SENDER_EMAIL, settings.SENDER_NAME)
        sendgrid_mail.send_html_mail(token_dict, "verification_cdg.html")

        success_msg = 'Verification code sent to your email.'
        crud.update_verification_token_on_extended_membership(dbsession, str(token.id),
                                                              str(membership_extended.id))
        return schema.BasicMessage(detail=success_msg)

    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=404, detail='Email Not Found.')


@router.post("/signup/verify-email", response_model=schema.TokenResponseWithMemberID, tags=['auth', ])
async def signup_user_verify_email(data: schema.SubmittedEmailVerificationToken,
                                   dbsession: SessionLocal = Depends(create_session)):
    """
    Email Verification endpoint for a migrated user to verify their email with the sent token
    """
    try:
        user = crud.get_migrated_user_by_email_v2(dbsession, data.email)
        membership = crud.get_user_membership_by_organization_id(dbsession, str(user.id),
                                                                 str(user.organization_id))
        membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, str(membership.id))

    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=404, detail='User does not exist.')

    membership_extended_status_update_data = {
        'verification_method': schema.VerificationMethodEnum.phone,
        'email_verified': True,
        'id': str(membership_extended.id),
        'verification_token_id': None,
    }
    user_status_update_data = {
        'verification_method': schema.VerificationMethodEnum.phone,
        'is_verified': True,
        'user_id': str(user.id)
    }
    user_dict = schema.User.from_orm(user)
    is_migrated_user = user_dict.is_migrated

    if is_migrated_user:
        user_status_update_data['migration_status'] = True
        user_status_update_data['created_at'] = datetime.now()

    member_plan_list = crud.get_user_existing_plan(dbsession, str(membership.id))

    if len(member_plan_list) > 0:
        user_status_update_data['link_date'] = datetime.now()

    if membership_extended.verification_token and membership_extended.verification_token.token == data.token:
        crud.update_membership_extended_verification(dbsession, membership_extended_status_update_data)
        crud.update_user_verification_status(dbsession, schema.VerificationMethodUpdate(**user_status_update_data))

        # ChargEV Apps require to return the token on successful verification
        user.password = ''  # nosec
        user = schema.UserAuth.from_orm(user)
        return user.generate_token_with_membership(str(membership.id), is_migrated_user)

    raise HTTPException(status_code=422, detail='Invalid token.')


# pylint: disable=too-many-branches
@router.post("/signup/resend-token", response_model=schema.BasicMessage, tags=['auth', ])
async def signup_user_resend(data: schema.ResendPhoneToken, channel: str = OTP_CHANNEL_SMS,  # noqa: MC0001
                             dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-up endpoint for a normal user to request a new verification token [to verify account - not to mixed with
    reset password]
    """
    success_msg = 'Token has been re-sent to your phone number.'
    if not is_valid_otp_channel(channel):
        raise HTTPException(status_code=422, detail=INVALID_OTP_CHANNEL_MESSAGE)
    try:
        # user = crud.get_user_by_phone_number_and_organization_id(dbsession, data.phone_number, data.organization_id)
        user = crud.get_user_by_phone_number_normal_user_v2(dbsession, data.phone_number)
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=404, detail='User does not exist.')

    try:
        membership = crud.get_user_membership_by_organization_id(dbsession, str(user.id), str(user.organization_id))
        membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, str(membership.id))
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=404, detail='Membership not found.')

    if ENABLE_RESTRICT_OTP_FAILURE and membership_extended.failed_otp_count >= 3:
        phone_number = getattr(data, "phone_number", None)
        error_message = FAILED_OTP_MESSAGE

        if phone_number:
            error_message = f"{FAILED_OTP_MESSAGE}\n\nYour number: ({phone_number})"

        raise HTTPException(status_code=429, detail=error_message)

    if not membership_extended.email_verified and not membership_extended.phone_verified:
        if membership_extended.verification_token:
            if membership_extended.verification_token.created_at + timedelta(minutes=2) > datetime.now().astimezone():
                raise HTTPException(status_code=422, detail='Verification token has not expired yet.')

        send_otp(phone_number=data.phone_number, channel=channel,
                 on_error=lambda: crud.update_failed_otp_count_on_extended_membership(
                     db=dbsession, membership_extended_id=str(membership_extended.id), plus=1))
        crud.create_dummy_verification_and_update_membership_verification(dbsession, str(user.id),
                                                                          str(membership_extended.id))
        return schema.BasicMessage(detail=success_msg)

    raise HTTPException(status_code=422, detail='User already verified.')


@router.post("/signup/verify", response_model=schema.TokenResponseWithMemberID, tags=['auth', ])
async def signup_user_verify(data: schema.SubmittedPhoneVerificationToken,
                             dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-up endpoint for a normal user to verify their phone number with the sent token
    """
    # success_msg = 'Account has been successfully verified.'

    try:
        # user = crud.get_user_by_phone_number_and_organization_id(dbsession, data.phone_number, data.organization_id)
        user = crud.get_user_by_phone_number_normal_user_v2(dbsession, data.phone_number)
        membership = crud.get_user_membership_by_organization_id(dbsession, str(user.id),
                                                                 str(user.organization_id))

        # The reason behind there is another call is cause the Mobile / App verificaiton is binded to organization
        # instead of user
        membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, str(membership.id))
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=404, detail='User does not exist.')

    membership_extended_status_update_data = {
        'verification_method': schema.VerificationMethodEnum.phone,
        'phone_verified': True,
        'id': str(membership_extended.id),
        'verification_token_id': None,
    }
    user_status_update_data = {
        'verification_method': schema.VerificationMethodEnum.phone,
        'is_verified': True,
        'user_id': str(user.id)
    }
    user_dict = schema.User.from_orm(user)
    is_migrated_user = user_dict.is_migrated

    # https://www.twilio.com/docs/verify/api/verification-check
    try:
        response = verify_otp(data.phone_number, data.token)
        if response.status == 'approved':
            crud.update_membership_extended_verification(dbsession, membership_extended_status_update_data)
            if not is_migrated_user:
                crud.update_user_verification_status(dbsession,
                                                     schema.VerificationMethodUpdate(**user_status_update_data))

            # ChargEV Apps require to return the token on successful verification
            user.password = ''  # nosec
            user = schema.UserAuth.from_orm(user)
            return user.generate_token_with_membership(str(membership.id), is_migrated_user)

        raise HTTPException(status_code=422, detail='Invalid token.')
    except TwilioRestException:
        raise HTTPException(status_code=422, detail='Verification failed.')


@router.post("/signin/user/password", response_model=schema.TokenResponseWithMemberID,  # noqa: MC0001
             tags=['auth', ])
async def signin_user_password(data: schema.SigninPhonePasswordRequest,  # noqa: MC0001
                               dbsession: SessionLocal = Depends(create_session)):
    """
    Sign-in endpoint for a normal user using their phone and password
    """
    try:
        # user = crud.get_user_by_phone_number_and_organization_id(dbsession, data.phone_number, data.organization_id)
        # user = crud.get_user_by_phone_number(dbsession, data.phone_number)
        user = crud.get_user_by_phone_number_normal_user_v2(dbsession, data.phone_number)
        membership = crud.get_user_membership_by_organization_id(dbsession, str(user.id),
                                                                 str(user.organization_id))
        membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, str(membership.id))
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=4040, detail="User not found.")

    login_attempts = user.login_attempts if user.login_attempts else []
    if settings.RATE_LIMIT_LOGIN:
        rate_login_attempts = int(settings.RATE_LOGIN_ATTEMPTS)
        if login_attempts:
            # Check if there are more than 5 attempts
            if len(login_attempts) >= rate_login_attempts:
                print("Account is locked due to 5 times attempts.")
                raise HTTPException(
                    status_code=403,
                    detail="Account is locked due to too many failed login attempts. Please try again later."
                )
    if not user.is_migrated:
        if not membership_extended.email_verified and not membership_extended.phone_verified:
            raise HTTPException(status_code=4040, detail="User not found.")
    if user.is_migrated:
        if not membership_extended.email_verified or not membership_extended.phone_verified:
            raise HTTPException(status_code=4040, detail="User not found.")
        # user that are not verified are considered to be non-existance as per requirement, they may re-signup
        # raise HTTPException(status_code=422, detail='Please verify your account.')

    # Get user's membership to organization
    try:
        membership = crud.get_user_membership_by_organization_id(
            dbsession,
            str(user.id),
            user.organization_id
        )
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=404, detail='User not found.')
    user.password = membership.password
    user_dict = schema.User.from_orm(user)
    is_migrated_user = user_dict.is_migrated
    user = schema.UserAuth.from_orm(user)
    try:
        user.verify_password(data.password)
    except Exception:
        if settings.RATE_LIMIT_LOGIN:
            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if user.login_attempts is not None:
                user.login_attempts.append(now)
            else:
                user.login_attempts = [now]
            crud.update_user_login_attempts(dbsession, str(user.id), user.login_attempts)

        raise HTTPException(status_code=401,
                            detail='Phone number with password does not matches.')

    if settings.RATE_LIMIT_LOGIN:
        crud.update_user_login_attempts(dbsession, str(user.id), [])

    return user.generate_token_with_membership(str(membership.id), is_migrated_user)


@router.post("/reset-password", response_model=schema.BasicMessage, tags=['auth', ])
async def reset_password(data: schema.ResetPasswordByPhoneNumberRequest, channel: str = OTP_CHANNEL_SMS,
                         dbsession: SessionLocal = Depends(create_session)):
    """
    Endpoint for the user to request a reset password token
    """
    # Get user by email
    if not is_valid_otp_channel(channel):
        raise HTTPException(status_code=422, detail=INVALID_OTP_CHANNEL_MESSAGE)
    try:
        # user = crud.get_user_by_phone_number_and_organization_id(dbsession, data.phone_number, data.organization_id)
        user = crud.get_user_by_phone_number_normal_user_v2(dbsession, data.phone_number)
        membership = crud.get_user_membership_by_organization_id(dbsession, str(user.id),
                                                                 str(user.organization_id))
        # The reason behind there is another call is cause the Mobile / App verificaiton is binded to organization
        # instead of user
        membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, str(membership.id))
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=404, detail='User not found, please register.')

    if not membership_extended.email_verified and not membership_extended.phone_verified:
        raise HTTPException(status_code=404, detail='User not found, please register.')
    if membership_extended.reset_password_token:
        if membership_extended.reset_password_token.created_at + timedelta(minutes=2) > datetime.now().astimezone():
            raise HTTPException(status_code=422, detail='Reset password token has not expired yet.')

    crud.create_dummy_reset_password_and_update_membership_reset_password(dbsession, str(user.id),
                                                                          str(membership_extended.id))

    send_otp(data.phone_number, channel=channel)
    return schema.BasicMessage(detail='Reset password code sent.')


@router.post("/reset-password-confirm", response_model=schema.BasicMessage, tags=['auth', ])
async def reset_password_confirm(data: schema_v2.V2ResetPassword,
                                 dbsession: SessionLocal = Depends(create_session)):
    try:
        user = crud.get_user_by_phone_number_normal_user_v2(dbsession, data.phone_number)
        membership = crud.get_user_membership_by_organization_id(dbsession, str(user.id),
                                                                 str(user.organization_id))
        # The reason behind there is another call is cause the Mobile / App verification is bound to organization
        # instead of user
    except exceptions.ApolloObjectDoesNotExist:
        raise HTTPException(status_code=404, detail="User not found.")

    if data.new_password != data.new_password_confirm:
        raise HTTPException(status_code=422, detail='Passwords do not match.')

    response = verify_otp(user.phone_number, data.token)
    if response.status != 'approved':
        raise HTTPException(status_code=422, detail='Invalid token.')

    # Reset password
    crud.update_user_password(
        dbsession,
        data.new_password,
        str(membership.id),
    )

    if settings.RATE_LIMIT_LOGIN:
        crud.update_user_login_attempts(dbsession, str(user.id), [])
    return schema.BasicMessage(detail='Password has been successfully reset.')


@router.get("/get-profile", response_model=schema.MembershipExtendedResponse, tags=['auth', ])
async def get_profile(request: Request, dbsession: SessionLocal = Depends(create_session)):
    token = request.headers.get('authorization')
    if token:
        token = token.removeprefix(f'{schema.TokenTypeEnum.bearer} ')
        decoded = jwt.decode(
            token,
            key=settings.JWT_SECRET,
            algorithms=[schema.JWT_ALGORITHM, ],
            options={
                'verify_exp': False,
            }
        )

        try:
            membership_filter = {'user_id': decoded['user_id'], 'membership_id': decoded['membership_id']}
            member = crud.get_membership_by_filters(dbsession, membership_filter)
            membership = crud.get_user_membership_extended_by_membership_id(dbsession, str(member.id))
            if member.user.is_guest:
                if member.user.email is None:
                    member.user.email = ''
                member.user.phone_number = ''
            return schema.MembershipExtendedResponse.from_orm(membership)
        except exceptions.ApolloObjectDoesNotExist:
            raise HTTPException(status_code=404, detail='User not found.')

    raise HTTPException(status_code=401, detail='Authorization token is missing.')


@router.post("/send-verify-email", response_model=schema.BasicMessage, tags=['auth', ])
async def send_verification_email(request: Request,
                                  dbsession: SessionLocal = Depends(create_session)):
    auth_token_data = decode_auth_token_from_headers(request.headers)
    membership_id = auth_token_data.get('membership_id')
    user_id = auth_token_data.get('user_id')
    try:
        user = crud.get_user_by_id(dbsession, user_id)
        membership = crud.get_membership_by_id(dbsession, membership_id)
        membership_extended = crud.get_user_membership_extended_by_membership_id(dbsession, membership_id)

    except (exceptions.ApolloObjectDoesNotExist, AttributeError):
        raise HTTPException(status_code=404, detail='User not found.')

    if membership_extended.email_verified:
        raise HTTPException(status_code=400, detail="User already verified.")

    if membership_extended.verification_token:
        if membership_extended.verification_token.created_at + timedelta(minutes=2) > datetime.now().astimezone():
            raise HTTPException(status_code=400, detail="Verification token has not expired yet.")

    token = crud.generate_verification_token_return_model(dbsession, str(user.id))
    verification_url = f'{csms_path}/auth/verify-email?token={token.token}&membership_id={str(membership.id)}'

    verification_dict = {
        'name': (str(membership.first_name)).upper() + ' ' + (str(membership.last_name)).upper(),
        'url': verification_url
    }

    if membership.organization.name.lower() == settings.YGT_ORGANIZATION_NAME.lower():
        sendgrid_mail = SendGridMail('Confirmation instructions', RecipientsSchema(emails=[user.email, ]),
                                     settings.SENDER_EMAIL, settings.YGT_APP_SENDER_NAME)
        sendgrid_mail.send_html_mail(verification_dict, "verification_ygt.html")
    else:
        sendgrid_mail = SendGridMail('Confirmation instructions', RecipientsSchema(emails=[user.email, ]),
                                     settings.SENDER_EMAIL, settings.SENDER_NAME)
        sendgrid_mail.send_html_mail(verification_dict, "verification.html")
    crud.update_verification_token_on_extended_membership(dbsession, str(token.id),
                                                          str(membership_extended.id))
    return schema.BasicMessage(detail='Verification code sent.')


@router.post("/logout", response_model=schema.BasicMessage, tags=['auth', ])
async def logout(request: Request):
    decoded_auth_headers = decode_auth_token_from_headers(request.headers)
    if not decoded_auth_headers:
        raise HTTPException(status_code=400, detail="Invalid token.")
    return schema.BasicMessage(detail='Logout succeed.')
